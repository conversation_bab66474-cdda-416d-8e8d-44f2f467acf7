#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试栅格数据缺失处理的修复
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_processor import MetroDataProcessor

def test_grid_data_missing():
    """测试栅格数据文件缺失时的处理"""
    print("=" * 60)
    print("测试栅格数据缺失处理")
    print("=" * 60)
    
    # 使用默认数据路径
    data_path = "C:/Users/<USER>/Desktop/接驳/"
    
    try:
        # 创建数据处理器
        processor = MetroDataProcessor(
            data_path=data_path,
            distance_threshold=1000,
            use_float16=False
        )
        
        print("\n步骤 1: 加载数据")
        processor.load_data()
        
        print("\n步骤 2: 预处理站点")
        processor.preprocess_stations()
        
        print("\n步骤 3: 预处理特征")
        processor.preprocess_features()
        
        print("\n步骤 4: 构建站点图")
        processor.build_station_graph()
        
        print("\n步骤 5: 预处理栅格数据")
        processor.preprocess_grid_data()
        
        print("\n步骤 6: 构建栅格-站点图")
        processor.build_grid_station_graph()
        
        print("\n步骤 7: 准备时间序列数据")
        processor.prepare_temporal_data()
        
        print("\n步骤 8: 分割数据")
        processor.split_data()
        
        # 检查关键属性
        print("\n" + "="*50)
        print("属性检查结果:")
        print("="*50)
        
        print(f"stations数量: {len(processor.stations)}")
        print(f"station_coords形状: {processor.station_coords.shape}")
        print(f"node_features形状: {processor.node_features.shape}")
        print(f"station_edges形状: {processor.station_edges.shape}")
        
        # 检查栅格相关属性
        print(f"grid_data: {processor.grid_data}")
        print(f"grid_coords: {processor.grid_coords}")
        print(f"grid_features: {processor.grid_features}")
        print(f"grid_station_edges: {processor.grid_station_edges}")
        print(f"grid_station_distances: {processor.grid_station_distances}")
        
        # 检查流量数据
        print(f"in_flows形状: {processor.in_flows.shape}")
        print(f"out_flows形状: {processor.out_flows.shape}")
        print(f"od_flows形状: {processor.od_flows.shape}")
        
        # 检查是否有NaN值
        import numpy as np
        print(f"\nNaN检查:")
        print(f"node_features中NaN数量: {np.isnan(processor.node_features).sum()}")
        print(f"in_flows中NaN数量: {np.isnan(processor.in_flows).sum()}")
        print(f"out_flows中NaN数量: {np.isnan(processor.out_flows).sum()}")
        print(f"od_flows中NaN数量: {np.isnan(processor.od_flows).sum()}")
        
        # 测试获取torch数据
        print(f"\n步骤 9: 测试获取torch数据")
        train_data = processor.get_torch_data('train')
        val_data = processor.get_torch_data('val')
        test_data = processor.get_torch_data('test')
        
        print(f"训练数据键: {list(train_data.keys())}")
        print(f"训练数据node_features形状: {train_data['node_features'].shape}")
        print(f"训练数据edge_index形状: {train_data['edge_index'].shape}")
        print(f"训练数据in_flows形状: {train_data['in_flows'].shape}")
        print(f"训练数据time_features形状: {train_data['time_features'].shape}")
        
        print("\n✅ 测试成功！栅格数据缺失处理正常")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_grid_data_missing()
    if success:
        print("\n🎉 栅格数据缺失处理修复验证成功！")
    else:
        print("\n⚠️  栅格数据缺失处理仍有问题，需要进一步调试") 