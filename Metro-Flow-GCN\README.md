# Metro Flow Prediction with GCN + Temporal Features

## 项目概述

本项目实现了基于图卷积网络(GCN)和时间特征的地铁流量预测系统，专门针对复杂的城市地铁网络流量预测问题。系统能够：

1. **预测地铁站点进出站流量** - 基于历史数据和站点特征预测未来流量
2. **预测站点间OD流量** - 预测起点-终点间的乘客流量
3. **预测栅格到站点流量** - 分析城市栅格与地铁站点间的人流关系
4. **分析新线路影响** - 评估新增地铁线路对现有流量的影响
5. **地理信息建图** - 使用栅格和地铁站的地理坐标构建网络图

## 主要特性

### 🚀 核心功能
- **纯GCN架构** + 时间特征编码，适合复杂时空数据
- **多任务学习** - 同时预测进站、出站、OD流量
- **流量平衡约束** - 确保进出站流量的合理性
- **栅格-站点关联** - 智能识别栅格与站点的连接关系

### 💾 内存优化
- **Float16精度** - 减少50%内存使用，适合8GB显存
- **批处理优化** - 高效的数据加载和处理
- **梯度裁剪** - 防止梯度爆炸，提高训练稳定性

### 📊 验证策略
- **分层验证** - 时间分割 + 空间分割 + 留一法验证
- **早停机制** - 防止过拟合，patience=5
- **学习率调度** - 自适应学习率调整

### 🗺️ 地理信息处理
- **Shapefile支持** - 直接读取地理空间数据
- **坐标系统** - 支持经纬度坐标系统
- **距离阈值** - 可配置的栅格-站点连接距离（默认1km）

## 数据格式说明

### 输入数据文件
```
数据目录/
├── in_500_with_coords.shp          # 进站流量数据（含坐标）
├── out_500_with_coords.shp         # 出站流量数据（含坐标）
├── updated北京市_subway_od_2024_modified3.csv  # OD流量数据
├── station_features_result.csv     # 站点特征数据（52维特征）
└── grid_data.shp                   # 栅格数据（可选）
```

### 数据字段说明
- **进/出站数据**: station, hour, count, longitude, latitude
- **OD数据**: o_rawname, d_rawname, hour, trip, surface_distance, translate, time, wait_time
- **站点特征**: 站名 + 51个POI和社会经济特征
- **栅格数据**: geometry + 流量特征

### 输出结果
```
outputs/run_YYYYMMDD_HHMMSS/
├── checkpoints/
│   └── best_model.pth              # 最佳模型权重
├── results/
│   ├── evaluation_results.json     # 评估指标
│   ├── evaluation_plots.png        # 结果可视化
│   ├── predictions_comparison.csv  # 预测对比
│   └── prediction_summary.json     # 预测汇总
└── new_line_impact_framework.json  # 新线路影响分析框架
```

## 快速开始

### 1. 环境安装
```bash
# 克隆项目
git clone <repository_url>
cd Metro-Flow-GCN

# 安装依赖
pip install -r requirements.txt
```

### 2. 数据准备
将数据文件放置在指定目录，默认路径：
```
C:/Users/<USER>/Desktop/接驳/
```

### 3. 运行示例
```bash
# 使用默认配置运行
python run_example.py

# 或自定义参数运行
python main.py --data_path /path/to/data --epochs 20 --patience 5
```

### 4. 参数配置
```bash
python main.py \
    --data_path "C:/Users/<USER>/Desktop/接驳/" \
    --epochs 20 \
    --patience 5 \
    --spatial_hidden_dim 128 \
    --temporal_embed_dim 64 \
    --num_gcn_layers 3 \
    --dropout 0.2 \
    --distance_threshold 1000 \
    --use_float16 \
    --save_predictions
```

## 模型架构详解

### 🧠 核心组件
1. **空间GCN模块** (SpatialGCN)
   - 3层图卷积网络
   - BatchNorm + ReLU + Dropout
   - 隐藏维度：128

2. **时间编码器** (TemporalEncoder)
   - 小时嵌入（24维）
   - 星期嵌入（7维）
   - 月份嵌入（12维）
   - 总嵌入维度：64

3. **多任务预测头**
   - 进站流量预测
   - 出站流量预测
   - OD流量预测（可扩展）

4. **流量平衡层**
   - 确保进出站流量平衡
   - 可调节平衡权重

### 📈 训练策略
- **损失函数**: MSE + MAE + 流量平衡损失
- **优化器**: AdamW (lr=0.001, weight_decay=1e-4)
- **学习率调度**: ReduceLROnPlateau
- **正则化**: Dropout(0.2) + 梯度裁剪

## 性能指标

### 评估指标
- **MAE** (Mean Absolute Error) - 平均绝对误差
- **MSE** (Mean Squared Error) - 均方误差  
- **R²** (R-squared) - 决定系数
- **流量平衡度** - 进出站流量差异

### 预期性能
- 整体MAE < 50（人次）
- R² > 0.8
- 训练时间：约20分钟（20 epochs）
- 内存使用：< 8GB GPU显存

## 高级功能

### 🔍 栅格分析
```python
from utils.grid_analyzer import GridAnalyzer

analyzer = GridAnalyzer(distance_threshold=1000)
connections, strengths = analyzer.analyze_grid_station_connections(
    grid_coords, station_coords, grid_flows, station_flows
)
```

### 🚇 新线路影响分析
```python
# 分析新线路对现有流量的影响
impact = analyzer.analyze_new_line_impact_on_grids(
    new_stations_coords, existing_grid_coords
)
```

### 📊 可视化
- 训练损失曲线
- 预测vs真实值散点图
- 残差分布直方图
- 栅格-站点连接关系图

## 项目结构

```
Metro-Flow-GCN/
├── main.py                    # 主程序入口
├── config.py                  # 配置文件
├── data_processor.py          # 数据处理模块
├── trainer.py                 # 训练器
├── run_example.py             # 示例运行脚本
├── requirements.txt           # 依赖包
├── README.md                  # 项目说明
├── models/
│   ├── __init__.py
│   └── gcn_temporal.py        # GCN模型定义
└── utils/
    ├── __init__.py
    └── grid_analyzer.py       # 栅格分析工具
```

## 技术特点

### 🎯 针对性设计
- **复杂数据适配** - 处理355个站点、24小时、多维特征
- **避免平均值预测** - 通过复杂模型架构提取细粒度特征
- **内存友好** - Float16精度，适合有限GPU资源

### 🔧 工程优化
- **模块化设计** - 易于扩展和维护
- **配置化参数** - 灵活调整模型和训练参数
- **完整日志** - 详细的训练和评估日志

### 📋 数据验证
- **数据完整性检查** - 自动验证数据格式和完整性
- **异常值处理** - 智能处理缺失值和异常值
- **数据平衡** - 确保训练数据的平衡性

## 注意事项

1. **数据路径** - 请确保数据文件路径正确
2. **内存管理** - 建议使用GPU训练，CPU训练较慢
3. **参数调优** - 可根据具体数据调整模型参数
4. **结果解释** - 预测结果需结合实际业务场景解释

## 联系方式

如有问题或建议，请联系项目维护者。

---

**版本**: 1.0.0  
**更新日期**: 2024年12月  
**Python版本**: 3.8+  
**PyTorch版本**: 1.9.0+ 