"""
优化的空间感知地铁流量预测系统 V1 完整版本
基于EnhancedSpatialModel架构，支持完整数据集预测和CUDA加速
目标：在完整数据集上复现R²=0.5168的性能
"""
import os
import sys
import time
import psutil
import gc
import numpy as np
import pandas as pd
import geopandas as gpd
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
import warnings
warnings.filterwarnings('ignore')

class EnhancedSpatialModel(nn.Module):
    """增强的空间模型 - V1架构"""
    
    def __init__(self, input_dim, spatial_dim=3, temporal_dim=6, hidden_dim=128, model_type='station'):
        super(EnhancedSpatialModel, self).__init__()
        
        self.model_type = model_type
        self.hidden_dim = hidden_dim
        
        if model_type == 'od':
            # OD模型架构
            self.origin_encoder = nn.Sequential(
                nn.Linear(input_dim, hidden_dim),
                nn.ReLU(),
                nn.BatchNorm1d(hidden_dim),
                nn.Dropout(0.2),
                nn.Linear(hidden_dim, hidden_dim),
                nn.ReLU(),
                nn.BatchNorm1d(hidden_dim),
                nn.Dropout(0.2),
                nn.Linear(hidden_dim, hidden_dim // 2)
            )
            
            self.dest_encoder = nn.Sequential(
                nn.Linear(input_dim, hidden_dim),
                nn.ReLU(),
                nn.BatchNorm1d(hidden_dim),
                nn.Dropout(0.2),
                nn.Linear(hidden_dim, hidden_dim),
                nn.ReLU(),
                nn.BatchNorm1d(hidden_dim),
                nn.Dropout(0.2),
                nn.Linear(hidden_dim, hidden_dim // 2)
            )
            
            self.od_pair_encoder = nn.Sequential(
                nn.Linear(4, 32),
                nn.ReLU(),
                nn.BatchNorm1d(32),
                nn.Dropout(0.2),
                nn.Linear(32, 16)
            )
            
            self.temporal_encoder = nn.Sequential(
                nn.Linear(temporal_dim, 32),
                nn.ReLU(),
                nn.BatchNorm1d(32),
                nn.Dropout(0.2),
                nn.Linear(32, 16)
            )
            
            fusion_dim = (hidden_dim // 2) * 2 + 16 + 16
            self.interaction_layer = nn.Sequential(
                nn.Linear(fusion_dim, hidden_dim * 2),
                nn.ReLU(),
                nn.BatchNorm1d(hidden_dim * 2),
                nn.Dropout(0.3),
                nn.Linear(hidden_dim * 2, hidden_dim),
                nn.ReLU(),
                nn.BatchNorm1d(hidden_dim),
                nn.Dropout(0.3),
                nn.Linear(hidden_dim, hidden_dim // 2),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(hidden_dim // 2, 1)
            )
            
        else:
            # 进出站模型架构
            self.main_encoder = nn.Sequential(
                nn.Linear(input_dim, hidden_dim),
                nn.ReLU(),
                nn.BatchNorm1d(hidden_dim),
                nn.Dropout(0.2),
                nn.Linear(hidden_dim, hidden_dim),
                nn.ReLU(),
                nn.BatchNorm1d(hidden_dim),
                nn.Dropout(0.2),
                nn.Linear(hidden_dim, hidden_dim // 2)
            )
            
            self.spatial_encoder = nn.Sequential(
                nn.Linear(spatial_dim, 32),
                nn.ReLU(),
                nn.BatchNorm1d(32),
                nn.Dropout(0.2),
                nn.Linear(32, 16)
            )
            
            self.temporal_encoder = nn.Sequential(
                nn.Linear(temporal_dim, 32),
                nn.ReLU(),
                nn.BatchNorm1d(32),
                nn.Dropout(0.2),
                nn.Linear(32, 16)
            )
            
            fusion_dim = (hidden_dim // 2) + 16 + 16
            self.predictor = nn.Sequential(
                nn.Linear(fusion_dim, hidden_dim * 2),
                nn.ReLU(),
                nn.BatchNorm1d(hidden_dim * 2),
                nn.Dropout(0.3),
                nn.Linear(hidden_dim * 2, hidden_dim),
                nn.ReLU(),
                nn.BatchNorm1d(hidden_dim),
                nn.Dropout(0.3),
                nn.Linear(hidden_dim, hidden_dim // 2),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(hidden_dim // 2, 1)
            )
    
    def forward(self, *args):
        if self.model_type == 'od':
            origin_features, dest_features, od_pair_features, temporal_features = args
            
            origin_embed = self.origin_encoder(origin_features)
            dest_embed = self.dest_encoder(dest_features)
            od_embed = self.od_pair_encoder(od_pair_features)
            temp_embed = self.temporal_encoder(temporal_features)
            
            combined_features = torch.cat([origin_embed, dest_embed, od_embed, temp_embed], dim=-1)
            output = self.interaction_layer(combined_features)
            
        else:
            main_features, spatial_features, temporal_features = args
            
            main_embed = self.main_encoder(main_features)
            spatial_embed = self.spatial_encoder(spatial_features)
            temporal_embed = self.temporal_encoder(temporal_features)
            
            combined_features = torch.cat([main_embed, spatial_embed, temporal_embed], dim=-1)
            output = self.predictor(combined_features)
        
        return torch.relu(output)

class V1FullSpatialPredictionSystem:
    """V1完整空间预测系统 - 支持完整数据集和CUDA加速"""
    
    def __init__(self):
        # CUDA设备检测和配置
        self.device = self._setup_device()
        self._print_system_info()
        
        # 数据存储
        self.in_data = None
        self.out_data = None
        self.od_data = None
        self.grid_data = None
        self.station_coords = {}
        
        # 模型
        self.in_model = None
        self.out_model = None
        self.od_model = None
        
        # 传统机器学习模型
        self.in_rf_model = None
        self.out_rf_model = None
        self.od_rf_model = None
        self.in_gb_model = None
        self.out_gb_model = None
        self.od_gb_model = None
        
        # 标准化器
        self.feature_scaler = RobustScaler()
        self.spatial_scaler = StandardScaler()
        self.temporal_scaler = StandardScaler()
        self.od_scaler = RobustScaler()
        
        # 站点信息
        self.stations = []
        self.station_to_idx = {}
        
        # 性能监控
        self.start_time = time.time()
        
    def _setup_device(self):
        """设置计算设备"""
        if torch.cuda.is_available():
            device = torch.device('cuda')
            # 清理GPU缓存
            torch.cuda.empty_cache()
            print(f"CUDA可用，使用GPU: {torch.cuda.get_device_name(0)}")
            print(f"  GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
        else:
            device = torch.device('cpu')
            print("CUDA不可用，使用CPU")
        
        return device
    
    def _print_system_info(self):
        """打印系统信息"""
        print(f"系统信息:")
        print(f"  CPU核心数: {psutil.cpu_count()}")
        print(f"  系统内存: {psutil.virtual_memory().total / 1024**3:.1f} GB")
        print(f"  可用内存: {psutil.virtual_memory().available / 1024**3:.1f} GB")
        print(f"  PyTorch版本: {torch.__version__}")
        
    def _monitor_memory(self, stage=""):
        """监控内存使用"""
        if stage:
            print(f"\n[{stage}] 内存使用情况:")
        
        # 系统内存
        mem = psutil.virtual_memory()
        print(f"  系统内存: {mem.used / 1024**3:.1f} GB / {mem.total / 1024**3:.1f} GB ({mem.percent:.1f}%)")
        
        # GPU内存
        if self.device.type == 'cuda':
            gpu_mem = torch.cuda.memory_allocated() / 1024**3
            gpu_cached = torch.cuda.memory_reserved() / 1024**3
            print(f"  GPU内存: {gpu_mem:.1f} GB (已分配), {gpu_cached:.1f} GB (已缓存)")
        
        # 强制垃圾回收
        gc.collect()
        if self.device.type == 'cuda':
            torch.cuda.empty_cache()
    
    def load_and_process_data(self):
        """加载和处理数据"""
        print("="*80)
        print("V1完整版本 - 加载完整数据集")
        print("="*80)
        
        try:
            self._monitor_memory("数据加载前")
            
            # 加载基础数据
            print("加载基础数据文件...")
            self.in_data = gpd.read_file('in_500_with_coords.shp')
            self.out_data = gpd.read_file('out_500_with_coords.shp')
            self.od_data = pd.read_csv('updated北京市_subway_od_2024_modified3.csv')
            self.grid_data = pd.read_csv('leti_data.csv')
            
            # 清理站点名称
            print("清理站点名称...")
            self.in_data['station_clean'] = self.in_data['station'].apply(lambda x: str(x).split('_')[0])
            self.out_data['station_clean'] = self.out_data['station'].apply(lambda x: str(x).split('_')[0])
            self.od_data['o_station_clean'] = self.od_data['o_rawname'].apply(lambda x: str(x).split('_')[0])
            self.od_data['d_station_clean'] = self.od_data['d_rawname'].apply(lambda x: str(x).split('_')[0])
            
            # 提取坐标
            print("提取坐标信息...")
            self.in_data['longitude'] = self.in_data.geometry.centroid.x
            self.in_data['latitude'] = self.in_data.geometry.centroid.y
            self.out_data['longitude'] = self.out_data.geometry.centroid.x
            self.out_data['latitude'] = self.out_data.geometry.centroid.y
            
            print(f"数据加载完成:")
            print(f"  进站数据: {self.in_data.shape}")
            print(f"  出站数据: {self.out_data.shape}")
            print(f"  OD数据: {self.od_data.shape}")
            print(f"  栅格数据: {self.grid_data.shape}")

            # 获取站点列表和坐标
            self._extract_station_coordinates()

            # 处理栅格特征
            print("处理栅格特征...")
            feature_cols = [col for col in self.grid_data.columns if col not in ['站名', 'id', 'geometry']]
            self.grid_features = self.grid_data[feature_cols].values
            self.grid_features = np.nan_to_num(self.grid_features, nan=0.0)

            print(f"栅格特征维度: {self.grid_features.shape}")
            print(f"提取了 {len(self.stations)} 个站点")
            
            self._monitor_memory("数据加载后")
            
            return True
            
        except Exception as e:
            print(f"数据加载失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _extract_station_coordinates(self):
        """提取站点坐标"""
        print("提取站点坐标...")
        all_stations = set()
        all_stations.update(self.in_data['station_clean'].unique())
        all_stations.update(self.out_data['station_clean'].unique())
        all_stations.update(self.od_data['o_station_clean'].unique())
        all_stations.update(self.od_data['d_station_clean'].unique())

        self.stations = sorted(list(all_stations))
        self.station_to_idx = {station: idx for idx, station in enumerate(self.stations)}

        # 提取每个站点的代表坐标
        for station in self.stations:
            in_coords = self.in_data[self.in_data['station_clean'] == station][['longitude', 'latitude']]
            out_coords = self.out_data[self.out_data['station_clean'] == station][['longitude', 'latitude']]

            all_coords = pd.concat([in_coords, out_coords])
            if len(all_coords) > 0:
                self.station_coords[station] = {
                    'longitude': all_coords['longitude'].mean(),
                    'latitude': all_coords['latitude'].mean()
                }
            else:
                self.station_coords[station] = {
                    'longitude': 116.4,  # 北京市中心
                    'latitude': 39.9
                }

        print(f"提取了 {len(self.stations)} 个站点的坐标")

    def _calculate_spatial_features(self, grid_lon, grid_lat, station_lon, station_lat):
        """计算增强的空间特征"""
        distance = np.sqrt((grid_lon - station_lon)**2 + (grid_lat - station_lat)**2)
        direction = np.arctan2(station_lat - grid_lat, station_lon - grid_lon)
        accessibility = 1.0 / (1.0 + distance * 100)
        return np.array([distance, direction, accessibility])

    def _calculate_time_features(self, hour):
        """计算增强的时间特征"""
        return np.array([
            hour / 23.0,
            np.sin(2 * np.pi * hour / 24),
            np.cos(2 * np.pi * hour / 24),
            1.0 if 6 <= hour <= 10 else 0.0,
            1.0 if 17 <= hour <= 20 else 0.0,
            1.0 if hour in [0,1,2,3,4,5,21,22,23] else 0.0
        ])

    def prepare_station_training_data(self, data, flow_type):
        """准备进出站训练数据"""
        print(f"准备{flow_type}站训练数据...")
        features = []
        spatial_features = []
        time_features = []
        targets = []

        avg_grid_features = np.mean(self.grid_features, axis=0)

        for idx, row in data.iterrows():
            station = row['station_clean']
            hour = row['hour']
            count = row['count']
            grid_lon = row['longitude']
            grid_lat = row['latitude']

            if station in self.station_coords:
                station_coord = self.station_coords[station]
                station_lon = station_coord['longitude']
                station_lat = station_coord['latitude']

                # 基础特征
                station_idx = self.station_to_idx[station]
                base_features = np.concatenate([
                    avg_grid_features,
                    [station_lon, station_lat, station_idx / len(self.stations)]
                ])

                # 空间特征
                spatial_feature = self._calculate_spatial_features(
                    grid_lon, grid_lat, station_lon, station_lat
                )

                # 时间特征
                time_feature = self._calculate_time_features(hour)

                features.append(base_features)
                spatial_features.append(spatial_feature)
                time_features.append(time_feature)
                targets.append(count)

        print(f"{flow_type}站数据准备完成: {len(features)} 条记录")
        return (np.array(features), np.array(spatial_features),
                np.array(time_features), np.array(targets))

    def prepare_od_training_data(self, sample_ratio=0.05):
        """准备OD训练数据"""
        print(f"准备OD训练数据 (采样比例: {sample_ratio})...")

        # 对OD数据进行采样以减少内存使用
        sampled_od_data = self.od_data.sample(frac=sample_ratio, random_state=42)
        print(f"采样后OD数据量: {len(sampled_od_data):,} 条记录")

        origin_features = []
        dest_features = []
        od_pair_features = []
        time_features = []
        targets = []

        avg_grid_features = np.mean(self.grid_features, axis=0)

        for idx, row in sampled_od_data.iterrows():
            o_station = row['o_station_clean']
            d_station = row['d_station_clean']
            hour = row['hour']
            trip = row['trip']

            if o_station in self.station_coords and d_station in self.station_coords:
                # 起点特征
                o_coord = self.station_coords[o_station]
                o_idx = self.station_to_idx[o_station]
                origin_feature = np.concatenate([
                    avg_grid_features,
                    [o_coord['longitude'], o_coord['latitude'], o_idx / len(self.stations)]
                ])

                # 终点特征
                d_coord = self.station_coords[d_station]
                d_idx = self.station_to_idx[d_station]
                dest_feature = np.concatenate([
                    avg_grid_features,
                    [d_coord['longitude'], d_coord['latitude'], d_idx / len(self.stations)]
                ])

                # OD对特征
                od_pair_feature = np.array([
                    row.get('surface_distance', 0),
                    row.get('translate', 0),
                    row.get('time', 0),
                    row.get('wait_time', 0)
                ])

                # 时间特征
                time_feature = self._calculate_time_features(hour)

                origin_features.append(origin_feature)
                dest_features.append(dest_feature)
                od_pair_features.append(od_pair_feature)
                time_features.append(time_feature)
                targets.append(trip)

        print(f"OD数据准备完成: {len(origin_features)} 条记录")
        return (np.array(origin_features), np.array(dest_features),
                np.array(od_pair_features), np.array(time_features), np.array(targets))

    def train_v1_full_models(self):
        """训练V1完整模型"""
        print("="*80)
        print("训练V1完整模型")
        print("="*80)

        self._monitor_memory("训练开始前")

        # 准备进站数据
        in_features, in_spatial, in_time, in_targets = self.prepare_station_training_data(self.in_data, 'in')

        # 准备出站数据
        out_features, out_spatial, out_time, out_targets = self.prepare_station_training_data(self.out_data, 'out')

        # 准备OD数据
        od_origin, od_dest, od_pair, od_time, od_targets = self.prepare_od_training_data()

        # 标准化特征
        print("标准化特征...")
        in_features = self.feature_scaler.fit_transform(in_features)
        in_spatial = self.spatial_scaler.fit_transform(in_spatial)
        in_time = self.temporal_scaler.fit_transform(in_time)

        # 训练进站模型
        print("\n训练进站模型...")
        self._train_v1_station_model(in_features, in_spatial, in_time, in_targets, 'in')

        # 使用相同的标准化器处理出站数据
        out_features = self.feature_scaler.transform(out_features)
        out_spatial = self.spatial_scaler.transform(out_spatial)
        out_time = self.temporal_scaler.transform(out_time)

        # 训练出站模型
        print("\n训练出站模型...")
        self._train_v1_station_model(out_features, out_spatial, out_time, out_targets, 'out')

        # 标准化OD特征
        od_origin = self.feature_scaler.transform(od_origin)
        od_dest = self.feature_scaler.transform(od_dest)
        od_pair = self.od_scaler.fit_transform(od_pair)
        od_time = self.temporal_scaler.transform(od_time)

        # 训练OD模型
        print("\n训练OD模型...")
        self._train_v1_od_model(od_origin, od_dest, od_pair, od_time, od_targets)

        self._monitor_memory("训练完成后")
        print("所有V1完整模型训练完成！")

    def _train_v1_station_model(self, features, spatial_features, time_features, targets, model_type):
        """训练进出站V1模型"""
        print(f"  训练{model_type}站深度学习模型...")
        input_dim = features.shape[1]

        if model_type == 'in':
            self.in_model = EnhancedSpatialModel(
                input_dim=input_dim,
                spatial_dim=3,
                temporal_dim=6,
                hidden_dim=128,
                model_type='station'
            ).to(self.device)
            model = self.in_model
        else:
            self.out_model = EnhancedSpatialModel(
                input_dim=input_dim,
                spatial_dim=3,
                temporal_dim=6,
                hidden_dim=128,
                model_type='station'
            ).to(self.device)
            model = self.out_model

        # 转换为张量
        features_tensor = torch.tensor(features, dtype=torch.float32).to(self.device)
        spatial_tensor = torch.tensor(spatial_features, dtype=torch.float32).to(self.device)
        time_tensor = torch.tensor(time_features, dtype=torch.float32).to(self.device)
        targets_tensor = torch.tensor(targets, dtype=torch.float32).to(self.device)

        # 训练设置
        optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=1e-4)
        criterion = nn.MSELoss()
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=15, factor=0.5)

        # 训练循环
        model.train()
        best_loss = float('inf')
        patience = 0

        for epoch in range(150):
            optimizer.zero_grad()

            predictions = model(features_tensor, spatial_tensor, time_tensor).squeeze()
            loss = criterion(predictions, targets_tensor)

            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
            optimizer.step()

            scheduler.step(loss.item())

            if loss.item() < best_loss:
                best_loss = loss.item()
                patience = 0
            else:
                patience += 1

            if epoch % 30 == 0:
                print(f"    {model_type} DL Epoch {epoch}: Loss = {loss.item():.6f}")

            if patience >= 30:
                print(f"    {model_type} DL 早停于第 {epoch} 轮")
                break

        # 清理GPU内存
        del features_tensor, spatial_tensor, time_tensor, targets_tensor
        if self.device.type == 'cuda':
            torch.cuda.empty_cache()

        # 训练传统机器学习模型
        print(f"  训练{model_type}站传统ML模型...")
        combined_features = np.concatenate([features, spatial_features, time_features], axis=1)

        if model_type == 'in':
            self.in_rf_model = RandomForestRegressor(
                n_estimators=200, max_depth=15, random_state=42, n_jobs=-1
            )
            self.in_rf_model.fit(combined_features, targets)

            self.in_gb_model = GradientBoostingRegressor(
                n_estimators=200, max_depth=8, learning_rate=0.1, random_state=42
            )
            self.in_gb_model.fit(combined_features, targets)
        else:
            self.out_rf_model = RandomForestRegressor(
                n_estimators=200, max_depth=15, random_state=42, n_jobs=-1
            )
            self.out_rf_model.fit(combined_features, targets)

            self.out_gb_model = GradientBoostingRegressor(
                n_estimators=200, max_depth=8, learning_rate=0.1, random_state=42
            )
            self.out_gb_model.fit(combined_features, targets)

    def _train_v1_od_model(self, origin_features, dest_features, od_pair_features, time_features, targets):
        """训练OD V1模型"""
        print("  训练OD深度学习模型（批处理模式）...")
        input_dim = origin_features.shape[1]

        self.od_model = EnhancedSpatialModel(
            input_dim=input_dim,
            spatial_dim=4,
            temporal_dim=6,
            hidden_dim=128,
            model_type='od'
        ).to(self.device)

        # 训练设置
        optimizer = optim.AdamW(self.od_model.parameters(), lr=0.001, weight_decay=1e-4)
        criterion = nn.MSELoss()
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=15, factor=0.5)

        # 批处理设置
        batch_size = 512
        n_samples = len(origin_features)
        n_batches = (n_samples + batch_size - 1) // batch_size

        print(f"    总样本数: {n_samples:,}, 批大小: {batch_size}, 批数: {n_batches}")

        # 训练循环
        self.od_model.train()
        best_loss = float('inf')
        patience = 0

        for epoch in range(100):
            epoch_loss = 0.0
            indices = np.random.permutation(n_samples)

            for batch_idx in range(n_batches):
                start_idx = batch_idx * batch_size
                end_idx = min((batch_idx + 1) * batch_size, n_samples)
                batch_indices = indices[start_idx:end_idx]

                # 获取批数据
                batch_origin = torch.tensor(origin_features[batch_indices], dtype=torch.float32).to(self.device)
                batch_dest = torch.tensor(dest_features[batch_indices], dtype=torch.float32).to(self.device)
                batch_od_pair = torch.tensor(od_pair_features[batch_indices], dtype=torch.float32).to(self.device)
                batch_time = torch.tensor(time_features[batch_indices], dtype=torch.float32).to(self.device)
                batch_targets = torch.tensor(targets[batch_indices], dtype=torch.float32).to(self.device)

                # 前向传播
                optimizer.zero_grad()
                predictions = self.od_model(
                    batch_origin, batch_dest, batch_od_pair, batch_time
                ).squeeze()

                loss = criterion(predictions, batch_targets)

                # 反向传播
                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.od_model.parameters(), 1.0)
                optimizer.step()

                epoch_loss += loss.item()

                # 清理内存
                del batch_origin, batch_dest, batch_od_pair, batch_time, batch_targets, predictions
                if self.device.type == 'cuda':
                    torch.cuda.empty_cache()

            avg_loss = epoch_loss / n_batches
            scheduler.step(avg_loss)

            if avg_loss < best_loss:
                best_loss = avg_loss
                patience = 0
            else:
                patience += 1

            if epoch % 20 == 0:
                print(f"    OD DL Epoch {epoch}: Loss = {avg_loss:.6f}")

            if patience >= 25:
                print(f"    OD DL 早停于第 {epoch} 轮")
                break

        # 训练传统机器学习模型
        print("  训练OD传统ML模型...")
        combined_features = np.concatenate([
            origin_features, dest_features, od_pair_features, time_features
        ], axis=1)

        self.od_rf_model = RandomForestRegressor(
            n_estimators=200, max_depth=15, random_state=42, n_jobs=-1
        )
        self.od_rf_model.fit(combined_features, targets)

        self.od_gb_model = GradientBoostingRegressor(
            n_estimators=200, max_depth=8, learning_rate=0.1, random_state=42
        )
        self.od_gb_model.fit(combined_features, targets)

    def generate_v1_full_predictions(self):
        """生成V1完整预测结果"""
        print("="*80)
        print("生成V1完整预测结果")
        print("="*80)

        self._monitor_memory("预测开始前")

        # 完整测试时间范围
        test_hours = list(range(18, 24))
        print(f"测试时间段: {test_hours}")

        # 生成进站预测
        print("\n生成进站预测...")
        in_test_data = self.in_data[self.in_data['hour'].isin(test_hours)].copy()
        print(f"进站测试数据: {len(in_test_data):,} 条记录")

        if len(in_test_data) > 0:
            in_test_features, in_test_spatial, in_test_time, _ = self.prepare_station_training_data(in_test_data, 'in')

            # 标准化
            in_test_features = self.feature_scaler.transform(in_test_features)
            in_test_spatial = self.spatial_scaler.transform(in_test_spatial)
            in_test_time = self.temporal_scaler.transform(in_test_time)

            # 集成预测
            in_predictions = self._v1_predict_station(
                in_test_features, in_test_spatial, in_test_time, 'in'
            )

            in_test_data.loc[:, 'prediction'] = np.maximum(in_predictions, 0)

            # 保存结果
            self._save_v1_predictions(in_test_data, 'in')

        # 生成出站预测
        print("\n生成出站预测...")
        out_test_data = self.out_data[self.out_data['hour'].isin(test_hours)].copy()
        print(f"出站测试数据: {len(out_test_data):,} 条记录")

        if len(out_test_data) > 0:
            out_test_features, out_test_spatial, out_test_time, _ = self.prepare_station_training_data(out_test_data, 'out')

            # 标准化
            out_test_features = self.feature_scaler.transform(out_test_features)
            out_test_spatial = self.spatial_scaler.transform(out_test_spatial)
            out_test_time = self.temporal_scaler.transform(out_test_time)

            # 集成预测
            out_predictions = self._v1_predict_station(
                out_test_features, out_test_spatial, out_test_time, 'out'
            )

            out_test_data.loc[:, 'prediction'] = np.maximum(out_predictions, 0)

            # 保存结果
            self._save_v1_predictions(out_test_data, 'out')

        # 生成OD预测（完整数据集）
        print("\n生成OD预测...")
        od_test_data = self.od_data[self.od_data['hour'].isin(test_hours)].copy()
        print(f"OD测试数据: {len(od_test_data):,} 条记录")

        if len(od_test_data) > 0:
            od_predictions = self._v1_predict_od_full(od_test_data)
            od_test_data.loc[:, 'prediction'] = np.maximum(od_predictions, 0)

            # 保存OD预测结果
            od_test_data.to_csv('v1_full_od_predictions.csv', index=False)
            print(f"✓ V1完整OD预测结果已保存: {len(od_test_data):,} 条记录")

        # 生成完整对比分析
        self._generate_v1_full_comparison(in_test_data, out_test_data, od_test_data)

        self._monitor_memory("预测完成后")

        return in_test_data, out_test_data, od_test_data

    def _v1_predict_station(self, features, spatial_features, time_features, model_type):
        """V1预测进出站流量"""
        model = self.in_model if model_type == 'in' else self.out_model
        model.eval()

        with torch.no_grad():
            features_tensor = torch.tensor(features, dtype=torch.float32).to(self.device)
            spatial_tensor = torch.tensor(spatial_features, dtype=torch.float32).to(self.device)
            time_tensor = torch.tensor(time_features, dtype=torch.float32).to(self.device)

            dl_predictions = model(features_tensor, spatial_tensor, time_tensor).squeeze()
            dl_predictions = dl_predictions.cpu().numpy()

            # 清理GPU内存
            del features_tensor, spatial_tensor, time_tensor
            if self.device.type == 'cuda':
                torch.cuda.empty_cache()

        # 传统机器学习预测
        combined_features = np.concatenate([features, spatial_features, time_features], axis=1)

        if model_type == 'in':
            rf_predictions = self.in_rf_model.predict(combined_features)
            gb_predictions = self.in_gb_model.predict(combined_features)
        else:
            rf_predictions = self.out_rf_model.predict(combined_features)
            gb_predictions = self.out_gb_model.predict(combined_features)

        # 集成预测
        ensemble_predictions = (
            0.5 * dl_predictions +
            0.3 * rf_predictions +
            0.2 * gb_predictions
        )

        return ensemble_predictions

    def _v1_predict_od_full(self, od_test_data):
        """V1完整OD预测（批处理）"""
        print("  使用批处理进行完整OD预测...")

        # 准备特征
        origin_features = []
        dest_features = []
        od_pair_features = []
        time_features = []

        avg_grid_features = np.mean(self.grid_features, axis=0)

        print("  准备OD特征...")
        for idx, row in od_test_data.iterrows():
            if idx % 100000 == 0:
                print(f"    处理进度: {idx:,} / {len(od_test_data):,}")

            o_station = row['o_station_clean']
            d_station = row['d_station_clean']
            hour = row['hour']

            if o_station in self.station_coords and d_station in self.station_coords:
                # 起点特征
                o_coord = self.station_coords[o_station]
                o_idx = self.station_to_idx[o_station]
                origin_feature = np.concatenate([
                    avg_grid_features,
                    [o_coord['longitude'], o_coord['latitude'], o_idx / len(self.stations)]
                ])

                # 终点特征
                d_coord = self.station_coords[d_station]
                d_idx = self.station_to_idx[d_station]
                dest_feature = np.concatenate([
                    avg_grid_features,
                    [d_coord['longitude'], d_coord['latitude'], d_idx / len(self.stations)]
                ])

                # OD对特征
                od_pair_feature = np.array([
                    row.get('surface_distance', 0),
                    row.get('translate', 0),
                    row.get('time', 0),
                    row.get('wait_time', 0)
                ])

                # 时间特征
                time_feature = self._calculate_time_features(hour)

                origin_features.append(origin_feature)
                dest_features.append(dest_feature)
                od_pair_features.append(od_pair_feature)
                time_features.append(time_feature)
            else:
                # 如果站点不存在，添加零向量
                zero_feature = np.zeros(len(avg_grid_features) + 3)
                origin_features.append(zero_feature)
                dest_features.append(zero_feature)
                od_pair_features.append(np.zeros(4))
                time_features.append(self._calculate_time_features(hour))

        # 转换为数组并标准化
        print("  标准化特征...")
        origin_features = np.array(origin_features)
        dest_features = np.array(dest_features)
        od_pair_features = np.array(od_pair_features)
        time_features = np.array(time_features)

        origin_features = self.feature_scaler.transform(origin_features)
        dest_features = self.feature_scaler.transform(dest_features)
        od_pair_features = self.od_scaler.transform(od_pair_features)
        time_features = self.temporal_scaler.transform(time_features)

        # 深度学习预测（批处理）
        print("  深度学习预测...")
        self.od_model.eval()
        dl_predictions = []

        batch_size = 1024
        n_samples = len(origin_features)
        n_batches = (n_samples + batch_size - 1) // batch_size

        with torch.no_grad():
            for batch_idx in range(n_batches):
                if batch_idx % 100 == 0:
                    print(f"    预测进度: {batch_idx} / {n_batches}")

                start_idx = batch_idx * batch_size
                end_idx = min((batch_idx + 1) * batch_size, n_samples)

                # 获取批数据
                batch_origin = torch.tensor(origin_features[start_idx:end_idx], dtype=torch.float32).to(self.device)
                batch_dest = torch.tensor(dest_features[start_idx:end_idx], dtype=torch.float32).to(self.device)
                batch_od_pair = torch.tensor(od_pair_features[start_idx:end_idx], dtype=torch.float32).to(self.device)
                batch_time = torch.tensor(time_features[start_idx:end_idx], dtype=torch.float32).to(self.device)

                # 预测
                batch_predictions = self.od_model(
                    batch_origin, batch_dest, batch_od_pair, batch_time
                ).squeeze()

                dl_predictions.extend(batch_predictions.cpu().numpy())

                # 清理内存
                del batch_origin, batch_dest, batch_od_pair, batch_time, batch_predictions
                if self.device.type == 'cuda':
                    torch.cuda.empty_cache()

        dl_predictions = np.array(dl_predictions)

        # 传统机器学习预测
        print("  传统ML预测...")
        combined_features = np.concatenate([
            origin_features, dest_features, od_pair_features, time_features
        ], axis=1)

        rf_predictions = self.od_rf_model.predict(combined_features)
        gb_predictions = self.od_gb_model.predict(combined_features)

        # 集成预测
        ensemble_predictions = (
            0.5 * dl_predictions +
            0.3 * rf_predictions +
            0.2 * gb_predictions
        )

        return ensemble_predictions

    def _save_v1_predictions(self, data, flow_type):
        """保存V1预测结果"""
        filename = f'v1_full_{flow_type}_500_predictions_with_coords'

        try:
            import glob
            for f in glob.glob(f'{filename}.*'):
                try:
                    os.remove(f)
                except:
                    pass

            data.to_file(f'{filename}.shp')
            print(f"✓ V1完整{flow_type}站预测结果已保存: {len(data):,} 条记录")
        except Exception as e:
            print(f"保存{flow_type}站shapefile失败: {e}")
            data_csv = data.drop(columns=['geometry'])
            data_csv.to_csv(f'{filename}.csv', index=False)
            print(f"✓ V1完整{flow_type}站预测结果已保存为CSV: {len(data):,} 条记录")

    def _generate_v1_full_comparison(self, in_test_data, out_test_data, od_test_data):
        """生成V1完整预测对比分析"""
        print("\n生成V1完整预测对比汇总...")

        comparison_data = []

        # 进站对比
        if in_test_data is not None and len(in_test_data) > 0:
            for _, row in in_test_data.iterrows():
                comparison_data.append({
                    'Type': 'In',
                    'True_Value': float(row['count']),
                    'Predicted_Value': float(row['prediction']),
                    'Station': str(row['station_clean']),
                    'Hour': int(row['hour']),
                    'Error': float(abs(row['count'] - row['prediction']))
                })

        # 出站对比
        if out_test_data is not None and len(out_test_data) > 0:
            for _, row in out_test_data.iterrows():
                comparison_data.append({
                    'Type': 'Out',
                    'True_Value': float(row['count']),
                    'Predicted_Value': float(row['prediction']),
                    'Station': str(row['station_clean']),
                    'Hour': int(row['hour']),
                    'Error': float(abs(row['count'] - row['prediction']))
                })

        # OD对比
        if od_test_data is not None and len(od_test_data) > 0:
            for _, row in od_test_data.iterrows():
                comparison_data.append({
                    'Type': 'OD',
                    'True_Value': float(row['trip']),
                    'Predicted_Value': float(row['prediction']),
                    'Station': f"{row['o_station_clean']}->{row['d_station_clean']}",
                    'Hour': int(row['hour']),
                    'Error': float(abs(row['trip'] - row['prediction']))
                })

        # 保存完整对比结果
        if comparison_data:
            comparison_df = pd.DataFrame(comparison_data)
            comparison_df.to_csv('v1_full_prediction_comparison.csv', index=False)
            print(f"✓ V1完整预测对比汇总已保存: {len(comparison_data):,} 条记录")

            # 生成性能分析
            self._analyze_v1_full_performance(comparison_df)

        return comparison_data

    def _analyze_v1_full_performance(self, comparison_df):
        """分析V1完整预测性能"""
        print("\n" + "="*80)
        print("V1完整预测性能分析")
        print("="*80)

        performance_results = {}

        # 按类型分析
        for pred_type in ['In', 'Out', 'OD']:
            type_data = comparison_df[comparison_df['Type'] == pred_type]

            if len(type_data) > 0:
                true_values = type_data['True_Value'].values
                pred_values = type_data['Predicted_Value'].values

                mae = mean_absolute_error(true_values, pred_values)
                rmse = np.sqrt(mean_squared_error(true_values, pred_values))
                r2 = r2_score(true_values, pred_values)

                performance_results[pred_type] = {
                    'samples': len(type_data),
                    'mae': mae,
                    'rmse': rmse,
                    'r2': r2,
                    'mean_true': np.mean(true_values),
                    'mean_pred': np.mean(pred_values)
                }

                print(f"{pred_type} 流量预测:")
                print(f"  样本数: {len(type_data):,}")
                print(f"  MAE:  {mae:.4f}")
                print(f"  RMSE: {rmse:.4f}")
                print(f"  R²:   {r2:.4f}")
                print(f"  真实值均值: {np.mean(true_values):.2f}")
                print(f"  预测值均值: {np.mean(pred_values):.2f}")
                print()

        # 整体性能
        all_true = comparison_df['True_Value'].values
        all_pred = comparison_df['Predicted_Value'].values

        overall_mae = mean_absolute_error(all_true, all_pred)
        overall_rmse = np.sqrt(mean_squared_error(all_true, all_pred))
        overall_r2 = r2_score(all_true, all_pred)

        performance_results['Overall'] = {
            'samples': len(comparison_df),
            'mae': overall_mae,
            'rmse': overall_rmse,
            'r2': overall_r2,
            'mean_true': np.mean(all_true),
            'mean_pred': np.mean(all_pred)
        }

        print("整体预测性能:")
        print(f"  总样本数: {len(comparison_df):,}")
        print(f"  MAE:  {overall_mae:.4f}")
        print(f"  RMSE: {overall_rmse:.4f}")
        print(f"  R²:   {overall_r2:.4f}")
        print(f"  真实值均值: {np.mean(all_true):.2f}")
        print(f"  预测值均值: {np.mean(all_pred):.2f}")

        # 保存性能结果
        import json
        with open('v1_full_performance_analysis.json', 'w') as f:
            json.dump(performance_results, f, indent=2)

        print("✓ V1完整性能分析已保存到 v1_full_performance_analysis.json")

def main():
    """主函数"""
    print("="*80)
    print("V1完整版本 - 优化的空间感知地铁流量预测系统")
    print("支持完整数据集预测和CUDA加速")
    print("="*80)

    start_time = time.time()

    try:
        # 初始化系统
        system = V1FullSpatialPredictionSystem()

        # 加载数据
        if not system.load_and_process_data():
            return False

        # 训练模型
        system.train_v1_full_models()

        # 生成预测
        in_results, out_results, od_results = system.generate_v1_full_predictions()

        elapsed_time = time.time() - start_time
        print(f"\n总运行时间: {elapsed_time:.2f} 秒 ({elapsed_time/60:.1f} 分钟)")
        print("="*80)
        print("V1完整版本预测系统运行完成！")
        print("="*80)

        return True

    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_system():
    """测试系统功能"""
    print("="*80)
    print("V1完整版本系统测试")
    print("="*80)

    try:
        # 初始化系统
        system = V1FullSpatialPredictionSystem()

        # 测试数据加载
        print("测试数据加载...")
        if not system.load_and_process_data():
            print("❌ 数据加载测试失败")
            return False

        print("✓ 数据加载测试通过")

        # 测试小批量预测
        print("\n测试小批量预测...")
        test_data = system.in_data.head(100)
        features, spatial, temporal, targets = system.prepare_station_training_data(test_data, 'in')

        print(f"✓ 特征准备测试通过: {features.shape}")

        print("✓ V1完整版本系统测试全部通过")
        return True

    except Exception as e:
        print(f"❌ 系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == 'test':
        success = test_system()
    else:
        success = main()

    sys.exit(0 if success else 1)
