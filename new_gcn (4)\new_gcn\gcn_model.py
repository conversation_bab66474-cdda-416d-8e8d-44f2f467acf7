import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import GCNConv, GATConv, GraphConv, BatchNorm
from torch_geometric.nn.pool import global_mean_pool
import numpy as np
from config import Config

class TemporalAttention(nn.Module):
    """时间注意力机制"""
    def __init__(self, hidden_dim):
        super(TemporalAttention, self).__init__()
        self.hidden_dim = hidden_dim
        self.attention = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, 1),
            nn.Sigmoid()
        )
    
    def forward(self, x):
        # x: [batch_size, seq_len, hidden_dim]
        attention_weights = self.attention(x)  # [batch_size, seq_len, 1]
        attended = x * attention_weights
        return attended, attention_weights

class SpatialTemporalEmbedding(nn.Module):
    """时空嵌入层"""
    def __init__(self, time_features, spatial_features, hidden_dim, use_float16=False):
        super(SpatialTemporalEmbedding, self).__init__()
        self.use_float16 = use_float16
        
        # 时间特征嵌入
        self.time_embedding = nn.Sequential(
            nn.Linear(time_features, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # 空间特征嵌入
        self.spatial_embedding = nn.Sequential(
            nn.Linear(spatial_features, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # 融合层
        self.fusion = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # 如果使用Float16，初始化时就设置权重类型
        if self.use_float16:
            self.time_embedding = self.time_embedding.half()
            self.spatial_embedding = self.spatial_embedding.half()
            self.fusion = self.fusion.half()
    
    def forward(self, time_features, spatial_features):
        # 确保输入张量在同一设备上
        device = next(self.parameters()).device
        time_features = time_features.to(device)
        spatial_features = spatial_features.to(device)
        
        # 确保数据类型一致
        if self.use_float16:
            time_features = time_features.half()
            spatial_features = spatial_features.half()
        
        time_emb = self.time_embedding(time_features)
        spatial_emb = self.spatial_embedding(spatial_features)
        
        # 拼接时空特征
        combined = torch.cat([time_emb, spatial_emb], dim=-1)
        output = self.fusion(combined)
        
        return output

class GCNLayer(nn.Module):
    """增强的GCN层"""
    def __init__(self, in_dim, out_dim, use_attention=True, dropout=0.2, use_float16=False):
        super(GCNLayer, self).__init__()
        self.use_attention = use_attention
        self.use_float16 = use_float16
        
        if use_attention:
            self.conv = GATConv(in_dim, out_dim, heads=2, concat=False, dropout=dropout)  # 降低heads数量
        else:
            self.conv = GCNConv(in_dim, out_dim)
        
        self.batch_norm = BatchNorm(out_dim)
        self.dropout = nn.Dropout(dropout)
        
        # 如果使用Float16，初始化后转换权重
        if self.use_float16:
            self.conv = self.conv.half()
            self.batch_norm = self.batch_norm.half()
        
    def forward(self, x, edge_index, edge_attr=None):
        if self.use_attention:
            x = self.conv(x, edge_index)
        else:
            x = self.conv(x, edge_index, edge_attr)
        
        x = self.batch_norm(x)
        x = F.relu(x)
        x = self.dropout(x)
        
        return x

class ODFlowPredictor(nn.Module):
    """OD流量预测模块"""
    def __init__(self, hidden_dim, od_feature_dim, dropout=0.2):
        super(ODFlowPredictor, self).__init__()
        
        self.od_embedding = nn.Sequential(
            nn.Linear(od_feature_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        self.flow_predictor = nn.Sequential(
            nn.Linear(hidden_dim + hidden_dim // 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, 1)
        )
    
    def forward(self, origin_features, dest_features, od_features):
        """
        origin_features: [batch_size, hidden_dim] - 起点站点特征
        dest_features: [batch_size, hidden_dim] - 终点站点特征
        od_features: [batch_size, od_feature_dim] - OD对特征
        """
        # 嵌入OD特征
        od_emb = self.od_embedding(od_features)
        
        # 组合起点、终点和OD特征
        combined = torch.cat([origin_features, dest_features, od_emb], dim=-1)
        
        # 预测流量
        flow_pred = self.flow_predictor(combined)
        
        return flow_pred.squeeze(-1)

class ComplexGCNModel(nn.Module):
    """复杂的GCN模型，适合大规模数据处理（修复版本）"""
    def __init__(self, config):
        super(ComplexGCNModel, self).__init__()
        self.config = config
        self.hidden_dim = config.HIDDEN_DIM
        self.num_layers = config.NUM_LAYERS
        self.dropout = config.DROPOUT
        self.use_float16 = config.USE_FLOAT16
        
        # 时空嵌入层
        self.st_embedding = SpatialTemporalEmbedding(
            time_features=config.TIME_FEATURES,
            spatial_features=config.SPATIAL_FEATURES,
            hidden_dim=self.hidden_dim,
            use_float16=self.use_float16
        )
        
        # 多层GCN
        self.gcn_layers = nn.ModuleList()
        
        # 第一层
        self.gcn_layers.append(GCNLayer(
            self.hidden_dim + 1,  # 嵌入维度 + 流量值
            self.hidden_dim,
            use_attention=True,
            dropout=self.dropout,
            use_float16=self.use_float16
        ))
        
        # 中间层
        for _ in range(self.num_layers - 2):
            self.gcn_layers.append(GCNLayer(
                self.hidden_dim,
                self.hidden_dim,
                use_attention=True,
                dropout=self.dropout,
                use_float16=self.use_float16
            ))
        
        # 最后一层
        if self.num_layers > 1:
            self.gcn_layers.append(GCNLayer(
                self.hidden_dim,
                self.hidden_dim // 2,
                use_attention=False,
                dropout=self.dropout,
                use_float16=self.use_float16
            ))
        
        # 时间注意力
        final_dim = self.hidden_dim // 2 if self.num_layers > 1 else self.hidden_dim
        self.temporal_attention = TemporalAttention(final_dim)
        
        # 输出层
        self.output_layers = nn.ModuleDict({
            'in_flow': nn.Sequential(
                nn.Linear(final_dim, final_dim // 2),
                nn.ReLU(),
                nn.Dropout(self.dropout),
                nn.Linear(final_dim // 2, 1)
            ),
            'out_flow': nn.Sequential(
                nn.Linear(final_dim, final_dim // 2),
                nn.ReLU(),
                nn.Dropout(self.dropout),
                nn.Linear(final_dim // 2, 1)
            )
        })
        
        # OD流量预测器
        if hasattr(config, 'OD_FEATURE_DIM'):
            self.od_predictor = ODFlowPredictor(
                final_dim, config.OD_FEATURE_DIM, self.dropout
            )
        
        # 残差连接
        self.residual_connections = nn.ModuleList([
            nn.Linear(self.hidden_dim, self.hidden_dim) 
            for _ in range(max(0, self.num_layers - 1))
        ])
        
        # 如果使用Float16，初始化后转换所有权重
        if self.use_float16:
            self.half()
        
    def forward(self, x, edge_index, edge_attr, time_features, spatial_features, task='in_flow'):
        """
        修复的forward方法
        x: [batch_size, seq_len, num_nodes] - 流量序列数据
        edge_index: [2, num_edges] - 图边索引
        edge_attr: [num_edges] - 边权重
        time_features: [batch_size, seq_len, num_nodes, time_feat_dim] - 时间特征
        spatial_features: [num_nodes, spatial_feat_dim] - 空间特征
        """
        # 确保所有张量在同一设备上并且类型一致
        device = next(self.parameters()).device
        dtype = next(self.parameters()).dtype
        
        x = x.to(device=device, dtype=dtype)
        edge_index = edge_index.to(device)
        edge_attr = edge_attr.to(device=device, dtype=dtype)
        time_features = time_features.to(device=device, dtype=dtype)
        spatial_features = spatial_features.to(device=device, dtype=dtype)
        
        batch_size, seq_len, num_nodes = x.shape
        
        # 扩展空间特征到批次和时间维度
        spatial_features_expanded = spatial_features.unsqueeze(0).unsqueeze(0).expand(
            batch_size, seq_len, -1, -1
        )  # [batch_size, seq_len, num_nodes, spatial_feat_dim]
        
        # 时空嵌入
        st_emb = self.st_embedding(time_features, spatial_features_expanded)  
        # [batch_size, seq_len, num_nodes, hidden_dim]
        
        # 处理每个时间步
        outputs = []
        
        for t in range(seq_len):
            # 当前时间步的特征
            flow_t = x[:, t, :].unsqueeze(-1)  # [batch_size, num_nodes, 1]
            st_emb_t = st_emb[:, t, :, :]  # [batch_size, num_nodes, hidden_dim]
            
            # 合并流量和时空特征
            node_features = torch.cat([flow_t, st_emb_t], dim=-1)  
            # [batch_size, num_nodes, hidden_dim+1]
            
            # 展平batch维度用于GCN处理
            node_features_flat = node_features.view(-1, node_features.shape[-1])  
            # [batch_size*num_nodes, hidden_dim+1]
            
            # 调整edge_index用于批处理
            edge_index_batch = edge_index.clone()
            for b in range(1, batch_size):
                offset_edges = edge_index + b * num_nodes
                edge_index_batch = torch.cat([edge_index_batch, offset_edges], dim=1)
            
            # 通过GCN层
            h = node_features_flat
            for i, gcn_layer in enumerate(self.gcn_layers):
                h_new = gcn_layer(h, edge_index_batch, edge_attr)
                
                # 残差连接（除了最后一层和维度不匹配的情况）
                if i < len(self.gcn_layers) - 1 and i < len(self.residual_connections):
                    if h.shape[-1] == h_new.shape[-1]:
                        h_new = h_new + h
                    else:
                        # 检查维度并修复
                        # 创建正确大小的残差连接层（如果需要）
                        if not hasattr(self, f'residual_connection_{i}_{h.shape[-1]}_{h_new.shape[-1]}'):
                            self.register_module(
                                f'residual_connection_{i}_{h.shape[-1]}_{h_new.shape[-1]}',
                                nn.Linear(h.shape[-1], h_new.shape[-1]).to(h.device)
                            )
                            setattr(self, 
                                   f'residual_connection_{i}_{h.shape[-1]}_{h_new.shape[-1]}',
                                   getattr(self, f'residual_connection_{i}_{h.shape[-1]}_{h_new.shape[-1]}').to(dtype=h.dtype)
                                  )
                        
                        # 使用正确大小的线性层
                        residual_layer = getattr(self, f'residual_connection_{i}_{h.shape[-1]}_{h_new.shape[-1]}')
                        h_new = h_new + residual_layer(h)
                h = h_new
            
            # 重新整理成批次形式
            h = h.view(batch_size, num_nodes, -1)
            outputs.append(h)
        
        # 堆叠时间维度
        outputs = torch.stack(outputs, dim=1)  # [batch_size, seq_len, num_nodes, hidden_dim]
        
        # 时间注意力
        batch_size, seq_len, num_nodes, hidden_dim = outputs.shape
        outputs_reshaped = outputs.view(batch_size * num_nodes, seq_len, hidden_dim)
        attended_outputs, attention_weights = self.temporal_attention(outputs_reshaped)
        attended_outputs = attended_outputs.view(batch_size, seq_len, num_nodes, hidden_dim)
        
        # 时间聚合（平均池化）
        final_output = torch.mean(attended_outputs, dim=1)  # [batch_size, num_nodes, hidden_dim]
        
        # 输出预测
        if task in self.output_layers:
            output = self.output_layers[task](final_output)
            output = output.squeeze(-1)  # [batch_size, num_nodes]
        else:
            raise ValueError(f"Unknown task: {task}")
        
        return output, attention_weights
    
    def predict_od_flow(self, origin_indices, dest_indices, od_features, node_embeddings):
        """
        预测OD流量
        origin_indices: [batch_size] - 起点站点索引
        dest_indices: [batch_size] - 终点站点索引  
        od_features: [batch_size, od_feature_dim] - OD对特征
        node_embeddings: [batch_size, num_nodes, hidden_dim] - 节点嵌入
        """
        if not hasattr(self, 'od_predictor'):
            raise ValueError("OD predictor not initialized")
        
        batch_size = origin_indices.shape[0]
        
        # 提取起点和终点的节点特征
        origin_features = node_embeddings[torch.arange(batch_size), origin_indices]
        dest_features = node_embeddings[torch.arange(batch_size), dest_indices]
        
        # 预测OD流量
        od_flow = self.od_predictor(origin_features, dest_features, od_features)
        
        return od_flow

class MultiTaskGCN(nn.Module):
    """多任务GCN模型"""
    def __init__(self, config):
        super(MultiTaskGCN, self).__init__()
        self.config = config
        
        # 共享的GCN编码器
        self.shared_encoder = ComplexGCNModel(config)
        
        # 任务特定的解码器
        final_dim = config.HIDDEN_DIM // 2 if config.NUM_LAYERS > 1 else config.HIDDEN_DIM
        self.task_decoders = nn.ModuleDict({
            'in_flow': nn.Sequential(
                nn.Linear(final_dim, final_dim // 2),
                nn.ReLU(),
                nn.Dropout(config.DROPOUT),
                nn.Linear(final_dim // 2, 1)
            ),
            'out_flow': nn.Sequential(
                nn.Linear(final_dim, final_dim // 2),
                nn.ReLU(),
                nn.Dropout(config.DROPOUT),
                nn.Linear(final_dim // 2, 1)
            ),
            'od_flow': nn.Sequential(
                nn.Linear(final_dim * 2, final_dim),
                nn.ReLU(),
                nn.Dropout(config.DROPOUT),
                nn.Linear(final_dim, 1)
            )
        })
        
    def forward(self, x, edge_index, edge_attr, time_features, spatial_features, tasks=['in_flow']):
        # 获取共享特征表示
        shared_features, attention_weights = self.shared_encoder(
            x, edge_index, edge_attr, time_features, spatial_features, task='in_flow'
        )
        
        outputs = {}
        for task in tasks:
            if task in self.task_decoders:
                if task == 'od_flow':
                    # 对于OD任务，使用特殊处理
                    task_output = self.task_decoders[task](shared_features)
                else:
                    task_output = self.task_decoders[task](shared_features)
                outputs[task] = task_output.squeeze(-1)
        
        return outputs, attention_weights

def create_model(config, model_type='complex'):
    """创建模型的工厂函数"""
    if model_type == 'complex':
        return ComplexGCNModel(config)
    elif model_type == 'multitask':
        return MultiTaskGCN(config)
    else:
        raise ValueError(f"Unknown model type: {model_type}")

class ModelUtils:
    """模型工具类"""
    
    @staticmethod
    def count_parameters(model):
        """计算模型参数数量"""
        return sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    @staticmethod
    def print_model_info(model):
        """打印模型信息"""
        total_params = ModelUtils.count_parameters(model)
        print(f"模型总参数数量: {total_params:,}")
        
        # 计算模型大小（MB）
        param_size = sum(p.numel() * p.element_size() for p in model.parameters())
        buffer_size = sum(b.numel() * b.element_size() for b in model.buffers())
        model_size_mb = (param_size + buffer_size) / (1024 * 1024)
        print(f"模型大小: {model_size_mb:.2f} MB")
        
        # 显示内存使用情况
        if hasattr(Config, 'get_memory_info'):
            print(f"当前{Config.get_memory_info()}")
    
    @staticmethod
    def save_model(model, optimizer, epoch, loss, filepath):
        """保存模型"""
        torch.save({
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'loss': loss,
        }, filepath)
        print(f"模型已保存到: {filepath}")
    
    @staticmethod
    def load_model(model, optimizer, filepath, device):
        """加载模型"""
        checkpoint = torch.load(filepath, map_location=device)
        model.load_state_dict(checkpoint['model_state_dict'])
        optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        epoch = checkpoint['epoch']
        loss = checkpoint['loss']
        print(f"模型已从 {filepath} 加载，epoch: {epoch}, loss: {loss:.4f}")
        return epoch, loss 