"""
GPU优化版主程序
集成内存监控、性能优化和智能资源管理
"""
import torch
import numpy as np
from config import Config
from data_loader import DataLoader
from data_preprocessor_improved import ImprovedDataPreprocessor
from trainer_gpu_optimized import GPUOptimizedTrainer
from memory_monitor import MemoryMonitor, quick_memory_check, estimate_tgcn_memory
from utils import ModelUtils

def main():
    """GPU优化版主函数"""
    # 初始化内存监控
    monitor = MemoryMonitor()
    
    print("=" * 60)
    print("🚀 GPU优化版T-GCN 地铁客流预测系统")
    print("=" * 60)
    
    # 初始检查
    quick_memory_check()
    
    # 开始监控
    monitor.start_monitoring()
    
    try:
        # 初始化配置
        config = Config()
        config.ACCUMULATION_STEPS = 4  # 梯度累积步数
        
        # 设备检查和优化设置
        if torch.cuda.is_available():
            print(f"🎮 GPU设备: {torch.cuda.get_device_name(0)}")
            print(f"🔋 GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
            config.DEVICE = "cuda"
            
            # 根据GPU内存调整配置
            gpu_memory_gb = torch.cuda.get_device_properties(0).total_memory / 1024**3
            if gpu_memory_gb < 4:
                config.HIDDEN_DIM = 32
                config.NUM_EPOCHS = 100
                print("📉 低GPU内存配置：减少模型复杂度")
            elif gpu_memory_gb < 8:
                config.HIDDEN_DIM = 48
                config.NUM_EPOCHS = 150
                print("⚖️  中等GPU内存配置")
            else:
                config.HIDDEN_DIM = 64
                config.NUM_EPOCHS = 200
                print("🔥 高GPU内存配置：启用全性能模式")
        else:
            print("⚠️  未检测到GPU，使用CPU模式")
            config.DEVICE = "cpu"
            config.HIDDEN_DIM = 32
            config.NUM_EPOCHS = 50
        
        ModelUtils.set_random_seed(config.RANDOM_SEED)
        
        # 初始化各个模块
        data_loader = DataLoader(config)
        preprocessor = ImprovedDataPreprocessor(config)
        trainer = GPUOptimizedTrainer(config)
        
        # 第一步：加载数据
        print("\n🔄 第一步：加载原始数据...")
        in_data, out_data, od_data, station_features = data_loader.load_data()
        
        # 检查数据加载后的内存
        current = monitor.get_current_memory()
        print(f"   数据加载后内存: {current['process_mb']:.1f} MB")
        
        # 第二步：预估内存需求并智能调整
        print("\n🔄 第二步：内存预估和智能调整...")
        num_stations = len(set(in_data['station'].unique()) & 
                          set(out_data['station'].unique()) &
                          set(station_features['站名'].unique()))
        
        # 估算内存需求
        estimated_memory = estimate_tgcn_memory(
            num_stations=num_stations,
            time_steps=config.TIME_STEPS,
            hidden_dim=config.HIDDEN_DIM,
            feature_dim=len(station_features.columns) - 1 + 2
        )
        
        # 智能调整数据大小
        current = monitor.get_current_memory()
        available_memory = current['available_mb'] if torch.cuda.is_available() else current['available_mb']
        
        if estimated_memory > available_memory * 0.7:  # 保留30%的内存余量
            print("🔧 自动优化数据规模...")
            
            # 调整站点数量
            if num_stations > 60:
                target_stations = min(60, int(num_stations * available_memory * 0.7 / estimated_memory))
                selected_stations = np.random.choice(
                    list(set(in_data['station'].unique())), 
                    target_stations, 
                    replace=False
                )
                in_data = in_data[in_data['station'].isin(selected_stations)]
                out_data = out_data[out_data['station'].isin(selected_stations)]
                od_data = od_data[
                    (od_data['o_rawname'].isin(selected_stations)) &
                    (od_data['d_rawname'].isin(selected_stations))
                ]
                print(f"   📊 站点数量调整为: {target_stations}")
            
            # 调整时间窗口
            if config.TIME_STEPS > 12:
                config.TIME_STEPS = 12
                print(f"   ⏰ 时间步数调整为: {config.TIME_STEPS}")
            
            # 调整模型复杂度
            if config.HIDDEN_DIM > 32:
                config.HIDDEN_DIM = 32
                print(f"   🧠 隐藏维度调整为: {config.HIDDEN_DIM}")
        
        # 第三步：数据预处理
        print("\n🔄 第三步：数据预处理...")
        (X, in_flows, out_flows, od_flows_tensor, edge_index, edge_weights,
         train_indices, test_indices, common_stations, train_stations, test_stations,
         station_to_idx, in_data_filtered, out_data_filtered, od_data_filtered) = preprocessor.preprocess_data(
            in_data, out_data, od_data, station_features
        )
        
        print("✅ 数据预处理完成")
        print(f"   📊 最终站点数量: {len(common_stations)}")
        print(f"   🚂 训练站点数量: {len(train_stations)}")
        print(f"   🧪 测试站点数量: {len(test_stations)}")
        print(f"   🔗 图边数量: {edge_index.shape[1]}")
        print(f"   📈 特征维度: {X.shape}")
        
        # 第四步：GPU优化训练
        print("\n🔄 第四步：GPU优化训练...")
        
        # 将数据移到GPU（如果可用）
        device = torch.device(config.DEVICE)
        print(f"🎯 目标设备: {device}")
        
        X = X.to(device)
        in_flows = in_flows.to(device)
        out_flows = out_flows.to(device)
        od_flows_tensor = od_flows_tensor.to(device)
        edge_index = edge_index.to(device)
        edge_weights = edge_weights.to(device)
        
        # 确保indices是numpy数组
        if isinstance(train_indices, torch.Tensor):
            train_indices = train_indices.cpu().numpy()
        if isinstance(test_indices, torch.Tensor):
            test_indices = test_indices.cpu().numpy()
        
        # 开始训练
        model = trainer.train_multitask_model(
            X, in_flows, out_flows, od_flows_tensor, edge_index, edge_weights,
            train_indices, test_indices, common_stations
        )
        print("✅ 模型训练完成")
        
        # 第五步：模型评估
        print("\n🔄 第五步：模型评估...")
        evaluation_results = evaluate_gpu_optimized_model(
            model, trainer, X, in_flows, out_flows, edge_index, edge_weights,
            train_indices, test_indices, test_stations, device
        )
        print("✅ 评估完成")
        
        # 第六步：保存结果
        print("\n🔄 第六步：保存结果...")
        save_gpu_optimized_results(
            evaluation_results, config, test_stations, 
            in_data_filtered, out_data_filtered
        )
        print("✅ 结果保存完成")
        
        # 打印最终结果摘要
        print_gpu_results_summary(evaluation_results)
        
        print("\n" + "=" * 60)
        print("🎉 GPU优化版程序执行完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 程序执行出错: {str(e)}")
        import traceback
        traceback.print_exc()
        raise
    
    finally:
        # 停止监控并生成报告
        print("\n🔄 生成内存使用报告...")
        final_report = monitor.stop_monitoring()
        
        # 清理GPU内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            print("🧹 GPU内存已清理")

def evaluate_gpu_optimized_model(model, trainer, X, in_flows, out_flows, edge_index, edge_weights,
                                train_indices, test_indices, test_stations, device):
    """GPU优化模型评估"""
    
    # 加载最佳模型
    try:
        model.load_state_dict(torch.load('best_gpu_optimized_model.pth', map_location=device))
        print("📁 已加载最佳模型")
    except:
        print("⚠️ 未找到最佳模型文件，使用当前模型")
    
    model.eval()
    
    # 准备测试数据
    test_data = trainer._prepare_optimized_data(
        X, in_flows, out_flows, edge_index, edge_weights, 
        train_indices, test_indices
    )
    
    evaluation_results = {}
    
    with torch.no_grad():
        # 使用混合精度进行推理
        from torch.cuda.amp import autocast
        with autocast(enabled=torch.cuda.is_available()):
            test_outputs = model(
                test_data['X_test'], 
                test_data['edge_index_test'], 
                test_data['edge_weights_test']
            )
        
        # 计算评估指标
        from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
        
        # 进站流量评估
        if 'in_flow' in test_outputs:
            in_pred = test_outputs['in_flow'].cpu().numpy().flatten()
            in_true = test_data['in_test'].cpu().numpy().flatten()
            
            evaluation_results['in_flow'] = {
                'predictions': in_pred,
                'true_values': in_true,
                'mae': mean_absolute_error(in_true, in_pred),
                'mse': mean_squared_error(in_true, in_pred),
                'rmse': np.sqrt(mean_squared_error(in_true, in_pred)),
                'r2': r2_score(in_true, in_pred)
            }
        
        # 出站流量评估
        if 'out_flow' in test_outputs:
            out_pred = test_outputs['out_flow'].cpu().numpy().flatten()
            out_true = test_data['out_test'].cpu().numpy().flatten()
            
            evaluation_results['out_flow'] = {
                'predictions': out_pred,
                'true_values': out_true,
                'mae': mean_absolute_error(out_true, out_pred),
                'mse': mean_squared_error(out_true, out_pred),
                'rmse': np.sqrt(mean_squared_error(out_true, out_pred)),
                'r2': r2_score(out_true, out_pred)
            }
        
        # 添加测试站点信息
        evaluation_results['test_stations'] = test_stations
    
    return evaluation_results

def save_gpu_optimized_results(evaluation_results, config, test_stations, in_data, out_data):
    """保存GPU优化结果"""
    import pandas as pd
    import geopandas as gpd
    
    # 保存进站预测结果
    if 'in_flow' in evaluation_results:
        in_predictions_df = pd.DataFrame({
            'station': test_stations[:len(evaluation_results['in_flow']['predictions'])],
            'prediction': evaluation_results['in_flow']['predictions'][:len(test_stations)],
            'true_value': evaluation_results['in_flow']['true_values'][:len(test_stations)]
        })
        
        # 合并到原始数据
        test_in_data = in_data[in_data['station'].isin(test_stations)].copy()
        if len(test_in_data) > 0:
            # 取每个站点的第一条记录进行合并
            test_in_data_sample = test_in_data.groupby('station').first().reset_index()
            test_in_data_merged = pd.merge(test_in_data_sample, in_predictions_df, on='station', how='left')
            
            # 保存为CSV（更稳定）
            test_in_data_merged.to_csv('gpu_optimized_in_predictions.csv', index=False)
            print(f"   📁 进站预测结果已保存: gpu_optimized_in_predictions.csv")
    
    # 保存出站预测结果
    if 'out_flow' in evaluation_results:
        out_predictions_df = pd.DataFrame({
            'station': test_stations[:len(evaluation_results['out_flow']['predictions'])],
            'prediction': evaluation_results['out_flow']['predictions'][:len(test_stations)],
            'true_value': evaluation_results['out_flow']['true_values'][:len(test_stations)]
        })
        
        test_out_data = out_data[out_data['station'].isin(test_stations)].copy()
        if len(test_out_data) > 0:
            test_out_data_sample = test_out_data.groupby('station').first().reset_index()
            test_out_data_merged = pd.merge(test_out_data_sample, out_predictions_df, on='station', how='left')
            
            test_out_data_merged.to_csv('gpu_optimized_out_predictions.csv', index=False)
            print(f"   📁 出站预测结果已保存: gpu_optimized_out_predictions.csv")
    
    # 保存综合比较结果
    comparison_results = []
    
    for task_name, task_results in evaluation_results.items():
        if isinstance(task_results, dict) and 'predictions' in task_results:
            for i, (pred, true) in enumerate(zip(task_results['predictions'], task_results['true_values'])):
                if i < len(test_stations):
                    comparison_results.append({
                        'Task': task_name,
                        'Station': test_stations[i],
                        'Predicted_Value': pred,
                        'True_Value': true,
                        'Absolute_Error': abs(pred - true),
                        'Relative_Error': abs(pred - true) / (abs(true) + 1e-8)
                    })
    
    if comparison_results:
        comparison_df = pd.DataFrame(comparison_results)
        comparison_df.to_csv('gpu_optimized_comparison.csv', index=False)
        print(f"   📁 预测比较结果已保存: gpu_optimized_comparison.csv")

def print_gpu_results_summary(evaluation_results):
    """打印GPU优化结果摘要"""
    print("\n" + "=" * 60)
    print("🏆 GPU优化模型评估结果")
    print("=" * 60)
    
    for task_name, task_results in evaluation_results.items():
        if isinstance(task_results, dict) and 'mae' in task_results:
            print(f"\n🎯 {task_name.upper().replace('_', ' ')} 预测结果:")
            print(f"   MAE:  {task_results['mae']:.4f}")
            print(f"   RMSE: {task_results['rmse']:.4f}")
            print(f"   R²:   {task_results['r2']:.4f}")
            
            # 性能评级
            if task_results['r2'] > 0.85:
                grade = "🌟 卓越"
                color = "绿色"
            elif task_results['r2'] > 0.7:
                grade = "👍 优秀" 
                color = "蓝色"
            elif task_results['r2'] > 0.5:
                grade = "🤔 良好"
                color = "黄色"
            elif task_results['r2'] > 0.3:
                grade = "⚠️  一般"
                color = "橙色"
            else:
                grade = "❌ 需改进"
                color = "红色"
            
            print(f"   预测质量: {grade}")
            
            # 计算预测准确率
            predictions = task_results['predictions']
            true_values = task_results['true_values']
            
            # 相对误差在20%以内的预测比例
            relative_errors = np.abs(predictions - true_values) / (np.abs(true_values) + 1e-8)
            accuracy_20 = np.mean(relative_errors < 0.2) * 100
            
            print(f"   20%误差内准确率: {accuracy_20:.1f}%")
    
    # GPU性能总结
    if torch.cuda.is_available():
        print(f"\n💻 GPU性能总结:")
        print(f"   设备: {torch.cuda.get_device_name(0)}")
        memory_allocated = torch.cuda.memory_allocated() / 1024**2
        memory_total = torch.cuda.get_device_properties(0).total_memory / 1024**2
        print(f"   峰值内存使用: {memory_allocated:.0f}/{memory_total:.0f} MB ({memory_allocated/memory_total*100:.1f}%)")

if __name__ == "__main__":
    main() 