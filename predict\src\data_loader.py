import os
import numpy as np
import pandas as pd
import geopandas as gpd
from tqdm import tqdm
from shapely.geometry import Point
from sklearn.preprocessing import StandardScaler, MinMaxScaler
import torch
from torch_geometric.data import Data
import torch_geometric.transforms as T
from scipy.spatial.distance import cdist
import random
import copy
from sklearn.neighbors import NearestNeighbors

# 配置GPU设备
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

class MetroDataLoader:
    """
    地铁站流量数据加载器
    处理原始数据并构建站点图和站点-站点图
    """
    def __init__(self, data_dir, batch_size=32, num_workers=0, pin_memory=True, use_fp16=True):
        """
        初始化数据加载器
        
        参数:
        data_dir: 数据目录，包含站点信息、流量数据等
        batch_size: 批处理大小
        num_workers: 数据加载的工作线程数
        pin_memory: 是否将数据固定在内存中（可加速GPU训练）
        use_fp16: 是否使用半精度浮点数减少内存使用
        """
        self.data_dir = data_dir
        self.batch_size = batch_size
        self.num_workers = num_workers
        self.pin_memory = pin_memory
        self.use_fp16 = use_fp16
        
        # 加载数据
        self.load_data()
        
        # 构建数据加载器
        self.build_data_loaders()
        
    def load_data(self):
        """加载所有数据"""
        print("加载站点数据...")
        self.stations = self.load_stations()
        
        print("加载进站流量数据...")
        self.in_data = self.load_in_flow_data()
        
        print("加载出站流量数据...")
        self.out_data = self.load_out_flow_data()
        
        print("加载OD流量数据...")
        self.od_data = self.load_od_flow_data()
        
        # 保存处理过的数据，方便调试
        self.stations.to_csv(os.path.join(self.data_dir, 'processed_stations.csv'), index=False)
        self.in_data.to_csv(os.path.join(self.data_dir, 'processed_in_flow.csv'), index=False)
        self.out_data.to_csv(os.path.join(self.data_dir, 'processed_out_flow.csv'), index=False)
        self.od_data.to_csv(os.path.join(self.data_dir, 'processed_od_flow.csv'), index=False)
        
        print(f"数据加载完成: {len(self.stations)}个站点, {len(self.in_data)}条进站记录, "
              f"{len(self.out_data)}条出站记录, {len(self.od_data)}条OD记录")
        
    def load_stations(self):
        """加载站点信息"""
        stations_file = os.path.join(self.data_dir, 'station_info.csv')
        stations = pd.read_csv(stations_file)
        
        # 确保坐标数据是浮点数
        stations['lon'] = stations['lon'].astype(float)
        stations['lat'] = stations['lat'].astype(float)
        
        # 移除任何重复的站点
        stations = stations.drop_duplicates(subset=['station_id']).reset_index(drop=True)
        
        # 确保站点ID是字符串类型，便于后续处理
        stations['station_id'] = stations['station_id'].astype(str)
        
        return stations
    
    def load_in_flow_data(self):
        """加载进站流量数据"""
        in_flow_file = os.path.join(self.data_dir, 'in_flow.csv')
        in_data = pd.read_csv(in_flow_file)
        
        # 确保站点ID是字符串类型
        in_data['station_id'] = in_data['station_id'].astype(str)
        
        # 移除hour为0-5的数据（凌晨地铁不运营）
        in_data = in_data[~in_data['hour'].isin([0, 1, 2, 3, 4, 5])].reset_index(drop=True)
        
        # 将日期和小时转换为时间特征
        in_data['date'] = pd.to_datetime(in_data['date'])
        in_data['day_of_week'] = in_data['date'].dt.dayofweek
        in_data['month'] = in_data['date'].dt.month
        in_data['day'] = in_data['date'].dt.day
        in_data['is_weekend'] = in_data['day_of_week'].apply(lambda x: 1 if x >= 5 else 0)
        
        # 基于站点ID划分训练/验证/测试集
        self._split_dataset_by_stations(in_data)
        
        return in_data
    
    def load_out_flow_data(self):
        """加载出站流量数据"""
        out_flow_file = os.path.join(self.data_dir, 'out_flow.csv')
        out_data = pd.read_csv(out_flow_file)
        
        # 确保站点ID是字符串类型
        out_data['station_id'] = out_data['station_id'].astype(str)
        
        # 移除hour为0-5的数据（凌晨地铁不运营）
        out_data = out_data[~out_data['hour'].isin([0, 1, 2, 3, 4, 5])].reset_index(drop=True)
        
        # 将日期和小时转换为时间特征
        out_data['date'] = pd.to_datetime(out_data['date'])
        out_data['day_of_week'] = out_data['date'].dt.dayofweek
        out_data['month'] = out_data['date'].dt.month
        out_data['day'] = out_data['date'].dt.day
        out_data['is_weekend'] = out_data['day_of_week'].apply(lambda x: 1 if x >= 5 else 0)
        
        # 基于站点ID划分训练/验证/测试集
        self._split_dataset_by_stations(out_data)
        
        return out_data
    
    def load_od_flow_data(self):
        """加载OD流量数据"""
        od_flow_file = os.path.join(self.data_dir, 'od_flow.csv')
        od_data = pd.read_csv(od_flow_file)
        
        # 确保站点ID是字符串类型
        od_data['o_station'] = od_data['o_station'].astype(str)
        od_data['d_station'] = od_data['d_station'].astype(str)
        
        # 移除hour为0-5的数据（凌晨地铁不运营）
        od_data = od_data[~od_data['hour'].isin([0, 1, 2, 3, 4, 5])].reset_index(drop=True)
        
        # 移除trip为0的数据行
        od_data = od_data[od_data['trip'] > 0].reset_index(drop=True)
        
        # 将日期和小时转换为时间特征
        od_data['date'] = pd.to_datetime(od_data['date'])
        od_data['day_of_week'] = od_data['date'].dt.dayofweek
        od_data['month'] = od_data['date'].dt.month
        od_data['day'] = od_data['date'].dt.day
        od_data['is_weekend'] = od_data['day_of_week'].apply(lambda x: 1 if x >= 5 else 0)
        
        # 基于站点ID划分训练/验证/测试集
        self._split_dataset_by_stations(od_data, o_station_col='o_station', d_station_col='d_station')
        
        return od_data
    
    def _split_dataset_by_stations(self, df, station_col='station_id', o_station_col=None, d_station_col=None):
        """
        基于站点ID划分数据集，确保相同站点的数据在同一个数据集中
        
        参数:
        df: 数据框
        station_col: 站点ID列名
        o_station_col: 起始站点ID列名（用于OD数据）
        d_station_col: 目的站点ID列名（用于OD数据）
        """
        # 获取站点集合
        if o_station_col is not None and d_station_col is not None:
            # 处理OD数据
            all_stations = set(df[o_station_col].unique()) | set(df[d_station_col].unique())
        else:
            # 处理进站/出站数据
            all_stations = set(df[station_col].unique())
        
        # 将站点随机分为训练(70%)、验证(15%)、测试(15%)集
        all_stations = list(all_stations)
        random.seed(42)  # 固定随机种子
        random.shuffle(all_stations)
        
        n_stations = len(all_stations)
        n_train = int(n_stations * 0.7)
        n_val = int(n_stations * 0.15)
        
        train_stations = set(all_stations[:n_train])
        val_stations = set(all_stations[n_train:n_train+n_val])
        test_stations = set(all_stations[n_train+n_val:])
        
        # 创建数据集列
        if o_station_col is not None and d_station_col is not None:
            # OD数据
            df['dataset'] = df.apply(
                lambda row: 'train' if row[o_station_col] in train_stations and row[d_station_col] in train_stations
                            else 'val' if row[o_station_col] in val_stations and row[d_station_col] in val_stations
                            else 'test' if row[o_station_col] in test_stations and row[d_station_col] in test_stations
                            else 'train',  # 默认分配给训练集
                axis=1
            )
        else:
            # 进站/出站数据
            df['dataset'] = df[station_col].apply(
                lambda x: 'train' if x in train_stations
                          else 'val' if x in val_stations
                          else 'test'
            )
    
    def get_grid_station_graph(self):
        """构建站点网格图"""
        # 获取站点坐标
        station_coords = self.stations[['station_id', 'lon', 'lat']].drop_duplicates().reset_index(drop=True)
        
        # 确保坐标是浮点数
        station_coords['lon'] = station_coords['lon'].astype(float)
        station_coords['lat'] = station_coords['lat'].astype(float)
        
        # 创建站点ID到索引的映射
        station_to_idx = {sid: i for i, sid in enumerate(station_coords['station_id'])}
        
        # 构建边索引（使用KNN算法连接最近的10个站点）
        coords = station_coords[['lon', 'lat']].values
        nbrs = NearestNeighbors(n_neighbors=11, algorithm='ball_tree').fit(coords)
        _, indices = nbrs.kneighbors(coords)
        
        # 转换为边索引
        edge_index = []
        for i, neighbors in enumerate(indices):
            for j in neighbors[1:]:  # 排除自身
                edge_index.append([i, j])
        
        edge_index = torch.tensor(edge_index, dtype=torch.long).t().contiguous()
        
        # 创建节点特征 - 包括地理位置和时间特征
        # 节点特征包括: [lon, lat, hour(one-hot), day_of_week(one-hot), is_weekend]
        hours = range(6, 24)  # 6点到23点
        days = range(7)  # 周一到周日
        
        # 初始化节点特征矩阵
        num_nodes = len(station_coords)
        node_features = np.zeros((num_nodes, 2 + len(hours) + len(days) + 1))
        
        # 添加地理位置特征
        node_features[:, 0] = station_coords['lon'].values
        node_features[:, 1] = station_coords['lat'].values
        
        # 添加时间特征 - 使用全0向量，后续会在批处理时填充
        
        # 创建Data对象
        data = Data(
            x=torch.tensor(node_features, dtype=torch.float),
            edge_index=edge_index
        )
        
        return data
    
    def get_station_station_graph(self):
        """构建站点-站点图"""
        # 获取所有站点对
        print("正在构建站点-站点图...")
        station_pairs = self.od_data[['o_station', 'd_station']].drop_duplicates().reset_index(drop=True)
        
        # 创建站点ID到索引的映射
        all_stations = set(self.stations['station_id'])
        station_to_idx = {sid: i for i, sid in enumerate(all_stations)}
        
        # 构建边索引
        edge_index = []
        for _, row in station_pairs.iterrows():
            o_idx = station_to_idx.get(row['o_station'])
            d_idx = station_to_idx.get(row['d_station'])
            if o_idx is not None and d_idx is not None:
                edge_index.append([o_idx, d_idx])
        
        edge_index = torch.tensor(edge_index, dtype=torch.long).t().contiguous()
        
        # 构建边特征 - 距离、出发时间
        edge_features = np.zeros((edge_index.shape[1], 4))  # [距离, hour(归一化), day_of_week(归一化), is_weekend]
        
        # 计算距离
        station_coords = {sid: (lon, lat) for sid, lon, lat in 
                          zip(self.stations['station_id'], self.stations['lon'], self.stations['lat'])}
        
        for i, (src, dst) in enumerate(zip(edge_index[0], edge_index[1])):
            src_id = list(all_stations)[src.item()]
            dst_id = list(all_stations)[dst.item()]
            
            if src_id in station_coords and dst_id in station_coords:
                src_coords = station_coords[src_id]
                dst_coords = station_coords[dst_id]
                # 计算地理距离（km）
                edge_features[i, 0] = haversine(src_coords[1], src_coords[0], dst_coords[1], dst_coords[0])
        
        # 时间特征在批处理时填充
        
        # 创建节点特征
        num_nodes = len(all_stations)
        node_features = np.zeros((num_nodes, 2 + 18 + 7 + 1))  # [lon, lat, hour(one-hot), day_of_week(one-hot), is_weekend]
        
        # 添加地理位置特征
        for i, sid in enumerate(all_stations):
            if sid in station_coords:
                node_features[i, 0] = station_coords[sid][0]  # lon
                node_features[i, 1] = station_coords[sid][1]  # lat
        
        # 时间特征在批处理时填充
        
        # 创建Data对象
        data = Data(
            x=torch.tensor(node_features, dtype=torch.float),
            edge_index=edge_index,
            edge_attr=torch.tensor(edge_features, dtype=torch.float)
        )
        
        print(f"站点-站点图构建完成：{num_nodes}个站点节点，{edge_index.shape[1]}条边，节点特征维度={node_features.shape[1]}，边特征维度={edge_features.shape[1]}")
        
        return data
    
    def build_data_loaders(self):
        """构建批处理数据加载器"""
        # 创建自定义数据集
        train_dataset = MetroGraphDataset(
            self.stations, self.in_data, self.out_data, self.od_data, 
            dataset_type='train', use_fp16=self.use_fp16
        )
        
        val_dataset = MetroGraphDataset(
            self.stations, self.in_data, self.out_data, self.od_data, 
            dataset_type='val', use_fp16=self.use_fp16
        )
        
        test_dataset = MetroGraphDataset(
            self.stations, self.in_data, self.out_data, self.od_data, 
            dataset_type='test', use_fp16=self.use_fp16
        )
        
        # 创建数据加载器
        self.train_loader = DataLoader(
            train_dataset, 
            batch_size=self.batch_size, 
            shuffle=True, 
            num_workers=self.num_workers,
            pin_memory=self.pin_memory
        )
        
        self.val_loader = DataLoader(
            val_dataset, 
            batch_size=self.batch_size, 
            shuffle=False, 
            num_workers=self.num_workers,
            pin_memory=self.pin_memory
        )
        
        self.test_loader = DataLoader(
            test_dataset, 
            batch_size=self.batch_size, 
            shuffle=False, 
            num_workers=self.num_workers,
            pin_memory=self.pin_memory
        )
        
        print(f"数据加载器构建完成：训练集 {len(train_dataset)} 个样本，验证集 {len(val_dataset)} 个样本，测试集 {len(test_dataset)} 个样本")


class MetroGraphDataset(Dataset):
    """地铁站图数据集"""
    def __init__(self, stations, in_data, out_data, od_data, dataset_type='train', use_fp16=True):
        """
        初始化数据集
        
        参数:
        stations: 站点信息
        in_data: 进站流量数据
        out_data: 出站流量数据
        od_data: OD流量数据
        dataset_type: 'train', 'val', 'test'
        use_fp16: 是否使用半精度浮点数
        """
        self.stations = stations
        self.in_data = in_data[in_data['dataset'] == dataset_type].reset_index(drop=True)
        self.out_data = out_data[out_data['dataset'] == dataset_type].reset_index(drop=True)
        self.od_data = od_data[od_data['dataset'] == dataset_type].reset_index(drop=True)
        self.dataset_type = dataset_type
        self.use_fp16 = use_fp16
        
        # 获取唯一的日期和小时组合，用于批处理
        date_hour_pairs = set()
        for df in [self.in_data, self.out_data, self.od_data]:
            if not df.empty:
                for _, row in df.iterrows():
                    date_hour_pairs.add((row['date'], row['hour']))
        
        self.date_hour_pairs = sorted(list(date_hour_pairs))
        
        # 构建基础图结构
        self.grid_graph = self._build_grid_graph()
        self.od_graph = self._build_od_graph()
        
        print(f"{dataset_type}数据集初始化完成，共 {len(self.date_hour_pairs)} 个时间点")
    
    def __len__(self):
        """返回数据集长度"""
        return len(self.date_hour_pairs)
    
    def __getitem__(self, idx):
        """获取数据集项"""
        date, hour = self.date_hour_pairs[idx]
        
        # 深复制基础图结构以避免修改原始数据
        grid_graph = copy.deepcopy(self.grid_graph)
        od_graph = copy.deepcopy(self.od_graph)
        
        # 更新图特征
        self._update_graph_features(grid_graph, od_graph, date, hour)
        
        # 获取当前时间点的目标数据
        in_targets = self._get_in_flow_targets(date, hour)
        out_targets = self._get_out_flow_targets(date, hour)
        od_targets = self._get_od_flow_targets(date, hour)
        
        # 将目标转换为张量
        targets = {
            'in': torch.tensor(in_targets, dtype=torch.float16 if self.use_fp16 else torch.float32),
            'out': torch.tensor(out_targets, dtype=torch.float16 if self.use_fp16 else torch.float32),
            'od': torch.tensor(od_targets, dtype=torch.float16 if self.use_fp16 else torch.float32)
        }
        
        # 创建一个包含所有图的数据对象
        combined_graph = self._combine_graphs(grid_graph, od_graph)
        
        return combined_graph, targets
    
    def _build_grid_graph(self):
        """构建基础网格图结构"""
        # 获取站点坐标
        station_coords = self.stations[['station_id', 'lon', 'lat']].drop_duplicates().reset_index(drop=True)
        
        # 确保坐标是浮点数
        station_coords['lon'] = station_coords['lon'].astype(float)
        station_coords['lat'] = station_coords['lat'].astype(float)
        
        # 创建站点ID到索引的映射
        self.station_to_idx = {sid: i for i, sid in enumerate(station_coords['station_id'])}
        self.idx_to_station = {i: sid for sid, i in self.station_to_idx.items()}
        
        # 构建边索引（使用KNN算法连接最近的10个站点）
        coords = station_coords[['lon', 'lat']].values
        nbrs = NearestNeighbors(n_neighbors=min(11, len(coords)), algorithm='ball_tree').fit(coords)
        _, indices = nbrs.kneighbors(coords)
        
        # 转换为边索引
        edge_index = []
        for i, neighbors in enumerate(indices):
            for j in neighbors[1:]:  # 排除自身
                edge_index.append([i, j])
        
        edge_index = torch.tensor(edge_index, dtype=torch.long).t().contiguous()
        
        # 创建节点特征
        num_nodes = len(station_coords)
        
        # 基础特征：位置 + 小时(one-hot) + 星期(one-hot) + 是否周末
        node_features = np.zeros((num_nodes, 2 + 18 + 7 + 1))
        
        # 添加地理位置特征
        node_features[:, 0] = station_coords['lon'].values
        node_features[:, 1] = station_coords['lat'].values
        
        # 创建Data对象
        data = Data(
            x=torch.tensor(node_features, dtype=torch.float16 if self.use_fp16 else torch.float32),
            edge_index=edge_index
        )
        
        return data
    
    def _build_od_graph(self):
        """构建基础OD图结构"""
        # 获取站点坐标
        station_coords = self.stations[['station_id', 'lon', 'lat']].drop_duplicates().reset_index(drop=True)
        
        # 创建边索引和边特征
        all_stations = set(self.stations['station_id'])
        
        # 使用已经构建的station_to_idx
        if not hasattr(self, 'station_to_idx'):
            self.station_to_idx = {sid: i for i, sid in enumerate(station_coords['station_id'])}
            self.idx_to_station = {i: sid for sid, i in self.station_to_idx.items()}
        
        # 获取所有站点对
        station_pairs = self.od_data[['o_station', 'd_station']].drop_duplicates().reset_index(drop=True)
        
        # 构建边索引
        edge_index = []
        for _, row in station_pairs.iterrows():
            o_idx = self.station_to_idx.get(row['o_station'])
            d_idx = self.station_to_idx.get(row['d_station'])
            if o_idx is not None and d_idx is not None:
                edge_index.append([o_idx, d_idx])
        
        if not edge_index:  # 如果没有边，创建一个空的边索引
            edge_index = torch.zeros((2, 0), dtype=torch.long)
        else:
            edge_index = torch.tensor(edge_index, dtype=torch.long).t().contiguous()
        
        # 边特征：[距离, 小时(归一化), 星期(归一化), 是否周末]
        edge_features = np.zeros((edge_index.shape[1], 4))
        
        # 计算距离
        station_coords_dict = {sid: (lon, lat) for sid, lon, lat in 
                              zip(self.stations['station_id'], self.stations['lon'], self.stations['lat'])}
        
        for i, (src, dst) in enumerate(zip(edge_index[0], edge_index[1])):
            src_id = self.idx_to_station[src.item()]
            dst_id = self.idx_to_station[dst.item()]
            
            if src_id in station_coords_dict and dst_id in station_coords_dict:
                src_coords = station_coords_dict[src_id]
                dst_coords = station_coords_dict[dst_id]
                # 计算地理距离（km）
                edge_features[i, 0] = haversine(src_coords[1], src_coords[0], dst_coords[1], dst_coords[0])
        
        # 创建节点特征
        num_nodes = len(self.station_to_idx)
        
        # 基础特征：位置 + 小时(one-hot) + 星期(one-hot) + 是否周末
        node_features = np.zeros((num_nodes, 2 + 18 + 7 + 1))
        
        # 添加地理位置特征
        for i, sid in enumerate(self.station_to_idx.keys()):
            if sid in station_coords_dict:
                node_features[i, 0] = station_coords_dict[sid][0]  # lon
                node_features[i, 1] = station_coords_dict[sid][1]  # lat
        
        # 创建Data对象
        data = Data(
            x=torch.tensor(node_features, dtype=torch.float16 if self.use_fp16 else torch.float32),
            edge_index=edge_index,
            edge_attr=torch.tensor(edge_features, dtype=torch.float16 if self.use_fp16 else torch.float32)
        )
        
        return data
    
    def _update_graph_features(self, grid_graph, od_graph, date, hour):
        """更新图特征"""
        # 提取日期特征
        date_dt = pd.to_datetime(date) if isinstance(date, str) else date
        day_of_week = date_dt.dayofweek
        is_weekend = 1 if day_of_week >= 5 else 0
        
        # 更新网格图节点特征
        x = grid_graph.x.numpy()
        
        # 重置时间特征部分
        x[:, 2:] = 0
        
        # 设置小时one-hot
        hour_idx = min(hour - 6, 17)  # 6点对应索引0
        x[:, 2 + hour_idx] = 1
        
        # 设置星期one-hot
        x[:, 2 + 18 + day_of_week] = 1
        
        # 设置是否周末
        x[:, -1] = is_weekend
        
        # 更新回张量
        grid_graph.x = torch.tensor(x, dtype=torch.float16 if self.use_fp16 else torch.float32)
        
        # 更新OD图节点特征
        x_od = od_graph.x.numpy()
        
        # 重置时间特征部分
        x_od[:, 2:] = 0
        
        # 设置小时one-hot
        x_od[:, 2 + hour_idx] = 1
        
        # 设置星期one-hot
        x_od[:, 2 + 18 + day_of_week] = 1
        
        # 设置是否周末
        x_od[:, -1] = is_weekend
        
        # 更新回张量
        od_graph.x = torch.tensor(x_od, dtype=torch.float16 if self.use_fp16 else torch.float32)
        
        # 更新OD图边特征
        if od_graph.edge_attr.size(0) > 0:
            edge_attr = od_graph.edge_attr.numpy()
            
            # 更新时间特征
            edge_attr[:, 1] = (hour - 6) / 18  # 归一化小时 (6-23)
            edge_attr[:, 2] = day_of_week / 6  # 归一化星期 (0-6)
            edge_attr[:, 3] = is_weekend
            
            # 更新回张量
            od_graph.edge_attr = torch.tensor(edge_attr, dtype=torch.float16 if self.use_fp16 else torch.float32)
    
    def _get_in_flow_targets(self, date, hour):
        """获取进站流量目标值"""
        # 筛选当前日期和小时的数据
        date_str = date.strftime('%Y-%m-%d') if isinstance(date, pd.Timestamp) else date
        filtered = self.in_data[(self.in_data['date'] == date_str) & (self.in_data['hour'] == hour)]
        
        # 创建目标向量
        targets = np.zeros(len(self.station_to_idx))
        
        # 填充已知的流量值
        for _, row in filtered.iterrows():
            station_id = row['station_id']
            if station_id in self.station_to_idx:
                idx = self.station_to_idx[station_id]
                targets[idx] = row['count']
        
        return targets
    
    def _get_out_flow_targets(self, date, hour):
        """获取出站流量目标值"""
        # 筛选当前日期和小时的数据
        date_str = date.strftime('%Y-%m-%d') if isinstance(date, pd.Timestamp) else date
        filtered = self.out_data[(self.out_data['date'] == date_str) & (self.out_data['hour'] == hour)]
        
        # 创建目标向量
        targets = np.zeros(len(self.station_to_idx))
        
        # 填充已知的流量值
        for _, row in filtered.iterrows():
            station_id = row['station_id']
            if station_id in self.station_to_idx:
                idx = self.station_to_idx[station_id]
                targets[idx] = row['count']
        
        return targets
    
    def _get_od_flow_targets(self, date, hour):
        """获取OD流量目标值"""
        # 筛选当前日期和小时的数据
        date_str = date.strftime('%Y-%m-%d') if isinstance(date, pd.Timestamp) else date
        filtered = self.od_data[(self.od_data['date'] == date_str) & (self.od_data['hour'] == hour)]
        
        # 创建目标向量 - 每条边对应一个流量值
        targets = np.zeros(self.od_graph.edge_index.shape[1])
        
        # 填充已知的流量值
        for _, row in filtered.iterrows():
            o_station = row['o_station']
            d_station = row['d_station']
            
            if o_station in self.station_to_idx and d_station in self.station_to_idx:
                o_idx = self.station_to_idx[o_station]
                d_idx = self.station_to_idx[d_station]
                
                # 查找对应的边索引
                for i, (src, dst) in enumerate(zip(self.od_graph.edge_index[0], self.od_graph.edge_index[1])):
                    if src.item() == o_idx and dst.item() == d_idx:
                        targets[i] = row['trip']
                        break
        
        return targets
    
    def _combine_graphs(self, grid_graph, od_graph):
        """合并网格图和OD图"""
        # 确保两个图使用相同的节点表示
        assert grid_graph.x.shape == od_graph.x.shape, "节点特征维度不匹配"
        
        # 创建合并的图数据对象
        combined_data = Data(
            x=grid_graph.x,
            edge_index=grid_graph.edge_index,
            od_edge_index=od_graph.edge_index,
            od_edge_attr=od_graph.edge_attr
        )
        
        return combined_data 