# 纯GCN+时间特征地铁流量预测系统

## 项目概述

本项目实现了一个基于纯图卷积网络(GCN)和时间特征的地铁流量预测系统，能够预测地铁站点的进出站流量、站间OD流量，并将流量分配到栅格级别。

## 模型架构

### 1. 核心模型设计

#### 1.1 时间感知图卷积网络 (TimeAwareGCN)
- **输入**: 节点特征(地理特征+时间特征) + 图结构
- **架构**: 多层GCN + 批归一化 + Dropout
- **输出**: 节点嵌入表示

#### 1.2 站点流量预测模型 (StationFlowPredictor)
- **功能**: 预测每个地铁站的进站和出站流量
- **架构**: TimeAwareGCN主干 + 双预测头(进站/出站)
- **特征**: 地理特征(51维) + 时间特征(3维)

#### 1.3 OD流量预测模型 (ODFlowPredictor)
- **功能**: 预测站点间的OD流量
- **架构**: TimeAwareGCN主干 + OD特征处理 + 流量预测头
- **特征**: 站点嵌入 + OD特征(距离、换乘、时间、等待时间)

#### 1.4 栅格流量分配模型 (GridFlowAllocator)
- **功能**: 将站点流量按比例分配到周围栅格
- **架构**: 注意力机制 + 分配比例预测
- **策略**: 基于地理距离的权重分配

### 2. 时间特征设计

#### 2.1 时间编码
- **小时编码**: sin/cos周期编码 (24小时周期)
- **时间段编码**: 早高峰(6-10h)、晚高峰(17-20h)、平峰(10-17h)、低峰(其他)

#### 2.2 时间注意力机制
- 区分不同时间段的人流模式
- 捕获时间相关的流量变化规律

### 3. 图构建策略

#### 3.1 节点定义
- **节点**: 地铁站点
- **节点特征**: 站点地理特征 + 时间特征
- **节点标签**: 进出站流量

#### 3.2 边定义
- **基础边**: 地铁线路连接关系
- **权重**: 相邻站间的实际流量
- **动态性**: 每小时的图结构和权重不同

#### 3.3 换乘处理
- 自动识别换乘站点
- 构建跨线路连接
- 避免重复添加换乘边

## 训练策略

### 1. 分层验证策略

#### 1.1 时间分割
- **训练集**: 0-17点数据
- **测试集**: 18-23点数据
- **目的**: 验证模型的时间泛化能力

#### 1.2 空间分割
- **训练集**: 80%站点
- **验证集**: 20%站点
- **目的**: 验证模型的空间泛化能力

#### 1.3 留一法验证
- 对验证集中每个站点进行留一法验证
- 评估模型对新站点的预测能力

### 2. 训练优化

#### 2.1 损失函数
- **站点流量损失**: MSE Loss (进站 + 出站)
- **OD流量损失**: MSE Loss
- **总损失**: 加权组合

#### 2.2 优化策略
- **优化器**: Adam
- **学习率**: 0.001 + ReduceLROnPlateau调度
- **正则化**: Weight Decay + Dropout
- **早停**: 20个epoch无改善则停止

#### 2.3 内存优化
- **Float16**: 减少内存使用
- **梯度累积**: 处理大批次数据
- **批次大小**: 自适应调整

## 数据处理

### 1. 输入数据

#### 1.1 进出站流量数据
- **文件**: `in_500_with_coords.shp`, `out_500_with_coords.shp`
- **内容**: 站点名称、小时、流量、经纬度
- **预处理**: 站点名称清理、异常值过滤

#### 1.2 OD流量数据
- **文件**: `updated_北京市_subway_od_2024_modified3.csv`
- **内容**: 起点站、终点站、小时、流量、距离、换乘等特征
- **预处理**: 站点名称清理、特征标准化

#### 1.3 站点连接数据
- **文件**: `station_connect_2023.csv`
- **内容**: 地铁线路连接关系
- **预处理**: 站点名称清理、去重

#### 1.4 相邻站流量数据
- **文件**: `subway_flow_24h_results.csv`
- **内容**: 相邻站间的实际流量
- **用途**: 图边权重赋值

#### 1.5 栅格特征数据
- **文件**: `leti_data.csv`
- **内容**: 栅格的地理和社会经济特征(51维)
- **预处理**: 特征标准化、聚合到站点

### 2. 特征工程

#### 2.1 站点特征构建
- 聚合周围3km内栅格特征
- 基于地理距离的加权平均
- 处理缺失坐标的站点

#### 2.2 时间特征构建
- 周期性编码(sin/cos)
- 时间段分类编码
- 全局时间特征

## 预测流程

### 1. 四层预测机制

#### 1.1 站点流量预测
- 预测各站点的进出站流量
- 输出: 每个站点每小时的流量值

#### 1.2 OD流量预测
- 预测站点间的流量分布
- 输出: 每个OD对每小时的流量值

#### 1.3 栅格流量分配
- 将站点流量按比例分配到栅格
- 基于地理距离和栅格特征
- 输出: 每个栅格每小时的流量值

#### 1.4 流量均衡
- 确保进出站流量平衡
- 通过反解优化分配比例

### 2. 预测输出

#### 2.1 站点预测结果
- `in_500_predictions_with_coords.shp`: 进站预测
- `out_500_predictions_with_coords.shp`: 出站预测

#### 2.2 OD预测结果
- `od_predictions.csv`: OD流量预测

#### 2.3 栅格预测结果
- `*_grid.csv`: 栅格级别流量分配

#### 2.4 评估结果
- `prediction_comparison.csv`: 预测值与真实值对比
- `training_results.json`: 训练过程和指标
- `hourly_metrics.csv`: 每小时评估指标

## 评估指标

### 1. 主要指标
- **MAE (Mean Absolute Error)**: 平均绝对误差
- **RMSE (Root Mean Square Error)**: 均方根误差
- **R² (Coefficient of Determination)**: 决定系数

### 2. 分层评估
- **整体指标**: 所有预测的综合表现
- **每小时指标**: 不同时间段的预测精度
- **高峰期分析**: 早晚高峰的特殊表现

## 使用方法

### 1. 环境要求
```bash
# 主要依赖
torch>=1.9.0
torch-geometric>=2.0.0
pandas>=1.3.0
geopandas>=0.9.0
scikit-learn>=1.0.0
numpy>=1.21.0
```

### 2. 运行方式

#### 2.1 完整训练和预测
```bash
cd gcn_metro_flow
python main.py
```

#### 2.2 快速测试模式
```bash
python main.py --test
```

#### 2.3 指定GPU设备
```bash
python main.py --gpu 0
```

### 3. 配置调整
- 修改 `config.py` 中的参数
- 调整模型架构、训练参数等
- 根据硬件配置优化内存使用

## 技术特点

### 1. 创新点
- **纯GCN架构**: 专注于图结构学习
- **时间特征融合**: 周期性和分段式时间编码
- **分层验证**: 时间+空间+留一法的综合验证
- **多层预测**: 站点→OD→栅格的层次化预测

### 2. 优化策略
- **内存优化**: Float16、梯度累积、批次优化
- **训练优化**: 早停、学习率调度、正则化
- **数据优化**: 异常值处理、特征标准化、缺失值处理

### 3. 鲁棒性设计
- **异常处理**: 完善的错误处理机制
- **数据验证**: 输入数据的完整性检查
- **模型保存**: 自动保存最佳模型

## 注意事项

### 1. 硬件要求
- **显存**: 建议8GB以上
- **内存**: 建议16GB以上
- **CUDA**: 支持CUDA加速

### 2. 数据要求
- 确保所有输入文件存在且格式正确
- 站点名称需要一致性处理
- 坐标信息完整性检查

### 3. 性能调优
- 根据硬件配置调整批次大小
- 使用Float16减少内存占用
- 合理设置训练轮数避免过拟合

## 扩展方向

### 1. 模型改进
- 引入图注意力机制(GAT)
- 添加时序建模(LSTM/GRU)
- 多任务学习优化

### 2. 特征增强
- 天气数据融合
- 事件数据集成
- 动态POI特征

### 3. 应用扩展
- 实时预测系统
- 可视化界面
- 决策支持系统
