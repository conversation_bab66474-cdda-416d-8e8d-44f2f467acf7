import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import pandas as pd
from sklearn.metrics import mean_absolute_error, mean_squared_error
import matplotlib.pyplot as plt
from tqdm import tqdm
import os

from data_loader import MetroDataLoader
from models import MetroFlowPredictor
from utils import EarlyStopping, save_predictions

class MetroFlowTrainer:
    def __init__(self, model, device='cuda', learning_rate=0.001, weight_decay=1e-4):
        self.model = model.to(device)
        self.device = device
        self.optimizer = optim.Adam(model.parameters(), lr=learning_rate, weight_decay=weight_decay)
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='min', patience=10, factor=0.5
        )
        self.criterion = nn.MSELoss()
        self.mae_criterion = nn.L1Loss()
        
        # 记录训练历史
        self.train_losses = []
        self.train_maes = []
        self.val_losses = []
        self.val_maes = []
        
    def train_epoch(self, data_loader, epoch):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        total_mae = 0
        num_batches = 0
        
        pbar = tqdm(data_loader, desc=f'Epoch {epoch}')
        for batch_data in pbar:
            # 将数据移到GPU
            graph_data = batch_data['graph'].to(self.device)
            
            self.optimizer.zero_grad()
            
            # 前向传播
            if 'od_pairs' in batch_data:
                # OD流量预测
                od_pairs = batch_data['od_pairs'].to(self.device)
                od_features = batch_data['od_features'].to(self.device)
                targets = batch_data['targets'].to(self.device)
                
                predictions = self.model.predict_od_flows(graph_data, od_pairs, od_features)
                loss = self.criterion(predictions.squeeze(), targets)
                
            else:
                # 站点流量预测
                targets_in = batch_data['targets_in'].to(self.device)
                targets_out = batch_data['targets_out'].to(self.device)
                
                pred_in, pred_out = self.model.predict_station_flows(graph_data)
                
                loss_in = self.criterion(pred_in.squeeze(), targets_in)
                loss_out = self.criterion(pred_out.squeeze(), targets_out)
                loss = loss_in + loss_out
                
                predictions = torch.cat([pred_in.squeeze(), pred_out.squeeze()])
                targets = torch.cat([targets_in, targets_out])
            
            # 反向传播
            loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            self.optimizer.step()
            
            # 计算指标
            with torch.no_grad():
                mae = self.mae_criterion(predictions, targets)
            
            total_loss += loss.item()
            total_mae += mae.item()
            num_batches += 1
            
            # 更新进度条
            pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'MAE': f'{mae.item():.4f}'
            })
        
        avg_loss = total_loss / num_batches
        avg_mae = total_mae / num_batches
        
        return avg_loss, avg_mae
    
    def validate(self, data_loader):
        """验证模型"""
        self.model.eval()
        total_loss = 0
        total_mae = 0
        num_batches = 0
        
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for batch_data in data_loader:
                # 将数据移到GPU
                graph_data = batch_data['graph'].to(self.device)
                
                # 前向传播
                if 'od_pairs' in batch_data:
                    # OD流量预测
                    od_pairs = batch_data['od_pairs'].to(self.device)
                    od_features = batch_data['od_features'].to(self.device)
                    targets = batch_data['targets'].to(self.device)
                    
                    predictions = self.model.predict_od_flows(graph_data, od_pairs, od_features)
                    loss = self.criterion(predictions.squeeze(), targets)
                    
                    all_predictions.extend(predictions.squeeze().cpu().numpy())
                    all_targets.extend(targets.cpu().numpy())
                    
                else:
                    # 站点流量预测
                    targets_in = batch_data['targets_in'].to(self.device)
                    targets_out = batch_data['targets_out'].to(self.device)
                    
                    pred_in, pred_out = self.model.predict_station_flows(graph_data)
                    
                    loss_in = self.criterion(pred_in.squeeze(), targets_in)
                    loss_out = self.criterion(pred_out.squeeze(), targets_out)
                    loss = loss_in + loss_out
                    
                    predictions = torch.cat([pred_in.squeeze(), pred_out.squeeze()])
                    targets = torch.cat([targets_in, targets_out])
                    
                    all_predictions.extend(predictions.cpu().numpy())
                    all_targets.extend(targets.cpu().numpy())
                
                # 计算指标
                mae = self.mae_criterion(predictions, targets)
                
                total_loss += loss.item()
                total_mae += mae.item()
                num_batches += 1
        
        avg_loss = total_loss / num_batches
        avg_mae = total_mae / num_batches
        
        return avg_loss, avg_mae, all_predictions, all_targets
    
    def train(self, train_loader, val_loader, num_epochs=100, patience=20):
        """完整的训练过程"""
        early_stopping = EarlyStopping(patience=patience, verbose=True)
        best_model_path = 'best_model.pth'
        
        print(f"Training on device: {self.device}")
        print(f"Model parameters: {sum(p.numel() for p in self.model.parameters()):,}")
        
        for epoch in range(num_epochs):
            # 训练
            train_loss, train_mae = self.train_epoch(train_loader, epoch)
            
            # 验证
            val_loss, val_mae, val_preds, val_targets = self.validate(val_loader)
            
            # 记录历史
            self.train_losses.append(train_loss)
            self.train_maes.append(train_mae)
            self.val_losses.append(val_loss)
            self.val_maes.append(val_mae)
            
            # 学习率调度
            self.scheduler.step(val_loss)
            
            # 打印结果
            print(f'Epoch {epoch+1}/{num_epochs}:')
            print(f'  Train Loss: {train_loss:.4f}, Train MAE: {train_mae:.4f}')
            print(f'  Val Loss: {val_loss:.4f}, Val MAE: {val_mae:.4f}')
            print(f'  Learning Rate: {self.optimizer.param_groups[0]["lr"]:.6f}')
            
            # 早停检查
            early_stopping(val_loss, self.model)
            if early_stopping.early_stop:
                print("Early stopping triggered")
                break
        
        # 加载最佳模型
        self.model.load_state_dict(torch.load(best_model_path))
        print("Training completed. Best model loaded.")
        
        return {
            'train_losses': self.train_losses,
            'train_maes': self.train_maes,
            'val_losses': self.val_losses,
            'val_maes': self.val_maes
        }
    
    def plot_training_history(self):
        """绘制训练历史"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))
        
        # Loss曲线
        ax1.plot(self.train_losses, label='Train Loss')
        ax1.plot(self.val_losses, label='Val Loss')
        ax1.set_title('Training and Validation Loss')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.legend()
        ax1.grid(True)
        
        # MAE曲线
        ax2.plot(self.train_maes, label='Train MAE')
        ax2.plot(self.val_maes, label='Val MAE')
        ax2.set_title('Training and Validation MAE')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('MAE')
        ax2.legend()
        ax2.grid(True)
        
        plt.tight_layout()
        plt.savefig('training_history.png', dpi=300, bbox_inches='tight')
        plt.show()

def create_data_loaders(data_dict, batch_size=32):
    """创建数据加载器"""
    # 这里需要实现具体的数据加载逻辑
    # 由于数据结构复杂，这里提供一个简化版本
    
    # 实际实现需要根据具体的数据格式来调整
    train_loader = []  # 需要实现
    val_loader = []    # 需要实现
    
    return train_loader, val_loader

def main():
    """主训练函数"""
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # 加载数据
    print("Loading data...")
    data_loader = MetroDataLoader()
    data_dict = data_loader.load_all_data()
    
    # 获取特征维度
    station_feature_dim = data_dict['station_features'].shape[1]
    grid_feature_dim = data_dict['grid_features'].shape[1] - 1  # 减去站名列
    
    print(f"Station feature dim: {station_feature_dim}")
    print(f"Grid feature dim: {grid_feature_dim}")
    
    # 创建模型
    model = MetroFlowPredictor(
        station_feature_dim=station_feature_dim,
        grid_feature_dim=grid_feature_dim,
        hidden_dim=128
    )
    
    # 创建训练器
    trainer = MetroFlowTrainer(model, device=device, learning_rate=0.001)
    
    # 创建数据加载器（需要实现具体逻辑）
    train_loader, val_loader = create_data_loaders(data_dict, batch_size=32)
    
    # 训练模型
    print("Starting training...")
    history = trainer.train(train_loader, val_loader, num_epochs=100)
    
    # 绘制训练历史
    trainer.plot_training_history()
    
    print("Training completed!")

if __name__ == '__main__':
    main()
