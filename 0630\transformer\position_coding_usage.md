# Position Coding 模块使用指南

## 概述

Position Coding模块是地铁流量预测系统V4的新增功能，专门用于预测新增站点对原有站点网络的分流作用。该模块通过深度学习和空间分析技术，能够准确预测新站点的建设对现有地铁网络流量分布的影响。

## 核心功能

### 1. 位置编码器 (Position Encoder)
- 将2D地理坐标映射到高维特征空间
- 捕获站点的空间语义信息
- 支持复杂的空间关系建模

### 2. 空间竞争建模 (Spatial Competition Modeling)
- 建模站点间的竞争关系
- 考虑距离、方向、可达性等因素
- 预测站点间的相互影响强度

### 3. 分流影响预测器 (Diversion Impact Predictor)
- 预测新站点对现有站点的分流比例
- 计算影响强度和影响范围
- 生成详细的影响分析报告

### 4. 影响范围分析 (Impact Range Analysis)
- 分析新站点的影响半径
- 识别高、中、低影响站点
- 计算网络级别的总体影响

## 使用方法

### 基本使用

```python
# 1. 初始化系统
system = V4SpatialPredictionSystem()

# 2. 加载数据并训练模型
system.load_and_process_data()
system.train_v4_models()  # 包含Position Coding模块训练

# 3. 预测新站点影响
new_station_coord = [116.4, 39.9]  # [经度, 纬度]
new_station_name = "新建站点"
hour = 8  # 预测时间（早高峰）

impact_result = system.predict_new_station_impact(
    new_station_coord, 
    new_station_name, 
    hour
)
```

### 命令行使用

```bash
# 运行完整系统（包含Position Coding训练和预测）
python main.py

# 测试基本功能
python main.py test

# 专门测试Position Coding功能
python main.py test_position
```

## 输出结果

### 1. 网络影响分析
```python
{
    'total_network_diversion': 0.1234,      # 总体网络分流
    'high_impact_stations': ['站点A', '站点B'],  # 高影响站点列表
    'medium_impact_stations': ['站点C'],     # 中等影响站点列表
    'num_affected_stations': 15,            # 受影响站点总数
    'average_impact_radius': 0.05,          # 平均影响半径
    'maximum_impact_radius': 0.12,          # 最大影响半径
    'station_impacts': {                    # 详细站点影响
        '站点A': {
            'diversion_ratio': 0.15,        # 分流比例
            'impact_intensity': 0.8,        # 影响强度
            'distance': 0.03,               # 距离
            'impact_decay': 0.9             # 影响衰减
        }
    }
}
```

### 2. 生成的文件
- `v4_diversion_predictions.json`: 详细的分流预测结果
- `v4_diversion_impact_report.csv`: 分流影响报告表格
- `v4_performance_analysis.json`: 模型性能分析

## 技术特点

### 1. 深度学习架构
- 使用PyTorch构建的神经网络
- 多层感知机结合批归一化和Dropout
- 自适应学习率调度

### 2. 空间特征工程
- 欧氏距离和曼哈顿距离计算
- 高斯衰减影响函数
- 方向性和可达性特征

### 3. 时间感知预测
- 支持不同时段的预测
- 考虑早晚高峰的差异
- 周期性时间特征编码

### 4. 影响等级分类
- High: 分流比例 > 0.1 且影响强度 > 0.5
- Medium: 分流比例 > 0.05 且影响强度 > 0.3
- Low: 分流比例 > 0.01
- Minimal: 分流比例 ≤ 0.01

## 应用场景

### 1. 城市规划
- 新地铁站选址优化
- 评估新站点对现有网络的影响
- 预测客流重新分布

### 2. 交通管理
- 预测新站点开通后的客流变化
- 制定相应的运营调整策略
- 优化列车班次安排

### 3. 商业分析
- 评估新站点对周边商业的影响
- 预测客流变化对商业价值的影响
- 支持投资决策

## 注意事项

1. **数据质量**: 确保输入的坐标数据准确
2. **训练时间**: Position Coding模块训练需要一定时间
3. **内存使用**: 大规模预测时注意内存管理
4. **结果解释**: 预测结果需要结合实际情况分析

## 扩展功能

未来可以考虑的扩展：
- 支持多个新站点同时分析
- 考虑不同交通方式的竞争
- 加入经济因素的影响
- 支持实时数据更新

## 技术支持

如有问题或建议，请参考代码注释或联系开发团队。
