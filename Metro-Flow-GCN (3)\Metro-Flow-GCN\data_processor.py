import pandas as pd
import numpy as np
import geopandas as gpd
from shapely.geometry import Point
import torch
from torch_geometric.data import Data
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
import networkx as nx
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')


class MetroDataProcessor:
    """地铁数据处理器"""
    
    def __init__(self, data_path, distance_threshold=1000, use_float16=False):
        self.data_path = data_path
        self.distance_threshold = distance_threshold  # 栅格-站点连接阈值(米)
        self.use_float16 = use_float16
        
        # 数据容器
        self.in_data = None
        self.out_data = None
        self.od_data = None
        self.station_features = None
        self.grid_data = None
        
        # 预处理后的数据
        self.stations = None
        self.station_coords = None
        self.grid_coords = None
        self.station_graph = None
        self.grid_station_graph = None
        
        # 标准化器
        self.feature_scaler = StandardScaler()
        self.flow_scaler = StandardScaler()
        
    def load_data(self):
        """加载所有数据"""
        print("正在加载数据...")
        
        # 加载进站数据
        self.in_data = gpd.read_file(f"{self.data_path}/in_500_with_coords.shp")
        print(f"进站数据形状: {self.in_data.shape}")
        
        # 加载出站数据
        self.out_data = gpd.read_file(f"{self.data_path}/out_500_with_coords.shp")
        print(f"出站数据形状: {self.out_data.shape}")
        
        # 加载OD数据
        self.od_data = pd.read_csv(f"{self.data_path}/updated北京市_subway_od_2024_modified3.csv")
        print(f"OD数据形状: {self.od_data.shape}")
        
        # 加载站点特征数据
        self.station_features = pd.read_csv(f"{self.data_path}/station_features_result.csv")
        print(f"站点特征数据形状: {self.station_features.shape}")
        
        # 如果有栅格数据，加载栅格数据
        try:
            self.grid_data = gpd.read_file(f"{self.data_path}/grid_data.shp")
            print(f"栅格数据形状: {self.grid_data.shape}")
        except:
            print("未找到栅格数据文件，将从其他数据中生成栅格信息")
            
    def preprocess_stations(self):
        """预处理站点数据"""
        print("正在预处理站点数据...")
        
        # 获取所有唯一站点
        in_stations = set(self.in_data['station'].unique())
        out_stations = set(self.out_data['station'].unique())
        od_o_stations = set(self.od_data['o_rawname'].unique())
        od_d_stations = set(self.od_data['d_rawname'].unique())
        feature_stations = set(self.station_features['站名'].unique())
        
        # 找到共同站点
        common_stations = in_stations & out_stations & od_o_stations & od_d_stations & feature_stations
        self.stations = sorted(list(common_stations))
        print(f"共同站点数量: {len(self.stations)}")
        
        # 创建站点索引映射
        self.station_to_idx = {station: idx for idx, station in enumerate(self.stations)}
        
        # 提取站点坐标
        station_coords = []
        for station in self.stations:
            # 从进站数据中获取坐标
            station_data = self.in_data[self.in_data['station'] == station].iloc[0]
            lon = station_data['longitude']
            lat = station_data['latitude']
            station_coords.append([lon, lat])
            
        self.station_coords = np.array(station_coords)
        print(f"站点坐标形状: {self.station_coords.shape}")
        
    def preprocess_features(self):
        """预处理站点特征"""
        print("正在预处理站点特征...")
        
        # 筛选共同站点的特征
        feature_data = self.station_features[self.station_features['站名'].isin(self.stations)]
        feature_data = feature_data.set_index('站名').loc[self.stations].reset_index()
        
        # 提取数值特征（除了站名列）
        feature_columns = [col for col in feature_data.columns if col != '站名']
        features = feature_data[feature_columns].values
        
        # 处理缺失值和异常值
        print(f"原始特征中NaN数量: {np.isnan(features).sum()}")
        print(f"原始特征中Inf数量: {np.isinf(features).sum()}")
        
        # 替换NaN和Inf值
        features = np.nan_to_num(features, nan=0.0, posinf=1e6, neginf=-1e6)
        
        # 检查是否还有异常值
        if np.isnan(features).any() or np.isinf(features).any():
            print("警告: 特征数据仍包含NaN或Inf值，进行进一步清理")
            features = np.where(np.isnan(features) | np.isinf(features), 0.0, features)
        
        # 标准化特征
        features_scaled = self.feature_scaler.fit_transform(features)
        
        # 检查标准化后的特征
        if np.isnan(features_scaled).any() or np.isinf(features_scaled).any():
            print("警告: 标准化后特征包含NaN或Inf值")
            features_scaled = np.nan_to_num(features_scaled, nan=0.0, posinf=1.0, neginf=-1.0)
        
        if self.use_float16:
            features_scaled = features_scaled.astype(np.float16)
        else:
            features_scaled = features_scaled.astype(np.float32)
            
        self.node_features = features_scaled
        print(f"节点特征形状: {self.node_features.shape}")
        print(f"预处理后特征NaN数量: {np.isnan(self.node_features).sum()}")
        print(f"预处理后特征Inf数量: {np.isinf(self.node_features).sum()}")
        print(f"特征值范围: [{np.min(self.node_features):.4f}, {np.max(self.node_features):.4f}]")
        
    def build_station_graph(self):
        """构建站点图"""
        print("正在构建站点图...")
        
        # 基于OD数据构建图
        edges = []
        edge_weights = []
        
        # 从OD数据中提取边
        od_grouped = self.od_data.groupby(['o_rawname', 'd_rawname'])['trip'].sum().reset_index()
        
        for _, row in od_grouped.iterrows():
            if row['o_rawname'] in self.station_to_idx and row['d_rawname'] in self.station_to_idx:
                o_idx = self.station_to_idx[row['o_rawname']]
                d_idx = self.station_to_idx[row['d_rawname']]
                if o_idx != d_idx:  # 避免自环
                    edges.append([o_idx, d_idx])
                    edge_weights.append(row['trip'])
        
        if len(edges) == 0:
            # 如果没有OD连接，基于距离构建图
            print("基于距离构建站点图...")
            coords = torch.tensor(self.station_coords, dtype=torch.float32)
            for i in range(len(self.stations)):
                for j in range(i+1, len(self.stations)):
                    dist = torch.norm(coords[i] - coords[j]).item()
                    if dist < 0.05:  # 约5km阈值
                        edges.append([i, j])
                        edges.append([j, i])  # 无向图
                        edge_weights.extend([1.0, 1.0])
        
        self.station_edges = np.array(edges).T
        self.station_edge_weights = np.array(edge_weights)
        print(f"站点图边数: {len(edges)}")
        
    def preprocess_grid_data(self):
        """预处理栅格数据"""
        if self.grid_data is not None:
            print("正在预处理栅格数据...")
            
            # 提取栅格中心坐标
            self.grid_coords = []
            self.grid_features = []
            
            for idx, row in self.grid_data.iterrows():
                # 获取栅格中心坐标
                centroid = row.geometry.centroid
                self.grid_coords.append([centroid.x, centroid.y])
                
                # 提取栅格特征（流量等）
                features = []
                for col in self.grid_data.columns:
                    if col not in ['geometry'] and pd.api.types.is_numeric_dtype(self.grid_data[col]):
                        features.append(row[col])
                self.grid_features.append(features)
            
            self.grid_coords = np.array(self.grid_coords)
            self.grid_features = np.array(self.grid_features)
            
            # 标准化栅格特征
            if len(self.grid_features) > 0:
                self.grid_features = self.feature_scaler.fit_transform(self.grid_features)
                if self.use_float16:
                    self.grid_features = self.grid_features.astype(np.float16)
                else:
                    self.grid_features = self.grid_features.astype(np.float32)
            
            print(f"栅格数量: {len(self.grid_coords)}")
            
    def build_grid_station_graph(self):
        """构建栅格-站点图"""
        if self.grid_coords is not None:
            print("正在构建栅格-站点图...")
            
            grid_coords_tensor = torch.tensor(self.grid_coords, dtype=torch.float32)
            station_coords_tensor = torch.tensor(self.station_coords, dtype=torch.float32)
            
            edges = []
            distances = []
            
            for g_idx, grid_coord in enumerate(grid_coords_tensor):
                for s_idx, station_coord in enumerate(station_coords_tensor):
                    # 计算距离（简化的欧几里得距离）
                    dist = torch.norm(grid_coord - station_coord).item() * 111000  # 转换为米
                    
                    if dist < self.distance_threshold:
                        # 栅格到站点
                        edges.append([g_idx, s_idx + len(self.grid_coords)])
                        # 站点到栅格
                        edges.append([s_idx + len(self.grid_coords), g_idx])
                        distances.extend([dist, dist])
            
            if len(edges) > 0:
                self.grid_station_edges = np.array(edges).T
                self.grid_station_distances = np.array(distances)
                print(f"栅格-站点连接数: {len(edges)}")
            else:
                print("警告: 未找到有效的栅格-站点连接")
    
    def prepare_temporal_data(self):
        """准备时间序列数据"""
        print("正在准备时间序列数据...")
        
        # 准备进站流量数据
        in_flow_data = []
        for hour in range(24):
            hour_data = []
            for station in self.stations:
                station_data = self.in_data[
                    (self.in_data['station'] == station) & 
                    (self.in_data['hour'] == hour)
                ]
                flow = station_data['count'].sum() if len(station_data) > 0 else 0.0
                # 确保流量值是有效的数值
                if pd.isna(flow) or np.isnan(flow) or np.isinf(flow):
                    flow = 0.0
                hour_data.append(flow)
            in_flow_data.append(hour_data)
        
        # 准备出站流量数据
        out_flow_data = []
        for hour in range(24):
            hour_data = []
            for station in self.stations:
                station_data = self.out_data[
                    (self.out_data['station'] == station) & 
                    (self.out_data['hour'] == hour)
                ]
                flow = station_data['count'].sum() if len(station_data) > 0 else 0.0
                # 确保流量值是有效的数值
                if pd.isna(flow) or np.isnan(flow) or np.isinf(flow):
                    flow = 0.0
                hour_data.append(flow)
            out_flow_data.append(hour_data)
        
        # 准备OD流量数据
        od_flow_data = []
        for hour in range(24):
            hour_matrix = np.zeros((len(self.stations), len(self.stations)))
            hour_od = self.od_data[self.od_data['hour'] == hour]
            
            for _, row in hour_od.iterrows():
                if row['o_rawname'] in self.station_to_idx and row['d_rawname'] in self.station_to_idx:
                    o_idx = self.station_to_idx[row['o_rawname']]
                    d_idx = self.station_to_idx[row['d_rawname']]
                    trip_value = row['trip']
                    # 确保trip值是有效的数值
                    if pd.isna(trip_value) or np.isnan(trip_value) or np.isinf(trip_value):
                        trip_value = 0.0
                    hour_matrix[o_idx, d_idx] += trip_value
            
            od_flow_data.append(hour_matrix.flatten())
        
        self.in_flows = np.array(in_flow_data, dtype=np.float32)
        self.out_flows = np.array(out_flow_data, dtype=np.float32)
        self.od_flows = np.array(od_flow_data, dtype=np.float32)
        
        # 检查流量数据中的异常值
        print(f"进站流量NaN数量: {np.isnan(self.in_flows).sum()}")
        print(f"出站流量NaN数量: {np.isnan(self.out_flows).sum()}")
        print(f"OD流量NaN数量: {np.isnan(self.od_flows).sum()}")
        print(f"进站流量Inf数量: {np.isinf(self.in_flows).sum()}")
        print(f"出站流量Inf数量: {np.isinf(self.out_flows).sum()}")
        print(f"OD流量Inf数量: {np.isinf(self.od_flows).sum()}")
        
        # 清理异常值
        self.in_flows = np.nan_to_num(self.in_flows, nan=0.0, posinf=0.0, neginf=0.0)
        self.out_flows = np.nan_to_num(self.out_flows, nan=0.0, posinf=0.0, neginf=0.0)
        self.od_flows = np.nan_to_num(self.od_flows, nan=0.0, posinf=0.0, neginf=0.0)
        
        if self.use_float16:
            self.in_flows = self.in_flows.astype(np.float16)
            self.out_flows = self.out_flows.astype(np.float16)
            self.od_flows = self.od_flows.astype(np.float16)
        else:
            self.in_flows = self.in_flows.astype(np.float32)
            self.out_flows = self.out_flows.astype(np.float32)
            self.od_flows = self.od_flows.astype(np.float32)
        
        print(f"进站流量形状: {self.in_flows.shape}")
        print(f"出站流量形状: {self.out_flows.shape}")
        print(f"OD流量形状: {self.od_flows.shape}")
        print(f"进站流量范围: [{np.min(self.in_flows):.2f}, {np.max(self.in_flows):.2f}]")
        print(f"出站流量范围: [{np.min(self.out_flows):.2f}, {np.max(self.out_flows):.2f}]")
    
    def create_time_features(self, hours):
        """创建时间特征"""
        time_features = []
        for hour in hours:
            # 假设数据来自某个特定日期，这里简化处理
            day_of_week = 1  # 周一
            month = 6  # 6月
            time_features.append([hour, day_of_week, month])
        
        return np.array(time_features)
    
    def split_data(self, test_size=0.2, val_size=0.1, random_state=42):
        """分割数据集 - 实现分层验证策略"""
        print("正在分割数据集...")
        
        # 时间分割：前70%训练，中间10%验证，后20%测试
        total_hours = 24
        train_end = int(total_hours * 0.7)
        val_end = int(total_hours * 0.8)
        
        train_hours = list(range(0, train_end))
        val_hours = list(range(train_end, val_end))
        test_hours = list(range(val_end, total_hours))
        
        # 空间分割：随机选择一部分站点作为测试
        train_stations, test_stations = train_test_split(
            range(len(self.stations)), 
            test_size=test_size, 
            random_state=random_state
        )
        
        # 创建数据分割索引
        self.train_data = {
            'hours': train_hours,
            'stations': train_stations,
            'time_features': self.create_time_features(train_hours)
        }
        
        self.val_data = {
            'hours': val_hours,
            'stations': train_stations,  # 验证使用训练站点
            'time_features': self.create_time_features(val_hours)
        }
        
        self.test_data = {
            'hours': test_hours,
            'stations': test_stations,
            'time_features': self.create_time_features(test_hours)
        }
        
        print(f"训练集: {len(train_hours)}小时 x {len(train_stations)}站点")
        print(f"验证集: {len(val_hours)}小时 x {len(train_stations)}站点")
        print(f"测试集: {len(test_hours)}小时 x {len(test_stations)}站点")
    
    def get_torch_data(self, split='train'):
        """获取PyTorch格式的数据"""
        if split == 'train':
            data_split = self.train_data
        elif split == 'val':
            data_split = self.val_data
        else:
            data_split = self.test_data
        
        # 添加索引检查
        max_station_idx = max(data_split['stations']) if data_split['stations'] else -1
        if max_station_idx >= len(self.node_features):
            print(f"❌ 索引错误: 最大站点索引 {max_station_idx} >= 特征数组长度 {len(self.node_features)}")
            # 修复索引范围
            valid_stations = [idx for idx in data_split['stations'] if idx < len(self.node_features)]
            data_split['stations'] = valid_stations
            print(f"✅ 修复后站点数: {len(valid_stations)}")
        
        # 获取对应的特征和流量数据
        try:
            node_features = torch.tensor(self.node_features[data_split['stations']], 
                                       dtype=torch.float16 if self.use_float16 else torch.float32)
            
            # 检查转换后的特征
            if torch.isnan(node_features).any() or torch.isinf(node_features).any():
                print(f"警告: torch转换后包含NaN/Inf，进行清理")
                node_features = torch.where(torch.isnan(node_features) | torch.isinf(node_features), 
                                           torch.zeros_like(node_features), node_features)
        except Exception as e:
            print(f"❌ 节点特征转换失败: {e}")
            # 创建零特征作为备用
            num_stations = len(data_split['stations'])
            num_features = self.node_features.shape[1] if hasattr(self, 'node_features') else 52
            node_features = torch.zeros(num_stations, num_features, dtype=torch.float32)
            print(f"✅ 使用零特征替代: {node_features.shape}")
        
        # 获取对应时间的流量数据
        in_flows = torch.tensor(self.in_flows[data_split['hours']][:, data_split['stations']], 
                               dtype=torch.float16 if self.use_float16 else torch.float32)
        out_flows = torch.tensor(self.out_flows[data_split['hours']][:, data_split['stations']], 
                                dtype=torch.float16 if self.use_float16 else torch.float32)
        
        # 构建边索引（需要重新映射到子图）
        station_mapping = {old_idx: new_idx for new_idx, old_idx in enumerate(data_split['stations'])}
        
        edges = []
        for edge in self.station_edges.T:
            if edge[0] in station_mapping and edge[1] in station_mapping:
                edges.append([station_mapping[edge[0]], station_mapping[edge[1]]])
        
        edge_index = torch.tensor(np.array(edges).T, dtype=torch.long)
        
        time_features = torch.tensor(data_split['time_features'], dtype=torch.long)
        
        return {
            'node_features': node_features,
            'edge_index': edge_index,
            'in_flows': in_flows,
            'out_flows': out_flows,
            'time_features': time_features
        }
    
    def process_all(self):
        """处理所有数据"""
        self.load_data()
        self.preprocess_stations()
        self.preprocess_features()
        self.build_station_graph()
        self.preprocess_grid_data()
        self.build_grid_station_graph()
        self.prepare_temporal_data()
        self.split_data()
        
        print("数据处理完成！")
        
        return {
            'num_stations': len(self.stations),
            'num_features': self.node_features.shape[1],
            'num_hours': 24,
            'stations': self.stations
        } 