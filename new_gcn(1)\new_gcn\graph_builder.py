import numpy as np
import torch
import networkx as nx
from sklearn.neighbors import NearestNeighbors
from sklearn.metrics.pairwise import haversine_distances
from config import Config

class GraphBuilder:
    def __init__(self):
        self.config = Config()
        
    def calculate_haversine_distance(self, coords1, coords2):
        """计算两点间的haversine距离（考虑地球曲率）"""
        # 转换为弧度
        coords1_rad = np.radians(coords1)
        coords2_rad = np.radians(coords2)
        
        # 计算haversine距离矩阵
        distances = haversine_distances(coords1_rad, coords2_rad) * 6371000  # 地球半径（米）
        return distances
    
    def create_distance_graph(self, station_coords, distance_threshold=None):
        """基于距离阈值创建图"""
        if distance_threshold is None:
            distance_threshold = self.config.DISTANCE_THRESHOLD
        
        print(f"正在创建距离图 (阈值: {distance_threshold}m)...")
        
        stations = list(station_coords.keys())
        coords = np.array([station_coords[station] for station in stations])
        
        # 计算距离矩阵
        distances = self.calculate_haversine_distance(coords, coords)
        
        # 创建邻接矩阵
        adj_matrix = (distances < distance_threshold) & (distances > 0)
        
        # 转换为edge_index格式
        edge_indices = np.where(adj_matrix)
        edge_index = torch.tensor([edge_indices[0], edge_indices[1]], dtype=torch.long)
        
        # 计算边权重（距离的倒数）
        edge_weights = 1.0 / (distances[edge_indices] + 1e-6)
        edge_attr = torch.tensor(edge_weights, dtype=torch.float32)
        
        if self.config.USE_FLOAT16:
            edge_attr = edge_attr.half()
        
        print(f"创建了 {edge_index.shape[1]} 条边")
        return edge_index, edge_attr, stations
    
    def create_knn_graph(self, station_coords, k=None):
        """基于K近邻创建图"""
        if k is None:
            k = self.config.K_NEAREST
        
        print(f"正在创建KNN图 (K={k})...")
        
        stations = list(station_coords.keys())
        coords = np.array([station_coords[station] for station in stations])
        
        # 使用KNN找到最近的k个邻居
        nbrs = NearestNeighbors(n_neighbors=k+1, metric='haversine').fit(np.radians(coords))
        distances, indices = nbrs.kneighbors(np.radians(coords))
        
        # 创建边
        edge_list = []
        edge_weights = []
        
        for i in range(len(stations)):
            for j in range(1, k+1):  # 跳过自己（索引0）
                neighbor_idx = indices[i, j]
                distance = distances[i, j] * 6371000  # 转换为米
                
                edge_list.append([i, neighbor_idx])
                edge_weights.append(1.0 / (distance + 1e-6))
        
        edge_index = torch.tensor(edge_list, dtype=torch.long).t()
        edge_attr = torch.tensor(edge_weights, dtype=torch.float32)
        
        if self.config.USE_FLOAT16:
            edge_attr = edge_attr.half()
        
        print(f"创建了 {edge_index.shape[1]} 条边")
        return edge_index, edge_attr, stations
    
    def create_od_graph(self, od_data, station_coords):
        """基于OD数据创建图"""
        print("正在基于OD数据创建图...")
        
        stations = list(station_coords.keys())
        station_to_idx = {station: i for i, station in enumerate(stations)}
        
        # 统计OD流量
        od_flow = {}
        for _, row in od_data.iterrows():
            origin = row['o_rawname']
            dest = row['d_rawname']
            flow = row['trip']
            
            if origin in station_to_idx and dest in station_to_idx:
                key = (station_to_idx[origin], station_to_idx[dest])
                od_flow[key] = od_flow.get(key, 0) + flow
        
        # 创建边和权重
        edge_list = []
        edge_weights = []
        
        for (o_idx, d_idx), flow in od_flow.items():
            if flow > 0:  # 只考虑有流量的OD对
                edge_list.append([o_idx, d_idx])
                edge_weights.append(flow)
        
        edge_index = torch.tensor(edge_list, dtype=torch.long).t()
        edge_attr = torch.tensor(edge_weights, dtype=torch.float32)
        
        # 标准化权重
        edge_attr = edge_attr / edge_attr.max()
        
        if self.config.USE_FLOAT16:
            edge_attr = edge_attr.half()
        
        print(f"基于OD数据创建了 {edge_index.shape[1]} 条边")
        return edge_index, edge_attr, stations
    
    def create_hybrid_graph(self, station_coords, od_data=None):
        """创建混合图（结合距离和OD流量）"""
        print("正在创建混合图...")
        
        # 创建距离图
        dist_edge_index, dist_edge_attr, stations = self.create_distance_graph(station_coords)
        
        # 创建KNN图
        knn_edge_index, knn_edge_attr, _ = self.create_knn_graph(station_coords)
        
        # 合并边
        combined_edge_index = torch.cat([dist_edge_index, knn_edge_index], dim=1)
        combined_edge_attr = torch.cat([dist_edge_attr, knn_edge_attr])
        
        # 如果有OD数据，添加OD边
        if od_data is not None:
            od_edge_index, od_edge_attr, _ = self.create_od_graph(od_data, station_coords)
            combined_edge_index = torch.cat([combined_edge_index, od_edge_index], dim=1)
            combined_edge_attr = torch.cat([combined_edge_attr, od_edge_attr])
        
        # 去除重复边
        edge_dict = {}
        for i in range(combined_edge_index.shape[1]):
            edge = (combined_edge_index[0, i].item(), combined_edge_index[1, i].item())
            if edge not in edge_dict:
                edge_dict[edge] = combined_edge_attr[i].item()
            else:
                # 取最大权重
                edge_dict[edge] = max(edge_dict[edge], combined_edge_attr[i].item())
        
        # 重建边索引和属性
        edges = list(edge_dict.keys())
        edge_index = torch.tensor(edges, dtype=torch.long).t()
        edge_attr = torch.tensor(list(edge_dict.values()), dtype=torch.float32)
        
        if self.config.USE_FLOAT16:
            edge_attr = edge_attr.half()
        
        print(f"混合图共有 {edge_index.shape[1]} 条边")
        return edge_index, edge_attr, stations
    
    def create_grid_station_graph(self, grid_coords, station_coords, threshold=None):
        """创建栅格-站点图"""
        if threshold is None:
            threshold = self.config.SPATIAL_THRESHOLD
        
        print(f"正在创建栅格-站点图 (阈值: {threshold})...")
        
        # 创建节点映射
        all_nodes = list(grid_coords.keys()) + list(station_coords.keys())
        node_to_idx = {node: i for i, node in enumerate(all_nodes)}
        
        # 提取坐标
        grid_list = list(grid_coords.keys())
        station_list = list(station_coords.keys())
        
        grid_coords_array = np.array([grid_coords[grid] for grid in grid_list])
        station_coords_array = np.array([station_coords[station] for station in station_list])
        
        # 计算栅格-站点距离
        grid_station_distances = self.calculate_haversine_distance(
            grid_coords_array, station_coords_array)
        
        # 创建边
        edge_list = []
        edge_weights = []
        
        for i, grid in enumerate(grid_list):
            for j, station in enumerate(station_list):
                distance = grid_station_distances[i, j]
                if distance < threshold:
                    grid_idx = node_to_idx[grid]
                    station_idx = node_to_idx[station]
                    
                    edge_list.extend([[grid_idx, station_idx], [station_idx, grid_idx]])
                    weight = 1.0 / (distance + 1e-6)
                    edge_weights.extend([weight, weight])
        
        edge_index = torch.tensor(edge_list, dtype=torch.long).t()
        edge_attr = torch.tensor(edge_weights, dtype=torch.float32)
        
        if self.config.USE_FLOAT16:
            edge_attr = edge_attr.half()
        
        print(f"栅格-站点图共有 {edge_index.shape[1]} 条边")
        return edge_index, edge_attr, all_nodes, node_to_idx
    
    def add_new_line_to_graph(self, edge_index, edge_attr, stations, new_stations, new_connections):
        """向现有图中添加新线路"""
        print("正在添加新线路到图中...")
        
        # 添加新站点
        all_stations = stations + new_stations
        station_to_idx = {station: i for i, station in enumerate(all_stations)}
        
        # 转换现有边索引
        new_edge_list = []
        new_edge_weights = []
        
        # 添加现有边
        for i in range(edge_index.shape[1]):
            new_edge_list.append([edge_index[0, i].item(), edge_index[1, i].item()])
            new_edge_weights.append(edge_attr[i].item())
        
        # 添加新连接
        for connection in new_connections:
            station1, station2, weight = connection
            if station1 in station_to_idx and station2 in station_to_idx:
                idx1 = station_to_idx[station1]
                idx2 = station_to_idx[station2]
                new_edge_list.extend([[idx1, idx2], [idx2, idx1]])
                new_edge_weights.extend([weight, weight])
        
        new_edge_index = torch.tensor(new_edge_list, dtype=torch.long).t()
        new_edge_attr = torch.tensor(new_edge_weights, dtype=torch.float32)
        
        if self.config.USE_FLOAT16:
            new_edge_attr = new_edge_attr.half()
        
        print(f"新图共有 {len(all_stations)} 个站点，{new_edge_index.shape[1]} 条边")
        return new_edge_index, new_edge_attr, all_stations
    
    def build_graph(self, station_coords, od_data=None, graph_type='hybrid'):
        """构建图的主方法"""
        print(f"正在构建 {graph_type} 图...")
        
        if graph_type == 'distance':
            return self.create_distance_graph(station_coords)
        elif graph_type == 'knn':
            return self.create_knn_graph(station_coords)
        elif graph_type == 'od':
            return self.create_od_graph(od_data, station_coords)
        elif graph_type == 'hybrid':
            return self.create_hybrid_graph(station_coords, od_data)
        else:
            raise ValueError(f"Unknown graph type: {graph_type}")
    
    def analyze_graph_properties(self, edge_index, stations):
        """分析图的属性"""
        print("正在分析图属性...")
        
        num_nodes = len(stations)
        num_edges = edge_index.shape[1]
        
        # 转换为NetworkX图进行分析
        G = nx.Graph()
        G.add_nodes_from(range(num_nodes))
        
        edges = edge_index.t().numpy()
        G.add_edges_from(edges)
        
        # 计算图属性
        density = nx.density(G)
        try:
            avg_clustering = nx.average_clustering(G)
        except:
            avg_clustering = 0
        
        connected_components = nx.number_connected_components(G)
        
        print(f"图统计信息:")
        print(f"  节点数: {num_nodes}")
        print(f"  边数: {num_edges}")
        print(f"  图密度: {density:.4f}")
        print(f"  平均聚类系数: {avg_clustering:.4f}")
        print(f"  连通分量数: {connected_components}")
        
        return {
            'num_nodes': num_nodes,
            'num_edges': num_edges,
            'density': density,
            'avg_clustering': avg_clustering,
            'connected_components': connected_components
        } 