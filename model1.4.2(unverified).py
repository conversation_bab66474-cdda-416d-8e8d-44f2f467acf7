
# -*- coding: utf-8 -*-
"""
Created on Tue Apr 15 10:38:20 2025

@author: <PERSON><PERSON><PERSON><PERSON>
"""

import os
os.environ["DGLBACKEND"] = "pytorch"

import torch
import torch.nn as nn
import torch.optim as optim
import pandas as pd
import geopandas as gpd
import numpy as np
import dgl
from dgl.nn import GraphConv
from torch.nn import TransformerEncoder, TransformerEncoderLayer
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import mean_absolute_error, mean_squared_error
from tqdm import tqdm
from torch.cuda.amp import GradScaler, autocast
import pickle
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 设置随机种子和设备
torch.manual_seed(42)
np.random.seed(42)
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"Using device: {device}")

# 数据加载
def load_data(in_shp, out_shp, od_csv, features_csv, cache_dir="cache"):
    os.makedirs(cache_dir, exist_ok=True)
    cache_file = os.path.join(cache_dir, "preprocessed_data.pkl")
    
    if os.path.exists(cache_file):
        logger.info("Loading cached data...")
        with open(cache_file, 'rb') as f:
            data = pickle.load(f)
        return data['in_gdf'], data['out_gdf'], data['od_df'], data['features_df']
    
    logger.info("Loading data...")
    in_gdf = gpd.read_file(in_shp)
    out_gdf = gpd.read_file(out_shp)
    od_df = pd.read_csv(od_csv)
    features_df = pd.read_csv(features_csv)
    
    with open(cache_file, 'wb') as f:
        pickle.dump({
            'in_gdf': in_gdf,
            'out_gdf': out_gdf,
            'od_df': od_df,
            'features_df': features_df
        }, f)
    
    logger.info("Data loaded and cached successfully.")
    return in_gdf, out_gdf, od_df, features_df

# 数据预处理
def preprocess_data(in_gdf, out_gdf, od_df, features_df, cache_dir="cache"):
    cache_file = os.path.join(cache_dir, "preprocessed_data_full.pkl")
    
    if os.path.exists(cache_file):
        logger.info("Loading cached preprocessed data...")
        with open(cache_file, 'rb') as f:
            data = pickle.load(f)
        return (data['in_gdf'], data['out_gdf'], data['od_df'], data['features_df'],
                data['station_encoder'], data['feature_cols'])
    
    logger.info("Preprocessing data...")
    in_gdf = in_gdf[~in_gdf['hour'].isin([0, 1, 2, 3, 4])].copy()
    out_gdf = out_gdf[~out_gdf['hour'].isin([0, 1, 2, 3, 4])].copy()
    od_df = od_df[~od_df['hour'].isin([0, 1, 2, 3, 4])].copy()
    features_df = features_df.copy()
    
    in_gdf = in_gdf.dropna(subset=['station', 'hour', 'count'])
    out_gdf = out_gdf.dropna(subset=['station', 'hour', 'count'])
    od_df = od_df.dropna(subset=['o_rawname', 'd_rawname', 'hour', 'trip', 'surface_distance', 'translate', 'time', 'wait_time'])
    features_df = features_df.dropna(subset=['站名'])
    
    all_stations = pd.concat([
        in_gdf['station'], out_gdf['station'],
        od_df['o_rawname'], od_df['d_rawname'],
        features_df['站名']
    ]).dropna().unique()
    
    station_encoder = LabelEncoder()
    station_encoder.fit(all_stations)
    
    in_gdf.loc[:, 'station_id'] = station_encoder.transform(in_gdf['station'])
    out_gdf.loc[:, 'station_id'] = station_encoder.transform(out_gdf['station'])
    od_df.loc[:, 'o_station_id'] = station_encoder.transform(od_df['o_rawname'])
    od_df.loc[:, 'd_station_id'] = station_encoder.transform(od_df['d_rawname'])
    
    scaler = StandardScaler()
    od_features = ['surface_distance', 'translate', 'time', 'wait_time']
    for col in od_features:
        if od_df[col].std() == 0:
            od_df.loc[:, col] = 0
        else:
            od_df.loc[:, col] = scaler.fit_transform(od_df[[col]]).flatten()
    
    feature_cols = [col for col in features_df.columns if col != '站名']
    for col in feature_cols:
        if features_df[col].std() == 0:
            features_df.loc[:, col] = 0
        else:
            features_df.loc[:, col] = scaler.fit_transform(features_df[[col]]).flatten()
    
    features_df.loc[:, 'station_id'] = features_df['站名'].apply(
        lambda x: station_encoder.transform([x])[0] if x in station_encoder.classes_ else -1
    )
    
    def encode_hour(hour):
        hour = hour % 24
        sin = np.sin(2 * np.pi * hour / 24)
        cos = np.cos(2 * np.pi * hour / 24)
        return sin, cos
    
    in_gdf.loc[:, ['hour_sin', 'hour_cos']] = pd.DataFrame(
        in_gdf['hour'].apply(encode_hour).tolist(), index=in_gdf.index
    )
    out_gdf.loc[:, ['hour_sin', 'hour_cos']] = pd.DataFrame(
        out_gdf['hour'].apply(encode_hour).tolist(), index=out_gdf.index
    )
    od_df.loc[:, ['hour_sin', 'hour_cos']] = pd.DataFrame(
        od_df['hour'].apply(encode_hour).tolist(), index=od_df.index
    )
    
    with open(cache_file, 'wb') as f:
        pickle.dump({
            'in_gdf': in_gdf,
            'out_gdf': out_gdf,
            'od_df': od_df,
            'features_df': features_df,
            'station_encoder': station_encoder,
            'feature_cols': feature_cols
        }, f)
    
    logger.info("Data preprocessed and cached successfully.")
    return in_gdf, out_gdf, od_df, features_df, station_encoder, feature_cols

# 创建图
def create_graph(in_gdf, out_gdf, od_df, station_encoder):
    num_nodes = len(station_encoder.classes_)
    src, dst = [], []
    for _, row in od_df.iterrows():
        src.append(row['o_station_id'])
        dst.append(row['d_station_id'])
    g = dgl.graph((src, dst), num_nodes=num_nodes).to(device, non_blocking=True)
    g = dgl.add_self_loop(g)
    return g

# 数据集
class TrafficDataset(torch.utils.data.Dataset):
    def __init__(self, in_gdf, out_gdf, od_df, features_df, feature_cols):
        self.in_gdf = in_gdf
        self.out_gdf = out_gdf
        self.od_df = od_df
        self.features_df = features_df
        self.feature_cols = feature_cols
        
    def __len__(self):
        return len(self.in_gdf) + len(self.out_gdf) + len(self.od_df)
    
    def __getitem__(self, idx):
        if idx < len(self.in_gdf):
            row = self.in_gdf.iloc[idx]
            station_id = row['station_id']
            geo_features = self.features_df[self.features_df['station_id'] == station_id][self.feature_cols].values
            geo_features = geo_features[0] if len(geo_features) > 0 else np.zeros(len(self.feature_cols))
            return {
                'type': 'in',
                'station_id': torch.tensor(station_id, dtype=torch.long),
                'hour': torch.tensor([row['hour_sin'], row['hour_cos']], dtype=torch.float),
                'geo_features': torch.tensor(geo_features, dtype=torch.float),
                'count': torch.tensor(row['count'], dtype=torch.float),
                'index': idx,
                'geometry': row['geometry']
            }
        elif idx < len(self.in_gdf) + len(self.out_gdf):
            row = self.out_gdf.iloc[idx - len(self.in_gdf)]
            station_id = row['station_id']
            geo_features = self.features_df[self.features_df['station_id'] == station_id][self.feature_cols].values
            geo_features = geo_features[0] if len(geo_features) > 0 else np.zeros(len(self.feature_cols))
            return {
                'type': 'out',
                'station_id': torch.tensor(station_id, dtype=torch.long),
                'hour': torch.tensor([row['hour_sin'], row['hour_cos']], dtype=torch.float),
                'geo_features': torch.tensor(geo_features, dtype=torch.float),
                'count': torch.tensor(row['count'], dtype=torch.float),
                'index': idx - len(self.in_gdf),
                'geometry': row['geometry']
            }
        else:
            row = self.od_df.iloc[idx - len(self.in_gdf) - len(self.out_gdf)]
            o_station_id = row['o_station_id']
            d_station_id = row['d_station_id']
            o_geo = self.features_df[self.features_df['station_id'] == o_station_id][self.feature_cols].values
            d_geo = self.features_df[self.features_df['station_id'] == d_station_id][self.feature_cols].values
            o_geo = o_geo[0] if len(o_geo) > 0 else np.zeros(len(self.feature_cols))
            d_geo = d_geo[0] if len(d_geo) > 0 else np.zeros(len(self.feature_cols))
            return {
                'type': 'od',
                'o_station_id': torch.tensor(o_station_id, dtype=torch.long),
                'd_station_id': torch.tensor(d_station_id, dtype=torch.long),
                'hour': torch.tensor([row['hour_sin'], row['hour_cos']], dtype=torch.float),
                'o_geo_features': torch.tensor(o_geo, dtype=torch.float),
                'd_geo_features': torch.tensor(d_geo, dtype=torch.float),
                'od_features': torch.tensor([
                    row['surface_distance'], row['translate'], row['time'], row['wait_time']
                ], dtype=torch.float),
                'trip': torch.tensor(row['trip'], dtype=torch.float),
                'index': idx - len(self.in_gdf) - len(self.out_gdf)
            }

def collate_fn(batch):
    in_batch = [x for x in batch if x['type'] == 'in']
    out_batch = [x for x in batch if x['type'] == 'out']
    od_batch = [x for x in batch if x['type'] == 'od']
    return in_batch, out_batch, od_batch

# 模型定义
class TrafficModel(nn.Module):
    def __init__(self, num_stations, geo_feature_dim, station_dim=64, time_dim=2, hidden_dim=128):
        super().__init__()
        self.num_stations = num_stations
        self.station_dim = station_dim
        self.hidden_dim = hidden_dim
        self.time_dim = time_dim
        
        self.station_embed = nn.Embedding(num_stations, station_dim)
        self.geo_embed = nn.Linear(geo_feature_dim, hidden_dim)
        self.time_embed = nn.Linear(time_dim, time_dim)
        
        self.gnn = GraphConv(station_dim + hidden_dim + time_dim, hidden_dim)
        transformer_layer = TransformerEncoderLayer(
            d_model=hidden_dim, nhead=8, batch_first=True
        )
        self.transformer = TransformerEncoder(transformer_layer, num_layers=3)  # Reduced layers
        self.fc_grid = nn.Linear(hidden_dim, 1)
        
        self.dnn = nn.Sequential(
            nn.Linear(station_dim*2 + hidden_dim*2 + 4 + time_dim, 256),  # Reduced size
            nn.ReLU(),
            nn.Dropout(0.2),  # Reduced dropout
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, hidden_dim)
        )
        self.attention = nn.MultiheadAttention(embed_dim=hidden_dim, num_heads=8)
        self.fc_od = nn.Linear(hidden_dim, 1)
        
    def forward(self, in_batch, out_batch, od_batch, graph):
        count_pred_in, count_pred_out, trip_pred = [], [], []
        node_features = torch.zeros(
            (self.num_stations, self.station_dim + self.hidden_dim + self.time_dim),
            device=device
        )
        
        # Precompute node features for reuse
        station_ids_all = []
        hours_all = []
        geo_features_all = []
        if in_batch:
            station_ids_all.append(torch.tensor([x['station_id'] for x in in_batch], dtype=torch.long, device=device))
            hours_all.append(torch.stack([x['hour'] for x in in_batch]).to(device, non_blocking=True))
            geo_features_all.append(torch.stack([x['geo_features'] for x in in_batch]).to(device, non_blocking=True))
        if out_batch:
            station_ids_all.append(torch.tensor([x['station_id'] for x in out_batch], dtype=torch.long, device=device))
            hours_all.append(torch.stack([x['hour'] for x in out_batch]).to(device, non_blocking=True))
            geo_features_all.append(torch.stack([x['geo_features'] for x in out_batch]).to(device, non_blocking=True))
        
        if station_ids_all:
            station_ids = torch.cat(station_ids_all, dim=0)
            hours = torch.cat(hours_all, dim=0)
            geo_features = torch.cat(geo_features_all, dim=0)
            
            station_emb = self.station_embed(station_ids)
            geo_emb = self.geo_embed(geo_features)
            time_emb = self.time_embed(hours)
            batch_features = torch.cat([station_emb, geo_emb, time_emb], dim=-1)
            
            unique_ids, inverse = torch.unique(station_ids, return_inverse=True)
            node_features[unique_ids] = batch_features[inverse].mean(dim=0, keepdim=True)
            
            gnn_out = self.gnn(graph, node_features)
            transformer_out = self.transformer(gnn_out.unsqueeze(1)).squeeze(1)
            
            if in_batch:
                idx_in = torch.tensor([x['station_id'] for x in in_batch], dtype=torch.long, device=device)
                count_pred_in = torch.relu(self.fc_grid(transformer_out[idx_in])).squeeze(-1)
                count_pred_in = torch.nan_to_num(count_pred_in, nan=0.0)
            
            if out_batch:
                idx_out = torch.tensor([x['station_id'] for x in out_batch], dtype=torch.long, device=device)
                count_pred_out = torch.relu(self.fc_grid(transformer_out[idx_out])).squeeze(-1)
                count_pred_out = torch.nan_to_num(count_pred_out, nan=0.0)
        
        if od_batch:
            o_station_ids = torch.tensor([x['o_station_id'] for x in od_batch], dtype=torch.long, device=device)
            d_station_ids = torch.tensor([x['d_station_id'] for x in od_batch], dtype=torch.long, device=device)
            hours = torch.stack([x['hour'] for x in od_batch]).to(device, non_blocking=True)
            o_geo = torch.stack([x['o_geo_features'] for x in od_batch]).to(device, non_blocking=True)
            d_geo = torch.stack([x['d_geo_features'] for x in od_batch]).to(device, non_blocking=True)
            od_features = torch.stack([x['od_features'] for x in od_batch]).to(device, non_blocking=True)
            
            o_emb = self.station_embed(o_station_ids)
            d_emb = self.station_embed(d_station_ids)
            o_geo_emb = self.geo_embed(o_geo)
            d_geo_emb = self.geo_embed(d_geo)
            time_emb = self.time_embed(hours)
            
            combined = torch.cat([o_emb, d_emb, o_geo_emb, d_geo_emb, od_features, time_emb], dim=-1)
            dnn_out = self.dnn(combined)
            attn_out, _ = self.attention(dnn_out.unsqueeze(0), dnn_out.unsqueeze(0), dnn_out.unsqueeze(0))
            trip_pred = torch.relu(self.fc_od(attn_out.squeeze(0))).squeeze(-1)
            trip_pred = torch.nan_to_num(trip_pred, nan=0.0)
            trip_pred = torch.round(trip_pred)  # Round OD predictions
        
        return count_pred_in, count_pred_out, trip_pred

# 训练模型
def train_model(model, train_loader, val_loader, graph, epochs=8):
    optimizer = optim.AdamW(model.parameters(), lr=1e-3, weight_decay=1e-5)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=5, factor=0.5)
    scaler = GradScaler()
    best_mae = float('inf')
    
    # Compile model for PyTorch 2.0+
    if hasattr(torch, 'compile'):
        model = torch.compile(model)
        logger.info("Model compiled with torch.compile")
    
    for epoch in range(epochs):
        model.train()
        total_loss_in, total_loss_out, total_loss_od = 0, 0, 0
        count_in, count_out, count_od = 0, 0, 0
        
        for in_batch, out_batch, od_batch in tqdm(train_loader, desc=f"Epoch {epoch+1}"):
            optimizer.zero_grad(set_to_none=True)  # Faster zero_grad
            
            with autocast():
                count_pred_in, count_pred_out, trip_pred = model(in_batch, out_batch, od_batch, graph)
                
                loss_in = loss_out = loss_od = 0
                if in_batch:
                    count_true_in = torch.stack([x['count'] for x in in_batch]).to(device, non_blocking=True)
                    loss_in = nn.MSELoss()(count_pred_in, count_true_in)
                    total_loss_in += loss_in.item()
                    count_in += 1
                if out_batch:
                    count_true_out = torch.stack([x['count'] for x in out_batch]).to(device, non_blocking=True)
                    loss_out = nn.MSELoss()(count_pred_out, count_true_out)
                    total_loss_out += loss_out.item()
                    count_out += 1
                if od_batch:
                    trip_true = torch.stack([x['trip'] for x in od_batch]).to(device, non_blocking=True)
                    loss_od = nn.MSELoss()(trip_pred, trip_true)
                    total_loss_od += loss_od.item()
                    count_od += 1
                
                loss = loss_in + loss_out + loss_od
            
            if isinstance(loss, torch.Tensor):
                scaler.scale(loss).backward()
                scaler.step(optimizer)
                scaler.update()
        
        # Validation
        mae_in, mae_out, mae_od, rmse_in, rmse_out, rmse_od = evaluate_metrics(model, val_loader, graph)
        
        avg_loss_in = total_loss_in / count_in if count_in > 0 else 0
        avg_loss_out = total_loss_out / count_out if count_out > 0 else 0
        avg_loss_od = total_loss_od / count_od if count_od > 0 else 0
        overall_mae = (mae_in + mae_out + mae_od) / 3
        
        logger.info(f"Epoch {epoch+1}, "
                    f"In-Flow Loss: {avg_loss_in:.4f}, MAE: {mae_in:.4f}, RMSE: {rmse_in:.4f}, "
                    f"Out-Flow Loss: {avg_loss_out:.4f}, MAE: {mae_out:.4f}, RMSE: {rmse_out:.4f}, "
                    f"OD-Flow Loss: {avg_loss_od:.4f}, MAE: {mae_od:.4f}, RMSE: {rmse_od:.4f}, "
                    f"GPU Memory: {torch.cuda.memory_allocated(device)/1e9:.2f} GB")
        
        scheduler.step(overall_mae)
        if overall_mae < best_mae:
            best_mae = overall_mae
            torch.save(model.state_dict(), 'best_model4.pth')

def evaluate_metrics(model, loader, graph):
    model.eval()
    maes_in, maes_out, maes_od = [], [], []
    rmses_in, rmses_out, rmses_od = [], [], []
    
    with torch.no_grad():
        for in_batch, out_batch, od_batch in loader:
            with autocast():
                count_pred_in, count_pred_out, trip_pred = model(in_batch, out_batch, od_batch, graph)
            
            if in_batch:
                count_true_in = torch.stack([x['count'] for x in in_batch]).cpu().numpy()
                pred_in = np.maximum(count_pred_in.cpu().numpy(), 0)
                maes_in.append(mean_absolute_error(count_true_in, pred_in))
                rmses_in.append(np.sqrt(mean_squared_error(count_true_in, pred_in)))
            
            if out_batch:
                count_true_out = torch.stack([x['count'] for x in out_batch]).cpu().numpy()
                pred_out = np.maximum(count_pred_out.cpu().numpy(), 0)
                maes_out.append(mean_absolute_error(count_true_out, pred_out))
                rmses_out.append(np.sqrt(mean_squared_error(count_true_out, pred_out)))
            
            if od_batch:
                trip_true = torch.stack([x['trip'] for x in od_batch]).cpu().numpy()
                pred_od = np.maximum(trip_pred.cpu().numpy(), 0)
                maes_od.append(mean_absolute_error(trip_true, pred_od))
                rmses_od.append(np.sqrt(mean_squared_error(trip_true, pred_od)))
    
    mae_in = np.mean(maes_in) if maes_in else 0
    mae_out = np.mean(maes_out) if maes_out else 0
    mae_od = np.mean(maes_od) if maes_od else 0
    rmse_in = np.mean(rmses_in) if rmses_in else 0
    rmse_out = np.mean(rmses_out) if rmses_out else 0
    rmse_od = np.mean(rmses_od) if rmses_od else 0
    
    return mae_in, mae_out, mae_od, rmse_in, rmse_out, rmse_od

# 评估模型并生成预测结果
def evaluate_model(model, loader, graph, test_in, test_out, test_od, station_encoder):
    model.eval()
    in_results = []
    out_results = []
    od_results = []
    
    with torch.no_grad():
        for in_batch, out_batch, od_batch in loader:
            with autocast():
                count_pred_in, count_pred_out, trip_pred = model(in_batch, out_batch, od_batch, graph)
            
            if in_batch:
                count_true_in = torch.stack([x['count'] for x in in_batch]).cpu().numpy()
                indices = [x['index'] for x in in_batch]
                geometries = [x['geometry'] for x in in_batch]
                pred_in = np.maximum(count_pred_in.cpu().numpy(), 0)
                for idx, geom, true, p in zip(indices, geometries, count_true_in, pred_in):
                    in_results.append({'index': idx, 'geometry': geom, 'true': true, 'pred': p})
            
            if out_batch:
                count_true_out = torch.stack([x['count'] for x in out_batch]).cpu().numpy()
                indices = [x['index'] for x in out_batch]
                geometries = [x['geometry'] for x in out_batch]
                pred_out = np.maximum(count_pred_out.cpu().numpy(), 0)
                for idx, geom, true, p in zip(indices, geometries, count_true_out, pred_out):
                    out_results.append({'index': idx, 'geometry': geom, 'true': true, 'pred': p})
            
            if od_batch:
                trip_true = torch.stack([x['trip'] for x in od_batch]).cpu().numpy()
                indices = [x['index'] for x in od_batch]
                o_ids = [x['o_station_id'] for x in od_batch]
                d_ids = [x['d_station_id'] for x in od_batch]
                hours = [x['hour'].cpu().numpy() for x in od_batch]
                pred_od = np.maximum(trip_pred.cpu().numpy(), 0)
                pred_od = np.round(pred_od).astype(int)
                for idx, o_id, d_id, hour, true, p in zip(indices, o_ids, d_ids, hours, trip_true, pred_od):
                    o_name = station_encoder.inverse_transform([o_id])[0]
                    d_name = station_encoder.inverse_transform([d_id])[0]
                    hour_val = np.arccos(hour[1]) / (2 * np.pi) * 24
                    od_results.append({
                        'index': idx,
                        'hour': round(hour_val),
                        'o_station': o_name,
                        'd_station': d_name,
                        'true': true,
                        'pred': p
                    })
    
    # Part 1: Grid to Station
    in_results_df = pd.DataFrame(in_results)
    test_in = test_in.copy()
    test_in['pred'] = 0.0
    test_in['abs_error'] = 0.0
    test_in['squared_error'] = 0.0
    for _, row in in_results_df.iterrows():
        idx = int(row['index'])
        test_in.iloc[idx, test_in.columns.get_loc('pred')] = row['pred']
        test_in.iloc[idx, test_in.columns.get_loc('abs_error')] = abs(row['true'] - row['pred'])
        test_in.iloc[idx, test_in.columns.get_loc('squared_error')] = (row['true'] - row['pred'])**2
    
    in_grid_metrics = test_in.groupby('geometry').agg({
        'abs_error': 'mean',
        'squared_error': lambda x: np.sqrt(np.mean(x)),
        'pred': 'sum',
        'count': 'sum'
    }).rename(columns={
        'abs_error': 'MAE',
        'squared_error': 'RMSE',
        'pred': 'pred_total',
        'count': 'true_total'
    }).reset_index()
    
    in_grid_gdf = gpd.GeoDataFrame(in_grid_metrics, geometry='geometry')
    in_grid_gdf.to_file('in_grid_metrics.shp')
    in_grid_metrics.to_csv('in_grid_metrics.csv', index=False)
    
    # Part 3: Station to Grid
    out_results_df = pd.DataFrame(out_results)
    test_out = test_out.copy()
    test_out['pred'] = 0.0
    test_out['abs_error'] = 0.0
    test_out['squared_error'] = 0.0
    for _, row in out_results_df.iterrows():
        idx = int(row['index'])
        test_out.iloc[idx, test_out.columns.get_loc('pred')] = row['pred']
        test_out.iloc[idx, test_out.columns.get_loc('abs_error')] = abs(row['true'] - row['pred'])
        test_out.iloc[idx, test_out.columns.get_loc('squared_error')] = (row['true'] - row['pred'])**2
    
    out_grid_metrics = test_out.groupby('geometry').agg({
        'abs_error': 'mean',
        'squared_error': lambda x: np.sqrt(np.mean(x)),
        'pred': 'sum',
        'count': 'sum'
    }).rename(columns={
        'abs_error': 'MAE',
        'squared_error': 'RMSE',
        'pred': 'pred_total',
        'count': 'true_total'
    }).reset_index()
    
    out_grid_gdf = gpd.GeoDataFrame(out_grid_metrics, geometry='geometry')
    out_grid_gdf.to_file('out_grid_metrics.shp')
    out_grid_metrics.to_csv('out_grid_metrics.csv', index=False)
    
    # Part 2: Station to Station
    od_results_df = pd.DataFrame(od_results)
    test_od = test_od.copy()
    test_od['pred'] = 0.0
    test_od['abs_error'] = 0.0
    test_od['squared_error'] = 0.0
    for _, row in od_results_df.iterrows():
        idx = int(row['index'])
        test_od.iloc[idx, test_od.columns.get_loc('pred')] = row['pred']
        test_od.iloc[idx, test_od.columns.get_loc('abs_error')] = abs(row['true'] - row['pred'])
        test_od.iloc[idx, test_od.columns.get_loc('squared_error')] = (row['true'] - row['pred'])**2
    
    station_metrics = []
    for station in station_encoder.classes_:
        station_id = station_encoder.transform([station])[0]
        od_data_o = test_od[test_od['o_station_id'] == station_id]
        if not od_data_o.empty:
            mae = od_data_o['abs_error'].mean()
            rmse = np.sqrt(od_data_o['squared_error'].mean())
            pred_total = od_data_o['pred'].sum()
            true_total = od_data_o['trip'].sum()
            station_metrics.append({
                'station': station,
                'MAE': mae,
                'RMSE': rmse,
                'pred_total': pred_total,
                'true_total': true_total
            })
    
    station_metrics_df = pd.DataFrame(station_metrics)
    station_metrics_df.to_csv('station_metrics_agg.csv', index=False)
    
    od_results_df[['hour', 'o_station', 'd_station', 'true', 'pred']].to_csv('od_predictions_detailed.csv', index=False)
    
    mae_in = in_results_df['true'].sub(in_results_df['pred']).abs().mean() if not in_results_df.empty else 0
    mae_out = out_results_df['true'].sub(out_results_df['pred']).abs().mean() if not out_results_df.empty else 0
    mae_od = od_results_df['true'].sub(od_results_df['pred']).abs().mean() if not od_results_df.empty else 0
    overall_mae = np.mean([mae_in, mae_out, mae_od])
    
    return overall_mae

# 执行
in_shp = "C:\\Users\\<USER>\\Desktop\\接驳\\in_500.shp"
out_shp = "C:\\Users\\<USER>\\Desktop\\接驳\\out_500.shp"
od_csv = "C:\\Users\\<USER>\\Desktop\\接驳\\updated_北京市_subway_od_2024_modified3.csv"
features_csv = "C:\\Users\\<USER>\\Desktop\\接驳\\station_features_result.csv"

in_gdf, out_gdf, od_df, features_df = load_data(in_shp, out_shp, od_csv, features_csv)
in_gdf, out_gdf, od_df, features_df, station_encoder, feature_cols = preprocess_data(
    in_gdf, out_gdf, od_df, features_df
)

train_in = in_gdf.sample(frac=0.7, random_state=42)
val_in = in_gdf.drop(train_in.index).sample(frac=0.5, random_state=42)
test_in = in_gdf.drop(train_in.index).drop(val_in.index)

train_out = out_gdf.sample(frac=0.7, random_state=42)
val_out = out_gdf.drop(train_out.index).sample(frac=0.5, random_state=42)
test_out = out_gdf.drop(train_out.index).drop(val_out.index)

train_od = od_df.sample(frac=0.7, random_state=42)
val_od = od_df.drop(train_od.index).sample(frac=0.5, random_state=42)
test_od = od_df.drop(train_od.index).drop(val_od.index)

train_dataset = TrafficDataset(train_in, train_out, train_od, features_df, feature_cols)
val_dataset = TrafficDataset(val_in, val_out, val_od, features_df, feature_cols)
test_dataset = TrafficDataset(test_in, test_out, test_od, features_df, feature_cols)

train_loader = torch.utils.data.DataLoader(
    train_dataset, batch_size=256, shuffle=True, collate_fn=collate_fn,
    num_workers=8, pin_memory=True  # Leverage i7-13700F cores
)
val_loader = torch.utils.data.DataLoader(
    val_dataset, batch_size=256, shuffle=False, collate_fn=collate_fn,
    num_workers=8, pin_memory=True
)
test_loader = torch.utils.data.DataLoader(
    test_dataset, batch_size=256, shuffle=False, collate_fn=collate_fn,
    num_workers=8, pin_memory=True
)

graph = create_graph(in_gdf, out_gdf, od_df, station_encoder)

model = TrafficModel(
    num_stations=len(station_encoder.classes_),
    geo_feature_dim=len(feature_cols)
).to(device)

train_model(model, train_loader, val_loader, graph)

model.load_state_dict(torch.load('best_model4.pth'))
test_mae = evaluate_model(model, test_loader, graph, test_in, test_out, test_od, station_encoder)
print(f"Test MAE: {test_mae:.4f}")