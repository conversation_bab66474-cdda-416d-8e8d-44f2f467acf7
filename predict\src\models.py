import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import GCNConv, GATConv, SAGEConv
from torch_geometric.data import Data
import numpy as np
import os

# 配置GPU设备
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

class TemporalAttention(nn.Module):
    """时间注意力模块"""
    def __init__(self, in_channels, out_channels):
        super(TemporalAttention, self).__init__()
        self.in_channels = in_channels
        self.out_channels = out_channels
        
        # 注意力权重
        self.W_1 = nn.Parameter(torch.Tensor(in_channels, out_channels))
        self.W_2 = nn.Parameter(torch.Tensor(in_channels, out_channels))
        self.W_3 = nn.Parameter(torch.Tensor(out_channels, 1))
        self.b_s = nn.Parameter(torch.Tensor(1, out_channels))
        self.b_t = nn.Parameter(torch.Tensor(1, 1))
        
        self.leaky_relu = nn.LeakyReLU(0.2)
        self.reset_parameters()
        
    def reset_parameters(self):
        """初始化参数"""
        nn.init.xavier_uniform_(self.W_1)
        nn.init.xavier_uniform_(self.W_2)
        nn.init.xavier_uniform_(self.W_3)
        nn.init.zeros_(self.b_s)
        nn.init.zeros_(self.b_t)
        
    def forward(self, x, batch_size, num_nodes, num_time_steps):
        """
        前向传播
        x: 输入特征 [batch_size * num_nodes * num_time_steps, in_channels]
        """
        # 确保所有张量在同一设备上
        x = x.to(device)
        self.W_1 = self.W_1.to(device)
        self.W_2 = self.W_2.to(device)
        self.W_3 = self.W_3.to(device)
        self.b_s = self.b_s.to(device)
        self.b_t = self.b_t.to(device)
        
        # 变形输入张量以进行注意力计算
        x = x.view(batch_size, num_nodes, num_time_steps, self.in_channels)
        
        # 计算注意力得分
        lhs = torch.matmul(x, self.W_1).view(batch_size, num_nodes, num_time_steps, self.out_channels)  # [b, n, t, c]
        rhs = torch.matmul(x, self.W_2).view(batch_size, num_nodes, num_time_steps, self.out_channels)  # [b, n, t, c]
        
        attention_scores = self.leaky_relu(lhs + rhs + self.b_s)  # [b, n, t, c]
        attention_scores = torch.matmul(attention_scores, self.W_3) + self.b_t  # [b, n, t, 1]
        
        # 应用softmax获取注意力权重
        attention_weights = F.softmax(attention_scores, dim=2)  # [b, n, t, 1]
        
        # 应用注意力权重
        output = torch.sum(attention_weights * x, dim=2)  # [b, n, c]
        
        return output, attention_weights
    
class SpatialAttention(nn.Module):
    """空间注意力模块"""
    def __init__(self, in_channels, out_channels):
        super(SpatialAttention, self).__init__()
        self.in_channels = in_channels
        self.out_channels = out_channels
        
        # 注意力权重
        self.W_1 = nn.Parameter(torch.Tensor(in_channels, out_channels))
        self.W_2 = nn.Parameter(torch.Tensor(in_channels, out_channels))
        self.W_3 = nn.Parameter(torch.Tensor(out_channels, 1))
        self.b_s = nn.Parameter(torch.Tensor(1, out_channels))
        self.b_t = nn.Parameter(torch.Tensor(1, 1))
        
        self.leaky_relu = nn.LeakyReLU(0.2)
        self.reset_parameters()
        
    def reset_parameters(self):
        """初始化参数"""
        nn.init.xavier_uniform_(self.W_1)
        nn.init.xavier_uniform_(self.W_2)
        nn.init.xavier_uniform_(self.W_3)
        nn.init.zeros_(self.b_s)
        nn.init.zeros_(self.b_t)
        
    def forward(self, x, edge_index):
        """
        前向传播
        x: 节点特征 [num_nodes, in_channels]
        edge_index: 边索引 [2, num_edges]
        """
        # 确保所有张量在同一设备上
        x = x.to(device)
        edge_index = edge_index.to(device)
        self.W_1 = self.W_1.to(device)
        self.W_2 = self.W_2.to(device)
        self.W_3 = self.W_3.to(device)
        self.b_s = self.b_s.to(device)
        self.b_t = self.b_t.to(device)
        
        # 获取源节点和目标节点索引
        source, target = edge_index
        
        # 计算注意力分数
        h_1 = torch.matmul(x, self.W_1)  # [num_nodes, out_channels]
        h_2 = torch.matmul(x, self.W_2)  # [num_nodes, out_channels]
        
        # 为每条边计算注意力得分
        source_h = torch.index_select(h_1, 0, source)  # [num_edges, out_channels]
        target_h = torch.index_select(h_2, 0, target)  # [num_edges, out_channels]
        
        attention_scores = self.leaky_relu(source_h + target_h + self.b_s)  # [num_edges, out_channels]
        attention_scores = torch.matmul(attention_scores, self.W_3) + self.b_t  # [num_edges, 1]
        
        # 对每个节点的所有相邻边应用softmax
        # 首先初始化一个全零张量
        attention_weights = torch.zeros_like(attention_scores)
        
        # 对每个唯一的源节点分别应用softmax
        for node in torch.unique(source):
            mask = (source == node)
            scores = attention_scores[mask]
            weights = F.softmax(scores, dim=0)
            attention_weights[mask] = weights
        
        return attention_weights
    
class STGCNLayer(nn.Module):
    """时空图卷积层"""
    def __init__(self, in_channels, out_channels, use_attention=True, use_edge_attr=False, edge_dim=None):
        super(STGCNLayer, self).__init__()
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.use_attention = use_attention
        self.use_edge_attr = use_edge_attr
        
        # 使用half precision以减少内存使用
        self.use_half = torch.cuda.is_available()
        
        # 图卷积
        if use_edge_attr and edge_dim is not None:
            # 使用边特征的GAT卷积
            self.conv = GATConv(in_channels, out_channels, edge_dim=edge_dim)
        else:
            # 普通图卷积
            self.conv = GCNConv(in_channels, out_channels)
        
        # 时间注意力机制
        if use_attention:
            self.temporal_attention = TemporalAttention(in_channels, out_channels)
            self.spatial_attention = SpatialAttention(in_channels, out_channels)
        
        # 批归一化和跳跃连接
        self.batch_norm = nn.BatchNorm1d(out_channels)
        
        # 跳跃连接的线性映射
        self.skip_connection = nn.Linear(in_channels, out_channels)
        
        # 移动到设备
        if torch.cuda.is_available():
            self.to(device)
        
    def forward(self, x, edge_index, edge_attr=None, batch_size=1, num_nodes=None, num_time_steps=None):
        """
        前向传播
        x: 节点特征
        edge_index: 边索引
        edge_attr: 边特征
        batch_size, num_nodes, num_time_steps: 用于时间注意力
        """
        # 确保所有输入都在同一设备上
        x = x.to(device)
        edge_index = edge_index.to(device)
        if edge_attr is not None:
            edge_attr = edge_attr.to(device)
            
        # 保存原始输入用于跳跃连接
        identity = x
        
        # 如果启用了半精度，转换为float16
        if self.use_half:
            x = x.half()
            if edge_attr is not None:
                edge_attr = edge_attr.half()
        
        # 应用空间注意力（如果启用）
        if self.use_attention:
            try:
                # 获取空间注意力权重
                if num_nodes is not None and num_time_steps is not None:
                    # 应用时间注意力
                    x_temp, _ = self.temporal_attention(x, batch_size, num_nodes, num_time_steps)
                    x_temp = x_temp.reshape(-1, self.in_channels)
                else:
                    x_temp = x
                
                # 应用空间注意力
                attention_weights = self.spatial_attention(x_temp, edge_index)
                
                # 调整空间注意力的形状以匹配图卷积的需求
                edge_weight = attention_weights.view(-1)
                
                # 执行图卷积
                if self.use_edge_attr and edge_attr is not None:
                    x = self.conv(x, edge_index, edge_attr=edge_attr)
                else:
                    x = self.conv(x, edge_index, edge_weight=edge_weight)
            except RuntimeError as e:
                # 如果出现内存错误，回退到不使用注意力的版本
                print(f"注意力计算出错，回退到标准图卷积: {str(e)}")
                if self.use_edge_attr and edge_attr is not None:
                    x = self.conv(x, edge_index, edge_attr=edge_attr)
                else:
                    x = self.conv(x, edge_index)
        else:
            # 不使用注意力直接执行图卷积
            if self.use_edge_attr and edge_attr is not None:
                x = self.conv(x, edge_index, edge_attr=edge_attr)
            else:
                x = self.conv(x, edge_index)
        
        try:
            # 跳跃连接 - 确保转回float32用于精确计算
            if self.use_half:
                x = x.float()
                identity = identity.float()
            
            # 应用批归一化
            x = self.batch_norm(x)
            
            # 跳跃连接
            x = x + self.skip_connection(identity)
            
            # 激活函数
            x = F.elu(x)
            
            # 添加内存清理
            torch.cuda.empty_cache()
            
            return x
        except RuntimeError as e:
            print(f"在STGCNLayer前向传播中发生错误: {str(e)}")
            # 确保我们至少返回一些有用的东西
            return self.skip_connection(identity)

class InFlowPredictionModel(nn.Module):
    """进站流量预测模型"""
    def __init__(self, node_features, hidden_dim=64, out_dim=1, num_layers=3, dropout=0.1, use_attention=True, use_fp16=True):
        super(InFlowPredictionModel, self).__init__()
        self.node_features = node_features
        self.hidden_dim = hidden_dim
        self.out_dim = out_dim
        self.num_layers = num_layers
        self.dropout = dropout
        self.use_attention = use_attention
        self.dtype = torch.float16 if use_fp16 else torch.float32
        
        # 使用半精度减少内存使用
        self.use_fp16 = use_fp16 and torch.cuda.is_available()
        
        # 图卷积层 - 逐层构建而不是一次性全部加载到GPU
        self.graph_layers = nn.ModuleList()
        
        # 先添加第一层并移到设备
        first_layer = STGCNLayer(node_features, hidden_dim, use_attention)
        if torch.cuda.is_available():
            first_layer = first_layer.to(device)
        self.graph_layers.append(first_layer)
        
        # 在设备上逐层构建剩余层
        for i in range(1, num_layers):
            layer = STGCNLayer(hidden_dim, hidden_dim, use_attention)
            if torch.cuda.is_available():
                layer = layer.to(device)
            self.graph_layers.append(layer)
        
        # 批归一化层，提高训练稳定性
        self.batch_norms = nn.ModuleList()
        for _ in range(num_layers):
            bn = nn.BatchNorm1d(hidden_dim)
            if torch.cuda.is_available():
                bn = bn.to(device)
            self.batch_norms.append(bn)
        
        # 增加线性预测层的复杂度
        self.fc1 = nn.Linear(hidden_dim, hidden_dim // 2)
        self.bn1 = nn.BatchNorm1d(hidden_dim // 2)
        self.fc2 = nn.Linear(hidden_dim // 2, hidden_dim // 4)
        self.fc3 = nn.Linear(hidden_dim // 4, out_dim)
        
        # 全局节点特征提取
        self.global_attention = nn.Linear(hidden_dim, 1)
        
        # 标准化参数
        self.register_buffer('mean', torch.tensor(0.0))
        self.register_buffer('std', torch.tensor(1.0))
        
        # 确保所有模块都在正确的设备上
        if torch.cuda.is_available():
            self.to(device)
        
    def forward(self, data, batch_size=1, num_nodes=None, num_time_steps=None):
        """
        前向传播
        data: 图数据对象，包含 x, edge_index
        """
        try:
            # 确保数据在正确的设备上
            x, edge_index = data.x.to(device), data.edge_index.to(device)
            
            # 如果启用半精度，转换输入
            if self.use_fp16:
                x = x.half()
            
            # 存储各层特征以用于残差连接
            features = [x]
            
            # 应用图卷积层
            for i, (layer, bn) in enumerate(zip(self.graph_layers, self.batch_norms)):
                # 清理显存
                if i > 0:
                    torch.cuda.empty_cache()
                
                # 应用当前层
                x_new = layer(x, edge_index, batch_size=batch_size, num_nodes=num_nodes, num_time_steps=num_time_steps)
                
                # 应用批归一化和残差连接
                if i < len(self.graph_layers) - 1:
                    if x.shape == x_new.shape and i > 0:  # 只在维度匹配时应用残差连接，且不是第一层
                        x = F.relu(bn(x_new) + x)
                    else:
                        x = F.relu(bn(x_new))
                    x = F.dropout(x, p=self.dropout, training=self.training)
                    features.append(x)
                else:
                    x = x_new
            
            # 如果使用了半精度，在FC层之前转回float32提高精度
            if self.use_fp16:
                x = x.float()
            
            # 计算全局注意力权重
            attention_weights = torch.sigmoid(self.global_attention(x))
            
            # 全连接层进行预测
            x = F.relu(self.bn1(self.fc1(x)))
            x = F.dropout(x, p=self.dropout, training=self.training)
            x = F.relu(self.fc2(x))
            x = self.fc3(x)
            
            # 如果存在均值和标准差，则逆标准化预测结果
            if hasattr(self, 'mean') and hasattr(self, 'std') and self.std != 0:
                x = x * self.std + self.mean
            
            # 清理缓存
            torch.cuda.empty_cache()
            
            return x
            
        except RuntimeError as e:
            print(f"InFlowPredictionModel前向传播中发生错误: {str(e)}")
            # 尝试回退到简单的处理
            try:
                if self.use_fp16:
                    x = x.float()  # 转回float32
                
                # 简单处理直接预测
                x = F.relu(self.fc1(x))
                x = self.fc3(x)  # 直接到最后一层
                
                if hasattr(self, 'mean') and hasattr(self, 'std') and self.std != 0:
                    x = x * self.std + self.mean
                
                return x
            except:
                # 如果还是失败，返回零张量
                print("完全回退，返回零张量")
                return torch.zeros((data.x.size(0), self.out_dim), device=device)

class OutFlowPredictionModel(nn.Module):
    """出站流量预测模型"""
    def __init__(self, node_features, hidden_dim=64, out_dim=1, num_layers=3, dropout=0.1, use_attention=True, use_fp16=True):
        super(OutFlowPredictionModel, self).__init__()
        self.node_features = node_features
        self.hidden_dim = hidden_dim
        self.out_dim = out_dim
        self.num_layers = num_layers
        self.dropout = dropout
        self.use_attention = use_attention
        self.dtype = torch.float16 if use_fp16 else torch.float32
        
        # 使用半精度减少内存使用
        self.use_fp16 = use_fp16 and torch.cuda.is_available()
        
        # 图卷积层 - 逐层构建而不是一次性全部加载到GPU
        self.graph_layers = nn.ModuleList()
        
        # 先添加第一层并移到设备
        first_layer = STGCNLayer(node_features, hidden_dim, use_attention)
        if torch.cuda.is_available():
            first_layer = first_layer.to(device)
        self.graph_layers.append(first_layer)
        
        # 在设备上逐层构建剩余层
        for i in range(1, num_layers):
            layer = STGCNLayer(hidden_dim, hidden_dim, use_attention)
            if torch.cuda.is_available():
                layer = layer.to(device)
            self.graph_layers.append(layer)
        
        # 批归一化层，提高训练稳定性
        self.batch_norms = nn.ModuleList()
        for _ in range(num_layers):
            bn = nn.BatchNorm1d(hidden_dim)
            if torch.cuda.is_available():
                bn = bn.to(device)
            self.batch_norms.append(bn)
        
        # 增加线性预测层的复杂度
        self.fc1 = nn.Linear(hidden_dim, hidden_dim // 2)
        self.bn1 = nn.BatchNorm1d(hidden_dim // 2)
        self.fc2 = nn.Linear(hidden_dim // 2, hidden_dim // 4)
        self.fc3 = nn.Linear(hidden_dim // 4, out_dim)
        
        # 全局节点特征提取
        self.global_attention = nn.Linear(hidden_dim, 1)
        
        # 标准化参数
        self.register_buffer('mean', torch.tensor(0.0))
        self.register_buffer('std', torch.tensor(1.0))
        
        # 确保所有模块都在正确的设备上
        if torch.cuda.is_available():
            self.to(device)
        
    def forward(self, data, batch_size=1, num_nodes=None, num_time_steps=None):
        """
        前向传播
        data: 图数据对象，包含 x, edge_index
        """
        try:
            # 确保数据在正确的设备上
            x, edge_index = data.x.to(device), data.edge_index.to(device)
            
            # 如果启用半精度，转换输入
            if self.use_fp16:
                x = x.half()
            
            # 存储各层特征以用于残差连接
            features = [x]
            
            # 应用图卷积层
            for i, (layer, bn) in enumerate(zip(self.graph_layers, self.batch_norms)):
                # 清理显存
                if i > 0:
                    torch.cuda.empty_cache()
                
                # 应用当前层
                x_new = layer(x, edge_index, batch_size=batch_size, num_nodes=num_nodes, num_time_steps=num_time_steps)
                
                # 应用批归一化和残差连接
                if i < len(self.graph_layers) - 1:
                    if x.shape == x_new.shape and i > 0:  # 只在维度匹配时应用残差连接，且不是第一层
                        x = F.relu(bn(x_new) + x)
                    else:
                        x = F.relu(bn(x_new))
                    x = F.dropout(x, p=self.dropout, training=self.training)
                    features.append(x)
                else:
                    x = x_new
            
            # 如果使用了半精度，在FC层之前转回float32提高精度
            if self.use_fp16:
                x = x.float()
            
            # 计算全局注意力权重
            attention_weights = torch.sigmoid(self.global_attention(x))
            
            # 全连接层进行预测
            x = F.relu(self.bn1(self.fc1(x)))
            x = F.dropout(x, p=self.dropout, training=self.training)
            x = F.relu(self.fc2(x))
            x = self.fc3(x)
            
            # 如果存在均值和标准差，则逆标准化预测结果
            if hasattr(self, 'mean') and hasattr(self, 'std') and self.std != 0:
                x = x * self.std + self.mean
            
            # 清理缓存
            torch.cuda.empty_cache()
            
            return x
            
        except RuntimeError as e:
            print(f"OutFlowPredictionModel前向传播中发生错误: {str(e)}")
            # 尝试回退到简单的处理
            try:
                if self.use_fp16:
                    x = x.float()  # 转回float32
                
                # 简单处理直接预测
                x = F.relu(self.fc1(x))
                x = self.fc3(x)  # 直接到最后一层
                
                if hasattr(self, 'mean') and hasattr(self, 'std') and self.std != 0:
                    x = x * self.std + self.mean
                
                return x
            except:
                # 如果还是失败，返回零张量
                print("完全回退，返回零张量")
                return torch.zeros((data.x.size(0), self.out_dim), device=device)

class ODFlowPredictionModel(nn.Module):
    """OD流量预测模型"""
    def __init__(self, node_features, edge_features, hidden_dim=64, out_dim=1, num_layers=3, dropout=0.1, use_fp16=True):
        super(ODFlowPredictionModel, self).__init__()
        self.node_features = node_features
        self.edge_features = edge_features
        self.hidden_dim = hidden_dim
        self.out_dim = out_dim
        self.num_layers = num_layers
        self.dropout = dropout
        self.dtype = torch.float16 if use_fp16 else torch.float32
        
        # 使用半精度减少内存使用
        self.use_fp16 = use_fp16 and torch.cuda.is_available()
        
        # 改进的图卷积层 - 使用更深的网络
        self.graph_layers = nn.ModuleList()
        
        # 先添加第一层
        first_layer = STGCNLayer(node_features, hidden_dim, use_attention=True, use_edge_attr=True, edge_dim=edge_features)
        if torch.cuda.is_available():
            first_layer = first_layer.to(device)
        self.graph_layers.append(first_layer)
        
        # 逐层添加剩余层
        for i in range(1, num_layers):
            layer = STGCNLayer(hidden_dim, hidden_dim, use_attention=True, use_edge_attr=True, edge_dim=edge_features)
            if torch.cuda.is_available():
                layer = layer.to(device)
            self.graph_layers.append(layer)
        
        # 批归一化层，提高训练稳定性
        self.batch_norms = nn.ModuleList()
        for _ in range(num_layers):
            bn = nn.BatchNorm1d(hidden_dim)
            if torch.cuda.is_available():
                bn = bn.to(device)
            self.batch_norms.append(bn)
        
        # 改进的边特征处理
        self.edge_embedding = nn.Sequential(
            nn.Linear(edge_features, hidden_dim),
            nn.BatchNorm1d(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim)
        )
        
        # 注意力机制，增强节点对之间的关系建模
        self.attention = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1),
            nn.Sigmoid()
        )
        
        # 更复杂的节点对预测网络 - 拆分为多个部分以减少GPU内存需求
        self.edge_fc1 = nn.Linear(hidden_dim * 2 + hidden_dim, hidden_dim)
        self.edge_bn1 = nn.BatchNorm1d(hidden_dim)
        self.edge_fc2 = nn.Linear(hidden_dim, hidden_dim // 2)
        self.edge_fc3 = nn.Linear(hidden_dim // 2, out_dim)
        
        # 标准化参数
        self.register_buffer('mean', torch.tensor(0.0))
        self.register_buffer('std', torch.tensor(1.0))
        
        # 确保所有模块都在正确的设备上
        if torch.cuda.is_available():
            self.to(device)
        
    def forward(self, data):
        """
        前向传播
        data: 图数据对象，包含 x, edge_index, edge_attr
        """
        try:
            # 确保数据在正确的设备上
            x = data.x.to(device)
            edge_index = data.edge_index.to(device)
            edge_attr = data.edge_attr.to(device)
            
            # 如果启用半精度，转换输入
            if self.use_fp16:
                x = x.half()
                edge_attr = edge_attr.half()
            
            # 应用图卷积层
            for i, (layer, bn) in enumerate(zip(self.graph_layers, self.batch_norms)):
                # 清理显存
                if i > 0:
                    torch.cuda.empty_cache()
                    
                # 应用当前层
                x_new = layer(x, edge_index, edge_attr=edge_attr)
                
                # 在非最后一层应用批归一化和残差连接
                if i < len(self.graph_layers) - 1:
                    if x.shape == x_new.shape:  # 只在维度匹配时应用残差连接
                        x = F.relu(bn(x_new) + x)
                    else:
                        x = F.relu(bn(x_new))
                    x = F.dropout(x, p=self.dropout, training=self.training)
                else:
                    x = x_new
            
            # 如果使用了半精度，在处理边特征前转回float32
            if self.use_fp16:
                x = x.float()
                edge_attr = edge_attr.float()
            
            # 处理边特征
            edge_features = self.edge_embedding(edge_attr)
            
            # 获取源节点和目标节点的特征
            source_nodes, target_nodes = edge_index
            source_features = x[source_nodes]
            target_features = x[target_nodes]
            
            # 计算节点对之间的注意力权重
            node_pairs = torch.cat([source_features, target_features], dim=1)
            attention_weights = self.attention(node_pairs)
            
            # 组合特征进行预测，加入注意力机制
            combined_features = torch.cat([
                source_features, 
                target_features, 
                edge_features * attention_weights  # 使用注意力权重调整边特征的重要性
            ], dim=1)
            
            # 拆分预测过程以减少内存使用
            torch.cuda.empty_cache()  # 清理显存
            
            # 第一层
            x = self.edge_fc1(combined_features)
            x = self.edge_bn1(x)
            x = F.relu(x)
            x = F.dropout(x, p=self.dropout, training=self.training)
            
            # 清理不需要的中间结果
            del combined_features
            torch.cuda.empty_cache()
            
            # 第二层
            x = F.relu(self.edge_fc2(x))
            x = F.dropout(x, p=self.dropout/2, training=self.training)
            
            # 第三层
            predictions = self.edge_fc3(x)
            
            # 如果存在均值和标准差，则逆标准化预测结果
            if hasattr(self, 'mean') and hasattr(self, 'std') and self.std != 0:
                predictions = predictions * self.std + self.mean
            
            # 清理缓存
            torch.cuda.empty_cache()
            
            return predictions
            
        except RuntimeError as e:
            print(f"ODFlowPredictionModel前向传播中发生错误: {str(e)}")
            # 尝试回退到简单的处理
            try:
                # 简单处理直接使用边特征预测
                if self.use_fp16:
                    edge_attr = edge_attr.float()
                
                # 简化的预测流程
                x = nn.Linear(edge_attr.shape[1], self.out_dim).to(device)(edge_attr)
                
                if hasattr(self, 'mean') and hasattr(self, 'std') and self.std != 0:
                    x = x * self.std + self.mean
                
                return x
            except:
                # 如果还是失败，返回零张量
                print("完全回退，返回零张量")
                return torch.zeros((edge_attr.size(0), self.out_dim), device=device)

class MetroFlowModel(nn.Module):
    """
    地铁站流量预测综合模型
    包括：进站流量预测、出站流量预测、OD流量预测
    """
    def __init__(self, 
                 node_features, 
                 edge_features, 
                 hidden_dim=256, 
                 out_dim=1, 
                 num_layers=4, 
                 dropout=0.1,
                 use_fp16=True):
        super(MetroFlowModel, self).__init__()
        
        self.node_features = node_features
        self.edge_features = edge_features
        self.hidden_dim = hidden_dim
        self.out_dim = out_dim
        self.num_layers = num_layers
        self.dropout = dropout
        
        # 使用半精度减少内存使用
        self.use_fp16 = use_fp16 and torch.cuda.is_available()
        self.fp16_enabled = False  # 跟踪是否已启用FP16
        
        # 启用半精度
        if self.use_fp16:
            try:
                # 创建scaler用于自动缩放
                self.fp16_enabled = True
                print("成功启用FP16精度，减少50%内存使用")
            except Exception as e:
                print(f"启用FP16精度失败: {e}")
                self.use_fp16 = False
        
        # 创建子模型
        print("初始化InFlowPredictionModel...")
        self.in_model = InFlowPredictionModel(
            node_features=node_features,
            hidden_dim=hidden_dim,
            out_dim=out_dim,
            num_layers=num_layers,
            dropout=dropout,
            use_attention=True,
            use_fp16=self.use_fp16
        )
        
        # 清理不必要的缓存
        torch.cuda.empty_cache()
        
        print("初始化OutFlowPredictionModel...")
        self.out_model = OutFlowPredictionModel(
            node_features=node_features,
            hidden_dim=hidden_dim,
            out_dim=out_dim,
            num_layers=num_layers,
            dropout=dropout,
            use_attention=True,
            use_fp16=self.use_fp16
        )
        
        # 清理不必要的缓存
        torch.cuda.empty_cache()
        
        print("初始化ODFlowPredictionModel...")
        self.od_model = ODFlowPredictionModel(
            node_features=node_features,
            edge_features=edge_features,
            hidden_dim=hidden_dim,
            out_dim=out_dim,
            num_layers=num_layers,
            dropout=dropout,
            use_fp16=self.use_fp16
        )
        
        # 清理不必要的缓存
        torch.cuda.empty_cache()
        
        # 将模型移动到设备
        self.to(device)
        print(f"MetroFlowModel初始化完成，使用设备: {device}")
        
    def forward(self, data, task='all', batch_size=1, num_nodes=None, num_time_steps=None):
        """
        前向传播
        data: 图数据对象
        task: 'in', 'out', 'od', 'all'
        """
        results = {}
        
        try:
            # 根据任务选择执行的模型
            if task in ['in', 'all']:
                with torch.cuda.amp.autocast(enabled=self.fp16_enabled):
                    results['in'] = self.in_model(data, batch_size, num_nodes, num_time_steps)
                    torch.cuda.empty_cache()
            
            if task in ['out', 'all']:
                with torch.cuda.amp.autocast(enabled=self.fp16_enabled):
                    results['out'] = self.out_model(data, batch_size, num_nodes, num_time_steps)
                    torch.cuda.empty_cache()
            
            if task in ['od', 'all']:
                with torch.cuda.amp.autocast(enabled=self.fp16_enabled):
                    results['od'] = self.od_model(data)
                    torch.cuda.empty_cache()
            
            # 如果是 'all' 任务，返回所有结果
            if task == 'all':
                return results
            # 否则返回特定任务的结果
            return results[task]
            
        except RuntimeError as e:
            print(f"MetroFlowModel前向传播中发生错误: {str(e)}")
            # 尝试回退到仅处理部分任务
            error_results = {}
            
            # 仍然尝试执行各个模型，但单独捕获异常
            if task in ['in', 'all']:
                try:
                    error_results['in'] = self.in_model(data)
                except Exception as e:
                    print(f"进站模型失败: {e}")
                    error_results['in'] = torch.zeros((data.x.size(0), self.out_dim), device=device)
            
            if task in ['out', 'all']:
                try:
                    error_results['out'] = self.out_model(data)
                except Exception as e:
                    print(f"出站模型失败: {e}")
                    error_results['out'] = torch.zeros((data.x.size(0), self.out_dim), device=device)
            
            if task in ['od', 'all']:
                try:
                    error_results['od'] = self.od_model(data)
                except Exception as e:
                    print(f"OD模型失败: {e}")
                    error_results['od'] = torch.zeros((data.edge_index.size(1), self.out_dim), device=device)
            
            # 返回回退结果
            if task == 'all':
                return error_results
            return error_results.get(task, torch.zeros((1, self.out_dim), device=device)) 