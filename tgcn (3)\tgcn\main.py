"""
主入口文件 - 改进版本（修复设备问题）
协调各个模块完成有向加权T-GCN训练和预测流程
"""
import torch
import numpy as np
from config import Config
from data_loader import DataLoader
from data_preprocessor_improved import ImprovedDataPreprocessor
from trainer_improved import ImprovedTrainer
from model_improved import DirectedWeightedTGCN
from utils import ModelUtils

def main():
    """主函数 - 改进版本"""
    # 初始化配置
    config = Config()
    
    # 设置随机种子
    ModelUtils.set_random_seed(config.RANDOM_SEED)
    
    # 设置设备
    device = torch.device(config.DEVICE if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    # 初始化各个模块
    data_loader = DataLoader(config)
    preprocessor = ImprovedDataPreprocessor(config)
    trainer = ImprovedTrainer(config)
    
    print("=" * 60)
    print("改进版T-GCN 地铁客流预测系统")
    print("支持有向加权图和多任务学习")
    print("=" * 60)
    
    try:
        # 第一步：加载数据
        print("\n🔄 第一步：加载原始数据...")
        in_data, out_data, od_data, station_features = data_loader.load_data()
        print("✅ 数据加载完成")
        
        # 第二步：数据预处理（构建有向加权图）
        print("\n🔄 第二步：数据预处理（构建有向加权图）...")
        (X, in_flows, out_flows, od_flows_tensor, edge_index, edge_weights,
         train_indices, test_indices, common_stations, train_stations, test_stations,
         station_to_idx, in_data_filtered, out_data_filtered, od_data_filtered) = preprocessor.preprocess_data(
            in_data, out_data, od_data, station_features
        )
        
        print("✅ 数据预处理完成")
        print(f"   📊 共同站点数量: {len(common_stations)}")
        print(f"   🚂 训练站点数量: {len(train_stations)}")
        print(f"   🧪 测试站点数量: {len(test_stations)}")
        print(f"   🔗 图边数量: {edge_index.shape[1]}")
        print(f"   📈 特征维度: {X.shape}")
        
        # 将数据移到指定设备
        print(f"\n🔄 将数据移动到设备: {device}")
        X = X.to(device)
        in_flows = in_flows.to(device)
        out_flows = out_flows.to(device)
        od_flows_tensor = od_flows_tensor.to(device)
        edge_index = edge_index.to(device)
        edge_weights = edge_weights.to(device)
        
        # 确保indices是numpy数组（不移动到GPU）
        if isinstance(train_indices, torch.Tensor):
            train_indices = train_indices.cpu().numpy()
        if isinstance(test_indices, torch.Tensor):
            test_indices = test_indices.cpu().numpy()
        
        print("✅ 数据移动完成")
        
        # 第三步：多任务模型训练
        print("\n🔄 第三步：多任务模型训练...")
        model = trainer.train_multitask_model(
            X, in_flows, out_flows, od_flows_tensor, edge_index, edge_weights,
            train_indices, test_indices, common_stations
        )
        print("✅ 模型训练完成")
        
        # 第四步：模型评估和预测
        print("\n🔄 第四步：模型评估和预测...")
        evaluation_results = evaluate_and_predict(
            model, X, in_flows, out_flows, od_flows_tensor, edge_index, edge_weights,
            train_indices, test_indices, common_stations, train_stations, test_stations,
            station_to_idx, in_data_filtered, out_data_filtered, od_data_filtered, config, device
        )
        print("✅ 评估和预测完成")
        
        # 第五步：保存结果
        print("\n🔄 第五步：保存预测结果...")
        save_prediction_results(
            evaluation_results, config, common_stations, test_stations,
            in_data_filtered, out_data_filtered, od_data_filtered
        )
        print("✅ 结果保存完成")
        
        # 打印最终结果摘要
        print_results_summary(evaluation_results)
        
        print("\n" + "=" * 60)
        print("🎉 程序执行完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 程序执行出错: {str(e)}")
        import traceback
        traceback.print_exc()
        raise

def evaluate_and_predict(model, X, in_flows, out_flows, od_flows_tensor, edge_index, edge_weights,
                        train_indices, test_indices, common_stations, train_stations, test_stations,
                        station_to_idx, in_data, out_data, od_data, config, device):
    """模型评估和预测"""
    
    # 加载最佳模型
    model.load_state_dict(torch.load('best_multitask_model.pth', map_location=device))
    model.eval()
    
    # 准备测试数据
    trainer = ImprovedTrainer(config)
    test_data = trainer._prepare_multitask_data(
        X, in_flows, out_flows, od_flows_tensor, edge_index, edge_weights, 
        train_indices, test_indices
    )
    
    evaluation_results = {}
    
    with torch.no_grad():
        # 测试集预测
        test_outputs = model(test_data['X_test'], test_data['edge_index_test'], test_data['edge_weights_test'])
        
        # 计算各任务的评估指标
        from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
        
        # 进站流量评估
        if 'in_flow' in test_outputs:
            in_pred = test_outputs['in_flow'].cpu().numpy().flatten()
            in_true = test_data['in_test'].cpu().numpy().flatten()
            
            evaluation_results['in_flow'] = {
                'predictions': in_pred,
                'true_values': in_true,
                'mae': mean_absolute_error(in_true, in_pred),
                'mse': mean_squared_error(in_true, in_pred),
                'rmse': np.sqrt(mean_squared_error(in_true, in_pred)),
                'r2': r2_score(in_true, in_pred)
            }
        
        # 出站流量评估
        if 'out_flow' in test_outputs:
            out_pred = test_outputs['out_flow'].cpu().numpy().flatten()
            out_true = test_data['out_test'].cpu().numpy().flatten()
            
            evaluation_results['out_flow'] = {
                'predictions': out_pred,
                'true_values': out_true,
                'mae': mean_absolute_error(out_true, out_pred),
                'mse': mean_squared_error(out_true, out_pred),
                'rmse': np.sqrt(mean_squared_error(out_true, out_pred)),
                'r2': r2_score(out_true, out_pred)
            }
        
        # OD流量评估
        if 'od_flow' in test_outputs:
            od_pred = test_outputs['od_flow'].cpu().numpy().flatten()
            od_true = test_data['od_test'].cpu().numpy().flatten()
            
            evaluation_results['od_flow'] = {
                'predictions': od_pred,
                'true_values': od_true,
                'mae': mean_absolute_error(od_true, od_pred),
                'mse': mean_squared_error(od_true, od_pred),
                'rmse': np.sqrt(mean_squared_error(od_true, od_pred)),
                'r2': r2_score(od_true, od_pred)
            }
        
        # 添加站点和边信息用于结果保存
        evaluation_results['test_stations'] = test_stations
        evaluation_results['test_edge_index'] = test_data['edge_index_test'].cpu().numpy()
        evaluation_results['station_to_idx'] = {station: i for i, station in enumerate(test_stations)}
    
    return evaluation_results

def save_prediction_results(evaluation_results, config, common_stations, test_stations,
                          in_data, out_data, od_data):
    """保存预测结果"""
    import pandas as pd
    import geopandas as gpd
    
    # 保存进站预测结果
    if 'in_flow' in evaluation_results:
        in_predictions_df = pd.DataFrame({
            'station': test_stations,
            'prediction': evaluation_results['in_flow']['predictions'],
            'true_value': evaluation_results['in_flow']['true_values']
        })
        
        # 合并到原始数据
        test_in_data = in_data[in_data['station'].isin(test_stations)].copy()
        test_in_data = pd.merge(test_in_data, in_predictions_df, on='station', how='left')
        
        # 保存为Shapefile
        if hasattr(test_in_data, 'geometry'):
            gdf_in_pred = gpd.GeoDataFrame(test_in_data, geometry='geometry')
            gdf_in_pred.to_file(config.OUTPUT_IN_PRED)
            print(f"   📁 进站预测结果已保存: {config.OUTPUT_IN_PRED}")
    
    # 保存出站预测结果
    if 'out_flow' in evaluation_results:
        out_predictions_df = pd.DataFrame({
            'station': test_stations,
            'prediction': evaluation_results['out_flow']['predictions'],
            'true_value': evaluation_results['out_flow']['true_values']
        })
        
        test_out_data = out_data[out_data['station'].isin(test_stations)].copy()
        test_out_data = pd.merge(test_out_data, out_predictions_df, on='station', how='left')
        
        if hasattr(test_out_data, 'geometry'):
            gdf_out_pred = gpd.GeoDataFrame(test_out_data, geometry='geometry')
            gdf_out_pred.to_file(config.OUTPUT_OUT_PRED)
            print(f"   📁 出站预测结果已保存: {config.OUTPUT_OUT_PRED}")
    
    # 保存OD预测结果
    if 'od_flow' in evaluation_results:
        edge_index = evaluation_results['test_edge_index']
        station_to_idx = evaluation_results['station_to_idx']
        
        od_predictions = []
        for i, (pred, true) in enumerate(zip(evaluation_results['od_flow']['predictions'], 
                                           evaluation_results['od_flow']['true_values'])):
            if i < edge_index.shape[1]:  # 确保索引不越界
                src_idx = edge_index[0, i]
                dst_idx = edge_index[1, i]
                
                # 跳过自环
                if src_idx != dst_idx and src_idx < len(test_stations) and dst_idx < len(test_stations):
                    src_station = test_stations[src_idx]
                    dst_station = test_stations[dst_idx]
                    
                    od_predictions.append({
                        'o_rawname': src_station,
                        'd_rawname': dst_station,
                        'prediction': pred,
                        'true_value': true
                    })
        
        od_pred_df = pd.DataFrame(od_predictions)
        od_pred_df.to_csv(config.OUTPUT_OD_PRED, index=False)
        print(f"   📁 OD预测结果已保存: {config.OUTPUT_OD_PRED}")
    
    # 保存综合比较结果
    comparison_results = []
    
    for task_name, task_results in evaluation_results.items():
        if isinstance(task_results, dict) and 'predictions' in task_results:
            for i, (pred, true) in enumerate(zip(task_results['predictions'], task_results['true_values'])):
                if task_name == 'od_flow':
                    station_name = f"Edge_{i}"
                else:
                    station_name = test_stations[i] if i < len(test_stations) else f"Node_{i}"
                
                comparison_results.append({
                    'Task': task_name,
                    'Index': i,
                    'Station': station_name,
                    'Predicted_Value': pred,
                    'True_Value': true,
                    'Absolute_Error': abs(pred - true),
                    'Relative_Error': abs(pred - true) / (abs(true) + 1e-8)
                })
    
    comparison_df = pd.DataFrame(comparison_results)
    comparison_df.to_csv(config.OUTPUT_COMPARISON, index=False)
    print(f"   📁 预测比较结果已保存: {config.OUTPUT_COMPARISON}")

def print_results_summary(evaluation_results):
    """打印结果摘要"""
    print("\n" + "=" * 60)
    print("📊 模型评估结果摘要")
    print("=" * 60)
    
    for task_name, task_results in evaluation_results.items():
        if isinstance(task_results, dict) and 'mae' in task_results:
            print(f"\n🎯 {task_name.upper()} 预测结果:")
            print(f"   MAE:  {task_results['mae']:.4f}")
            print(f"   RMSE: {task_results['rmse']:.4f}")
            print(f"   R²:   {task_results['r2']:.4f}")
            
            # 判断预测质量
            if task_results['r2'] > 0.8:
                quality = "🌟 优秀"
            elif task_results['r2'] > 0.6:
                quality = "👍 良好"
            elif task_results['r2'] > 0.4:
                quality = "🤔 一般"
            else:
                quality = "❌ 较差"
            
            print(f"   预测质量: {quality}")

if __name__ == "__main__":
    main()



