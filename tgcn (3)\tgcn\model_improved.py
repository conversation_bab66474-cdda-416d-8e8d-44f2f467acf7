"""
改进的T-GCN模型 - 支持有向加权图和多任务学习
"""
import torch
import torch.nn as nn
import torch.nn.functional as F

class DirectedWeightedTGCN(nn.Module):
    def __init__(self, input_dim, hidden_dim, num_nodes, output_tasks=['in_flow', 'out_flow', 'od_flow']):
        super(DirectedWeightedTGCN, self).__init__()
        
        self.hidden_dim = hidden_dim
        self.num_nodes = num_nodes
        self.output_tasks = output_tasks
        
        # 有向图卷积层
        self.gcn_layers = nn.ModuleList([
            DirectedGCNLayer(input_dim, hidden_dim),
            DirectedGCNLayer(hidden_dim, hidden_dim)
        ])
        
        # 时序建模层
        self.temporal_conv = nn.Conv1d(hidden_dim, hidden_dim, kernel_size=3, padding=1)
        self.gru = nn.GRU(hidden_dim, hidden_dim, batch_first=True)
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(hidden_dim, num_heads=4, batch_first=True)
        
        # 多任务输出头
        self.output_heads = nn.ModuleDict()
        
        if 'in_flow' in output_tasks:
            self.output_heads['in_flow'] = nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim // 2),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(hidden_dim // 2, 1)
            )
        
        if 'out_flow' in output_tasks:
            self.output_heads['out_flow'] = nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim // 2),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(hidden_dim // 2, 1)
            )
        
        if 'od_flow' in output_tasks:
            self.output_heads['od_flow'] = nn.Sequential(
                nn.Linear(hidden_dim * 2, hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(hidden_dim, 1)
            )
        
        # Dropout和批标准化
        self.dropout = nn.Dropout(0.2)
        self.batch_norm = nn.BatchNorm1d(hidden_dim)
    
    def forward(self, x, edge_index, edge_weights):
        """
        前向传播
        
        Args:
            x: [batch_size, time_steps, num_nodes, input_dim]
            edge_index: [2, num_edges]
            edge_weights: [num_edges]
        
        Returns:
            dict: 多任务预测结果
        """
        batch_size, time_steps, num_nodes, input_dim = x.shape
        
        # 时序图卷积
        temporal_features = []
        
        for t in range(time_steps):
            current_x = x[:, t]  # [batch_size, num_nodes, input_dim]
            
            # 多层有向图卷积
            h = current_x
            for gcn_layer in self.gcn_layers:
                h = gcn_layer(h, edge_index, edge_weights)
                h = F.relu(h)
                h = self.dropout(h)
            
            temporal_features.append(h.unsqueeze(1))
        
        # 合并时序特征 [batch_size, time_steps, num_nodes, hidden_dim]
        temporal_features = torch.cat(temporal_features, dim=1)
        
        # 时序卷积
        batch_size, time_steps, num_nodes, hidden_dim = temporal_features.shape
        
        # 重塑以应用时序卷积 [batch_size * num_nodes, hidden_dim, time_steps]
        temp_reshaped = temporal_features.permute(0, 2, 3, 1).reshape(-1, hidden_dim, time_steps)
        temp_conv = self.temporal_conv(temp_reshaped)
        temp_conv = temp_conv.reshape(batch_size, num_nodes, hidden_dim, time_steps).permute(0, 3, 1, 2)
        
        # GRU处理每个节点的时序
        final_features = []
        for node in range(num_nodes):
            node_sequence = temp_conv[:, :, node, :]  # [batch_size, time_steps, hidden_dim]
            gru_out, _ = self.gru(node_sequence)
            final_features.append(gru_out[:, -1:, :])  # 取最后时间步
        
        # [batch_size, num_nodes, hidden_dim]
        node_features = torch.cat(final_features, dim=1)
        
        # 注意力机制
        attn_features, _ = self.attention(node_features, node_features, node_features)
        
        # 残差连接
        final_features = attn_features + node_features
        
        # 多任务预测
        outputs = {}
        
        # 节点级别预测（进站流量、出站流量）
        if 'in_flow' in self.output_tasks:
            outputs['in_flow'] = self.output_heads['in_flow'](final_features).squeeze(-1)
        
        if 'out_flow' in self.output_tasks:
            outputs['out_flow'] = self.output_heads['out_flow'](final_features).squeeze(-1)
        
        # OD流量预测（边级别预测）
        if 'od_flow' in self.output_tasks:
            od_predictions = []
            
            for i in range(edge_index.shape[1]):
                src_idx = edge_index[0, i]
                dst_idx = edge_index[1, i]
                
                # 连接源节点和目标节点特征
                src_features = final_features[:, src_idx, :]
                dst_features = final_features[:, dst_idx, :]
                edge_features = torch.cat([src_features, dst_features], dim=-1)
                
                # 预测该边的流量
                od_pred = self.output_heads['od_flow'](edge_features)
                od_predictions.append(od_pred)
            
            outputs['od_flow'] = torch.stack(od_predictions, dim=1).squeeze(-1)  # [batch_size, num_edges]
        
        return outputs

class DirectedGCNLayer(nn.Module):
    """有向图卷积层"""
    
    def __init__(self, input_dim, output_dim):
        super(DirectedGCNLayer, self).__init__()
        self.input_dim = input_dim
        self.output_dim = output_dim
        
        # 分别处理入边和出边
        self.weight_in = nn.Parameter(torch.FloatTensor(input_dim, output_dim))
        self.weight_out = nn.Parameter(torch.FloatTensor(input_dim, output_dim))
        self.weight_self = nn.Parameter(torch.FloatTensor(input_dim, output_dim))
        
        self.bias = nn.Parameter(torch.FloatTensor(output_dim))
        
        self.reset_parameters()
    
    def reset_parameters(self):
        nn.init.xavier_uniform_(self.weight_in)
        nn.init.xavier_uniform_(self.weight_out)
        nn.init.xavier_uniform_(self.weight_self)
        nn.init.zeros_(self.bias)
    
    def forward(self, x, edge_index, edge_weights):
        """
        有向图卷积操作
        
        Args:
            x: [batch_size, num_nodes, input_dim]
            edge_index: [2, num_edges]
            edge_weights: [num_edges]
        """
        batch_size, num_nodes, input_dim = x.shape
        
        # 自连接
        out = torch.matmul(x, self.weight_self)
        
        # 处理有向边
        for i in range(edge_index.shape[1]):
            src_idx = edge_index[0, i]
            dst_idx = edge_index[1, i]
            weight = edge_weights[i]
            
            if src_idx != dst_idx:  # 跳过自环，已在上面处理
                # 出边：从src到dst
                out[:, dst_idx] += weight * torch.matmul(x[:, src_idx].unsqueeze(1), self.weight_out).squeeze(1)
                
                # 入边：从dst到src (反向)
                out[:, src_idx] += weight * torch.matmul(x[:, dst_idx].unsqueeze(1), self.weight_in).squeeze(1)
        
        return out + self.bias 