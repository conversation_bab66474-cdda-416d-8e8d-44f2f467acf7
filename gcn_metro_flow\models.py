"""
纯GCN+时间特征深度学习模型
支持时序预测和时空模式学习
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import GCNConv, GATConv, global_mean_pool, global_max_pool
from torch_geometric.data import Batch
import numpy as np
from typing import Dict, List, Optional, Tuple

from config import MetroFlowConfig

class TemporalEncoder(nn.Module):
    """时间编码器"""

    def __init__(self, time_embed_dim: int = 32):
        super(TemporalEncoder, self).__init__()
        self.time_embed_dim = time_embed_dim

        # 小时嵌入
        self.hour_embedding = nn.Embedding(24, time_embed_dim // 2)

        # 时间段嵌入
        self.period_embedding = nn.Embedding(4, time_embed_dim // 4)  # 4个时间段

        # 周期性编码
        self.time_projection = nn.Linear(2, time_embed_dim // 4)  # sin/cos编码

    def forward(self, hour: torch.Tensor) -> torch.Tensor:
        """
        Args:
            hour: [batch_size] 小时数 (0-23)
        Returns:
            time_features: [batch_size, time_embed_dim] 时间特征
        """
        batch_size = hour.size(0)

        # 小时嵌入
        hour_embed = self.hour_embedding(hour)  # [batch_size, time_embed_dim//2]

        # 时间段编码
        period = torch.zeros_like(hour)
        period[(hour >= 6) & (hour < 10)] = 1  # 早高峰
        period[(hour >= 17) & (hour < 20)] = 2  # 晚高峰
        period[(hour >= 10) & (hour < 17)] = 3  # 平峰
        # 其他时间为0 (低峰)

        period_embed = self.period_embedding(period)  # [batch_size, time_embed_dim//4]

        # 周期性编码
        hour_float = hour.float()
        sin_hour = torch.sin(2 * np.pi * hour_float / 24).unsqueeze(-1)
        cos_hour = torch.cos(2 * np.pi * hour_float / 24).unsqueeze(-1)
        cyclic_features = torch.cat([sin_hour, cos_hour], dim=-1)  # [batch_size, 2]
        cyclic_embed = self.time_projection(cyclic_features)  # [batch_size, time_embed_dim//4]

        # 合并所有时间特征
        time_features = torch.cat([hour_embed, period_embed, cyclic_embed], dim=-1)

        return time_features

class SpatialGCN(nn.Module):
    """空间图卷积网络"""

    def __init__(self, input_dim: int, hidden_dim: int, num_layers: int = 3, dropout: float = 0.3):
        super(SpatialGCN, self).__init__()

        self.num_layers = num_layers
        self.dropout = dropout

        # GCN层
        self.gcn_layers = nn.ModuleList()
        self.gcn_layers.append(GCNConv(input_dim, hidden_dim))

        for _ in range(num_layers - 2):
            self.gcn_layers.append(GCNConv(hidden_dim, hidden_dim))

        if num_layers > 1:
            self.gcn_layers.append(GCNConv(hidden_dim, hidden_dim))

        # 批归一化
        self.batch_norms = nn.ModuleList([
            nn.BatchNorm1d(hidden_dim) for _ in range(num_layers)
        ])

        # Dropout
        self.dropout_layer = nn.Dropout(dropout)

        # 残差连接
        self.use_residual = input_dim == hidden_dim
        if not self.use_residual:
            self.input_projection = nn.Linear(input_dim, hidden_dim)

    def forward(self, x: torch.Tensor, edge_index: torch.Tensor,
                edge_weight: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Args:
            x: [num_nodes, input_dim] 节点特征
            edge_index: [2, num_edges] 边索引
            edge_weight: [num_edges] 边权重
        Returns:
            node_embeddings: [num_nodes, hidden_dim] 节点嵌入
        """
        # 输入投影
        if self.use_residual:
            residual = x
        else:
            residual = self.input_projection(x)

        # GCN层
        for i, gcn_layer in enumerate(self.gcn_layers):
            x = gcn_layer(x, edge_index, edge_weight)

            # 批归一化
            if x.size(0) > 1:  # 避免单样本批归一化错误
                x = self.batch_norms[i](x)

            # 激活函数
            x = F.relu(x)

            # Dropout
            x = self.dropout_layer(x)

            # 残差连接 (仅在第一层)
            if i == 0 and self.use_residual:
                x = x + residual

        return x

class TemporalGCN(nn.Module):
    """时空图卷积网络"""

    def __init__(self, config: MetroFlowConfig):
        super(TemporalGCN, self).__init__()

        self.config = config

        # 配置参数
        self.geo_feature_dim = config.get('model.geo_feature_dim', 51)
        self.time_embed_dim = config.get('model.time_embed_dim', 32)
        self.hidden_dim = config.get('model.gcn_hidden_dim', 128)
        self.num_layers = config.get('model.gcn_layers', 3)
        self.dropout = config.get('model.gcn_dropout', 0.3)
        self.sequence_length = config.get('data_processing.sequence_length', 12)
        self.prediction_horizon = config.get('data_processing.prediction_horizon', 6)

        # 时间编码器
        self.temporal_encoder = TemporalEncoder(self.time_embed_dim)

        # 空间GCN
        spatial_input_dim = self.geo_feature_dim + self.time_embed_dim
        self.spatial_gcn = SpatialGCN(
            input_dim=spatial_input_dim,
            hidden_dim=self.hidden_dim,
            num_layers=self.num_layers,
            dropout=self.dropout
        )

        # 时序建模 (LSTM)
        self.temporal_lstm = nn.LSTM(
            input_size=self.hidden_dim,
            hidden_size=self.hidden_dim,
            num_layers=2,
            dropout=self.dropout if self.num_layers > 1 else 0,
            batch_first=True
        )

        # 注意力机制
        self.temporal_attention = nn.MultiheadAttention(
            embed_dim=self.hidden_dim,
            num_heads=8,
            dropout=self.dropout
        )

        # 输出投影
        self.output_projection = nn.Linear(self.hidden_dim, self.hidden_dim)

    def forward(self, node_features: torch.Tensor, edge_index: torch.Tensor,
                edge_weight: Optional[torch.Tensor], hours: torch.Tensor,
                sequence_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Args:
            node_features: [seq_len, num_nodes, geo_feature_dim] 节点地理特征序列
            edge_index: [2, num_edges] 边索引
            edge_weight: [seq_len, num_edges] 边权重序列
            hours: [seq_len] 小时序列
            sequence_mask: [seq_len] 序列掩码
        Returns:
            spatiotemporal_embeddings: [seq_len, num_nodes, hidden_dim] 时空嵌入
        """
        seq_len, num_nodes, _ = node_features.shape

        # 时空特征融合
        spatiotemporal_embeddings = []

        for t in range(seq_len):
            # 时间编码
            hour_t = hours[t:t+1].expand(num_nodes)  # [num_nodes]
            time_features = self.temporal_encoder(hour_t)  # [num_nodes, time_embed_dim]

            # 空间-时间特征融合
            node_feat_t = node_features[t]  # [num_nodes, geo_feature_dim]
            combined_features = torch.cat([node_feat_t, time_features], dim=-1)

            # 空间GCN
            edge_weight_t = edge_weight[t] if edge_weight is not None else None
            spatial_embed = self.spatial_gcn(combined_features, edge_index, edge_weight_t)

            spatiotemporal_embeddings.append(spatial_embed)

        # 堆叠时序嵌入
        spatiotemporal_embeddings = torch.stack(spatiotemporal_embeddings, dim=0)
        # [seq_len, num_nodes, hidden_dim]

        # 时序建模
        # 重塑为 [num_nodes, seq_len, hidden_dim] 用于LSTM
        embeddings_reshaped = spatiotemporal_embeddings.transpose(0, 1)

        # LSTM处理
        lstm_out, _ = self.temporal_lstm(embeddings_reshaped)
        # [num_nodes, seq_len, hidden_dim]

        # 注意力机制
        # 重塑为 [seq_len, num_nodes, hidden_dim] 用于注意力
        lstm_out = lstm_out.transpose(0, 1)

        attended_out, _ = self.temporal_attention(
            lstm_out, lstm_out, lstm_out,
            key_padding_mask=sequence_mask.unsqueeze(1).expand(-1, num_nodes) if sequence_mask is not None else None
        )

        # 输出投影
        final_embeddings = self.output_projection(attended_out)

        return final_embeddings

class StationFlowPredictor(nn.Module):
    """站点流量预测模型（进出站）"""

    def __init__(self, config: MetroFlowConfig):
        super(StationFlowPredictor, self).__init__()

        self.config = config
        self.hidden_dim = config.get('model.gcn_hidden_dim', 128)
        self.dropout = config.get('model.gcn_dropout', 0.3)
        self.prediction_horizon = config.get('data_processing.prediction_horizon', 6)

        # 时空GCN主干网络
        self.temporal_gcn = TemporalGCN(config)

        # 流量预测头
        self.flow_predictor = nn.Sequential(
            nn.Linear(self.hidden_dim, self.hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.hidden_dim // 2, self.hidden_dim // 4),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.hidden_dim // 4, 2 * self.prediction_horizon)  # 进站+出站 * 预测时长
        )

        # 流量分解器
        self.flow_decomposer = nn.ModuleDict({
            'trend': nn.Linear(self.hidden_dim, self.prediction_horizon),
            'seasonal': nn.Linear(self.hidden_dim, self.prediction_horizon),
            'residual': nn.Linear(self.hidden_dim, self.prediction_horizon)
        })

    def forward(self, node_features: torch.Tensor, edge_index: torch.Tensor,
                edge_weight: Optional[torch.Tensor], hours: torch.Tensor,
                target_hours: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        Args:
            node_features: [seq_len, num_nodes, geo_feature_dim] 历史节点特征
            edge_index: [2, num_edges] 边索引
            edge_weight: [seq_len, num_edges] 历史边权重
            hours: [seq_len] 历史小时
            target_hours: [prediction_horizon] 目标预测小时
        Returns:
            predictions: 包含进出站流量预测的字典
        """
        # 时空特征提取
        spatiotemporal_embeddings = self.temporal_gcn(
            node_features, edge_index, edge_weight, hours
        )  # [seq_len, num_nodes, hidden_dim]

        # 使用最后一个时间步的嵌入进行预测
        final_embeddings = spatiotemporal_embeddings[-1]  # [num_nodes, hidden_dim]

        # 流量预测
        flow_predictions = self.flow_predictor(final_embeddings)
        # [num_nodes, 2 * prediction_horizon]

        num_nodes = final_embeddings.size(0)

        # 分离进出站预测
        in_flow_pred = flow_predictions[:, :self.prediction_horizon]
        out_flow_pred = flow_predictions[:, self.prediction_horizon:]

        # 流量分解 (可选的高级特性)
        trend_in = self.flow_decomposer['trend'](final_embeddings)
        seasonal_in = self.flow_decomposer['seasonal'](final_embeddings)
        residual_in = self.flow_decomposer['residual'](final_embeddings)

        # 组合分解结果
        decomposed_in = trend_in + seasonal_in + residual_in

        return {
            'in_flow': in_flow_pred,
            'out_flow': out_flow_pred,
            'in_flow_decomposed': decomposed_in,
            'embeddings': final_embeddings,
            'spatiotemporal_embeddings': spatiotemporal_embeddings
        }

class ODFlowPredictor(nn.Module):
    """OD流量预测模型"""

    def __init__(self, config: MetroFlowConfig):
        super(ODFlowPredictor, self).__init__()

        self.config = config
        self.hidden_dim = config.get('model.gcn_hidden_dim', 128)
        self.dropout = config.get('model.gcn_dropout', 0.3)
        self.prediction_horizon = config.get('data_processing.prediction_horizon', 6)

        # 时空GCN主干网络
        self.temporal_gcn = TemporalGCN(config)

        # OD特征处理器
        self.od_feature_processor = nn.Sequential(
            nn.Linear(4, 32),  # 4个OD特征：距离、换乘、时间、等待时间
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(32, 16),
            nn.ReLU(),
            nn.Dropout(self.dropout)
        )

        # OD交互建模
        self.od_interaction = nn.MultiheadAttention(
            embed_dim=self.hidden_dim,
            num_heads=8,
            dropout=self.dropout
        )

        # OD流量预测头
        self.od_flow_head = nn.Sequential(
            nn.Linear(self.hidden_dim * 2 + 16, self.hidden_dim),  # 起点+终点嵌入+OD特征
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.hidden_dim, self.hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.hidden_dim // 2, self.prediction_horizon)  # 预测未来多个时间步
        )
    
    def forward(self, data, od_data):
        """前向传播"""
        x, edge_index = data.x, data.edge_index
        edge_weight = data.edge_attr.squeeze() if data.edge_attr is not None else None
        
        # GCN特征提取
        node_embeddings = self.gcn(x, edge_index, edge_weight)
        
        if od_data is None:
            return {'od_flow': torch.tensor([])}
        
        # 获取OD对的嵌入
        od_pairs = od_data['od_pairs']
        od_features = od_data['od_features']
        
        origin_embeddings = node_embeddings[od_pairs[:, 0]]
        dest_embeddings = node_embeddings[od_pairs[:, 1]]
        
        # 处理OD特征
        processed_od_features = self.od_feature_processor(od_features)
        
        # 合并特征
        combined_features = torch.cat([
            origin_embeddings,
            dest_embeddings,
            processed_od_features
        ], dim=1)
        
        # 预测OD流量
        od_flow = self.od_flow_head(combined_features).squeeze()
        
        return {
            'od_flow': od_flow,
            'embeddings': node_embeddings
        }

class GridFlowAllocator(nn.Module):
    """栅格流量分配模型"""
    
    def __init__(self, grid_feature_dim):
        super(GridFlowAllocator, self).__init__()
        
        hidden_dim = MODEL_CONFIG['gcn_hidden_dim']
        
        # 栅格特征处理
        self.grid_feature_processor = nn.Sequential(
            nn.Linear(grid_feature_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(MODEL_CONFIG['gcn_dropout']),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(MODEL_CONFIG['gcn_dropout'])
        )
        
        # 站点-栅格关系建模
        self.station_grid_attention = nn.MultiheadAttention(
            embed_dim=hidden_dim // 2,
            num_heads=4,
            dropout=MODEL_CONFIG['gcn_dropout']
        )
        
        # 分配比例预测
        self.allocation_head = nn.Sequential(
            nn.Linear(hidden_dim // 2, 32),
            nn.ReLU(),
            nn.Dropout(MODEL_CONFIG['gcn_dropout']),
            nn.Linear(32, 1),
            nn.Sigmoid()  # 输出0-1之间的分配比例
        )
    
    def forward(self, station_embeddings, grid_features, station_grid_mapping):
        """
        前向传播
        Args:
            station_embeddings: 站点嵌入 [num_stations, hidden_dim]
            grid_features: 栅格特征 [num_grids, grid_feature_dim]
            station_grid_mapping: 站点-栅格映射关系
        """
        # 处理栅格特征
        processed_grid_features = self.grid_feature_processor(grid_features)
        
        # 注意力机制建模站点-栅格关系
        # 这里简化处理，实际应该根据地理距离建立映射
        grid_embeddings, _ = self.station_grid_attention(
            processed_grid_features.unsqueeze(0),
            processed_grid_features.unsqueeze(0),
            processed_grid_features.unsqueeze(0)
        )
        grid_embeddings = grid_embeddings.squeeze(0)
        
        # 预测分配比例
        allocation_ratios = self.allocation_head(grid_embeddings).squeeze()
        
        return allocation_ratios

class MetroFlowGCN(nn.Module):
    """完整的地铁流量预测模型"""
    
    def __init__(self, grid_feature_dim):
        super(MetroFlowGCN, self).__init__()
        
        # 子模型
        self.station_flow_predictor = StationFlowPredictor()
        self.od_flow_predictor = ODFlowPredictor()
        self.grid_flow_allocator = GridFlowAllocator(grid_feature_dim)
        
        # 损失权重
        self.station_loss_weight = 1.0
        self.od_loss_weight = 1.0
        self.grid_loss_weight = 0.5
    
    def forward(self, data, od_data=None, grid_features=None, station_grid_mapping=None):
        """前向传播"""
        results = {}
        
        # 站点流量预测
        station_results = self.station_flow_predictor(data)
        results.update(station_results)
        
        # OD流量预测
        if od_data is not None:
            od_results = self.od_flow_predictor(data, od_data)
            results['od_flow'] = od_results['od_flow']
        
        # 栅格流量分配
        if grid_features is not None and station_grid_mapping is not None:
            allocation_ratios = self.grid_flow_allocator(
                station_results['embeddings'],
                grid_features,
                station_grid_mapping
            )
            results['grid_allocation'] = allocation_ratios
        
        return results
    
    def compute_loss(self, predictions, targets, od_targets=None):
        """计算损失"""
        losses = {}
        total_loss = 0
        
        # 站点流量损失
        if 'in_flow' in predictions and 'in_flow' in targets:
            in_flow_loss = F.mse_loss(predictions['in_flow'], targets['in_flow'])
            losses['in_flow_loss'] = in_flow_loss
            total_loss += self.station_loss_weight * in_flow_loss
        
        if 'out_flow' in predictions and 'out_flow' in targets:
            out_flow_loss = F.mse_loss(predictions['out_flow'], targets['out_flow'])
            losses['out_flow_loss'] = out_flow_loss
            total_loss += self.station_loss_weight * out_flow_loss
        
        # OD流量损失
        if 'od_flow' in predictions and od_targets is not None:
            if len(predictions['od_flow']) > 0 and len(od_targets) > 0:
                od_flow_loss = F.mse_loss(predictions['od_flow'], od_targets)
                losses['od_flow_loss'] = od_flow_loss
                total_loss += self.od_loss_weight * od_flow_loss
        
        losses['total_loss'] = total_loss
        return losses
