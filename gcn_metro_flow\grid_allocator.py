"""
栅格流量分配模块
"""
import numpy as np
import pandas as pd
import geopandas as gpd
import torch
import torch.nn as nn
import torch.nn.functional as F
from sklearn.neighbors import BallTree
from sklearn.preprocessing import StandardScaler
from geopy.distance import geodesic
import warnings
warnings.filterwarnings('ignore')

from config import DATA_CONFIG, MODEL_CONFIG, OUTPUT_FILES

class GridFlowAllocator:
    """栅格流量分配器"""
    
    def __init__(self, data_loader, predictor):
        self.data_loader = data_loader
        self.predictor = predictor
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 构建站点-栅格映射关系
        self.station_grid_mapping = self._build_station_grid_mapping()
        
        # 初始化分配模型
        self.allocation_model = GridAllocationModel().to(self.device)
        
        # 栅格预测结果
        self.grid_predictions = {
            'in_flow': {},  # {grid_id: {hour: flow}}
            'out_flow': {}
        }
    
    def _build_station_grid_mapping(self):
        """构建站点-栅格映射关系"""
        print("Building station-grid mapping...")
        
        if (self.data_loader.in_flow_data is None or 
            self.data_loader.grid_features is None):
            print("Warning: Missing data for station-grid mapping")
            return {}
        
        # 获取站点坐标
        station_coords = {}
        for _, row in self.data_loader.in_flow_data.iterrows():
            station = row['station_clean']
            if station not in station_coords:
                station_coords[station] = (row['latitude'], row['longitude'])
        
        # 获取栅格坐标（假设从栅格ID中解析或从几何信息中获取）
        grid_coords = self._extract_grid_coordinates()
        
        # 构建映射关系
        mapping = {}
        radius_km = DATA_CONFIG['station_radius_km']
        
        for station, (lat, lon) in station_coords.items():
            nearby_grids = []
            
            for grid_id, (grid_lat, grid_lon) in grid_coords.items():
                distance = geodesic((lat, lon), (grid_lat, grid_lon)).kilometers
                
                if distance <= radius_km:
                    nearby_grids.append({
                        'grid_id': grid_id,
                        'distance': distance,
                        'weight': self._calculate_distance_weight(distance, radius_km)
                    })
            
            # 按距离排序
            nearby_grids.sort(key=lambda x: x['distance'])
            mapping[station] = nearby_grids
        
        print(f"Built mapping for {len(mapping)} stations")
        return mapping
    
    def _extract_grid_coordinates(self):
        """提取栅格坐标"""
        # 这里需要根据实际的栅格数据格式来实现
        # 假设栅格ID包含坐标信息或者有单独的几何字段
        
        grid_coords = {}
        
        # 方法1：从栅格ID解析坐标（如果ID格式包含坐标信息）
        if 'grid_ids' in self.data_loader.grid_features:
            for grid_id in self.data_loader.grid_features['grid_ids']:
                # 假设grid_id格式为 "lat_lon" 或类似格式
                # 这里需要根据实际格式调整
                try:
                    # 示例解析逻辑，需要根据实际数据调整
                    parts = str(grid_id).split('_')
                    if len(parts) >= 2:
                        lat = float(parts[0]) / 1000000  # 假设是微度格式
                        lon = float(parts[1]) / 1000000
                        grid_coords[grid_id] = (lat, lon)
                except:
                    # 如果解析失败，使用默认坐标
                    grid_coords[grid_id] = (39.9042, 116.4074)  # 北京中心
        
        # 方法2：从几何信息获取（如果有geometry字段）
        elif 'geometries' in self.data_loader.grid_features:
            geometries = self.data_loader.grid_features['geometries']
            if geometries is not None:
                for i, geom in enumerate(geometries):
                    try:
                        # 假设geometry是点或多边形的中心点
                        if hasattr(geom, 'centroid'):
                            centroid = geom.centroid
                            grid_coords[i] = (centroid.y, centroid.x)
                        else:
                            grid_coords[i] = (39.9042, 116.4074)
                    except:
                        grid_coords[i] = (39.9042, 116.4074)
        
        # 如果没有坐标信息，生成虚拟坐标
        if not grid_coords:
            print("Warning: No grid coordinates found, generating virtual coordinates")
            num_grids = len(self.data_loader.grid_features['features'])
            for i in range(num_grids):
                # 在北京周围生成随机坐标
                lat = 39.9042 + np.random.normal(0, 0.1)
                lon = 116.4074 + np.random.normal(0, 0.1)
                grid_coords[i] = (lat, lon)
        
        return grid_coords
    
    def _calculate_distance_weight(self, distance, max_distance):
        """计算距离权重"""
        # 使用反距离权重
        if distance == 0:
            return 1.0
        else:
            return 1.0 / (1.0 + distance / max_distance)
    
    def train_allocation_model(self):
        """训练栅格分配模型"""
        print("Training grid allocation model...")
        
        # 准备训练数据
        train_data = self._prepare_allocation_training_data()
        
        if not train_data:
            print("Warning: No training data for allocation model")
            return
        
        # 训练参数
        optimizer = torch.optim.Adam(self.allocation_model.parameters(), lr=0.001)
        criterion = nn.MSELoss()
        
        # 训练循环
        self.allocation_model.train()
        
        for epoch in range(100):  # 简化训练，实际可以更多epoch
            total_loss = 0
            
            for batch in train_data:
                station_features = batch['station_features'].to(self.device)
                grid_features = batch['grid_features'].to(self.device)
                target_ratios = batch['target_ratios'].to(self.device)
                
                # 前向传播
                predicted_ratios = self.allocation_model(station_features, grid_features)
                
                # 计算损失
                loss = criterion(predicted_ratios, target_ratios)
                
                # 反向传播
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
                
                total_loss += loss.item()
            
            if epoch % 20 == 0:
                print(f"Allocation training epoch {epoch}, loss: {total_loss/len(train_data):.4f}")
        
        print("Grid allocation model training completed!")
    
    def _prepare_allocation_training_data(self):
        """准备分配模型训练数据"""
        # 这里需要根据实际数据构建训练样本
        # 简化实现，实际应该使用历史数据构建站点-栅格流量分配关系
        
        train_data = []
        
        # 为每个站点构建训练样本
        for station, grid_list in self.station_grid_mapping.items():
            if not grid_list:
                continue
            
            # 获取站点特征
            if station in self.data_loader.station_to_idx:
                station_idx = self.data_loader.station_to_idx[station]
                station_features = self.data_loader.station_features[station_idx]
            else:
                continue
            
            # 获取栅格特征
            grid_features_list = []
            target_ratios = []
            
            for grid_info in grid_list:
                grid_id = grid_info['grid_id']
                weight = grid_info['weight']
                
                # 获取栅格特征
                if isinstance(grid_id, int) and grid_id < len(self.data_loader.grid_features['features']):
                    grid_feat = self.data_loader.grid_features['features'][grid_id]
                    grid_features_list.append(grid_feat)
                    target_ratios.append(weight)
            
            if len(grid_features_list) > 0:
                # 归一化目标比例
                target_ratios = np.array(target_ratios)
                target_ratios = target_ratios / target_ratios.sum()
                
                train_data.append({
                    'station_features': torch.tensor(station_features, dtype=torch.float),
                    'grid_features': torch.tensor(grid_features_list, dtype=torch.float),
                    'target_ratios': torch.tensor(target_ratios, dtype=torch.float)
                })
        
        return train_data
    
    def allocate_station_flows_to_grids(self):
        """将站点流量分配到栅格"""
        print("Allocating station flows to grids...")
        
        self.allocation_model.eval()
        
        with torch.no_grad():
            for hour in range(24):
                if hour not in self.predictor.hourly_results:
                    continue
                
                station_data = self.predictor.hourly_results[hour]['station_predictions']
                stations = station_data['stations']
                in_flows = station_data['in_flow_pred']
                out_flows = station_data['out_flow_pred']
                
                # 为每个站点分配流量到栅格
                for i, station in enumerate(stations):
                    if station not in self.station_grid_mapping:
                        continue
                    
                    station_in_flow = in_flows[i]
                    station_out_flow = out_flows[i]
                    
                    # 获取该站点的栅格列表
                    grid_list = self.station_grid_mapping[station]
                    
                    if not grid_list:
                        continue
                    
                    # 预测分配比例
                    allocation_ratios = self._predict_allocation_ratios(station, grid_list)
                    
                    # 分配流量到栅格
                    for j, grid_info in enumerate(grid_list):
                        grid_id = grid_info['grid_id']
                        ratio = allocation_ratios[j] if j < len(allocation_ratios) else 0
                        
                        # 分配进站流量
                        if grid_id not in self.grid_predictions['in_flow']:
                            self.grid_predictions['in_flow'][grid_id] = {}
                        
                        if hour not in self.grid_predictions['in_flow'][grid_id]:
                            self.grid_predictions['in_flow'][grid_id][hour] = 0
                        
                        self.grid_predictions['in_flow'][grid_id][hour] += station_in_flow * ratio
                        
                        # 分配出站流量
                        if grid_id not in self.grid_predictions['out_flow']:
                            self.grid_predictions['out_flow'][grid_id] = {}
                        
                        if hour not in self.grid_predictions['out_flow'][grid_id]:
                            self.grid_predictions['out_flow'][grid_id][hour] = 0
                        
                        self.grid_predictions['out_flow'][grid_id][hour] += station_out_flow * ratio
        
        print("Grid flow allocation completed!")
    
    def _predict_allocation_ratios(self, station, grid_list):
        """预测分配比例"""
        # 简化实现：使用距离权重作为分配比例
        weights = [grid_info['weight'] for grid_info in grid_list]
        weights = np.array(weights)
        
        # 归一化
        if weights.sum() > 0:
            ratios = weights / weights.sum()
        else:
            ratios = np.ones(len(weights)) / len(weights)
        
        return ratios
    
    def save_grid_predictions(self):
        """保存栅格预测结果"""
        print("Saving grid predictions...")
        
        # 保存进站栅格预测
        self._save_grid_flow_predictions('in')
        
        # 保存出站栅格预测
        self._save_grid_flow_predictions('out')
        
        print("Grid predictions saved!")
    
    def _save_grid_flow_predictions(self, flow_type):
        """保存栅格流量预测结果"""
        # 创建预测结果列表
        predictions_list = []
        
        flow_data = self.grid_predictions[f'{flow_type}_flow']
        
        for grid_id, hour_flows in flow_data.items():
            for hour, flow in hour_flows.items():
                predictions_list.append({
                    'grid_id': grid_id,
                    'hour': hour,
                    'prediction': flow
                })
        
        if not predictions_list:
            print(f"Warning: No {flow_type} flow grid predictions to save")
            return
        
        pred_df = pd.DataFrame(predictions_list)
        
        # 读取原始栅格数据（如果有的话）
        if flow_type == 'in':
            output_file = OUTPUT_FILES['in_predictions'].replace('.shp', '_grid.csv')
        else:
            output_file = OUTPUT_FILES['out_predictions'].replace('.shp', '_grid.csv')
        
        # 保存为CSV
        pred_df.to_csv(output_file, index=False)
        print(f"Saved {flow_type} flow grid predictions to {output_file}")

class GridAllocationModel(nn.Module):
    """栅格分配模型"""
    
    def __init__(self):
        super(GridAllocationModel, self).__init__()
        
        station_feature_dim = MODEL_CONFIG['geo_feature_dim']
        grid_feature_dim = MODEL_CONFIG['geo_feature_dim']
        hidden_dim = 64
        
        # 站点特征处理
        self.station_processor = nn.Sequential(
            nn.Linear(station_feature_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        # 栅格特征处理
        self.grid_processor = nn.Sequential(
            nn.Linear(grid_feature_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(
            embed_dim=hidden_dim,
            num_heads=4,
            dropout=0.3
        )
        
        # 分配比例预测
        self.allocation_head = nn.Sequential(
            nn.Linear(hidden_dim, 32),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(32, 1),
            nn.Sigmoid()
        )
    
    def forward(self, station_features, grid_features):
        """
        前向传播
        Args:
            station_features: [station_feature_dim]
            grid_features: [num_grids, grid_feature_dim]
        """
        # 处理站点特征
        station_embed = self.station_processor(station_features)
        station_embed = station_embed.unsqueeze(0).unsqueeze(0)  # [1, 1, hidden_dim]
        
        # 处理栅格特征
        grid_embeds = self.grid_processor(grid_features)  # [num_grids, hidden_dim]
        grid_embeds = grid_embeds.unsqueeze(0)  # [1, num_grids, hidden_dim]
        
        # 注意力机制
        attended_grids, _ = self.attention(
            station_embed,  # query
            grid_embeds,    # key
            grid_embeds     # value
        )
        
        attended_grids = attended_grids.squeeze(0)  # [num_grids, hidden_dim]
        
        # 预测分配比例
        allocation_ratios = self.allocation_head(attended_grids).squeeze(-1)
        
        # 归一化（确保和为1）
        allocation_ratios = F.softmax(allocation_ratios, dim=0)
        
        return allocation_ratios
