#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
地铁流量预测系统示例运行脚本
"""

import os
import sys

# 添加项目路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.append(project_root)

from main import main
import argparse


def run_example():
    """运行示例"""
    print("=" * 60)
    print("地铁流量预测系统 - 示例运行")
    print("=" * 60)
    
    # 设置示例参数
    sys.argv = [
        'run_example.py',
        '--data_path', 'C:/Users/<USER>/Desktop/接驳/',
        '--epochs', '20',
        '--patience', '5',
        '--spatial_hidden_dim', '128',
        '--temporal_embed_dim', '64',
        '--num_gcn_layers', '3',
        '--dropout', '0.2',
        '--distance_threshold', '1000',
        '--output_dir', './outputs',
        '--use_float16',
        '--save_predictions'
    ]
    
    try:
        # 运行主程序
        main()
        
        print("\n" + "=" * 60)
        print("示例运行完成！")
        print("请检查 ./outputs 目录中的结果文件")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n运行出错: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    run_example() 