import torch
import torch.nn as nn
import torch.optim as optim
from torch.optim.lr_scheduler import ReduceLROnPlateau
import numpy as np
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
import os
import json
from datetime import datetime


class FlowBalanceLoss(nn.Module):
    """流量平衡损失函数"""
    def __init__(self, balance_weight=0.1):
        super(FlowBalanceLoss, self).__init__()
        self.balance_weight = balance_weight
        self.mse_loss = nn.MSELoss()
        self.mae_loss = nn.L1Loss()
        
    def forward(self, predictions, targets, flow_type='all'):
        """
        Args:
            predictions: [batch_size, num_stations, 3] or [num_stations, 3]
            targets: [batch_size, num_stations, 3] or [num_stations, 3]
            flow_type: 'in', 'out', 'od', or 'all'
        """
        # 基础预测损失
        if flow_type == 'all':
            pred_loss = self.mse_loss(predictions, targets)
            mae_loss = self.mae_loss(predictions, targets)
            
            # 流量平衡约束：进站 ≈ 出站
            if predictions.shape[-1] >= 2:
                in_flow = predictions[..., 0]  # 进站流量
                out_flow = predictions[..., 1]  # 出站流量
                balance_loss = self.mse_loss(in_flow, out_flow)
                
                total_loss = pred_loss + mae_loss + self.balance_weight * balance_loss
                
                return total_loss, {
                    'mse_loss': pred_loss.item(),
                    'mae_loss': mae_loss.item(), 
                    'balance_loss': balance_loss.item(),
                    'total_loss': total_loss.item()
                }
            else:
                total_loss = pred_loss + mae_loss
                return total_loss, {
                    'mse_loss': pred_loss.item(),
                    'mae_loss': mae_loss.item(),
                    'total_loss': total_loss.item()
                }
        else:
            pred_loss = self.mse_loss(predictions, targets)
            mae_loss = self.mae_loss(predictions, targets)
            total_loss = pred_loss + mae_loss
            
            return total_loss, {
                'mse_loss': pred_loss.item(),
                'mae_loss': mae_loss.item(),
                'total_loss': total_loss.item()
            }


class MetroFlowTrainer:
    """地铁流量预测训练器"""
    
    def __init__(self, model, device='cuda' if torch.cuda.is_available() else 'cpu'):
        self.model = model.to(device)
        self.device = device
        self.criterion = FlowBalanceLoss()
        
        # 优化器
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=0.001,
            weight_decay=1e-4
        )
        
        # 学习率调度器
        self.scheduler = ReduceLROnPlateau(
            self.optimizer,
            mode='min',
            factor=0.5,
            patience=3,
            verbose=True
        )
        
        # 训练历史
        self.train_history = {
            'loss': [],
            'mae': [],
            'mse': [],
            'balance_loss': []
        }
        
        self.val_history = {
            'loss': [],
            'mae': [],
            'mse': [],
            'balance_loss': []
        }
        
        # 最佳模型
        self.best_val_loss = float('inf')
        self.best_model_state = None
        self.patience_counter = 0
        
    def train_epoch(self, data_loader):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        total_mae = 0
        total_mse = 0
        total_balance = 0
        num_batches = 0
        
        pbar = tqdm(data_loader, desc="Training")
        
        for batch_idx, batch_data in enumerate(pbar):
            self.optimizer.zero_grad()
            
            # 前向传播
            node_features = batch_data['node_features'].to(self.device)
            edge_index = batch_data['edge_index'].to(self.device)
            time_features = batch_data['time_features'].to(self.device)
            
            # 目标值：[时间步, 站点数, 任务数]
            in_flows = batch_data['in_flows'].to(self.device)  # [时间步, 站点数]
            out_flows = batch_data['out_flows'].to(self.device)  # [时间步, 站点数]
            
            batch_losses = []
            batch_maes = []
            batch_mses = []
            batch_balances = []
            
            # 对每个时间步进行预测
            for t in range(len(time_features)):
                # 获取当前时间步的时间特征
                current_time = time_features[t:t+1]
                
                # 模型预测
                predictions = self.model(node_features, edge_index, current_time)
                
                # 构建目标张量 [站点数, 2] (进站, 出站)
                targets = torch.stack([in_flows[t], out_flows[t]], dim=1)
                
                # 只使用前两个预测任务（进站、出站）
                pred_subset = predictions[:, :2]
                
                # 计算损失
                loss, loss_dict = self.criterion(pred_subset, targets)
                
                batch_losses.append(loss)
                batch_maes.append(loss_dict['mae_loss'])
                batch_mses.append(loss_dict['mse_loss'])
                if 'balance_loss' in loss_dict:
                    batch_balances.append(loss_dict['balance_loss'])
            
            # 平均所有时间步的损失
            avg_loss = torch.stack(batch_losses).mean()
            avg_mae = np.mean(batch_maes)
            avg_mse = np.mean(batch_mses)
            avg_balance = np.mean(batch_balances) if batch_balances else 0
            
            # 反向传播
            avg_loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            self.optimizer.step()
            
            # 累计损失
            total_loss += avg_loss.item()
            total_mae += avg_mae
            total_mse += avg_mse
            total_balance += avg_balance
            num_batches += 1
            
            # 更新进度条
            pbar.set_postfix({
                'Loss': f'{avg_loss.item():.4f}',
                'MAE': f'{avg_mae:.4f}',
                'MSE': f'{avg_mse:.4f}'
            })
        
        # 计算平均损失
        avg_epoch_loss = total_loss / num_batches
        avg_epoch_mae = total_mae / num_batches
        avg_epoch_mse = total_mse / num_batches
        avg_epoch_balance = total_balance / num_batches
        
        return {
            'loss': avg_epoch_loss,
            'mae': avg_epoch_mae,
            'mse': avg_epoch_mse,
            'balance_loss': avg_epoch_balance
        }
    
    def validate_epoch(self, data_loader):
        """验证一个epoch"""
        self.model.eval()
        total_loss = 0
        total_mae = 0
        total_mse = 0
        total_balance = 0
        num_batches = 0
        
        with torch.no_grad():
            for batch_data in tqdm(data_loader, desc="Validation"):
                node_features = batch_data['node_features'].to(self.device)
                edge_index = batch_data['edge_index'].to(self.device)
                time_features = batch_data['time_features'].to(self.device)
                
                in_flows = batch_data['in_flows'].to(self.device)
                out_flows = batch_data['out_flows'].to(self.device)
                
                batch_losses = []
                batch_maes = []
                batch_mses = []
                batch_balances = []
                
                for t in range(len(time_features)):
                    current_time = time_features[t:t+1]
                    predictions = self.model(node_features, edge_index, current_time)
                    
                    targets = torch.stack([in_flows[t], out_flows[t]], dim=1)
                    pred_subset = predictions[:, :2]
                    
                    loss, loss_dict = self.criterion(pred_subset, targets)
                    
                    batch_losses.append(loss)
                    batch_maes.append(loss_dict['mae_loss'])
                    batch_mses.append(loss_dict['mse_loss'])
                    if 'balance_loss' in loss_dict:
                        batch_balances.append(loss_dict['balance_loss'])
                
                avg_loss = torch.stack(batch_losses).mean()
                avg_mae = np.mean(batch_maes)
                avg_mse = np.mean(batch_mses)
                avg_balance = np.mean(batch_balances) if batch_balances else 0
                
                total_loss += avg_loss.item()
                total_mae += avg_mae
                total_mse += avg_mse
                total_balance += avg_balance
                num_batches += 1
        
        avg_epoch_loss = total_loss / num_batches
        avg_epoch_mae = total_mae / num_batches
        avg_epoch_mse = total_mse / num_batches
        avg_epoch_balance = total_balance / num_batches
        
        return {
            'loss': avg_epoch_loss,
            'mae': avg_epoch_mae,
            'mse': avg_epoch_mse,
            'balance_loss': avg_epoch_balance
        }
    
    def train(self, train_loader, val_loader, epochs=20, patience=5, save_dir='./checkpoints'):
        """完整训练过程"""
        os.makedirs(save_dir, exist_ok=True)
        
        print(f"开始训练，共 {epochs} 个epoch")
        print(f"设备: {self.device}")
        print(f"模型参数数量: {sum(p.numel() for p in self.model.parameters())}")
        
        for epoch in range(epochs):
            print(f"\nEpoch {epoch+1}/{epochs}")
            print("-" * 50)
            
            # 训练
            train_metrics = self.train_epoch(train_loader)
            
            # 验证
            val_metrics = self.validate_epoch(val_loader)
            
            # 更新学习率
            self.scheduler.step(val_metrics['loss'])
            
            # 记录历史
            for key in self.train_history:
                if key in train_metrics:
                    self.train_history[key].append(train_metrics[key])
                    
            for key in self.val_history:
                if key in val_metrics:
                    self.val_history[key].append(val_metrics[key])
            
            # 打印结果
            print(f"训练 - Loss: {train_metrics['loss']:.4f}, "
                  f"MAE: {train_metrics['mae']:.4f}, "
                  f"MSE: {train_metrics['mse']:.4f}")
            print(f"验证 - Loss: {val_metrics['loss']:.4f}, "
                  f"MAE: {val_metrics['mae']:.4f}, "
                  f"MSE: {val_metrics['mse']:.4f}")
            
            # 保存最佳模型
            if val_metrics['loss'] < self.best_val_loss:
                self.best_val_loss = val_metrics['loss']
                self.best_model_state = self.model.state_dict().copy()
                self.patience_counter = 0
                
                # 保存最佳模型
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': self.best_model_state,
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'scheduler_state_dict': self.scheduler.state_dict(),
                    'best_val_loss': self.best_val_loss,
                    'train_history': self.train_history,
                    'val_history': self.val_history
                }, os.path.join(save_dir, 'best_model.pth'))
                
                print(f"保存最佳模型，验证损失: {self.best_val_loss:.4f}")
            else:
                self.patience_counter += 1
            
            # 早停
            if self.patience_counter >= patience:
                print(f"验证损失连续 {patience} 个epoch未改善，早停训练")
                break
        
        # 加载最佳模型
        if self.best_model_state is not None:
            self.model.load_state_dict(self.best_model_state)
            print("已加载最佳模型权重")
    
    def evaluate(self, test_loader, save_results=True, save_dir='./results'):
        """评估模型"""
        print("开始评估模型...")
        self.model.eval()
        
        all_predictions = []
        all_targets = []
        all_mae_per_hour = []
        
        os.makedirs(save_dir, exist_ok=True)
        
        with torch.no_grad():
            for batch_data in tqdm(test_loader, desc="Evaluating"):
                node_features = batch_data['node_features'].to(self.device)
                edge_index = batch_data['edge_index'].to(self.device)
                time_features = batch_data['time_features'].to(self.device)
                
                in_flows = batch_data['in_flows'].to(self.device)
                out_flows = batch_data['out_flows'].to(self.device)
                
                for t in range(len(time_features)):
                    current_time = time_features[t:t+1]
                    predictions = self.model(node_features, edge_index, current_time)
                    
                    targets = torch.stack([in_flows[t], out_flows[t]], dim=1)
                    pred_subset = predictions[:, :2]
                    
                    # 转换为numpy
                    pred_np = pred_subset.cpu().numpy()
                    target_np = targets.cpu().numpy()
                    
                    all_predictions.append(pred_np)
                    all_targets.append(target_np)
                    
                    # 计算每小时的MAE
                    mae_per_station = np.abs(pred_np - target_np).mean(axis=1)
                    all_mae_per_hour.append(mae_per_station.mean())
        
        # 合并所有预测结果
        all_predictions = np.concatenate(all_predictions, axis=0)
        all_targets = np.concatenate(all_targets, axis=0)
        
        # 计算评估指标
        mae_in = mean_absolute_error(all_targets[:, 0], all_predictions[:, 0])
        mae_out = mean_absolute_error(all_targets[:, 1], all_predictions[:, 1])
        mae_overall = mean_absolute_error(all_targets.flatten(), all_predictions.flatten())
        
        mse_in = mean_squared_error(all_targets[:, 0], all_predictions[:, 0])
        mse_out = mean_squared_error(all_targets[:, 1], all_predictions[:, 1])
        mse_overall = mean_squared_error(all_targets.flatten(), all_predictions.flatten())
        
        r2_in = r2_score(all_targets[:, 0], all_predictions[:, 0])
        r2_out = r2_score(all_targets[:, 1], all_predictions[:, 1])
        r2_overall = r2_score(all_targets.flatten(), all_predictions.flatten())
        
        # 评估结果
        results = {
            'mae': {
                'in_station': mae_in,
                'out_station': mae_out,
                'overall': mae_overall,
                'per_hour': all_mae_per_hour
            },
            'mse': {
                'in_station': mse_in,
                'out_station': mse_out,
                'overall': mse_overall
            },
            'r2': {
                'in_station': r2_in,
                'out_station': r2_out,
                'overall': r2_overall
            },
            'predictions': all_predictions.tolist(),
            'targets': all_targets.tolist()
        }
        
        print("\n评估结果:")
        print(f"整体 MAE: {mae_overall:.4f}")
        print(f"进站 MAE: {mae_in:.4f}")
        print(f"出站 MAE: {mae_out:.4f}")
        print(f"整体 MSE: {mse_overall:.4f}")
        print(f"整体 R²: {r2_overall:.4f}")
        
        if save_results:
            # 保存结果
            with open(os.path.join(save_dir, 'evaluation_results.json'), 'w') as f:
                json.dump(results, f, indent=2)
            
            # 绘制结果图
            self.plot_results(results, save_dir)
        
        return results
    
    def plot_results(self, results, save_dir):
        """绘制结果图表"""
        # 训练历史
        plt.figure(figsize=(15, 10))
        
        # 损失曲线
        plt.subplot(2, 3, 1)
        plt.plot(self.train_history['loss'], label='训练损失')
        plt.plot(self.val_history['loss'], label='验证损失')
        plt.title('训练损失')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend()
        plt.grid(True)
        
        # MAE曲线
        plt.subplot(2, 3, 2)
        plt.plot(self.train_history['mae'], label='训练MAE')
        plt.plot(self.val_history['mae'], label='验证MAE')
        plt.title('平均绝对误差')
        plt.xlabel('Epoch')
        plt.ylabel('MAE')
        plt.legend()
        plt.grid(True)
        
        # 每小时MAE
        plt.subplot(2, 3, 3)
        plt.plot(results['mae']['per_hour'])
        plt.title('每小时MAE')
        plt.xlabel('时间步')
        plt.ylabel('MAE')
        plt.grid(True)
        
        # 预测 vs 真实值散点图（进站）
        plt.subplot(2, 3, 4)
        targets_in = np.array(results['targets'])[:, 0]
        preds_in = np.array(results['predictions'])[:, 0]
        plt.scatter(targets_in, preds_in, alpha=0.5)
        plt.plot([targets_in.min(), targets_in.max()], 
                 [targets_in.min(), targets_in.max()], 'r--')
        plt.title('进站流量预测 vs 真实值')
        plt.xlabel('真实值')
        plt.ylabel('预测值')
        plt.grid(True)
        
        # 预测 vs 真实值散点图（出站）
        plt.subplot(2, 3, 5)
        targets_out = np.array(results['targets'])[:, 1]
        preds_out = np.array(results['predictions'])[:, 1]
        plt.scatter(targets_out, preds_out, alpha=0.5)
        plt.plot([targets_out.min(), targets_out.max()], 
                 [targets_out.min(), targets_out.max()], 'r--')
        plt.title('出站流量预测 vs 真实值')
        plt.xlabel('真实值')
        plt.ylabel('预测值')
        plt.grid(True)
        
        # 残差分布
        plt.subplot(2, 3, 6)
        residuals = np.array(results['predictions']).flatten() - np.array(results['targets']).flatten()
        plt.hist(residuals, bins=50, alpha=0.7)
        plt.title('残差分布')
        plt.xlabel('残差')
        plt.ylabel('频次')
        plt.grid(True)
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'evaluation_plots.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"结果图表已保存至: {save_dir}/evaluation_plots.png")
    
    def predict_new_line_impact(self, new_stations, new_connections, base_data):
        """预测新线路对流量的影响"""
        print("分析新线路影响...")
        
        # 这里需要根据具体需求实现
        # 可以通过修改图结构来模拟新线路的影响
        
        results = {
            'baseline_flow': {},
            'new_line_flow': {},
            'impact_analysis': {}
        }
        
        return results 