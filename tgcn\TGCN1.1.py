import os
import pandas as pd
import numpy as np
import geopandas as gpd
import torch
import torch.nn as nn
import torch.optim as optim
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_absolute_error
import matplotlib.pyplot as plt

# 路径设置
BASE_PATH = "C:\\Users\\<USER>\\Desktop\\接驳\\"
IN_FILE = os.path.join(BASE_PATH, "in_500_with_coords.shp")
OUT_FILE = os.path.join(BASE_PATH, "out_500_with_coords.shp")
OD_FILE = os.path.join(BASE_PATH, "updated北京市_subway_od_2024_modified3.csv")
FEATURES_FILE = os.path.join(BASE_PATH, "station_features_result.csv")


# 读取数据
def load_data():
    print("读取数据...")
    in_data = gpd.read_file(IN_FILE)
    out_data = gpd.read_file(OUT_FILE)

    # 提取栅格中心点坐标
    in_data['longitude'] = in_data.geometry.centroid.x
    in_data['latitude'] = in_data.geometry.centroid.y
    out_data['longitude'] = out_data.geometry.centroid.x
    out_data['latitude'] = out_data.geometry.centroid.y

    od_data = pd.read_csv(OD_FILE)
    station_features = pd.read_csv(FEATURES_FILE)

    print(f"进站数据形状: {in_data.shape}")
    print(f"出站数据形状: {out_data.shape}")
    print(f"OD数据形状: {od_data.shape}")
    print(f"站点特征数据形状: {station_features.shape}")

    return in_data, out_data, od_data, station_features


# 数据预处理
def preprocess_data(in_data, out_data, od_data, station_features):
    print("预处理数据...")
    # 标准化站点特征
    feature_columns = station_features.columns.drop('站名')
    scaler = StandardScaler()
    station_features[feature_columns] = scaler.fit_transform(station_features[feature_columns])

    # 获取共同站点
    in_stations = set(in_data['station'].unique())
    out_stations = set(out_data['station'].unique())
    feature_stations = set(station_features['站名'].unique())

    common_stations = list(in_stations & out_stations & feature_stations)
    print(f"共同站点数量: {len(common_stations)}")

    # 过滤数据，只保留共同站点
    in_data = in_data[in_data['station'].isin(common_stations)]
    out_data = out_data[out_data['station'].isin(common_stations)]
    station_features = station_features[station_features['站名'].isin(common_stations)]

    # 创建站点索引映射
    station_to_idx = {station: idx for idx, station in enumerate(common_stations)}
    print(f"处理后的站点数量: {len(station_to_idx)}")

    # 构建稀疏邻接矩阵 (基于OD数据)
    print("正在构建图结构...")
    n_stations = len(common_stations)

    # 使用scipy的稀疏矩阵来节省内存
    from scipy.sparse import lil_matrix
    adj_matrix = lil_matrix((n_stations, n_stations), dtype=np.float32)

    edge_count = 0
    # 只处理共同站点的OD数据
    od_subset = od_data[
        (od_data['o_rawname'].isin(common_stations)) &
        (od_data['d_rawname'].isin(common_stations))
        ]

    for _, row in od_subset.iterrows():
        o_station = row['o_rawname']
        d_station = row['d_rawname']

        o_idx = station_to_idx[o_station]
        d_idx = station_to_idx[d_station]

        # 用流量作为边权重
        adj_matrix[o_idx, d_idx] = 1
        adj_matrix[d_idx, o_idx] = 1  # 无向图
        edge_count += 2  # 算上双向边

    # 确保图是连通的（添加自环）
    for i in range(n_stations):
        if adj_matrix[i, i] == 0:
            adj_matrix[i, i] = 1
            edge_count += 1

    print(f"图边数量: {edge_count}")

    # 转换为PyTorch稀疏张量格式
    adj_indices = adj_matrix.nonzero()
    edge_index = torch.LongTensor([adj_indices[0], adj_indices[1]])

    # 准备时序特征矩阵
    print("正在准备时空特征...")

    # 每小时的特征
    time_steps = 24
    station_count = len(common_stations)
    feature_dim = len(feature_columns)

    # 初始化特征矩阵 (时间, 站点, 特征)
    X = np.zeros((time_steps, station_count, feature_dim + 2))  # +2 表示进站和出站流量

    # 构建站点特征字典以快速查询
    station_feature_dict = {}
    for _, row in station_features.iterrows():
        station = row['站名']
        if station in station_to_idx:
            station_feature_dict[station] = row[feature_columns].values

    # 构建流量矩阵 (时间, 站点)
    flow_matrix = np.zeros((time_steps, station_count))

    # 填充进站流量
    for _, row in in_data.iterrows():
        station = row['station']
        hour = int(row['hour'])
        count = float(row['count'])

        if station in station_to_idx:
            idx = station_to_idx[station]
            X[hour, idx, 0] = count  # 第一个特征是进站流量
            flow_matrix[hour, idx] = count  # 将进站流量作为目标

    # 填充出站流量
    for _, row in out_data.iterrows():
        station = row['station']
        hour = int(row['hour'])
        count = float(row['count'])

        if station in station_to_idx:
            idx = station_to_idx[station]
            X[hour, idx, 1] = count  # 第二个特征是出站流量

    # 填充站点特征
    for station, idx in station_to_idx.items():
        if station in station_feature_dict:
            features = station_feature_dict[station]
            for hour in range(time_steps):
                X[hour, idx, 2:] = features

    print(f"特征矩阵形状: {X.shape}")
    print(f"流量矩阵形状: {flow_matrix.shape}")

    # 划分训练集和测试集
    train_ratio = 0.8
    n_train_stations = int(station_count * train_ratio)

    # 随机选择训练站点
    train_indices = np.random.choice(station_count, n_train_stations, replace=False)
    test_indices = np.array([i for i in range(station_count) if i not in train_indices])

    # 获取训练站点和测试站点名称
    train_stations = [common_stations[i] for i in train_indices]
    test_stations = [common_stations[i] for i in test_indices]

    # 创建站点到索引的映射（基于测试集）
    test_station_to_idx = {station: i for i, station in enumerate(test_stations)}

    # 返回处理后的数据
    return (X, flow_matrix, edge_index, train_indices, test_indices,
            common_stations, train_stations, test_stations,
            station_to_idx, test_station_to_idx, in_data, out_data, od_data)


# 定义优化版本的T-GCN模型
class TGCN(nn.Module):
    def __init__(self, input_dim, hidden_dim, output_dim=1, num_nodes=None):
        super(TGCN, self).__init__()

        self.hidden_dim = hidden_dim
        self.num_nodes = num_nodes

        # GCN权重
        self.weight = nn.Parameter(torch.FloatTensor(input_dim, hidden_dim))
        self.bias = nn.Parameter(torch.FloatTensor(hidden_dim))

        # GRU单元参数
        self.ru_gate_weight = nn.Parameter(torch.FloatTensor(hidden_dim + hidden_dim, hidden_dim * 2))
        self.ru_gate_bias = nn.Parameter(torch.FloatTensor(hidden_dim * 2))

        self.candidate_weight = nn.Parameter(torch.FloatTensor(hidden_dim + hidden_dim, hidden_dim))
        self.candidate_bias = nn.Parameter(torch.FloatTensor(hidden_dim))

        # 输出层
        self.output_layer = nn.Linear(hidden_dim, output_dim)

        # 初始化参数
        self.reset_parameters()

    def reset_parameters(self):
        nn.init.xavier_uniform_(self.weight)
        nn.init.zeros_(self.bias)

        nn.init.xavier_uniform_(self.ru_gate_weight)
        nn.init.zeros_(self.ru_gate_bias)

        nn.init.xavier_uniform_(self.candidate_weight)
        nn.init.zeros_(self.candidate_bias)

    def gcn_operation(self, x, adj):
        # 简化的GCN操作
        batch_size, num_nodes, input_dim = x.shape

        # 线性变换
        x = x.reshape(-1, input_dim)
        x = torch.matmul(x, self.weight) + self.bias

        # 重塑以应用邻接矩阵
        x = x.reshape(batch_size, num_nodes, self.hidden_dim)

        # 图卷积操作
        output = torch.zeros_like(x)

        # 对每个批次应用图卷积
        for b in range(batch_size):
            # 稀疏邻接矩阵乘法
            adj_indices = adj[0], adj[1]
            out_nodes, in_nodes = adj_indices

            # 收集邻居节点特征
            for i in range(adj.shape[1]):
                out_node = out_nodes[i].item()
                in_node = in_nodes[i].item()
                output[b, out_node] += x[b, in_node]

        return output

    def gru_operation(self, x, h):
        # GRU操作
        batch_size, num_nodes, _ = x.shape

        # 合并输入和隐藏状态
        combined = torch.cat([x, h], dim=2)
        combined = combined.reshape(-1, self.hidden_dim * 2)

        # 计算重置门和更新门
        ru_gates = torch.matmul(combined, self.ru_gate_weight) + self.ru_gate_bias
        ru_gates = ru_gates.reshape(batch_size, num_nodes, self.hidden_dim * 2)

        r, u = torch.split(torch.sigmoid(ru_gates), self.hidden_dim, dim=2)

        # 计算候选隐藏状态
        combined_r = torch.cat([x, r * h], dim=2)
        combined_r = combined_r.reshape(-1, self.hidden_dim * 2)

        c = torch.matmul(combined_r, self.candidate_weight) + self.candidate_bias
        c = c.reshape(batch_size, num_nodes, self.hidden_dim)
        c = torch.tanh(c)

        # 更新隐藏状态
        new_h = u * h + (1 - u) * c

        return new_h

    def forward(self, x, adj):
        """
        x: [batch_size, time_steps, num_nodes, input_dim]
        adj: 稀疏邻接矩阵的边索引
        """
        batch_size, time_steps, num_nodes, input_dim = x.shape

        # 初始化隐藏状态
        h = torch.zeros(batch_size, num_nodes, self.hidden_dim, device=x.device)

        # 对每个时间步进行处理
        for t in range(time_steps):
            # 获取当前时间步的输入
            current_input = x[:, t]

            # 执行GCN操作
            gcn_out = self.gcn_operation(current_input, adj)

            # 执行GRU操作
            h = self.gru_operation(gcn_out, h)

        # 输出层
        output = self.output_layer(h)

        return output.squeeze(-1)  # [batch_size, num_nodes]


# 训练和评估
def train_and_evaluate(X, flow_matrix, edge_index, train_indices, test_indices, common_stations,
                       train_stations, test_stations, station_to_idx, test_station_to_idx, in_data, out_data, od_data):
    print("准备训练数据...")

    # 模型参数
    input_dim = X.shape[2]  # 特征维度
    hidden_dim = 32  # 降低隐藏层维度以节省内存
    time_steps = X.shape[0]  # 时间步数
    num_nodes = X.shape[1]  # 节点数量

    # 将数据转换为PyTorch张量
    X_tensor = torch.FloatTensor(X)
    flow_tensor = torch.FloatTensor(flow_matrix)

    # 创建训练集和测试集
    X_train = X_tensor[:, train_indices]
    X_test = X_tensor[:, test_indices]

    y_train = flow_tensor[:, train_indices]
    y_test = flow_tensor[:, test_indices]

    # 重新安排数据维度为 [batch_size(1), time_steps, num_nodes, features]
    X_train = X_train.unsqueeze(0)
    X_test = X_test.unsqueeze(0)

    # 重新安排目标维度为 [batch_size(1), time_steps, num_nodes]
    y_train = y_train.unsqueeze(0)
    y_test = y_test.unsqueeze(0)

    # 初始化模型
    model = TGCN(input_dim, hidden_dim, output_dim=1, num_nodes=len(train_indices))

    # 损失函数和优化器
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=0.01)

    # 训练参数
    num_epochs = 100

    # 训练循环
    print("开始训练...")
    all_epoch_loss = []
    all_epoch_mae = []

    for epoch in range(num_epochs):
        model.train()

        # 前向传播
        outputs = model(X_train, edge_index)

        # 计算损失 (只计算进站流量预测的损失)
        loss = criterion(outputs, y_train[:, -1])  # 使用最后一个时间步的流量作为目标

        # 反向传播和优化
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()

        # 评估
        model.eval()
        with torch.no_grad():
            test_outputs = model(X_test, edge_index)
            test_loss = criterion(test_outputs, y_test[:, -1])

            # 计算MAE
            mae = torch.mean(torch.abs(test_outputs - y_test[:, -1]))

            # 记录损失和MAE
            all_epoch_loss.append(loss.item())
            all_epoch_mae.append(mae.item())

            print(
                f'Epoch {epoch + 1}/{num_epochs}, Loss: {loss.item():.4f}, Test Loss: {test_loss.item():.4f}, MAE: {mae.item():.4f}')

    # 绘制训练曲线
    plt.figure(figsize=(12, 5))

    plt.subplot(1, 2, 1)
    plt.plot(all_epoch_loss)
    plt.title('Training Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')

    plt.subplot(1, 2, 2)
    plt.plot(all_epoch_mae)
    plt.title('Validation MAE')
    plt.xlabel('Epoch')
    plt.ylabel('MAE')

    plt.tight_layout()
    plt.savefig(os.path.join(BASE_PATH, 'training_curves.png'))

    # 生成预测结果
    print("生成预测结果...")
    model.eval()
    with torch.no_grad():
        # 进行预测
        test_predictions = model(X_test, edge_index).cpu().numpy()[0]  # [num_test_nodes]

        # 创建预测结果DataFrame
        prediction_df = pd.DataFrame()

        # 对于测试集中的每个站点
        for i, station_idx in enumerate(test_indices):
            station_name = common_stations[station_idx]
            prediction = test_predictions[i]

            # 将预测添加到DataFrame
            prediction_df = prediction_df.append({
                'station': station_name,
                'prediction': prediction
            }, ignore_index=True)

        # 合并预测到原始数据
        test_in_data = in_data[in_data['station'].isin(test_stations)].copy()
        test_in_data = pd.merge(test_in_data, prediction_df, on='station', how='left')

        # 预测出站数据 (假设与进站相关)
        test_out_data = out_data[out_data['station'].isin(test_stations)].copy()
        test_out_data = pd.merge(test_out_data, prediction_df, on='station', how='left')

        # 计算OD预测
        od_subset = od_data[
            (od_data['o_rawname'].isin(test_stations)) &
            (od_data['d_rawname'].isin(test_stations))
            ].copy()

        # 基于进站预测估计OD流量
        od_predictions = []

        for _, row in od_subset.iterrows():
            o_station = row['o_rawname']
            d_station = row['d_rawname']

            # 找到对应站点的预测值
            o_pred = prediction_df[prediction_df['station'] == o_station]['prediction'].values
            d_pred = prediction_df[prediction_df['station'] == d_station]['prediction'].values

            if len(o_pred) > 0 and len(d_pred) > 0:
                # 简单估计：使用两站点预测的几何平均
                od_pred = np.sqrt(o_pred[0] * d_pred[0])

                od_predictions.append({
                    'o_rawname': o_station,
                    'd_rawname': d_station,
                    'prediction': od_pred
                })

        # 创建OD预测DataFrame
        od_pred_df = pd.DataFrame(od_predictions)

        # 合并OD预测到原始数据
        od_subset = pd.merge(od_subset, od_pred_df, on=['o_rawname', 'd_rawname'], how='left')

        # 保存预测结果
        # 将进站预测结果转换为GeoDataFrame并保存
        gdf_in_pred = gpd.GeoDataFrame(test_in_data, geometry='geometry')
        gdf_in_pred.to_file(os.path.join(BASE_PATH, 'in_500_predictions_with_coords.shp'))

        # 将出站预测结果转换为GeoDataFrame并保存
        gdf_out_pred = gpd.GeoDataFrame(test_out_data, geometry='geometry')
        gdf_out_pred.to_file(os.path.join(BASE_PATH, 'out_500_predictions_with_coords.shp'))

        # 保存OD预测结果
        od_subset.to_csv(os.path.join(BASE_PATH, 'od_predictions.csv'), index=False)

        # 创建预测比较
        prediction_comparison = []

        # 进站预测比较
        for _, row in test_in_data.iterrows():
            if pd.notna(row['prediction']):
                prediction_comparison.append({
                    'Type': 'In',
                    'Station': row['station'],
                    'Hour': row['hour'],
                    'True_Value': row['count'],
                    'Predicted_Value': row['prediction']
                })

        # 出站预测比较
        for _, row in test_out_data.iterrows():
            if pd.notna(row['prediction']):
                prediction_comparison.append({
                    'Type': 'Out',
                    'Station': row['station'],
                    'Hour': row['hour'],
                    'True_Value': row['count'],
                    'Predicted_Value': row['prediction']
                })

        # OD预测比较
        for _, row in od_subset.iterrows():
            if pd.notna(row['prediction']):
                prediction_comparison.append({
                    'Type': 'OD',
                    'Origin': row['o_rawname'],
                    'Destination': row['d_rawname'],
                    'Hour': row['hour'],
                    'True_Value': row['trip'],
                    'Predicted_Value': row['prediction']
                })

        # 保存预测比较结果
        pd.DataFrame(prediction_comparison).to_csv(os.path.join(BASE_PATH, 'prediction_comparison.csv'), index=False)

        # 计算总体评估指标
        in_results = test_in_data.dropna(subset=['prediction'])
        out_results = test_out_data.dropna(subset=['prediction'])
        od_results = od_subset.dropna(subset=['prediction'])

        if len(in_results) > 0:
            in_mae = mean_absolute_error(in_results['count'], in_results['prediction'])
            print(f"进站预测 MAE: {in_mae:.4f}")

        if len(out_results) > 0:
            out_mae = mean_absolute_error(out_results['count'], out_results['prediction'])
            print(f"出站预测 MAE: {out_mae:.4f}")

        if len(od_results) > 0:
            od_mae = mean_absolute_error(od_results['trip'], od_results['prediction'])
            print(f"OD预测 MAE: {od_mae:.4f}")

    print("预测完成，结果已保存。")


# 主函数
def main():
    # 加载数据
    in_data, out_data, od_data, station_features = load_data()

    # 预处理数据
    (X, flow_matrix, edge_index, train_indices, test_indices,
     common_stations, train_stations, test_stations,
     station_to_idx, test_station_to_idx, in_data, out_data, od_data) = preprocess_data(
        in_data, out_data, od_data, station_features
    )

    # 训练和评估
    train_and_evaluate(
        X, flow_matrix, edge_index, train_indices, test_indices, common_stations,
        train_stations, test_stations, station_to_idx, test_station_to_idx, in_data, out_data, od_data
    )


if __name__ == "__main__":
    main()
