# NaN错误修复指南

## 🐛 问题描述

在模型评估阶段，`sklearn.metrics.mean_absolute_error`函数遇到包含NaN值的输入，导致以下错误：
```
ValueError: Input contains NaN.
```

## 🔍 错误原因分析

NaN值可能来源于以下几个方面：

### 1. 数据源问题
- 原始数据文件中包含缺失值
- 坐标数据异常（经纬度为空）
- 流量数据异常（count字段为空）

### 2. 数据处理问题
- 特征标准化时除零操作
- 浮点数精度溢出（特别是float16）
- 数组操作中的无效计算

### 3. 模型计算问题
- 梯度爆炸导致权重变为NaN
- GCN层计算时的数值不稳定
- 激活函数输出异常

## ✅ 已实施修复

### 1. 数据预处理修复 (`data_processor.py`)

#### 特征预处理增强：
```python
# 检查原始特征
print(f"原始特征中NaN数量: {np.isnan(features).sum()}")
print(f"原始特征中Inf数量: {np.isinf(features).sum()}")

# 替换异常值
features = np.nan_to_num(features, nan=0.0, posinf=1e6, neginf=-1e6)

# 标准化后再检查
if np.isnan(features_scaled).any() or np.isinf(features_scaled).any():
    features_scaled = np.nan_to_num(features_scaled, nan=0.0, posinf=1.0, neginf=-1.0)
```

#### 流量数据处理增强：
```python
# 确保每个流量值都是有效数值
if pd.isna(flow) or np.isnan(flow) or np.isinf(flow):
    flow = 0.0

# 最终清理所有流量数据
self.in_flows = np.nan_to_num(self.in_flows, nan=0.0, posinf=0.0, neginf=0.0)
self.out_flows = np.nan_to_num(self.out_flows, nan=0.0, posinf=0.0, neginf=0.0)
self.od_flows = np.nan_to_num(self.od_flows, nan=0.0, posinf=0.0, neginf=0.0)
```

### 2. 模型计算稳定性 (`models/gcn_temporal.py`)

#### 前向传播NaN检查：
```python
# 在每个关键步骤检查并处理NaN
if torch.isnan(node_features).any() or torch.isinf(node_features).any():
    node_features = torch.where(torch.isnan(node_features) | torch.isinf(node_features), 
                               torch.zeros_like(node_features), node_features)
```

#### 多层检查点：
- 输入数据检查
- 特征投影后检查  
- 时间编码后检查
- GCN输出检查
- 预测头输出检查
- 最终输出检查

### 3. 评估过程强化 (`trainer.py`)

#### 评估时NaN处理：
```python
# 实时检查预测和目标值
if torch.isnan(pred_subset).any() or torch.isinf(pred_subset).any():
    pred_subset = torch.where(torch.isnan(pred_subset) | torch.isinf(pred_subset), 
                             torch.zeros_like(pred_subset), pred_subset)

# numpy转换后再次检查
if np.isnan(pred_np).any() or np.isinf(pred_np).any():
    pred_np = np.nan_to_num(pred_np, nan=0.0, posinf=0.0, neginf=0.0)
```

#### 异常捕获：
```python
try:
    mae_in = mean_absolute_error(all_targets[:, 0], all_predictions[:, 0])
    # ... 其他指标计算
except Exception as e:
    print(f"计算评估指标时出错: {e}")
    # 使用默认值
    mae_in = mae_out = mae_overall = 0.0
```

## 🔧 额外建议

### 1. 模型训练优化

#### 降低学习率：
```python
# 在main.py中调整
parser.add_argument('--learning_rate', type=float, default=0.0001,  # 从0.001降到0.0001
                   help='学习率')
```

#### 增强梯度裁剪：
```python
# 在trainer.py中
torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=0.5)  # 从1.0降到0.5
```

### 2. 数据质量检查

#### 运行前检查脚本：
```python
# 创建 check_data.py
def check_data_quality(data_path):
    # 检查所有数据文件
    in_data = gpd.read_file(f"{data_path}/in_500_with_coords.shp")
    print(f"进站数据NaN列: {in_data.isnull().sum()}")
    
    out_data = gpd.read_file(f"{data_path}/out_500_with_coords.shp")
    print(f"出站数据NaN列: {out_data.isnull().sum()}")
    
    # 检查坐标有效性
    invalid_coords = in_data[(in_data['longitude'].isnull()) | (in_data['latitude'].isnull())]
    print(f"无效坐标数量: {len(invalid_coords)}")
```

### 3. 模型架构调整

#### 禁用float16（如果仍有问题）：
```python
# 在config.py中
MODEL_CONFIG = {
    'use_float16': False,  # 改为False
    # ... 其他配置
}
```

#### 添加Dropout正则化：
```python
# 在模型中增加Dropout层
self.feature_dropout = nn.Dropout(0.1)
node_feat_proj = self.feature_dropout(node_feat_proj)
```

## 🚀 运行建议

### 1. 逐步测试
```bash
# 先用小数据集测试
python main.py --epochs 3 --patience 2

# 检查日志输出中的警告信息
grep "警告" output.log
```

### 2. 监控内存使用
```bash
# 监控GPU内存
nvidia-smi -l 1

# 如果内存不足，减少批次大小或使用CPU
python main.py --batch_size 1 --use_float16 False
```

### 3. 调试模式
在代码中添加更多调试信息：
```python
# 在trainer.py的evaluate函数开始处添加
torch.autograd.set_detect_anomaly(True)
```

## 📝 修复验证

运行修复后的代码，应该看到：

1. **数据处理阶段**：
   ```
   原始特征中NaN数量: 0
   预处理后特征NaN数量: 0
   进站流量NaN数量: 0
   ```

2. **训练阶段**：
   - 无NaN相关警告信息
   - 损失值正常下降

3. **评估阶段**：
   ```
   合并后预测数据NaN数量: 0
   合并后目标数据NaN数量: 0
   整体 MAE: [正常数值]
   ```

## ⚠️ 注意事项

1. **零值替换**：用0替换NaN可能影响模型性能，但保证了数值稳定性
2. **性能监控**：关注替换操作是否频繁发生
3. **数据质量**：建议从源头改善数据质量
4. **模型验证**：确保修复后的结果在业务上仍然合理

如果问题仍然存在，请检查：
- 输入数据文件的完整性
- PyTorch和scikit-learn版本兼容性
- 系统内存和GPU状态 