import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import GCNConv, GATConv, global_mean_pool
from torch_geometric.data import Data, Batch
import numpy as np

class TimeAwareGCN(nn.Module):
    """时间感知的GCN模型"""
    
    def __init__(self, input_dim, hidden_dim=128, output_dim=1, num_layers=3, 
                 dropout=0.2, use_attention=True):
        super(TimeAwareGCN, self).__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.output_dim = output_dim
        self.num_layers = num_layers
        self.use_attention = use_attention
        
        # 输入投影层
        self.input_proj = nn.Linear(input_dim, hidden_dim)
        
        # GCN层
        self.gcn_layers = nn.ModuleList()
        for i in range(num_layers):
            if i == 0:
                self.gcn_layers.append(GCNConv(hidden_dim, hidden_dim))
            else:
                self.gcn_layers.append(GCNConv(hidden_dim, hidden_dim))
        
        # 时间注意力机制
        if use_attention:
            self.time_attention = nn.MultiheadAttention(
                embed_dim=hidden_dim, 
                num_heads=8, 
                dropout=dropout,
                batch_first=True
            )
        
        # 输出层
        self.output_layers = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 4, output_dim)
        )
        
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, data):
        x, edge_index, edge_attr = data.x, data.edge_index, data.edge_attr
        batch = data.batch if hasattr(data, 'batch') else None
        
        # 输入投影
        x = self.input_proj(x)
        x = F.relu(x)
        x = self.dropout(x)
        
        # GCN层
        for i, gcn_layer in enumerate(self.gcn_layers):
            x = gcn_layer(x, edge_index, edge_attr)
            x = F.relu(x)
            x = self.dropout(x)
        
        # 时间注意力机制
        if self.use_attention:
            # 重塑为序列格式进行注意力计算
            if batch is not None:
                # 处理批量数据
                x_reshaped = x.unsqueeze(1)  # [num_nodes, 1, hidden_dim]
                attn_output, _ = self.time_attention(x_reshaped, x_reshaped, x_reshaped)
                x = attn_output.squeeze(1)
            else:
                x_reshaped = x.unsqueeze(0)  # [1, num_nodes, hidden_dim]
                attn_output, _ = self.time_attention(x_reshaped, x_reshaped, x_reshaped)
                x = attn_output.squeeze(0)
        
        # 输出预测
        out = self.output_layers(x)
        
        return out

class StationFlowGCN(nn.Module):
    """站点流量预测GCN"""
    
    def __init__(self, input_dim, hidden_dim=128, dropout=0.2):
        super(StationFlowGCN, self).__init__()
        
        self.gcn = TimeAwareGCN(
            input_dim=input_dim,
            hidden_dim=hidden_dim,
            output_dim=1,  # 预测单个流量值
            num_layers=3,
            dropout=dropout,
            use_attention=True
        )
        
    def forward(self, data):
        return self.gcn(data)

class ODFlowGCN(nn.Module):
    """OD流量预测GCN"""
    
    def __init__(self, input_dim, hidden_dim=128, dropout=0.2):
        super(ODFlowGCN, self).__init__()
        
        self.station_gcn = TimeAwareGCN(
            input_dim=input_dim,
            hidden_dim=hidden_dim,
            output_dim=hidden_dim,
            num_layers=3,
            dropout=dropout,
            use_attention=True
        )
        
        # OD特征处理
        self.od_feature_dim = 4  # surface_distance, translate, time, wait_time
        self.od_proj = nn.Linear(self.od_feature_dim, hidden_dim // 2)
        
        # OD预测层
        self.od_predictor = nn.Sequential(
            nn.Linear(hidden_dim * 2 + hidden_dim // 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, 1)
        )
        
    def forward(self, data, od_pairs, od_features):
        # 获取站点表示
        station_embeddings = self.station_gcn(data)  # [num_stations, hidden_dim]
        
        # 处理OD特征
        od_feat = self.od_proj(od_features)  # [num_od_pairs, hidden_dim//2]
        
        # 获取OD对的起点和终点嵌入
        origin_embeddings = station_embeddings[od_pairs[:, 0]]  # [num_od_pairs, hidden_dim]
        dest_embeddings = station_embeddings[od_pairs[:, 1]]    # [num_od_pairs, hidden_dim]
        
        # 组合特征
        combined_features = torch.cat([
            origin_embeddings, 
            dest_embeddings, 
            od_feat
        ], dim=1)
        
        # 预测OD流量
        od_flow = self.od_predictor(combined_features)
        
        return od_flow

class GridFlowDistribution(nn.Module):
    """栅格流量分配模型"""
    
    def __init__(self, grid_feature_dim, station_feature_dim, hidden_dim=64):
        super(GridFlowDistribution, self).__init__()
        
        self.grid_encoder = nn.Sequential(
            nn.Linear(grid_feature_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, hidden_dim // 2)
        )
        
        self.station_encoder = nn.Sequential(
            nn.Linear(station_feature_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, hidden_dim // 2)
        )
        
        self.predictor = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim // 2, 1),
            nn.Sigmoid()  # 输出概率分布
        )
        
    def forward(self, grid_features, station_features):
        grid_emb = self.grid_encoder(grid_features)
        station_emb = self.station_encoder(station_features)
        
        # 计算相似度或组合特征
        combined = grid_emb + station_emb  # 或者使用其他组合方式
        
        # 预测分配比例
        ratio = self.predictor(combined)
        
        return ratio

class MetroFlowPredictor(nn.Module):
    """完整的地铁流量预测系统"""
    
    def __init__(self, station_feature_dim, grid_feature_dim, hidden_dim=128):
        super(MetroFlowPredictor, self).__init__()
        
        # 站点流量预测模型
        self.in_flow_model = StationFlowGCN(
            input_dim=station_feature_dim + 3,  # +3 for time features
            hidden_dim=hidden_dim
        )
        
        self.out_flow_model = StationFlowGCN(
            input_dim=station_feature_dim + 3,
            hidden_dim=hidden_dim
        )
        
        # OD流量预测模型
        self.od_flow_model = ODFlowGCN(
            input_dim=station_feature_dim + 3,
            hidden_dim=hidden_dim
        )
        
        # 栅格流量分配模型
        self.grid_distribution_model = GridFlowDistribution(
            grid_feature_dim=grid_feature_dim,
            station_feature_dim=station_feature_dim,
            hidden_dim=hidden_dim // 2
        )
        
    def predict_station_flows(self, graph_data):
        """预测站点进出流量"""
        in_flows = self.in_flow_model(graph_data)
        out_flows = self.out_flow_model(graph_data)
        return in_flows, out_flows
    
    def predict_od_flows(self, graph_data, od_pairs, od_features):
        """预测OD流量"""
        od_flows = self.od_flow_model(graph_data, od_pairs, od_features)
        return od_flows
    
    def predict_grid_distribution(self, grid_features, station_features):
        """预测栅格流量分配"""
        distribution = self.grid_distribution_model(grid_features, station_features)
        return distribution
