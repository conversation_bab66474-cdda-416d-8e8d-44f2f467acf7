"""
数据预处理模块
负责数据清洗、标准化、图构建等预处理操作
"""
import numpy as np
import pandas as pd
import torch
from sklearn.preprocessing import StandardScaler
from scipy.sparse import lil_matrix
from config import Config

class DataPreprocessor:
    def __init__(self, config=None):
        self.config = config or Config()
        self.scaler = StandardScaler()
    
    def preprocess_data(self, in_data, out_data, od_data, station_features):
        """
        预处理所有数据
        
        Args:
            in_data: 进站数据
            out_data: 出站数据
            od_data: OD数据
            station_features: 站点特征数据
            
        Returns:
            tuple: 预处理后的数据
        """
        print("预处理数据...")
        
        # 标准化站点特征
        station_features = self._standardize_features(station_features)
        
        # 获取共同站点
        common_stations = self._get_common_stations(in_data, out_data, station_features)
        
        # 过滤数据
        in_data, out_data, station_features = self._filter_data(
            in_data, out_data, station_features, common_stations
        )
        
        # 创建站点索引映射
        station_to_idx = {station: idx for idx, station in enumerate(common_stations)}
        
        # 构建图结构
        edge_index = self._build_graph(od_data, common_stations, station_to_idx)
        
        # 准备时空特征
        X, flow_matrix = self._prepare_temporal_features(
            in_data, out_data, station_features, common_stations, station_to_idx
        )
        
        # 划分训练集和测试集
        train_indices, test_indices = self._split_train_test(len(common_stations))
        
        # 获取站点名称列表
        train_stations = [common_stations[i] for i in train_indices]
        test_stations = [common_stations[i] for i in test_indices]
        test_station_to_idx = {station: i for i, station in enumerate(test_stations)}
        
        return (X, flow_matrix, edge_index, train_indices, test_indices,
                common_stations, train_stations, test_stations,
                station_to_idx, test_station_to_idx, in_data, out_data, od_data)
    
    def _standardize_features(self, station_features):
        """标准化站点特征"""
        feature_columns = station_features.columns.drop('站名')
        station_features[feature_columns] = self.scaler.fit_transform(
            station_features[feature_columns]
        )
        return station_features
    
    def _get_common_stations(self, in_data, out_data, station_features):
        """获取共同站点"""
        in_stations = set(in_data['station'].unique())
        out_stations = set(out_data['station'].unique())
        feature_stations = set(station_features['站名'].unique())
        
        common_stations = list(in_stations & out_stations & feature_stations)
        print(f"共同站点数量: {len(common_stations)}")
        return common_stations
    
    def _filter_data(self, in_data, out_data, station_features, common_stations):
        """过滤数据，只保留共同站点"""
        in_data = in_data[in_data['station'].isin(common_stations)]
        out_data = out_data[out_data['station'].isin(common_stations)]
        station_features = station_features[station_features['站名'].isin(common_stations)]
        return in_data, out_data, station_features
    
    def _build_graph(self, od_data, common_stations, station_to_idx):
        """构建图结构"""
        print("正在构建图结构...")
        n_stations = len(common_stations)
        
        # 使用稀疏矩阵
        adj_matrix = lil_matrix((n_stations, n_stations), dtype=np.float32)
        
        edge_count = 0
        od_subset = od_data[
            (od_data['o_rawname'].isin(common_stations)) &
            (od_data['d_rawname'].isin(common_stations))
        ]
        
        for _, row in od_subset.iterrows():
            o_station = row['o_rawname']
            d_station = row['d_rawname']
            
            o_idx = station_to_idx[o_station]
            d_idx = station_to_idx[d_station]
            
            adj_matrix[o_idx, d_idx] = 1
            adj_matrix[d_idx, o_idx] = 1  # 无向图
            edge_count += 2
        
        # 添加自环确保图连通
        for i in range(n_stations):
            if adj_matrix[i, i] == 0:
                adj_matrix[i, i] = 1
                edge_count += 1
        
        print(f"图边数量: {edge_count}")
        
        # 转换为PyTorch稀疏张量格式
        adj_indices = adj_matrix.nonzero()
        edge_index = torch.LongTensor([adj_indices[0], adj_indices[1]])
        
        return edge_index
    
    def _prepare_temporal_features(self, in_data, out_data, station_features, 
                                 common_stations, station_to_idx):
        """准备时空特征矩阵"""
        print("正在准备时空特征...")
        
        time_steps = self.config.TIME_STEPS
        station_count = len(common_stations)
        feature_columns = station_features.columns.drop('站名')
        feature_dim = len(feature_columns)
        
        # 初始化特征矩阵
        X = np.zeros((time_steps, station_count, feature_dim + 2))
        flow_matrix = np.zeros((time_steps, station_count))
        
        # 构建站点特征字典
        station_feature_dict = {}
        for _, row in station_features.iterrows():
            station = row['站名']
            if station in station_to_idx:
                station_feature_dict[station] = row[feature_columns].values
        
        # 填充进站流量
        for _, row in in_data.iterrows():
            station = row['station']
            hour = int(row['hour'])
            count = float(row['count'])
            
            if station in station_to_idx:
                idx = station_to_idx[station]
                X[hour, idx, 0] = count
                flow_matrix[hour, idx] = count
        
        # 填充出站流量
        for _, row in out_data.iterrows():
            station = row['station']
            hour = int(row['hour'])
            count = float(row['count'])
            
            if station in station_to_idx:
                idx = station_to_idx[station]
                X[hour, idx, 1] = count
        
        # 填充站点特征
        for station, idx in station_to_idx.items():
            if station in station_feature_dict:
                features = station_feature_dict[station]
                for hour in range(time_steps):
                    X[hour, idx, 2:] = features
        
        print(f"特征矩阵形状: {X.shape}")
        print(f"流量矩阵形状: {flow_matrix.shape}")
        
        return X, flow_matrix
    
    def _split_train_test(self, station_count):
        """划分训练集和测试集"""
        n_train_stations = int(station_count * self.config.TRAIN_RATIO)
        train_indices = np.random.choice(station_count, n_train_stations, replace=False)
        test_indices = np.array([i for i in range(station_count) if i not in train_indices])
        
        return train_indices, test_indices



