# -*- coding: utf-8 -*-
"""
Created on Wed Apr  9 14:54:49 2025

@author: wxj01
"""
import pandas as pd
import numpy as np
from shapely.geometry import Polygon
import geopandas as gpd
import math

df1 = pd.read_csv('jiebo_in.csv')
df2 = pd.read_csv('jiebo_out.csv')
df = pd.read_csv('updated_北京市_subway_od_2024.csv')

# 获取df1中station列的所有唯一值
stations1 = list(df1['station'].unique())
stations2 = list(df2['station'].unique())
stationso = list(df['o_rawname'].unique())
stationsd = list(df['d_rawname'].unique())




# Function to clean station names
def clean_station_name(name):
    if isinstance(name, str) and name.startswith('北京市地铁('):
        return name.replace('北京市地铁(', '').rstrip(')')
    return name

# Clean station names and save to new CSV files
df1['station'] = df1['station'].apply(clean_station_name)
df2['station'] = df2['station'].apply(clean_station_name)

df1_cleaned = df1[['station']].drop_duplicates()
df2_cleaned = df2[['station']].drop_duplicates()

df1_cleaned.to_csv('jiebo_in_cleaned.csv', index=False)
df2_cleaned.to_csv('jiebo_out_cleaned.csv', index=False)



# 1. Replace station names in df1 and df2
station_replacements = {
    'T2航站楼': '2号航站楼',
    'T3航站楼': '3号航站楼',
    '丰台': '丰台站',
    '亦庄火车': '亦庄火车站',
    '北京': '北京站',
    '北京南': '北京南站',
    '北京西': '北京西站',
    '黄村火车': '黄村火车站'
}

df1['station'] = df1['station'].replace(station_replacements)
df2['station'] = df2['station'].replace(station_replacements)
df['o_rawname'] = df['o_rawname'].replace(station_replacements)
df['d_rawname'] = df['d_rawname'].replace(station_replacements)
# Find differences
# 2. Compare stations1 and stations2 with stationso
stations1 = set(df1['station'].unique())
stations2 = set(df2['station'].unique())
stationso = set(df['o_rawname'].unique())

not_in_stationso_from_s1 = stations1 - stationso
not_in_stationso_from_s2 = stationso - stations1


print("\nStations in jiebo_in_cleaned.csv but not in updated_北京市_subway_od_2024.csv:")
for station in not_in_stationso_from_s1:
    print(f"{station} (from jiebo_in_cleaned.csv)")

print("\nStations in updated_北京市_subway_od_2024.csv but not in jiebo_out_cleaned.csv:")
for station in not_in_stationso_from_s2:
    print(f"{station} (from jiebo_out_cleaned.csv)")





# 2. Remove specified stations from df1 and df2
stations_to_remove = [
    '二里沟', '天通苑东', '宛平城', '工人体育场', '左家庄', 
    '未来科学城', '未来科学城北', '模式口', '洪泰庄', '清河', 
    '清河营', '物资学院路', '红军营', '西坝河'
]

df1 = df1[~df1['station'].isin(stations_to_remove)]
df2 = df2[~df2['station'].isin(stations_to_remove)]

# Save modified df1 and df2
df1.to_csv('jiebo_in_modified.csv', index=False)
df2.to_csv('jiebo_out_modified.csv', index=False)

# 3. Remove rows from df where o_rawname or d_rawname match specified stations
stations_to_remove_df = [
    '万安', '上岸', '九号村', '亦创会展中心', '亦庄同仁', 
    '四海庄', '四道桥', '大兴机场', '太和桥北', '定海园', 
    '定海园西', '小园', '屈庄', '栗园庄', '桥户营', 
    '植物园', '泰河路', '瑞合庄', '石厂', '经海一路', 
    '茶棚', '融兴街', '颐和园西门', '香山', '鹿圈东'
]

mask = ~((df['o_rawname'].isin(stations_to_remove_df)) | (df['d_rawname'].isin(stations_to_remove_df)))
df_filtered = df[mask]

# Save modified df
df_filtered.to_csv('updated_北京市_subway_od_2024_modified.csv', index=False)


