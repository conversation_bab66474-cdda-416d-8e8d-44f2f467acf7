import os
import pandas as pd
import numpy as np
import geopandas as gpd
import torch
import torch.nn as nn
import torch.optim as optim
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_absolute_error
import matplotlib.pyplot as plt

# 路径设置
BASE_PATH = "C:\\Users\\<USER>\\Desktop\\接驳\\"
IN_FILE = os.path.join(BASE_PATH, "in_500_with_coords.shp")
OUT_FILE = os.path.join(BASE_PATH, "out_500_with_coords.shp")
OD_FILE = os.path.join(BASE_PATH, "updated北京市_subway_od_2024_modified3.csv")
FEATURES_FILE = os.path.join(BASE_PATH, "station_features_result.csv")


# 读取数据
def load_data():
    print("读取数据...")
    in_data = gpd.read_file(IN_FILE)
    out_data = gpd.read_file(OUT_FILE)

    # 提取栅格中心点坐标
    in_data['longitude'] = in_data.geometry.centroid.x
    in_data['latitude'] = in_data.geometry.centroid.y
    out_data['longitude'] = out_data.geometry.centroid.x
    out_data['latitude'] = out_data.geometry.centroid.y

    od_data = pd.read_csv(OD_FILE)
    station_features = pd.read_csv(FEATURES_FILE)

    print(f"进站数据形状: {in_data.shape}")
    print(f"出站数据形状: {out_data.shape}")
    print(f"OD数据形状: {od_data.shape}")
    print(f"站点特征数据形状: {station_features.shape}")

    return in_data, out_data, od_data, station_features


# 数据预处理
def preprocess_data(in_data, out_data, od_data, station_features):
    print("预处理数据...")
    # 标准化站点特征
    feature_columns = station_features.columns.drop('站名')
    scaler = StandardScaler()
    station_features[feature_columns] = scaler.fit_transform(station_features[feature_columns])

    # 获取共同站点
    in_stations = set(in_data['station'].unique())
    out_stations = set(out_data['station'].unique())
    feature_stations = set(station_features['站名'].unique())

    common_stations = list(in_stations & out_stations & feature_stations)
    print(f"共同站点数量: {len(common_stations)}")

    # 过滤数据，只保留共同站点
    in_data = in_data[in_data['station'].isin(common_stations)]
    out_data = out_data[out_data['station'].isin(common_stations)]
    station_features = station_features[station_features['站名'].isin(common_stations)]

    # 创建站点索引映射
    station_to_idx = {station: idx for idx, station in enumerate(common_stations)}
    print(f"处理后的站点数量: {len(station_to_idx)}")

    # 构建稀疏邻接矩阵 (基于OD数据)
    print("正在构建图结构...")
    n_stations = len(common_stations)

    # 使用scipy的稀疏矩阵来节省内存
    from scipy.sparse import lil_matrix
    adj_matrix = lil_matrix((n_stations, n_stations), dtype=np.float32)

    edge_count = 0
    # 只处理共同站点的OD数据
    od_subset = od_data[
        (od_data['o_rawname'].isin(common_stations)) &
        (od_data['d_rawname'].isin(common_stations))
        ]

    for _, row in od_subset.iterrows():
        o_station = row['o_rawname']
        d_station = row['d_rawname']

        o_idx = station_to_idx[o_station]
        d_idx = station_to_idx[d_station]

        # 用流量作为边权重
        adj_matrix[o_idx, d_idx] = 1
        adj_matrix[d_idx, o_idx] = 1  # 无向图
        edge_count += 2  # 算上双向边

    # 确保图是连通的（添加自环）
    for i in range(n_stations):
        if adj_matrix[i, i] == 0:
            adj_matrix[i, i] = 1
            edge_count += 1

    print(f"图边数量: {edge_count}")

    # 转换为PyTorch稀疏张量格式
    adj_indices = adj_matrix.nonzero()
    edge_index = torch.LongTensor([adj_indices[0], adj_indices[1]])

    # 准备时序特征矩阵
    print("正在准备时空特征...")

    # 每小时的特征
    time_steps = 24
    station_count = len(common_stations)
    feature_dim = len(feature_columns)

    # 初始化特征矩阵 (时间, 站点, 特征)
    X = np.zeros((time_steps, station_count, feature_dim + 2))  # +2 表示进站和出站流量

    # 构建站点特征字典以快速查询
    station_feature_dict = {}
    for _, row in station_features.iterrows():
        station = row['站名']
        if station in station_to_idx:
            station_feature_dict[station] = row[feature_columns].values

    # 构建流量矩阵 (时间, 站点)
    flow_matrix = np.zeros((time_steps, station_count))

    # 填充进站流量
    for _, row in in_data.iterrows():
        station = row['station']
        hour = int(row['hour'])
        count = float(row['count'])

        if station in station_to_idx:
            idx = station_to_idx[station]
            X[hour, idx, 0] = count  # 第一个特征是进站流量
            flow_matrix[hour, idx] = count  # 将进站流量作为目标

    # 填充出站流量
    for _, row in out_data.iterrows():
        station = row['station']
        hour = int(row['hour'])
        count = float(row['count'])

        if station in station_to_idx:
            idx = station_to_idx[station]
            X[hour, idx, 1] = count  # 第二个特征是出站流量

    # 填充站点特征
    for station, idx in station_to_idx.items():
        if station in station_feature_dict:
            features = station_feature_dict[station]
            for hour in range(time_steps):
                X[hour, idx, 2:] = features

    print(f"特征矩阵形状: {X.shape}")
    print(f"流量矩阵形状: {flow_matrix.shape}")

    # 划分训练集和测试集
    train_ratio = 0.8
    n_train_stations = int(station_count * train_ratio)

    # 随机选择训练站点
    train_indices = np.random.choice(station_count, n_train_stations, replace=False)
    test_indices = np.array([i for i in range(station_count) if i not in train_indices])

    # 获取训练站点和测试站点名称
    train_stations = [common_stations[i] for i in train_indices]
    test_stations = [common_stations[i] for i in test_indices]

    # 创建站点到索引的映射（基于测试集）
    test_station_to_idx = {station: i for i, station in enumerate(test_stations)}

    # 返回处理后的数据
    return (X, flow_matrix, edge_index, train_indices, test_indices,
            common_stations, train_stations, test_stations,
            station_to_idx, test_station_to_idx, in_data, out_data, od_data)


# 改进版本的T-GCN模型
class ImprovedTGCN(nn.Module):
    def __init__(self, input_dim, hidden_dim, output_dim=1, num_nodes=None):
        super(ImprovedTGCN, self).__init__()
        
        self.hidden_dim = hidden_dim
        self.num_nodes = num_nodes
        
        # 多层GCN
        self.gcn1 = nn.Linear(input_dim, hidden_dim)
        self.gcn2 = nn.Linear(hidden_dim, hidden_dim)
        self.dropout = nn.Dropout(0.2)
        
        # 改进的GRU
        self.gru = nn.GRU(hidden_dim, hidden_dim, batch_first=True)
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(hidden_dim, num_heads=4, batch_first=True)
        
        # 多层输出网络
        self.output_layers = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim // 2, output_dim)
        )
        
        # 批量标准化
        self.batch_norm1 = nn.BatchNorm1d(hidden_dim)
        self.batch_norm2 = nn.BatchNorm1d(hidden_dim)
        
    def gcn_operation(self, x, adj):
        """改进的GCN操作，包含正确的归一化"""
        batch_size, num_nodes, input_dim = x.shape
        
        # 计算度矩阵
        adj_indices = adj[0], adj[1]
        out_nodes, in_nodes = adj_indices
        
        # 创建度矩阵
        degree = torch.zeros(num_nodes, device=x.device)
        for i in range(adj.shape[1]):
            out_node = out_nodes[i].item()
            degree[out_node] += 1
        
        # 避免除零
        degree = torch.clamp(degree, min=1)
        degree_inv_sqrt = torch.pow(degree, -0.5)
        
        # 第一层GCN
        x_transformed = self.gcn1(x)  # [batch, nodes, hidden]
        
        # 图卷积操作
        output = torch.zeros_like(x_transformed)
        
        for b in range(batch_size):
            # 邻居聚合
            for i in range(adj.shape[1]):
                out_node = out_nodes[i].item()
                in_node = in_nodes[i].item()
                # 使用度归一化
                output[b, out_node] += x_transformed[b, in_node] * degree_inv_sqrt[out_node] * degree_inv_sqrt[in_node]
        
        # 应用批量标准化和激活
        output = output.transpose(1, 2)  # [batch, hidden, nodes]
        output = self.batch_norm1(output)
        output = output.transpose(1, 2)  # [batch, nodes, hidden]
        output = torch.relu(output)
        output = self.dropout(output)
        
        # 第二层GCN
        x_transformed2 = self.gcn2(output)
        
        # 第二次图卷积
        output2 = torch.zeros_like(x_transformed2)
        for b in range(batch_size):
            for i in range(adj.shape[1]):
                out_node = out_nodes[i].item()
                in_node = in_nodes[i].item()
                output2[b, out_node] += x_transformed2[b, in_node] * degree_inv_sqrt[out_node] * degree_inv_sqrt[in_node]
        
        # 残差连接
        if output.shape == output2.shape:
            output2 = output2 + output
            
        # 第二次批量标准化
        output2 = output2.transpose(1, 2)
        output2 = self.batch_norm2(output2)
        output2 = output2.transpose(1, 2)
        output2 = torch.relu(output2)
        
        return output2
    
    def forward(self, x, adj):
        """
        x: [batch_size, time_steps, num_nodes, input_dim]
        adj: 稀疏邻接矩阵的边索引
        """
        batch_size, time_steps, num_nodes, input_dim = x.shape
        
        # 存储每个时间步的GCN输出
        gcn_outputs = []
        
        for t in range(time_steps):
            current_input = x[:, t]  # [batch, nodes, features]
            gcn_out = self.gcn_operation(current_input, adj)
            gcn_outputs.append(gcn_out.unsqueeze(1))  # [batch, 1, nodes, hidden]
        
        # 合并所有时间步 [batch, time_steps, nodes, hidden]
        gcn_sequence = torch.cat(gcn_outputs, dim=1)
        
        # 对每个节点分别应用GRU
        gru_outputs = []
        for node in range(num_nodes):
            node_sequence = gcn_sequence[:, :, node, :]  # [batch, time_steps, hidden]
            gru_out, _ = self.gru(node_sequence)
            gru_outputs.append(gru_out[:, -1:, :])  # 取最后一个时间步
        
        # 合并所有节点的GRU输出 [batch, nodes, hidden]
        gru_combined = torch.cat(gru_outputs, dim=1)
        
        # 应用注意力机制
        attn_output, _ = self.attention(gru_combined, gru_combined, gru_combined)
        
        # 残差连接
        final_features = attn_output + gru_combined
        
        # 输出预测
        output = self.output_layers(final_features)
        
        return output.squeeze(-1)


# 改进的训练函数
def improved_train_and_evaluate(X, flow_matrix, edge_index, train_indices, test_indices, common_stations,
                               train_stations, test_stations, station_to_idx, test_station_to_idx, in_data, out_data, od_data):
    print("准备训练数据...")
    
    # 数据标准化 - 分别标准化每个特征
    X_normalized = X.copy()
    scalers = []
    
    for feature_idx in range(X.shape[2]):
        scaler = StandardScaler()
        feature_data = X[:, :, feature_idx].reshape(-1, 1)
        X_normalized[:, :, feature_idx] = scaler.fit_transform(feature_data).reshape(X.shape[0], X.shape[1])
        scalers.append(scaler)
    
    # 流量数据也进行标准化
    flow_scaler = StandardScaler()
    flow_normalized = flow_scaler.fit_transform(flow_matrix.reshape(-1, 1)).reshape(flow_matrix.shape)
    
    # 模型参数
    input_dim = X.shape[2]
    hidden_dim = 64  # 增加隐藏层维度
    time_steps = X.shape[0]
    num_nodes = X.shape[1]
    
    # 转换为张量
    X_tensor = torch.FloatTensor(X_normalized)
    flow_tensor = torch.FloatTensor(flow_normalized)
    
    # 创建序列预测任务
    sequence_length = 12  # 使用12小时的数据预测下一小时
    X_sequences = []
    y_sequences = []
    
    for t in range(sequence_length, time_steps):
        X_sequences.append(X_tensor[t-sequence_length:t])
        y_sequences.append(flow_tensor[t])
    
    X_sequences = torch.stack(X_sequences, dim=0)  # [num_sequences, seq_len, nodes, features]
    y_sequences = torch.stack(y_sequences, dim=0)  # [num_sequences, nodes]
    
    # 划分训练集和测试集
    train_size = int(0.8 * len(X_sequences))
    
    X_train = X_sequences[:train_size]
    X_test = X_sequences[train_size:]
    y_train = y_sequences[:train_size]
    y_test = y_sequences[train_size:]
    
    # 进一步按站点划分
    X_train_stations = X_train[:, :, train_indices]
    X_test_stations = X_test[:, :, test_indices]
    y_train_stations = y_train[:, train_indices]
    y_test_stations = y_test[:, test_indices]
    
    # 初始化改进的模型
    model = ImprovedTGCN(input_dim, hidden_dim, output_dim=1, num_nodes=len(train_indices))
    
    # 使用不同的损失函数组合
    mse_loss = nn.MSELoss()
    mae_loss = nn.L1Loss()
    
    # 使用学习率调度器
    optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-5)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=10)
    
    # 训练参数
    num_epochs = 10
    best_loss = float('inf')
    patience = 5
    patience_count = 0
    
    print("开始训练...")
    all_epoch_loss = []
    all_epoch_mae = []
    
    for epoch in range(num_epochs):
        model.train()
        
        # 批量训练
        total_loss = 0
        num_batches = 0
        
        for batch_start in range(0, len(X_train_stations), 8):  # 批量大小8
            batch_end = min(batch_start + 8, len(X_train_stations))
            
            X_batch = X_train_stations[batch_start:batch_end]
            y_batch = y_train_stations[batch_start:batch_end]
            
            # 重新构造边索引以匹配训练站点
            train_edge_index = []
            for i in range(edge_index.shape[1]):
                src, dst = edge_index[0, i].item(), edge_index[1, i].item()
                if src in train_indices and dst in train_indices:
                    src_new = list(train_indices).index(src)
                    dst_new = list(train_indices).index(dst)
                    train_edge_index.append([src_new, dst_new])
            
            if train_edge_index:
                train_edge_index = torch.LongTensor(train_edge_index).T
            else:
                # 如果没有边，创建自环
                n_train = len(train_indices)
                train_edge_index = torch.LongTensor([[i, i] for i in range(n_train)]).T
            
            # 前向传播
            outputs = model(X_batch, train_edge_index)
            
            # 组合损失
            mse = mse_loss(outputs, y_batch)
            mae = mae_loss(outputs, y_batch)
            loss = 0.7 * mse + 0.3 * mae
            
            # 反向传播
            optimizer.zero_grad()
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
        
        avg_loss = total_loss / num_batches
        
        # 验证
        model.eval()
        with torch.no_grad():
            # 构造测试边索引
            test_edge_index = []
            for i in range(edge_index.shape[1]):
                src, dst = edge_index[0, i].item(), edge_index[1, i].item()
                if src in test_indices and dst in test_indices:
                    src_new = list(test_indices).index(src)
                    dst_new = list(test_indices).index(dst)
                    test_edge_index.append([src_new, dst_new])
            
            if test_edge_index:
                test_edge_index = torch.LongTensor(test_edge_index).T
            else:
                n_test = len(test_indices)
                test_edge_index = torch.LongTensor([[i, i] for i in range(n_test)]).T
            
            test_outputs = model(X_test_stations, test_edge_index)
            test_loss = mse_loss(test_outputs, y_test_stations)
            test_mae = mae_loss(test_outputs, y_test_stations)
            
            all_epoch_loss.append(avg_loss)
            all_epoch_mae.append(test_mae.item())
            
            # 学习率调度
            scheduler.step(test_loss)
            
            # 早停
            if test_loss < best_loss:
                best_loss = test_loss
                patience_count = 0
                # 保存最佳模型
                torch.save(model.state_dict(), os.path.join(BASE_PATH, 'best_model.pth'))
            else:
                patience_count += 1
            
            if patience_count >= patience:
                print(f"Early stopping at epoch {epoch+1}")
                break
            

            print(f'Epoch {epoch+1}/{num_epochs}, Train Loss: {avg_loss:.4f}, Test Loss: {test_loss.item():.4f}, Test MAE: {test_mae.item():.4f}')
    
    # 加载最佳模型进行最终预测
    model.load_state_dict(torch.load(os.path.join(BASE_PATH, 'best_model.pth')))
    
    # 生成预测结果
    print("生成预测结果...")
    model.eval()
    with torch.no_grad():
        # 进行预测
        test_predictions = model(X_test_stations, test_edge_index).cpu().numpy()[0]  # [num_test_nodes]

        # 创建预测结果DataFrame
        prediction_df = pd.DataFrame()

        # 对于测试集中的每个站点
        for i, station_idx in enumerate(test_indices):
            station_name = common_stations[station_idx]
            prediction = test_predictions[i]

            # 将预测添加到DataFrame
            prediction_df = prediction_df.append({
                'station': station_name,
                'prediction': prediction
            }, ignore_index=True)

        # 合并预测到原始数据
        test_in_data = in_data[in_data['station'].isin(test_stations)].copy()
        test_in_data = pd.merge(test_in_data, prediction_df, on='station', how='left')

        # 预测出站数据 (假设与进站相关)
        test_out_data = out_data[out_data['station'].isin(test_stations)].copy()
        test_out_data = pd.merge(test_out_data, prediction_df, on='station', how='left')

        # 计算OD预测
        od_subset = od_data[
            (od_data['o_rawname'].isin(test_stations)) &
            (od_data['d_rawname'].isin(test_stations))
            ].copy()

        # 基于进站预测估计OD流量
        od_predictions = []

        for _, row in od_subset.iterrows():
            o_station = row['o_rawname']
            d_station = row['d_rawname']

            # 找到对应站点的预测值
            o_pred = prediction_df[prediction_df['station'] == o_station]['prediction'].values
            d_pred = prediction_df[prediction_df['station'] == d_station]['prediction'].values

            if len(o_pred) > 0 and len(d_pred) > 0:
                # 简单估计：使用两站点预测的几何平均
                od_pred = np.sqrt(o_pred[0] * d_pred[0])

                od_predictions.append({
                    'o_rawname': o_station,
                    'd_rawname': d_station,
                    'prediction': od_pred
                })

        # 创建OD预测DataFrame
        od_pred_df = pd.DataFrame(od_predictions)

        # 合并OD预测到原始数据
        od_subset = pd.merge(od_subset, od_pred_df, on=['o_rawname', 'd_rawname'], how='left')

        # 保存预测结果
        # 将进站预测结果转换为GeoDataFrame并保存
        gdf_in_pred = gpd.GeoDataFrame(test_in_data, geometry='geometry')
        gdf_in_pred.to_file(os.path.join(BASE_PATH, 'in_500_predictions_with_coords.shp'))

        # 将出站预测结果转换为GeoDataFrame并保存
        gdf_out_pred = gpd.GeoDataFrame(test_out_data, geometry='geometry')
        gdf_out_pred.to_file(os.path.join(BASE_PATH, 'out_500_predictions_with_coords.shp'))

        # 保存OD预测结果
        od_subset.to_csv(os.path.join(BASE_PATH, 'od_predictions.csv'), index=False)

        # 创建预测比较
        prediction_comparison = []

        # 进站预测比较
        for _, row in test_in_data.iterrows():
            if pd.notna(row['prediction']):
                prediction_comparison.append({
                    'Type': 'In',
                    'Station': row['station'],
                    'Hour': row['hour'],
                    'True_Value': row['count'],
                    'Predicted_Value': row['prediction']
                })

        # 出站预测比较
        for _, row in test_out_data.iterrows():
            if pd.notna(row['prediction']):
                prediction_comparison.append({
                    'Type': 'Out',
                    'Station': row['station'],
                    'Hour': row['hour'],
                    'True_Value': row['count'],
                    'Predicted_Value': row['prediction']
                })

        # OD预测比较
        for _, row in od_subset.iterrows():
            if pd.notna(row['prediction']):
                prediction_comparison.append({
                    'Type': 'OD',
                    'Origin': row['o_rawname'],
                    'Destination': row['d_rawname'],
                    'Hour': row['hour'],
                    'True_Value': row['trip'],
                    'Predicted_Value': row['prediction']
                })

        # 保存预测比较结果
        pd.DataFrame(prediction_comparison).to_csv(os.path.join(BASE_PATH, 'prediction_comparison.csv'), index=False)

        # 计算总体评估指标
        in_results = test_in_data.dropna(subset=['prediction'])
        out_results = test_out_data.dropna(subset=['prediction'])
        od_results = od_subset.dropna(subset=['prediction'])

        if len(in_results) > 0:
            in_mae = mean_absolute_error(in_results['count'], in_results['prediction'])
            print(f"进站预测 MAE: {in_mae:.4f}")

        if len(out_results) > 0:
            out_mae = mean_absolute_error(out_results['count'], out_results['prediction'])
            print(f"出站预测 MAE: {out_mae:.4f}")

        if len(od_results) > 0:
            od_mae = mean_absolute_error(od_results['trip'], od_results['prediction'])
            print(f"OD预测 MAE: {od_mae:.4f}")

    print("改进的训练完成!")


# 修改主函数
def main():
    # 加载数据
    in_data, out_data, od_data, station_features = load_data()
    
    # 预处理数据
    (X, flow_matrix, edge_index, train_indices, test_indices,
     common_stations, train_stations, test_stations,
     station_to_idx, test_station_to_idx, in_data, out_data, od_data) = preprocess_data(
        in_data, out_data, od_data, station_features
    )
    
    # 使用改进的训练函数
    improved_train_and_evaluate(
        X, flow_matrix, edge_index, train_indices, test_indices, common_stations,
        train_stations, test_stations, station_to_idx, test_station_to_idx, in_data, out_data, od_data
    )


if __name__ == "__main__":
    main()
