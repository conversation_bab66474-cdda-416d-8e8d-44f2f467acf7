"""
完整的空间差异化地铁流量预测系统
包含进站、出站、OD流量预测的完整功能
"""
import os
import sys
import time
import numpy as np
import pandas as pd
import geopandas as gpd
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')

class SpatialAwareModel(nn.Module):
    """空间感知模型"""
    
    def __init__(self, input_dim, hidden_dim=64, model_type='station'):
        super(SpatialAwareModel, self).__init__()
        
        self.model_type = model_type
        
        if model_type == 'od':
            # OD模型：处理起点+终点+OD特征
            # 起点特征编码器
            self.origin_encoder = nn.Sequential(
                nn.Linear(input_dim // 2, hidden_dim),
                nn.ReLU(),
                nn.BatchNorm1d(hidden_dim),
                nn.Dropout(0.2),
                nn.Linear(hidden_dim, hidden_dim // 2)
            )
            
            # 终点特征编码器
            self.destination_encoder = nn.Sequential(
                nn.Linear(input_dim // 2, hidden_dim),
                nn.ReLU(),
                nn.BatchNorm1d(hidden_dim),
                nn.Dropout(0.2),
                nn.Linear(hidden_dim, hidden_dim // 2)
            )
            
            # OD对特征编码器
            self.od_pair_encoder = nn.Sequential(
                nn.Linear(4, 16),  # surface_distance, translate, time, wait_time
                nn.ReLU(),
                nn.Linear(16, 8)
            )
            
            # 时间编码器
            self.time_encoder = nn.Sequential(
                nn.Linear(6, 16),
                nn.ReLU(),
                nn.Linear(16, 8)
            )
            
            # OD交互层
            interaction_dim = hidden_dim // 2 + hidden_dim // 2 + 8 + 8
            self.interaction_layer = nn.Sequential(
                nn.Linear(interaction_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.3),
                nn.Linear(hidden_dim, 32),
                nn.ReLU(),
                nn.Linear(32, 1)
            )
            
        else:
            # 进出站模型：处理单站点特征
            self.feature_encoder = nn.Sequential(
                nn.Linear(input_dim, hidden_dim),
                nn.ReLU(),
                nn.BatchNorm1d(hidden_dim),
                nn.Dropout(0.2),
                nn.Linear(hidden_dim, hidden_dim // 2),
                nn.ReLU()
            )
            
            # 空间距离编码器
            self.spatial_encoder = nn.Sequential(
                nn.Linear(3, 16),
                nn.ReLU(),
                nn.Linear(16, 8)
            )
            
            # 时间编码器
            self.time_encoder = nn.Sequential(
                nn.Linear(6, 16),
                nn.ReLU(),
                nn.Linear(16, 8)
            )
            
            # 融合层
            fusion_dim = hidden_dim // 2 + 8 + 8
            self.fusion_layer = nn.Sequential(
                nn.Linear(fusion_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.3),
                nn.Linear(hidden_dim, 32),
                nn.ReLU(),
                nn.Linear(32, 1)
            )
    
    def forward(self, *args):
        if self.model_type == 'od':
            origin_features, dest_features, od_pair_features, time_features = args
            
            # 编码起点和终点特征
            origin_encoded = self.origin_encoder(origin_features)
            dest_encoded = self.destination_encoder(dest_features)
            
            # 编码OD对特征和时间特征
            od_encoded = self.od_pair_encoder(od_pair_features)
            time_encoded = self.time_encoder(time_features)
            
            # 特征融合
            combined = torch.cat([origin_encoded, dest_encoded, od_encoded, time_encoded], dim=-1)
            
            # OD交互预测
            output = self.interaction_layer(combined)
            
        else:
            features, spatial_features, time_features = args
            
            # 编码各类特征
            feat_encoded = self.feature_encoder(features)
            spatial_encoded = self.spatial_encoder(spatial_features)
            time_encoded = self.time_encoder(time_features)
            
            # 特征融合
            combined = torch.cat([feat_encoded, spatial_encoded, time_encoded], dim=-1)
            
            # 最终预测
            output = self.fusion_layer(combined)
        
        return torch.relu(output)  # 确保非负

class CompleteSpatialPredictionSystem:
    """完整的空间预测系统"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
        # 数据存储
        self.in_data = None
        self.out_data = None
        self.od_data = None
        self.grid_data = None
        self.station_coords = {}
        
        # 模型
        self.in_model = None
        self.out_model = None
        self.od_model = None
        
        # 标准化器
        self.feature_scaler = StandardScaler()
        self.spatial_scaler = StandardScaler()
        self.time_scaler = StandardScaler()
        self.od_scaler = StandardScaler()
        
        # 站点信息
        self.stations = []
        self.station_to_idx = {}
        
    def load_and_process_data(self):
        """加载和处理数据"""
        print("="*60)
        print("加载完整数据集")
        print("="*60)
        
        try:
            # 加载基础数据
            self.in_data = gpd.read_file('in_500_with_coords.shp')
            self.out_data = gpd.read_file('out_500_with_coords.shp')
            self.od_data = pd.read_csv('updated北京市_subway_od_2024_modified3.csv')
            self.grid_data = pd.read_csv('leti_data.csv')
            
            # 清理站点名称
            self.in_data['station_clean'] = self.in_data['station'].apply(lambda x: str(x).split('_')[0])
            self.out_data['station_clean'] = self.out_data['station'].apply(lambda x: str(x).split('_')[0])
            self.od_data['o_station_clean'] = self.od_data['o_rawname'].apply(lambda x: str(x).split('_')[0])
            self.od_data['d_station_clean'] = self.od_data['d_rawname'].apply(lambda x: str(x).split('_')[0])
            
            # 提取坐标
            self.in_data['longitude'] = self.in_data.geometry.centroid.x
            self.in_data['latitude'] = self.in_data.geometry.centroid.y
            self.out_data['longitude'] = self.out_data.geometry.centroid.x
            self.out_data['latitude'] = self.out_data.geometry.centroid.y
            
            print(f"进站数据: {self.in_data.shape}")
            print(f"出站数据: {self.out_data.shape}")
            print(f"OD数据: {self.od_data.shape}")
            print(f"栅格数据: {self.grid_data.shape}")
            
            # 获取站点列表和坐标
            self._extract_station_coordinates()
            
            # 处理栅格特征
            feature_cols = [col for col in self.grid_data.columns if col not in ['站名', 'id', 'geometry']]
            self.grid_features = self.grid_data[feature_cols].values
            self.grid_features = np.nan_to_num(self.grid_features, nan=0.0)
            
            print(f"栅格特征维度: {self.grid_features.shape}")
            
            return True
            
        except Exception as e:
            print(f"数据加载失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _extract_station_coordinates(self):
        """提取站点坐标"""
        all_stations = set()
        all_stations.update(self.in_data['station_clean'].unique())
        all_stations.update(self.out_data['station_clean'].unique())
        all_stations.update(self.od_data['o_station_clean'].unique())
        all_stations.update(self.od_data['d_station_clean'].unique())
        
        self.stations = sorted(list(all_stations))
        self.station_to_idx = {station: idx for idx, station in enumerate(self.stations)}
        
        # 提取每个站点的代表坐标
        for station in self.stations:
            in_coords = self.in_data[self.in_data['station_clean'] == station][['longitude', 'latitude']]
            out_coords = self.out_data[self.out_data['station_clean'] == station][['longitude', 'latitude']]
            
            all_coords = pd.concat([in_coords, out_coords])
            if len(all_coords) > 0:
                self.station_coords[station] = {
                    'longitude': all_coords['longitude'].mean(),
                    'latitude': all_coords['latitude'].mean()
                }
            else:
                # 如果没有进出站数据，使用默认坐标
                self.station_coords[station] = {
                    'longitude': 116.4,  # 北京市中心
                    'latitude': 39.9
                }
        
        print(f"提取了 {len(self.stations)} 个站点的坐标")
    
    def _calculate_spatial_features(self, grid_lon, grid_lat, station_lon, station_lat):
        """计算空间特征"""
        distance = np.sqrt((grid_lon - station_lon)**2 + (grid_lat - station_lat)**2)
        direction = np.arctan2(station_lat - grid_lat, station_lon - grid_lon)
        accessibility = 1.0 / (1.0 + distance * 100)
        return np.array([distance, direction, accessibility])
    
    def _calculate_time_features(self, hour):
        """计算时间特征"""
        return np.array([
            hour / 23.0,
            np.sin(2 * np.pi * hour / 24),
            np.cos(2 * np.pi * hour / 24),
            1.0 if 6 <= hour <= 10 else 0.0,
            1.0 if 17 <= hour <= 20 else 0.0,
            1.0 if hour in [0,1,2,3,4,5,21,22,23] else 0.0
        ])
    
    def prepare_station_training_data(self, data, flow_type):
        """准备进出站训练数据"""
        features = []
        spatial_features = []
        time_features = []
        targets = []
        
        avg_grid_features = np.mean(self.grid_features, axis=0)
        
        for idx, row in data.iterrows():
            station = row['station_clean']
            hour = row['hour']
            count = row['count']
            grid_lon = row['longitude']
            grid_lat = row['latitude']
            
            if station in self.station_coords:
                station_coord = self.station_coords[station]
                station_lon = station_coord['longitude']
                station_lat = station_coord['latitude']
                
                # 基础特征
                station_idx = self.station_to_idx[station]
                base_features = np.concatenate([
                    avg_grid_features,
                    [station_lon, station_lat, station_idx / len(self.stations)]
                ])
                
                # 空间特征
                spatial_feature = self._calculate_spatial_features(
                    grid_lon, grid_lat, station_lon, station_lat
                )
                
                # 时间特征
                time_feature = self._calculate_time_features(hour)
                
                features.append(base_features)
                spatial_features.append(spatial_feature)
                time_features.append(time_feature)
                targets.append(count)
        
        return (np.array(features), np.array(spatial_features), 
                np.array(time_features), np.array(targets))
    
    def prepare_od_training_data(self):
        """准备OD训练数据"""
        origin_features = []
        dest_features = []
        od_pair_features = []
        time_features = []
        targets = []
        
        avg_grid_features = np.mean(self.grid_features, axis=0)
        
        for idx, row in self.od_data.iterrows():
            o_station = row['o_station_clean']
            d_station = row['d_station_clean']
            hour = row['hour']
            trip = row['trip']
            
            # 检查站点是否存在
            if o_station in self.station_coords and d_station in self.station_coords:
                # 起点特征
                o_coord = self.station_coords[o_station]
                o_idx = self.station_to_idx[o_station]
                origin_feature = np.concatenate([
                    avg_grid_features,
                    [o_coord['longitude'], o_coord['latitude'], o_idx / len(self.stations)]
                ])
                
                # 终点特征
                d_coord = self.station_coords[d_station]
                d_idx = self.station_to_idx[d_station]
                dest_feature = np.concatenate([
                    avg_grid_features,
                    [d_coord['longitude'], d_coord['latitude'], d_idx / len(self.stations)]
                ])
                
                # OD对特征
                od_pair_feature = np.array([
                    row.get('surface_distance', 0),
                    row.get('translate', 0),
                    row.get('time', 0),
                    row.get('wait_time', 0)
                ])
                
                # 时间特征
                time_feature = self._calculate_time_features(hour)
                
                origin_features.append(origin_feature)
                dest_features.append(dest_feature)
                od_pair_features.append(od_pair_feature)
                time_features.append(time_feature)
                targets.append(trip)
        
        return (np.array(origin_features), np.array(dest_features),
                np.array(od_pair_features), np.array(time_features), np.array(targets))
    
    def train_complete_models(self):
        """训练完整的模型"""
        print("="*60)
        print("训练完整的空间感知模型")
        print("="*60)
        
        # 准备进站数据
        print("准备进站训练数据...")
        in_features, in_spatial, in_time, in_targets = self.prepare_station_training_data(self.in_data, 'in')
        
        # 准备出站数据
        print("准备出站训练数据...")
        out_features, out_spatial, out_time, out_targets = self.prepare_station_training_data(self.out_data, 'out')
        
        # 准备OD数据
        print("准备OD训练数据...")
        od_origin, od_dest, od_pair, od_time, od_targets = self.prepare_od_training_data()
        
        # 标准化特征
        in_features = self.feature_scaler.fit_transform(in_features)
        in_spatial = self.spatial_scaler.fit_transform(in_spatial)
        in_time = self.time_scaler.fit_transform(in_time)
        
        # 训练进站模型
        print("训练进站模型...")
        self.in_model = self._train_station_model(in_features, in_spatial, in_time, in_targets, 'in')
        
        # 使用相同的标准化器处理出站数据
        out_features = self.feature_scaler.transform(out_features)
        out_spatial = self.spatial_scaler.transform(out_spatial)
        out_time = self.time_scaler.transform(out_time)
        
        # 训练出站模型
        print("训练出站模型...")
        self.out_model = self._train_station_model(out_features, out_spatial, out_time, out_targets, 'out')
        
        # 标准化OD特征
        od_origin = self.feature_scaler.transform(od_origin)
        od_dest = self.feature_scaler.transform(od_dest)
        od_pair = self.od_scaler.fit_transform(od_pair)
        od_time = self.time_scaler.transform(od_time)
        
        # 训练OD模型
        print("训练OD模型...")
        self.od_model = self._train_od_model(od_origin, od_dest, od_pair, od_time, od_targets)
        
        print("所有模型训练完成！")
    
    def _train_station_model(self, features, spatial_features, time_features, targets, model_type):
        """训练进出站模型"""
        input_dim = features.shape[1]
        model = SpatialAwareModel(input_dim=input_dim, hidden_dim=64, model_type='station').to(self.device)
        
        # 转换为张量
        features_tensor = torch.tensor(features, dtype=torch.float32).to(self.device)
        spatial_tensor = torch.tensor(spatial_features, dtype=torch.float32).to(self.device)
        time_tensor = torch.tensor(time_features, dtype=torch.float32).to(self.device)
        targets_tensor = torch.tensor(targets, dtype=torch.float32).to(self.device)
        
        # 训练设置
        optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=1e-4)
        criterion = nn.MSELoss()
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
        
        # 训练循环
        model.train()
        best_loss = float('inf')
        patience = 0
        
        for epoch in range(100):
            optimizer.zero_grad()
            
            predictions = model(features_tensor, spatial_tensor, time_tensor).squeeze()
            loss = criterion(predictions, targets_tensor)
            
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
            optimizer.step()
            
            scheduler.step(loss.item())
            
            if loss.item() < best_loss:
                best_loss = loss.item()
                patience = 0
            else:
                patience += 1
            
            if epoch % 20 == 0:
                print(f"  {model_type} Epoch {epoch}: Loss = {loss.item():.6f}")
            
            if patience >= 20:
                print(f"  {model_type} 早停于第 {epoch} 轮")
                break
        
        return model
    
    def _train_od_model(self, origin_features, dest_features, od_pair_features, time_features, targets):
        """训练OD模型"""
        input_dim = origin_features.shape[1] * 2  # 起点+终点特征
        model = SpatialAwareModel(input_dim=input_dim, hidden_dim=64, model_type='od').to(self.device)
        
        # 转换为张量
        origin_tensor = torch.tensor(origin_features, dtype=torch.float32).to(self.device)
        dest_tensor = torch.tensor(dest_features, dtype=torch.float32).to(self.device)
        od_pair_tensor = torch.tensor(od_pair_features, dtype=torch.float32).to(self.device)
        time_tensor = torch.tensor(time_features, dtype=torch.float32).to(self.device)
        targets_tensor = torch.tensor(targets, dtype=torch.float32).to(self.device)
        
        # 训练设置
        optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=1e-4)
        criterion = nn.MSELoss()
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
        
        # 训练循环
        model.train()
        best_loss = float('inf')
        patience = 0
        
        for epoch in range(100):
            optimizer.zero_grad()
            
            predictions = model(origin_tensor, dest_tensor, od_pair_tensor, time_tensor).squeeze()
            loss = criterion(predictions, targets_tensor)
            
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
            optimizer.step()
            
            scheduler.step(loss.item())
            
            if loss.item() < best_loss:
                best_loss = loss.item()
                patience = 0
            else:
                patience += 1
            
            if epoch % 20 == 0:
                print(f"  OD Epoch {epoch}: Loss = {loss.item():.6f}")
            
            if patience >= 20:
                print(f"  OD 早停于第 {epoch} 轮")
                break
        
        return model

    def generate_complete_predictions(self):
        """生成完整的预测结果"""
        print("="*60)
        print("生成完整的预测结果")
        print("="*60)

        # 时间分割策略
        test_hours = list(range(18, 24))

        # 生成进站预测
        print("生成进站预测...")
        in_test_data = self.in_data[self.in_data['hour'].isin(test_hours)].copy()

        if len(in_test_data) > 0:
            in_test_features, in_test_spatial, in_test_time, _ = self.prepare_station_training_data(in_test_data, 'in')

            # 标准化
            in_test_features = self.feature_scaler.transform(in_test_features)
            in_test_spatial = self.spatial_scaler.transform(in_test_spatial)
            in_test_time = self.time_scaler.transform(in_test_time)

            # 预测
            in_predictions = self._predict_with_station_model(
                self.in_model, in_test_features, in_test_spatial, in_test_time
            )

            in_test_data.loc[:, 'prediction'] = np.maximum(in_predictions, 0)

            # 保存结果
            self._save_station_predictions(in_test_data, 'in')

        # 生成出站预测
        print("生成出站预测...")
        out_test_data = self.out_data[self.out_data['hour'].isin(test_hours)].copy()

        if len(out_test_data) > 0:
            out_test_features, out_test_spatial, out_test_time, _ = self.prepare_station_training_data(out_test_data, 'out')

            # 标准化
            out_test_features = self.feature_scaler.transform(out_test_features)
            out_test_spatial = self.spatial_scaler.transform(out_test_spatial)
            out_test_time = self.time_scaler.transform(out_test_time)

            # 预测
            out_predictions = self._predict_with_station_model(
                self.out_model, out_test_features, out_test_spatial, out_test_time
            )

            out_test_data.loc[:, 'prediction'] = np.maximum(out_predictions, 0)

            # 保存结果
            self._save_station_predictions(out_test_data, 'out')

        # 生成OD预测
        print("生成OD预测...")
        od_test_data = self.od_data[self.od_data['hour'].isin(test_hours)].copy()

        if len(od_test_data) > 0:
            od_predictions = self._generate_od_predictions(od_test_data)

            od_test_data.loc[:, 'prediction'] = np.maximum(od_predictions, 0)

            # 保存OD预测结果
            od_test_data.to_csv('od_predictions.csv', index=False)
            print(f"✓ OD预测结果已保存: {len(od_test_data)} 条记录")

        # 生成完整对比分析
        self._generate_complete_comparison(in_test_data, out_test_data, od_test_data)

        return in_test_data, out_test_data, od_test_data

    def _predict_with_station_model(self, model, features, spatial_features, time_features):
        """使用进出站模型进行预测"""
        model.eval()

        with torch.no_grad():
            features_tensor = torch.tensor(features, dtype=torch.float32).to(self.device)
            spatial_tensor = torch.tensor(spatial_features, dtype=torch.float32).to(self.device)
            time_tensor = torch.tensor(time_features, dtype=torch.float32).to(self.device)

            predictions = model(features_tensor, spatial_tensor, time_tensor).squeeze()

        return predictions.cpu().numpy()

    def _generate_od_predictions(self, od_test_data):
        """生成OD预测"""
        origin_features = []
        dest_features = []
        od_pair_features = []
        time_features = []

        avg_grid_features = np.mean(self.grid_features, axis=0)

        for idx, row in od_test_data.iterrows():
            o_station = row['o_station_clean']
            d_station = row['d_station_clean']
            hour = row['hour']

            if o_station in self.station_coords and d_station in self.station_coords:
                # 起点特征
                o_coord = self.station_coords[o_station]
                o_idx = self.station_to_idx[o_station]
                origin_feature = np.concatenate([
                    avg_grid_features,
                    [o_coord['longitude'], o_coord['latitude'], o_idx / len(self.stations)]
                ])

                # 终点特征
                d_coord = self.station_coords[d_station]
                d_idx = self.station_to_idx[d_station]
                dest_feature = np.concatenate([
                    avg_grid_features,
                    [d_coord['longitude'], d_coord['latitude'], d_idx / len(self.stations)]
                ])

                # OD对特征
                od_pair_feature = np.array([
                    row.get('surface_distance', 0),
                    row.get('translate', 0),
                    row.get('time', 0),
                    row.get('wait_time', 0)
                ])

                # 时间特征
                time_feature = self._calculate_time_features(hour)

                origin_features.append(origin_feature)
                dest_features.append(dest_feature)
                od_pair_features.append(od_pair_feature)
                time_features.append(time_feature)
            else:
                # 如果站点不存在，添加零向量
                zero_feature = np.zeros(len(avg_grid_features) + 3)
                origin_features.append(zero_feature)
                dest_features.append(zero_feature)
                od_pair_features.append(np.zeros(4))
                time_features.append(self._calculate_time_features(hour))

        # 转换为数组并标准化
        origin_features = np.array(origin_features)
        dest_features = np.array(dest_features)
        od_pair_features = np.array(od_pair_features)
        time_features = np.array(time_features)

        origin_features = self.feature_scaler.transform(origin_features)
        dest_features = self.feature_scaler.transform(dest_features)
        od_pair_features = self.od_scaler.transform(od_pair_features)
        time_features = self.time_scaler.transform(time_features)

        # 预测
        self.od_model.eval()
        with torch.no_grad():
            origin_tensor = torch.tensor(origin_features, dtype=torch.float32).to(self.device)
            dest_tensor = torch.tensor(dest_features, dtype=torch.float32).to(self.device)
            od_pair_tensor = torch.tensor(od_pair_features, dtype=torch.float32).to(self.device)
            time_tensor = torch.tensor(time_features, dtype=torch.float32).to(self.device)

            predictions = self.od_model(origin_tensor, dest_tensor, od_pair_tensor, time_tensor).squeeze()

        return predictions.cpu().numpy()

    def _save_station_predictions(self, data, flow_type):
        """保存进出站预测结果"""
        filename = f'complete_{flow_type}_500_predictions_with_coords'

        try:
            import glob
            for f in glob.glob(f'{filename}.*'):
                try:
                    os.remove(f)
                except:
                    pass

            data.to_file(f'{filename}.shp')
            print(f"✓ {flow_type}站预测结果已保存: {len(data)} 条记录")
        except Exception as e:
            print(f"保存{flow_type}站shapefile失败: {e}")
            data_csv = data.drop(columns=['geometry'])
            data_csv.to_csv(f'{filename}.csv', index=False)
            print(f"✓ {flow_type}站预测结果已保存为CSV: {len(data)} 条记录")

    def _generate_complete_comparison(self, in_test_data, out_test_data, od_test_data):
        """生成完整的预测对比分析"""
        print("生成完整预测对比汇总...")

        comparison_data = []

        # 进站对比
        if in_test_data is not None and len(in_test_data) > 0:
            for _, row in in_test_data.iterrows():
                comparison_data.append({
                    'Type': 'In',
                    'True_Value': float(row['count']),
                    'Predicted_Value': float(row['prediction']),
                    'Station': str(row['station_clean']),
                    'Hour': int(row['hour']),
                    'Error': float(abs(row['count'] - row['prediction']))
                })

        # 出站对比
        if out_test_data is not None and len(out_test_data) > 0:
            for _, row in out_test_data.iterrows():
                comparison_data.append({
                    'Type': 'Out',
                    'True_Value': float(row['count']),
                    'Predicted_Value': float(row['prediction']),
                    'Station': str(row['station_clean']),
                    'Hour': int(row['hour']),
                    'Error': float(abs(row['count'] - row['prediction']))
                })

        # OD对比
        if od_test_data is not None and len(od_test_data) > 0:
            for _, row in od_test_data.iterrows():
                comparison_data.append({
                    'Type': 'OD',
                    'True_Value': float(row['trip']),
                    'Predicted_Value': float(row['prediction']),
                    'Station': f"{row['o_station_clean']}->{row['d_station_clean']}",
                    'Hour': int(row['hour']),
                    'Error': float(abs(row['trip'] - row['prediction']))
                })

        # 保存完整对比结果
        if comparison_data:
            comparison_df = pd.DataFrame(comparison_data)
            comparison_df.to_csv('complete_prediction_comparison.csv', index=False)
            print(f"✓ 完整预测对比汇总已保存: {len(comparison_data)} 条记录")

            # 生成性能分析
            self._analyze_complete_performance(comparison_df)

        return comparison_data

    def _analyze_complete_performance(self, comparison_df):
        """分析完整的预测性能"""
        print("\n" + "="*60)
        print("完整预测性能分析")
        print("="*60)

        performance_results = {}

        # 按类型分析
        for pred_type in ['In', 'Out', 'OD']:
            type_data = comparison_df[comparison_df['Type'] == pred_type]

            if len(type_data) > 0:
                true_values = type_data['True_Value'].values
                pred_values = type_data['Predicted_Value'].values

                mae = mean_absolute_error(true_values, pred_values)
                rmse = np.sqrt(mean_squared_error(true_values, pred_values))
                r2 = r2_score(true_values, pred_values)

                performance_results[pred_type] = {
                    'samples': len(type_data),
                    'mae': mae,
                    'rmse': rmse,
                    'r2': r2,
                    'mean_true': np.mean(true_values),
                    'mean_pred': np.mean(pred_values)
                }

                print(f"{pred_type} 流量预测:")
                print(f"  样本数: {len(type_data):,}")
                print(f"  MAE:  {mae:.4f}")
                print(f"  RMSE: {rmse:.4f}")
                print(f"  R²:   {r2:.4f}")
                print(f"  真实值均值: {np.mean(true_values):.2f}")
                print(f"  预测值均值: {np.mean(pred_values):.2f}")
                print()

        # 整体性能
        all_true = comparison_df['True_Value'].values
        all_pred = comparison_df['Predicted_Value'].values

        overall_mae = mean_absolute_error(all_true, all_pred)
        overall_rmse = np.sqrt(mean_squared_error(all_true, all_pred))
        overall_r2 = r2_score(all_true, all_pred)

        performance_results['Overall'] = {
            'samples': len(comparison_df),
            'mae': overall_mae,
            'rmse': overall_rmse,
            'r2': overall_r2,
            'mean_true': np.mean(all_true),
            'mean_pred': np.mean(all_pred)
        }

        print("整体预测性能:")
        print(f"  总样本数: {len(comparison_df):,}")
        print(f"  MAE:  {overall_mae:.4f}")
        print(f"  RMSE: {overall_rmse:.4f}")
        print(f"  R²:   {overall_r2:.4f}")
        print(f"  真实值均值: {np.mean(all_true):.2f}")
        print(f"  预测值均值: {np.mean(all_pred):.2f}")

        # 保存性能结果
        import json
        with open('complete_performance_analysis.json', 'w') as f:
            json.dump(performance_results, f, indent=2)

        print("✓ 完整性能分析已保存到 complete_performance_analysis.json")

        # OD距离衰减效应验证
        self._verify_od_distance_effect(comparison_df)

    def _verify_od_distance_effect(self, comparison_df):
        """验证OD预测的距离衰减效应"""
        print("\n验证OD距离衰减效应:")
        print("-" * 40)

        od_data = comparison_df[comparison_df['Type'] == 'OD']

        if len(od_data) > 0:
            # 计算OD对的距离
            distances = []
            predictions = []

            for _, row in od_data.iterrows():
                station_pair = row['Station']
                if '->' in station_pair:
                    o_station, d_station = station_pair.split('->')

                    if o_station in self.station_coords and d_station in self.station_coords:
                        o_coord = self.station_coords[o_station]
                        d_coord = self.station_coords[d_station]

                        # 计算距离
                        distance = np.sqrt(
                            (o_coord['longitude'] - d_coord['longitude'])**2 +
                            (o_coord['latitude'] - d_coord['latitude'])**2
                        )

                        distances.append(distance)
                        predictions.append(row['Predicted_Value'])

            if len(distances) > 0:
                # 计算相关性
                try:
                    from scipy.stats import pearsonr
                    correlation, p_value = pearsonr(distances, predictions)
                except ImportError:
                    correlation = np.corrcoef(distances, predictions)[0, 1]
                    p_value = 0.05

                print(f"OD距离-流量相关系数: {correlation:.4f}")
                print(f"p值: {p_value:.6f}")

                if correlation < -0.1 and p_value < 0.05:
                    print("✅ 发现显著的距离衰减效应（距离越远，OD流量越小）")
                elif correlation > 0.1 and p_value < 0.05:
                    print("⚠️ 发现正相关关系（需要进一步调查）")
                else:
                    print("○ OD距离-流量关系不显著")

                # 按距离分组分析
                distance_bins = np.percentile(distances, [0, 25, 50, 75, 100])
                for i in range(len(distance_bins)-1):
                    mask = (np.array(distances) >= distance_bins[i]) & (np.array(distances) < distance_bins[i+1])
                    if mask.sum() > 0:
                        avg_flow = np.array(predictions)[mask].mean()
                        print(f"距离区间 [{distance_bins[i]:.3f}, {distance_bins[i+1]:.3f}): 平均OD流量 {avg_flow:.2f}")
            else:
                print("无法计算OD距离衰减效应（缺少有效的站点坐标）")
        else:
            print("无OD预测数据")

def main():
    """主函数"""
    print("="*80)
    print("完整的空间差异化地铁流量预测系统")
    print("包含进站、出站、OD流量预测的完整功能")
    print("="*80)

    start_time = time.time()

    try:
        # 初始化系统
        system = CompleteSpatialPredictionSystem()

        # 加载数据
        if not system.load_and_process_data():
            return False

        # 训练模型
        system.train_complete_models()

        # 生成预测
        in_results, out_results, od_results = system.generate_complete_predictions()

        print(f"\n运行时间: {time.time() - start_time:.2f} 秒")
        print("="*60)
        print("完整的空间感知预测系统运行完成！")
        print("="*60)

        return True

    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
