"""
T-GCN项目配置文件 - 改进版本
包含数据路径、模型参数、训练参数等配置
"""
import os

class Config:
    """配置类 - 改进版本"""
    
    # ================== 基础路径配置 ==================
    BASE_PATH = "C:\\Users\\<USER>\\Desktop\\接驳\\"
    
    # ================== 数据文件路径 ==================
    IN_FILE = os.path.join(BASE_PATH, "in_500_with_coords.shp")
    OUT_FILE = os.path.join(BASE_PATH, "out_500_with_coords.shp")
    OD_FILE = os.path.join(BASE_PATH, "updated北京市_subway_od_2024_modified3.csv")
    FEATURES_FILE = os.path.join(BASE_PATH, "station_features_result.csv")
    
    # ================== 输出文件路径 ==================
    OUTPUT_IN_PRED = os.path.join(BASE_PATH, "improved_in_predictions_with_coords.shp")
    OUTPUT_OUT_PRED = os.path.join(BASE_PATH, "improved_out_predictions_with_coords.shp")
    OUTPUT_OD_PRED = os.path.join(BASE_PATH, "improved_od_predictions.csv")
    OUTPUT_COMPARISON = os.path.join(BASE_PATH, "improved_prediction_comparison.csv")
    OUTPUT_TRAINING_CURVES = os.path.join(BASE_PATH, "improved_training_curves.png")
    
    # ================== 数据处理参数 ==================
    TRAIN_RATIO = 0.8           # 训练集比例
    TIME_STEPS = 24             # 时间步数(小时)
    
    # ================== 模型参数 ==================
    HIDDEN_DIM = 64             # 隐藏层维度（增加了）
    OUTPUT_DIM = 1              # 输出维度
    NUM_HEADS = 4               # 注意力头数
    
    # ================== 训练参数 ==================
    NUM_EPOCHS = 20            # 训练轮数（增加了）
    LEARNING_RATE = 0.001       # 学习率（降低了）
    BATCH_SIZE = 1              # 批次大小
    PATIENCE = 5               # 早停耐心值
    
    # ================== 多任务权重 ==================
    TASK_WEIGHTS = {
        'in_flow': 0.3,
        'out_flow': 0.3, 
        'od_flow': 0.4
    }
    
    # ================== 其他参数 ==================
    RANDOM_SEED = 42            # 随机种子
    USE_GPU = True              # 是否使用GPU
    DEVICE = "cuda" if USE_GPU else "cpu"
    
    # ================== 图构建参数 ==================
    MIN_OD_THRESHOLD = 0.0      # 最小OD流量阈值
    MAX_EDGES_PER_NODE = 50     # 每个节点最大连接数
    USE_SPATIAL_EDGES = True    # 是否使用空间邻接边
    SPATIAL_THRESHOLD_KM = 2.0  # 空间邻接阈值(公里)
    
    # ================== GPU优化参数 ==================
    USE_MIXED_PRECISION = True     # 混合精度训练
    ACCUMULATION_STEPS = 4         # 梯度累积步数
    COMPILE_MODEL = True           # 模型编译（PyTorch 2.0+）
    
    # ================== 内存优化参数 ==================
    USE_GRADIENT_CHECKPOINTING = True  # 梯度检查点
    MAX_NODES_PER_BATCH = 100          # 每批最大节点数
    MAX_EDGES_PER_NODE = 15            # 每节点最大边数
    
    # ================== 性能监控参数 ==================
    MEMORY_MONITOR = True              # 内存监控
    PROFILE_GPU = True                 # GPU性能分析
