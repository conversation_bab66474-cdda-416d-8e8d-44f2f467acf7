"""
测试数据加载功能
"""
import os
import sys
import pandas as pd
import numpy as np

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_data_files():
    """测试数据文件是否存在和可读"""
    print("测试数据文件...")
    
    data_dir = r"C:\Users\<USER>\Desktop\接驳"
    
    files_to_check = {
        'subway_flow': 'subway_flow_24h_results.csv',
        'station_connect': 'station_connect_2023.csv',
        'grid_features': 'leti_data.csv',
        'od_data': 'updated北京市_subway_od_2024_modified3.csv'
    }
    
    results = {}
    
    for name, filename in files_to_check.items():
        filepath = os.path.join(data_dir, filename)
        
        if os.path.exists(filepath):
            try:
                df = pd.read_csv(filepath)
                results[name] = {
                    'exists': True,
                    'readable': True,
                    'shape': df.shape,
                    'columns': list(df.columns)[:10],  # 只显示前10列
                    'sample': df.head(2).to_dict('records') if len(df) > 0 else []
                }
                print(f"✓ {name}: {df.shape}")
            except Exception as e:
                results[name] = {
                    'exists': True,
                    'readable': False,
                    'error': str(e)
                }
                print(f"✗ {name}: 读取失败 - {e}")
        else:
            results[name] = {'exists': False}
            print(f"✗ {name}: 文件不存在")
    
    return results

def test_shapefile_reading():
    """测试Shapefile读取"""
    print("\n测试Shapefile读取...")
    
    try:
        import geopandas as gpd
        
        data_dir = r"C:\Users\<USER>\Desktop\接驳"
        shp_files = ['in_500_with_coords.shp', 'out_500_with_coords.shp']
        
        for shp_file in shp_files:
            filepath = os.path.join(data_dir, shp_file)
            
            if os.path.exists(filepath):
                try:
                    gdf = gpd.read_file(filepath)
                    print(f"✓ {shp_file}: {gdf.shape}, CRS: {gdf.crs}")
                    print(f"  Columns: {list(gdf.columns)}")
                    
                    # 检查关键列
                    required_cols = ['station', 'hour', 'count']
                    missing_cols = [col for col in required_cols if col not in gdf.columns]
                    if missing_cols:
                        print(f"  ⚠ 缺少列: {missing_cols}")
                    
                    # 检查坐标列
                    coord_cols = ['longitude', 'latitude']
                    has_coords = all(col in gdf.columns for col in coord_cols)
                    print(f"  坐标信息: {'✓' if has_coords else '✗'}")
                    
                except Exception as e:
                    print(f"✗ {shp_file}: 读取失败 - {e}")
            else:
                print(f"✗ {shp_file}: 文件不存在")
    
    except ImportError:
        print("✗ geopandas未安装，无法测试Shapefile")

def test_data_consistency():
    """测试数据一致性"""
    print("\n测试数据一致性...")
    
    data_dir = r"C:\Users\<USER>\Desktop\接驳"
    
    try:
        # 读取站点连接数据
        connect_file = os.path.join(data_dir, 'station_connect_2023.csv')
        if os.path.exists(connect_file):
            connect_df = pd.read_csv(connect_file)
            
            # 清理站点名称
            connect_df['station_1_clean'] = connect_df['station_1'].apply(lambda x: str(x).split('_')[0])
            connect_df['station_2_clean'] = connect_df['station_2'].apply(lambda x: str(x).split('_')[0])
            
            unique_stations_connect = set(connect_df['station_1_clean'].unique()) | set(connect_df['station_2_clean'].unique())
            print(f"连接数据中的唯一站点数: {len(unique_stations_connect)}")
        
        # 读取流量数据
        flow_file = os.path.join(data_dir, 'subway_flow_24h_results.csv')
        if os.path.exists(flow_file):
            flow_df = pd.read_csv(flow_file)
            
            # 清理站点名称
            flow_df['o_station_clean'] = flow_df['o_rawname'].apply(lambda x: str(x).split('_')[0])
            flow_df['d_station_clean'] = flow_df['d_rawname'].apply(lambda x: str(x).split('_')[0])
            
            unique_stations_flow = set(flow_df['o_station_clean'].unique()) | set(flow_df['d_station_clean'].unique())
            print(f"流量数据中的唯一站点数: {len(unique_stations_flow)}")
            
            # 检查小时范围
            hours = sorted(flow_df['hour'].unique())
            print(f"流量数据小时范围: {min(hours)} - {max(hours)}")
        
        # 读取OD数据
        od_file = os.path.join(data_dir, 'updated北京市_subway_od_2024_modified3.csv')
        if os.path.exists(od_file):
            od_df = pd.read_csv(od_file)
            
            # 清理站点名称
            od_df['o_station_clean'] = od_df['o_rawname'].apply(lambda x: str(x).split('_')[0])
            od_df['d_station_clean'] = od_df['d_rawname'].apply(lambda x: str(x).split('_')[0])
            
            unique_stations_od = set(od_df['o_station_clean'].unique()) | set(od_df['d_station_clean'].unique())
            print(f"OD数据中的唯一站点数: {len(unique_stations_od)}")
            
            # 检查小时范围
            hours = sorted(od_df['hour'].unique())
            print(f"OD数据小时范围: {min(hours)} - {max(hours)}")
        
        # 读取栅格特征数据
        grid_file = os.path.join(data_dir, 'leti_data.csv')
        if os.path.exists(grid_file):
            grid_df = pd.read_csv(grid_file)
            
            # 检查特征列
            feature_cols = [col for col in grid_df.columns if col not in ['站名', 'id', 'geometry']]
            print(f"栅格特征数: {len(feature_cols)}")
            print(f"栅格数量: {len(grid_df)}")
            
            # 检查站点名称
            if '站名' in grid_df.columns:
                unique_stations_grid = set(grid_df['站名'].unique())
                print(f"栅格数据中的唯一站点数: {len(unique_stations_grid)}")
    
    except Exception as e:
        print(f"数据一致性检查失败: {e}")

def generate_sample_data():
    """生成示例数据用于测试"""
    print("\n生成示例数据...")
    
    # 生成示例站点列表
    stations = ['站点A', '站点B', '站点C', '站点D', '站点E']
    
    # 生成示例进出站数据
    in_flow_data = []
    out_flow_data = []
    
    for station in stations:
        for hour in range(24):
            # 模拟流量模式
            base_flow = np.random.poisson(100)
            if 7 <= hour <= 9:  # 早高峰
                flow = base_flow * 2
            elif 17 <= hour <= 19:  # 晚高峰
                flow = base_flow * 1.8
            else:
                flow = base_flow
            
            in_flow_data.append({
                'station': station,
                'hour': hour,
                'count': flow + np.random.normal(0, 10),
                'longitude': 116.4 + np.random.normal(0, 0.1),
                'latitude': 39.9 + np.random.normal(0, 0.1)
            })
            
            out_flow_data.append({
                'station': station,
                'hour': hour,
                'count': flow + np.random.normal(0, 10),
                'longitude': 116.4 + np.random.normal(0, 0.1),
                'latitude': 39.9 + np.random.normal(0, 0.1)
            })
    
    # 生成示例连接数据
    connections = [
        ('站点A', '站点B'),
        ('站点B', '站点C'),
        ('站点C', '站点D'),
        ('站点D', '站点E'),
        ('站点A', '站点E')  # 环形连接
    ]
    
    connect_data = []
    for station1, station2 in connections:
        connect_data.append({
            'station_1': station1,
            'station_2': station2
        })
    
    # 生成示例流量数据
    flow_data = []
    for station1, station2 in connections:
        for hour in range(24):
            flow = np.random.poisson(50)
            flow_data.append({
                'hour': hour,
                'o_rawname': station1,
                'd_rawname': station2,
                'trip': flow
            })
    
    # 生成示例栅格特征
    grid_data = []
    for station in stations:
        features = np.random.normal(0, 1, 51)  # 51维特征
        row = {'站名': station}
        for i, feat in enumerate(features):
            row[f'feature_{i}'] = feat
        grid_data.append(row)
    
    # 保存示例数据
    sample_dir = 'sample_data'
    os.makedirs(sample_dir, exist_ok=True)
    
    pd.DataFrame(in_flow_data).to_csv(os.path.join(sample_dir, 'sample_in_flow.csv'), index=False)
    pd.DataFrame(out_flow_data).to_csv(os.path.join(sample_dir, 'sample_out_flow.csv'), index=False)
    pd.DataFrame(connect_data).to_csv(os.path.join(sample_dir, 'sample_connections.csv'), index=False)
    pd.DataFrame(flow_data).to_csv(os.path.join(sample_dir, 'sample_subway_flow.csv'), index=False)
    pd.DataFrame(grid_data).to_csv(os.path.join(sample_dir, 'sample_grid_features.csv'), index=False)
    
    print(f"示例数据已保存到 {sample_dir} 目录")

def main():
    """主测试函数"""
    print("="*50)
    print("数据加载测试")
    print("="*50)
    
    # 测试数据文件
    results = test_data_files()
    
    # 测试Shapefile
    test_shapefile_reading()
    
    # 测试数据一致性
    test_data_consistency()
    
    # 生成示例数据
    generate_sample_data()
    
    print("\n测试完成！")
    return results

if __name__ == "__main__":
    main()
