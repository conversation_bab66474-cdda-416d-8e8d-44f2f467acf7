import numpy as np
import pandas as pd
import geopandas as gpd
from shapely.geometry import Point, Polygon
import torch
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import seaborn as sns


class GridAnalyzer:
    """栅格分析器 - 用于分析栅格与地铁站的关系"""
    
    def __init__(self, distance_threshold=1000):
        self.distance_threshold = distance_threshold
        self.grid_station_connections = {}
        self.connection_strengths = {}
        
    def analyze_grid_station_connections(self, grid_coords, station_coords, 
                                       grid_flows, station_flows):
        """分析栅格与站点的连接关系"""
        print("分析栅格-站点连接关系...")
        
        num_grids = len(grid_coords)
        num_stations = len(station_coords)
        
        # 计算距离矩阵
        distance_matrix = self._calculate_distance_matrix(grid_coords, station_coords)
        
        # 基于距离的连接
        distance_connections = distance_matrix < self.distance_threshold
        
        # 基于流量相关性的连接
        flow_connections = self._calculate_flow_correlations(grid_flows, station_flows)
        
        # 综合连接强度
        connection_strength = self._combine_connections(
            distance_connections, flow_connections, distance_matrix
        )
        
        # 应用阈值过滤
        strong_connections = connection_strength > 0.3  # 可调整阈值
        
        self.grid_station_connections = strong_connections
        self.connection_strengths = connection_strength
        
        print(f"发现 {np.sum(strong_connections)} 个强连接")
        
        return strong_connections, connection_strength
    
    def _calculate_distance_matrix(self, grid_coords, station_coords):
        """计算栅格与站点间的距离矩阵"""
        grid_coords = np.array(grid_coords)
        station_coords = np.array(station_coords)
        
        # 广播计算距离
        grid_expanded = grid_coords[:, np.newaxis, :]  # [num_grids, 1, 2]
        station_expanded = station_coords[np.newaxis, :, :]  # [1, num_stations, 2]
        
        # 欧几里得距离 (简化，实际应使用地理距离)
        distances = np.sqrt(np.sum((grid_expanded - station_expanded) ** 2, axis=2))
        
        # 转换为米 (粗略估算)
        distances_meters = distances * 111000  # 1度约111km
        
        return distances_meters
    
    def _calculate_flow_correlations(self, grid_flows, station_flows):
        """计算流量相关性"""
        if grid_flows is None or station_flows is None:
            return np.zeros((len(grid_flows) if grid_flows is not None else 0, 
                           len(station_flows) if station_flows is not None else 0))
        
        # 标准化流量数据
        scaler = StandardScaler()
        grid_flows_norm = scaler.fit_transform(grid_flows.reshape(-1, 1)).flatten()
        station_flows_norm = scaler.fit_transform(station_flows.reshape(-1, 1)).flatten()
        
        # 计算相关性矩阵
        correlations = np.corrcoef(grid_flows_norm, station_flows_norm)
        
        # 取绝对值作为连接强度
        return np.abs(correlations[:len(grid_flows_norm), len(grid_flows_norm):])
    
    def _combine_connections(self, distance_connections, flow_connections, distances):
        """综合距离和流量信息计算连接强度"""
        # 距离权重 (距离越近权重越大)
        max_distance = np.max(distances)
        distance_weights = 1 - (distances / max_distance)
        distance_weights = np.where(distance_connections, distance_weights, 0)
        
        # 流量权重
        flow_weights = flow_connections if flow_connections.size > 0 else np.zeros_like(distance_weights)
        
        # 综合权重 (可调整权重比例)
        combined_strength = 0.6 * distance_weights + 0.4 * flow_weights
        
        return combined_strength
    
    def predict_grid_to_station_flow(self, model, grid_features, station_features, 
                                   time_features, device='cpu'):
        """预测栅格到站点的流量"""
        print("预测栅格到站点流量...")
        
        model.eval()
        predictions = {}
        
        with torch.no_grad():
            # 合并特征
            all_features = torch.cat([
                torch.tensor(grid_features, dtype=torch.float32),
                torch.tensor(station_features, dtype=torch.float32)
            ], dim=0).to(device)
            
            # 构建边索引 (基于连接关系)
            edge_index = self._build_edge_index_from_connections()
            edge_index = torch.tensor(edge_index, dtype=torch.long).to(device)
            
            # 时间特征
            time_tensor = torch.tensor(time_features, dtype=torch.long).to(device)
            
            # 模型预测
            flow_predictions = model(all_features, edge_index, time_tensor)
            
            # 分离栅格和站点预测
            num_grids = len(grid_features)
            grid_predictions = flow_predictions[:num_grids].cpu().numpy()
            station_predictions = flow_predictions[num_grids:].cpu().numpy()
            
            predictions['grid_to_station'] = grid_predictions
            predictions['station_flows'] = station_predictions
        
        return predictions
    
    def _build_edge_index_from_connections(self):
        """基于连接关系构建边索引"""
        if not hasattr(self, 'grid_station_connections'):
            raise ValueError("请先运行 analyze_grid_station_connections")
        
        connections = self.grid_station_connections
        grid_indices, station_indices = np.where(connections)
        
        # 调整站点索引 (因为在合并特征矩阵中的位置)
        num_grids = connections.shape[0]
        station_indices_adjusted = station_indices + num_grids
        
        # 构建双向边
        edge_index = np.array([
            np.concatenate([grid_indices, station_indices_adjusted]),
            np.concatenate([station_indices_adjusted, grid_indices])
        ])
        
        return edge_index
    
    def analyze_new_line_impact_on_grids(self, new_stations_coords, existing_grid_coords):
        """分析新线路对栅格的影响"""
        print("分析新线路对栅格的影响...")
        
        # 计算新站点与栅格的距离
        new_distances = self._calculate_distance_matrix(
            existing_grid_coords, new_stations_coords
        )
        
        # 找到受影响的栅格 (距离新站点较近的栅格)
        affected_grids = np.any(new_distances < self.distance_threshold, axis=1)
        
        # 计算影响强度
        impact_strength = np.zeros(len(existing_grid_coords))
        for i, grid_coord in enumerate(existing_grid_coords):
            if affected_grids[i]:
                # 距离最近新站点的距离
                min_distance = np.min(new_distances[i])
                # 影响强度与距离成反比
                impact_strength[i] = max(0, 1 - min_distance / self.distance_threshold)
        
        impact_analysis = {
            'affected_grids': affected_grids,
            'impact_strength': impact_strength,
            'num_affected_grids': np.sum(affected_grids),
            'avg_impact_strength': np.mean(impact_strength[affected_grids]) if np.any(affected_grids) else 0
        }
        
        return impact_analysis
    
    def visualize_grid_station_connections(self, grid_coords, station_coords, 
                                         save_path=None):
        """可视化栅格-站点连接"""
        plt.figure(figsize=(12, 10))
        
        # 绘制栅格
        grid_coords = np.array(grid_coords)
        plt.scatter(grid_coords[:, 0], grid_coords[:, 1], 
                   c='lightblue', s=20, alpha=0.6, label='栅格')
        
        # 绘制站点
        station_coords = np.array(station_coords)
        plt.scatter(station_coords[:, 0], station_coords[:, 1], 
                   c='red', s=100, alpha=0.8, label='地铁站', marker='s')
        
        # 绘制连接线
        if hasattr(self, 'grid_station_connections'):
            connections = self.grid_station_connections
            strengths = self.connection_strengths
            
            grid_indices, station_indices = np.where(connections)
            
            for g_idx, s_idx in zip(grid_indices, station_indices):
                strength = strengths[g_idx, s_idx]
                alpha = min(1.0, strength * 2)  # 调整透明度
                
                plt.plot([grid_coords[g_idx, 0], station_coords[s_idx, 0]],
                        [grid_coords[g_idx, 1], station_coords[s_idx, 1]],
                        'gray', alpha=alpha, linewidth=0.5)
        
        plt.xlabel('经度')
        plt.ylabel('纬度')
        plt.title('栅格-地铁站连接关系')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"连接关系图已保存至: {save_path}")
        
        plt.show()
    
    def generate_connection_report(self, grid_coords, station_coords, station_names=None):
        """生成连接关系报告"""
        if not hasattr(self, 'grid_station_connections'):
            raise ValueError("请先运行 analyze_grid_station_connections")
        
        connections = self.grid_station_connections
        strengths = self.connection_strengths
        
        # 统计信息
        total_connections = np.sum(connections)
        avg_strength = np.mean(strengths[connections])
        
        # 每个站点的连接数
        station_connections = np.sum(connections, axis=0)
        
        # 每个栅格的连接数
        grid_connections = np.sum(connections, axis=1)
        
        report = {
            'summary': {
                'total_grids': len(grid_coords),
                'total_stations': len(station_coords),
                'total_connections': int(total_connections),
                'avg_connection_strength': float(avg_strength),
                'connection_density': float(total_connections / (len(grid_coords) * len(station_coords)))
            },
            'station_stats': {
                'connections_per_station': station_connections.tolist(),
                'avg_connections_per_station': float(np.mean(station_connections)),
                'max_connections_station': int(np.argmax(station_connections))
            },
            'grid_stats': {
                'connections_per_grid': grid_connections.tolist(),
                'avg_connections_per_grid': float(np.mean(grid_connections)),
                'isolated_grids': int(np.sum(grid_connections == 0))
            }
        }
        
        if station_names:
            report['station_names'] = station_names
        
        return report 