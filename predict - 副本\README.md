# 地铁站流量预测系统

## 项目概述
本项目旨在构建一个高精度的地铁站流量预测系统，预测内容包括：
1. 地铁站进站流量
2. 地铁站出站流量 
3. 站间OD流量（从起点站到终点站的乘客量）

## 数据来源
- 进站数据 (`in_500_with_coords.shp`): 包含地铁站点进站流量数据，按小时统计
- 出站数据 (`out_500_with_coords.shp`): 包含地铁站点出站流量数据，按小时统计
- OD数据 (`updated北京市_subway_od_2024_modified3.csv`): 包含站点间OD流量数据
- 站点特征数据 (`station_features_result.csv`): 包含地铁站点的地理和社会经济特征

## 模型架构

本项目采用基于图神经网络的复合机器学习模型架构，主要包括以下几个部分：

### 1. 数据预处理模块
- 地理空间数据处理：处理栅格和地铁站的地理信息
- 特征工程：构建时空特征、距离特征、社会经济特征等
- 数据划分：训练集、验证集和测试集的自动划分

### 2. 图神经网络模型
- **时空注意力机制**：
  - 时间注意力：捕捉不同时间段之间的关系
  - 空间注意力：学习节点间的空间依赖关系
- **时空图卷积层**：
  - 整合时间和空间维度上的特征
  - 使用跳跃连接提升模型性能
  - 批归一化层减少过拟合

### 3. 专门化预测模型
- **进站流量预测模型**：
  - 基于栅格-站点图结构
  - 多层图卷积网络
  - 注意力机制加权特征
- **出站流量预测模型**：
  - 与进站模型对称设计
  - 使用相同的基础架构
- **OD流量预测模型**：
  - 基于站点-站点图结构
  - 利用边特征（距离、换乘次数等）
  - 边预测器实现精确OD流量估计

### 4. 各模型详细架构

#### 4.1 进站流量预测模型 (InFlowPredictionModel)

该模型利用栅格-站点图结构预测进站流量，架构如下：

1. **输入层**：
   - 节点特征：包含栅格和站点的特征
   - 图结构：栅格与对应地铁站的连接关系

2. **图卷积层**：
   - 3-5层时空图卷积网络 (STGCNLayer)
   - 每层包含：
     - 空间注意力机制：学习栅格与站点间的重要连接
     - 时间注意力机制：捕捉不同时间段的流量模式
     - 图卷积操作：聚合邻居节点信息
     - 批归一化：稳定训练
     - 跳跃连接：缓解梯度消失
     - 非线性激活：ELU函数
     - Dropout层：防止过拟合

3. **全连接层**：
   - 第一层：hidden_dim → hidden_dim/2
   - 第二层：hidden_dim/2 → 1 (输出层)
   - 激活函数：ELU

4. **输出**：
   - 预测每个栅格在不同时间的进站流量

#### 4.2 出站流量预测模型 (OutFlowPredictionModel)

出站流量预测模型与进站模型架构基本相同，但方向相反，预测从地铁站到各栅格的人流：

1. **输入层**：
   - 节点特征：包含栅格和站点的特征
   - 图结构：地铁站与对应栅格的连接关系

2. **图卷积层**：
   - 与进站模型相同的时空图卷积网络结构
   - 通过注意力机制学习站点到栅格的流量模式

3. **全连接层**：
   - 结构与进站模型相同
   - 第一层：hidden_dim → hidden_dim/2
   - 第二层：hidden_dim/2 → 1 (输出层)

4. **输出**：
   - 预测每个地铁站在不同时间的出站流量

#### 4.3 OD流量预测模型 (ODFlowPredictionModel)

该模型预测站点间的OD流量，具有更复杂的结构：

1. **输入层**：
   - 节点特征：站点特征向量
   - 边特征：包含站点间距离、换乘次数、旅行时间等特征
   - 图结构：站点间的连接关系

2. **图卷积层**：
   - 3-5层带边特征的时空图卷积网络
   - 使用GATConv代替普通GCNConv，支持边特征
   - 通过注意力机制学习站点间的流量关系

3. **边特征处理**：
   - 边特征嵌入网络：
     - 第一层：edge_features → hidden_dim
     - ReLU激活
     - 第二层：hidden_dim → hidden_dim

4. **节点对预测**：
   - 组合源节点、目标节点和边特征
   - 三层全连接网络：
     - 第一层：hidden_dim*2 + hidden_dim → hidden_dim
     - ReLU激活
     - Dropout层
     - 第二层：hidden_dim → 1 (输出层)

5. **输出**：
   - 预测每对站点间在不同时间的OD流量

### 5. 模型优化技术

- **批处理训练**：提高训练效率
- **GPU加速**：使用CUDA加速矩阵运算
- **半精度浮点数（FP16）**：减少内存使用
- **早停机制**：避免过拟合
- **Adam优化器**：自适应学习率

## 技术特点

1. **多层次图结构**：
   - 栅格-站点图：捕捉地理位置关系
   - 站点-站点图：捕捉站点间的流量关系

2. **时空特征融合**：
   - 时间特征：小时、工作日/周末等
   - 空间特征：地理坐标、栅格距离等
   - 社会经济特征：站点周边POI和用地特征

3. **高效GPU加速**：
   - CUDA支持
   - 半精度（FP16）选项以降低内存占用

4. **综合评估系统**：
   - 自动生成评估报告
   - 可视化预测结果

## 代码结构

- `src/data_loader.py`: 数据加载和预处理模块
  - 加载地理空间数据
  - 提取栅格和站点坐标
  - 构建图结构

- `src/models.py`: 模型定义模块
  - 时间注意力机制
  - 空间注意力机制
  - 时空图卷积层
  - 进站/出站/OD预测模型

- `src/train.py`: 模型训练模块
  - 批处理训练
  - 早停机制
  - 模型保存

- `src/predict.py`: 预测结果生成模块
  - 预测进站/出站/OD流量
  - 评估模型性能
  - 生成结果文件

- `src/utils.py`: 工具函数模块
  - 评估指标计算
  - 可视化函数
  - 日志记录

- `run.py`: 主运行脚本
  - 提供命令行接口
  - 训练和预测流程

## 使用方法

### 环境配置
```bash
pip install -r requirements.txt
```

### 训练模型
```bash
python run.py train --data_dir="C:/Users/<USER>/Desktop/接驳" --output_dir="./output" --hidden_dim=128 --batch_size=64 --epochs=100 --use_fp16
```

### 预测结果
```bash
python run.py predict --data_dir="C:/Users/<USER>/Desktop/接驳" --output_dir="./output" --results_dir="./results" --model_prefix="best" --use_fp16
```

### 训练并预测
```bash
python run.py both --data_dir="C:/Users/<USER>/Desktop/接驳" --output_dir="./output" --results_dir="./results" --hidden_dim=128 --batch_size=64 --epochs=100 --use_fp16
```

## 参数说明

- `--data_dir`: 数据目录路径
- `--output_dir`: 模型输出目录
- `--results_dir`: 预测结果输出目录
- `--hidden_dim`: 隐藏层维度
- `--batch_size`: 批处理大小
- `--epochs`: 训练轮数
- `--dropout`: Dropout比例
- `--use_fp16`: 使用半精度浮点数
- `--model_prefix`: 模型文件前缀，可选值: best或final

## 输出文件

- 模型文件：`output/best_*.pth` 和 `output/final_*.pth`
- 训练曲线：`output/*_training_curves.png`
- 训练日志：`output/training.log`
- 预测结果：
  - 进站预测：`results/in_500_predictions_with_coords.shp`
  - 出站预测：`results/out_500_predictions_with_coords.shp`
  - OD预测：`results/od_predictions.csv`
  - 比较结果：`results/prediction_comparison.csv`
- 预测可视化：`results/*_prediction_scatter.png`
- 预测日志：`results/prediction.log` 