# Position Coding 真实数据测试方案

## 概述

本文档描述了Position Coding模块的真实数据测试方案，该方案使用80%的时段数据作为训练集，20%的站点作为"新增站点"测试集，以验证算法预测新增站点对人流影响的能力。

## 测试策略

### 数据分割方案

#### 1. 时段分割（80% vs 20%）
- **训练时段**: 随机选择80%的时段用于训练基础流量预测模型
- **测试时段**: 剩余20%的时段用于验证和对比
- **目的**: 确保模型在不同时段都有良好的泛化能力

#### 2. 站点分割（80% vs 20%）
- **已有站点**: 80%的站点作为"已有网络"
- **新增站点**: 20%的站点作为"新增站点"进行测试
- **目的**: 模拟真实的新站点建设场景

### 训练流程

```
1. 数据加载和预处理
   ├── 加载进站、出站、OD数据
   ├── 提取站点坐标
   └── 执行数据分割

2. 基础模型训练
   ├── 使用80%时段的已有站点数据
   ├── 训练进站/出站预测模型
   └── 训练OD流量预测模型

3. Position Coding模块训练
   ├── 基于真实流量变化计算分流影响
   ├── 使用测试站点作为"新增站点"样本
   └── 训练分流预测神经网络

4. 真实数据测试
   ├── 对每个测试站点预测其网络影响
   ├── 计算实际的流量变化
   └── 对比预测与实际结果
```

## 核心算法

### 分流影响计算

```python
def calculate_diversion_impact(new_station, existing_station):
    # 1. 计算新站点的流量增长
    new_growth = test_period_flows - train_period_flows
    
    # 2. 计算现有站点的流量变化
    existing_change = existing_test_flows - existing_train_flows
    
    # 3. 计算分流比例
    if new_growth > 0 and existing_change < 0:
        diversion_ratio = abs(existing_change) / new_growth
    else:
        diversion_ratio = 0.0
    
    # 4. 计算影响强度
    impact_intensity = abs(existing_change) / existing_baseline_flows
    
    return diversion_ratio, impact_intensity
```

### 预测准确度评估

```python
def evaluate_prediction_accuracy(predicted, actual):
    # 1. 总分流准确度
    diversion_accuracy = 1 - abs(predicted_diversion - actual_diversion) / actual_diversion
    
    # 2. 受影响站点数准确度
    affected_accuracy = 1 - abs(predicted_count - actual_count) / actual_count
    
    # 3. 高影响站点识别F1分数
    precision = len(predicted_high ∩ actual_high) / len(predicted_high)
    recall = len(predicted_high ∩ actual_high) / len(actual_high)
    f1_score = 2 * precision * recall / (precision + recall)
    
    # 4. 综合准确度
    overall_accuracy = (diversion_accuracy + affected_accuracy + f1_score) / 3
    
    return overall_accuracy
```

## 使用方法

### 命令行运行

```bash
# 运行完整的真实数据测试
python main.py

# 专门测试Position Coding功能
python main.py test_position
```

### 程序化调用

```python
# 初始化系统
system = V4SpatialPredictionSystem()

# 加载数据（自动执行数据分割）
system.load_and_process_data()

# 训练模型（包括Position Coding）
system.train_v4_models()

# 运行真实数据测试
test_results = system.test_position_coding_with_real_data()

# 预测特定新站点的影响
impact = system.predict_new_station_impact([116.4, 39.9], "新站点", hour=8)
```

## 输出结果

### 1. 数据分割信息
- `position_coding_data_split.json`: 记录训练/测试数据的分割详情

### 2. 测试报告
- `position_coding_test_report.csv`: 测试结果汇总表格
- `position_coding_test_results.json`: 详细的测试结果

### 3. 预测结果
- `v4_diversion_predictions.json`: 分流预测详细结果
- `v4_diversion_impact_report.csv`: 分流影响报告

## 评估指标

### 1. 预测准确度指标
- **总体准确度**: 综合评估预测质量
- **分流准确度**: 总分流量预测的准确性
- **受影响站点准确度**: 受影响站点数量预测的准确性
- **高影响站点F1**: 高影响站点识别的精确度和召回率

### 2. 网络影响指标
- **总网络分流**: 新站点对整个网络的分流影响
- **影响半径**: 新站点的影响范围
- **高/中/低影响站点数**: 不同影响等级的站点统计

## 技术优势

### 1. 真实性
- 基于真实的历史流量数据
- 模拟真实的新站点建设场景
- 考虑时间变化和空间竞争

### 2. 可验证性
- 可以与实际流量变化对比
- 提供量化的准确度评估
- 支持多维度的性能分析

### 3. 实用性
- 直接适用于城市规划决策
- 支持多种场景的影响预测
- 提供详细的分析报告

## 应用场景

### 1. 新地铁站选址
- 评估候选位置的网络影响
- 优化站点布局
- 预测客流重新分布

### 2. 交通规划
- 制定新站点开通策略
- 调整运营计划
- 优化资源配置

### 3. 政策制定
- 评估交通政策影响
- 支持投资决策
- 风险评估和管理

## 注意事项

1. **数据质量**: 确保输入数据的完整性和准确性
2. **计算资源**: 大规模测试需要足够的计算资源
3. **结果解释**: 预测结果需要结合实际情况分析
4. **模型更新**: 定期使用新数据更新模型

## 未来改进

1. **多模态交通**: 考虑其他交通方式的竞争
2. **动态更新**: 支持实时数据的模型更新
3. **不确定性量化**: 提供预测结果的置信区间
4. **可视化**: 增强结果的可视化展示
