import torch
import numpy as np
import pandas as pd
import argparse
import os
import gc
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

from data_loader import MetroDataLoader
from models import MetroFlowPredictor
from train import MetroFlowTrainer
from utils import (save_predictions, save_shapefile_predictions, 
                   create_comparison_dataframe, print_data_summary,
                   plot_predictions_vs_actual, memory_usage)

def setup_directories():
    """设置输出目录"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"results_{timestamp}"
    os.makedirs(output_dir, exist_ok=True)
    
    return output_dir

def run_predictions(model, data_dict, data_loader, device, output_dir):
    """运行预测并保存结果"""
    model.eval()
    
    all_in_preds = []
    all_in_true = []
    all_out_preds = []
    all_out_true = []
    all_od_preds = []
    all_od_true = []
    
    test_stations = data_dict['data_split']['test_stations']
    
    print("Running predictions for all hours...")
    
    with torch.no_grad():
        for hour in range(24):
            try:
                # 创建图数据
                graph_data = data_loader.create_graph_data(
                    data_dict['connections'], 
                    data_dict['flow_data'], 
                    data_dict['station_features'], 
                    hour
                )
                
                if graph_data is None:
                    continue
                
                graph_data = graph_data.to(device)
                
                # 预测进出站流量
                pred_in, pred_out = model.predict_station_flows(graph_data)
                
                # 获取测试集的真实值
                test_in = data_dict['data_split'].get('test_in', pd.DataFrame())
                test_out = data_dict['data_split'].get('test_out', pd.DataFrame())
                
                if not test_in.empty and not test_out.empty:
                    hour_in = test_in[test_in['hour'] == hour]
                    hour_out = test_out[test_out['hour'] == hour]
                    
                    if not hour_in.empty and not hour_out.empty:
                        # 获取对应站点的预测值
                        for _, row in hour_in.iterrows():
                            station = row['station']
                            if station in data_loader.station_to_id:
                                station_id = data_loader.station_to_id[station]
                                if station_id < len(pred_in):
                                    all_in_preds.append(pred_in[station_id].item())
                                    all_in_true.append(row['count'])
                        
                        for _, row in hour_out.iterrows():
                            station = row['station']
                            if station in data_loader.station_to_id:
                                station_id = data_loader.station_to_id[station]
                                if station_id < len(pred_out):
                                    all_out_preds.append(pred_out[station_id].item())
                                    all_out_true.append(row['count'])
                
                # 清理GPU内存
                del graph_data, pred_in, pred_out
                torch.cuda.empty_cache()
                
                if hour % 6 == 0:
                    print(f"Completed hour {hour}/24")
                    memory_usage()
                
            except Exception as e:
                print(f"Error in prediction for hour {hour}: {e}")
                continue
    
    # 保存预测结果
    if all_in_preds:
        in_metrics = save_predictions(all_in_preds, all_in_true, 'In', output_dir)
        plot_predictions_vs_actual(all_in_true, all_in_preds, 'In', output_dir)
    
    if all_out_preds:
        out_metrics = save_predictions(all_out_preds, all_out_true, 'Out', output_dir)
        plot_predictions_vs_actual(all_out_true, all_out_preds, 'Out', output_dir)
    
    # 创建比较数据框
    if all_in_preds and all_out_preds:
        comparison_df = create_comparison_dataframe(
            all_in_preds, all_in_true,
            all_out_preds, all_out_true
        )
        
        comparison_path = os.path.join(output_dir, 'predictions_comparison.csv')
        comparison_df.to_csv(comparison_path, index=False, encoding='utf-8-sig')
        print(f"Comparison data saved to {comparison_path}")
    
    # 保存到shapefile
    base_path = "C:\\Users\\<USER>\\Desktop\\接驳\\"
    
    if all_in_preds:
        try:
            # 获取测试站点名称
            test_in_stations = []
            for _, row in data_dict['data_split']['test_in'].iterrows():
                test_in_stations.append(row['station'])
            
            # 去重并匹配预测值
            unique_stations = list(set(test_in_stations))
            station_predictions = {}
            
            # 计算每个站点的平均预测值
            for i, station in enumerate(test_in_stations):
                if station not in station_predictions:
                    station_predictions[station] = []
                if i < len(all_in_preds):
                    station_predictions[station].append(all_in_preds[i])
            
            # 计算平均值
            avg_predictions = []
            station_names = []
            for station, preds in station_predictions.items():
                if preds:
                    avg_predictions.append(np.mean(preds))
                    station_names.append(station)
            
            # 保存到shapefile
            in_shp_output = os.path.join(base_path, "in_500_predictions_with_coords.shp")
            save_shapefile_predictions(
                f"{base_path}in_500_with_coords.shp",
                avg_predictions,
                station_names,
                in_shp_output
            )
        except Exception as e:
            print(f"Error saving in station shapefile: {e}")
    
    if all_out_preds:
        try:
            # 类似的处理出站数据
            test_out_stations = []
            for _, row in data_dict['data_split']['test_out'].iterrows():
                test_out_stations.append(row['station'])
            
            unique_stations = list(set(test_out_stations))
            station_predictions = {}
            
            for i, station in enumerate(test_out_stations):
                if station not in station_predictions:
                    station_predictions[station] = []
                if i < len(all_out_preds):
                    station_predictions[station].append(all_out_preds[i])
            
            avg_predictions = []
            station_names = []
            for station, preds in station_predictions.items():
                if preds:
                    avg_predictions.append(np.mean(preds))
                    station_names.append(station)
            
            out_shp_output = os.path.join(base_path, "out_500_predictions_with_coords.shp")
            save_shapefile_predictions(
                f"{base_path}out_500_with_coords.shp",
                avg_predictions,
                station_names,
                out_shp_output
            )
        except Exception as e:
            print(f"Error saving out station shapefile: {e}")
    
    return {
        'in_metrics': in_metrics if all_in_preds else {},
        'out_metrics': out_metrics if all_out_preds else {},
        'in_predictions': all_in_preds,
        'out_predictions': all_out_preds
    }

def main():
    parser = argparse.ArgumentParser(description='Metro Flow Prediction with GCN + Time Features')
    parser.add_argument('--epochs', type=int, default=100, help='Number of training epochs')
    parser.add_argument('--batch_size', type=int, default=32, help='Batch size')
    parser.add_argument('--lr', type=float, default=0.001, help='Learning rate')
    parser.add_argument('--hidden_dim', type=int, default=128, help='Hidden dimension')
    parser.add_argument('--patience', type=int, default=20, help='Early stopping patience')
    parser.add_argument('--test_ratio', type=float, default=0.2, help='Test set ratio')
    parser.add_argument('--data_path', type=str, 
                       default="C:\\Users\\<USER>\\Desktop\\接驳\\",
                       help='Path to data directory')
    
    args = parser.parse_args()
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    if device.type == 'cuda':
        print(f"GPU: {torch.cuda.get_device_name()}")
        print(f"Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
        # 设置内存增长策略
        torch.cuda.empty_cache()
    
    # 设置输出目录
    output_dir = setup_directories()
    print(f"Output directory: {output_dir}")
    
    try:
        # 加载数据
        print("="*60)
        print("LOADING DATA")
        print("="*60)
        
        data_loader = MetroDataLoader(base_path=args.data_path)
        data_dict = data_loader.load_all_data()
        
        # 将data_loader添加到data_dict中
        data_dict['data_loader'] = data_loader
        
        # 打印数据摘要
        print_data_summary(data_dict)
        
        # 检查是否有足够的数据
        if (data_dict['station_features'].empty or 
            len(data_dict['data_split'].get('train_stations', [])) == 0):
            print("Insufficient data for training. Please check data files.")
            return
        
        # 获取特征维度
        station_feature_dim = data_dict['station_features'].shape[1]
        grid_feature_dim = data_dict['grid_features'].shape[1] - 1 if not data_dict['grid_features'].empty else 50
        
        print(f"Station feature dimension: {station_feature_dim}")
        print(f"Grid feature dimension: {grid_feature_dim}")
        
        # 创建模型
        print("\n" + "="*60)
        print("CREATING MODEL")
        print("="*60)
        
        model = MetroFlowPredictor(
            station_feature_dim=station_feature_dim + 3,  # +3 for time features
            grid_feature_dim=grid_feature_dim,
            hidden_dim=args.hidden_dim
        )
        
        print(f"Model created with {sum(p.numel() for p in model.parameters()):,} parameters")
        
        # 创建训练器
        trainer = MetroFlowTrainer(
            model, 
            device=device, 
            learning_rate=args.lr
        )
        
        # 训练模型
        print("\n" + "="*60)
        print("STARTING TRAINING")
        print("="*60)
        
        history = trainer.train(
            data_dict, 
            num_epochs=args.epochs,
            patience=args.patience
        )
        
        # 绘制训练历史
        trainer.plot_training_history()
        
        # 运行预测
        print("\n" + "="*60)
        print("RUNNING PREDICTIONS")
        print("="*60)
        
        results = run_predictions(model, data_dict, data_loader, device, output_dir)
        
        # 保存训练历史
        history_df = pd.DataFrame(history)
        history_path = os.path.join(output_dir, 'training_history.csv')
        history_df.to_csv(history_path, index=False)
        print(f"Training history saved to {history_path}")
        
        # 保存模型
        model_path = os.path.join(output_dir, 'final_model.pth')
        torch.save(model.state_dict(), model_path)
        print(f"Model saved to {model_path}")
        
        print("\n" + "="*60)
        print("TRAINING AND PREDICTION COMPLETED")
        print("="*60)
        print(f"Results saved to: {output_dir}")
        
        # 打印最终指标
        if results['in_metrics']:
            print(f"\nIn Station Flow Metrics:")
            for metric, value in results['in_metrics'].items():
                print(f"  {metric}: {value:.4f}")
        
        if results['out_metrics']:
            print(f"\nOut Station Flow Metrics:")
            for metric, value in results['out_metrics'].items():
                print(f"  {metric}: {value:.4f}")
        
    except Exception as e:
        print(f"Error in main execution: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理GPU内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            print(f"\nGPU memory cleared")

if __name__ == '__main__':
    main()
