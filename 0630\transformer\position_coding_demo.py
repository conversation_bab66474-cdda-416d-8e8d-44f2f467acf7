#!/usr/bin/env python3
"""
Position Coding 模块演示脚本

这个脚本演示如何使用Position Coding模块来预测新增站点对原站点的分流作用。
"""

import sys
import os
import numpy as np
import pandas as pd

# 添加主模块路径
sys.path.append('.')

try:
    from main import V4SpatialPredictionSystem
except ImportError:
    print("错误: 无法导入主模块，请确保main.py在当前目录")
    sys.exit(1)


def demo_position_coding():
    """演示Position Coding功能"""
    print("=" * 80)
    print("Position Coding 模块演示")
    print("=" * 80)
    
    # 初始化系统
    print("1. 初始化预测系统...")
    system = V4SpatialPredictionSystem()
    
    # 加载数据
    print("2. 加载数据...")
    if not system.load_and_process_data():
        print("❌ 数据加载失败")
        return False
    
    print(f"   成功加载 {len(system.stations)} 个站点的数据")
    
    # 训练模型
    print("3. 训练模型（包括Position Coding模块）...")
    system.train_v4_models()
    
    # 检查Position Coding模块是否训练成功
    if system.position_coding_module is None:
        print("❌ Position Coding模块训练失败")
        return False
    
    print("✅ Position Coding模块训练完成")
    
    # 演示案例
    print("\n4. 演示新站点分流预测...")
    
    demo_cases = [
        {
            "name": "CBD核心区新站",
            "coord": [116.45, 39.92],
            "description": "位于CBD核心区域，预期高分流影响"
        },
        {
            "name": "郊区新站",
            "coord": [116.2, 39.8],
            "description": "位于郊区，预期中等分流影响"
        },
        {
            "name": "远郊新站",
            "coord": [116.7, 40.2],
            "description": "位于远郊，预期低分流影响"
        }
    ]
    
    results = {}
    
    for i, case in enumerate(demo_cases, 1):
        print(f"\n案例 {i}: {case['name']}")
        print(f"位置: {case['coord']}")
        print(f"描述: {case['description']}")
        print("-" * 50)
        
        # 预测早高峰影响
        morning_impact = system.predict_new_station_impact(
            case['coord'], 
            case['name'], 
            hour=8
        )
        
        # 预测晚高峰影响
        evening_impact = system.predict_new_station_impact(
            case['coord'], 
            case['name'], 
            hour=18
        )
        
        results[case['name']] = {
            'morning': morning_impact,
            'evening': evening_impact,
            'coord': case['coord']
        }
        
        # 显示对比结果
        print(f"\n早高峰 vs 晚高峰对比:")
        print(f"  总体分流: {morning_impact['total_network_diversion']:.4f} vs {evening_impact['total_network_diversion']:.4f}")
        print(f"  受影响站点: {morning_impact['num_affected_stations']} vs {evening_impact['num_affected_stations']}")
        print(f"  高影响站点: {len(morning_impact['high_impact_stations'])} vs {len(evening_impact['high_impact_stations'])}")
    
    # 生成对比分析
    print("\n5. 生成对比分析...")
    generate_comparison_analysis(results)
    
    print("\n✅ Position Coding演示完成")
    print("📁 结果文件已保存到当前目录")
    
    return True


def generate_comparison_analysis(results):
    """生成对比分析报告"""
    
    # 创建对比数据
    comparison_data = []
    
    for station_name, data in results.items():
        morning = data['morning']
        evening = data['evening']
        coord = data['coord']
        
        comparison_data.append({
            'Station_Name': station_name,
            'Longitude': coord[0],
            'Latitude': coord[1],
            'Morning_Total_Diversion': morning['total_network_diversion'],
            'Evening_Total_Diversion': evening['total_network_diversion'],
            'Morning_Affected_Stations': morning['num_affected_stations'],
            'Evening_Affected_Stations': evening['num_affected_stations'],
            'Morning_High_Impact': len(morning['high_impact_stations']),
            'Evening_High_Impact': len(evening['high_impact_stations']),
            'Morning_Avg_Radius': morning['average_impact_radius'],
            'Evening_Avg_Radius': evening['average_impact_radius'],
            'Peak_Difference': abs(morning['total_network_diversion'] - evening['total_network_diversion'])
        })
    
    # 保存对比报告
    comparison_df = pd.DataFrame(comparison_data)
    comparison_df.to_csv('position_coding_demo_comparison.csv', index=False, encoding='utf-8')
    
    # 打印总结
    print("\n📊 对比分析总结:")
    print(f"   平均早高峰分流: {comparison_df['Morning_Total_Diversion'].mean():.4f}")
    print(f"   平均晚高峰分流: {comparison_df['Evening_Total_Diversion'].mean():.4f}")
    print(f"   最大影响站点: {comparison_df['Station_Name'].iloc[comparison_df['Morning_Total_Diversion'].idxmax()]}")
    print(f"   最小影响站点: {comparison_df['Station_Name'].iloc[comparison_df['Morning_Total_Diversion'].idxmin()]}")


def interactive_prediction():
    """交互式预测功能"""
    print("\n" + "=" * 80)
    print("交互式新站点分流预测")
    print("=" * 80)
    
    # 初始化系统（如果还没有）
    if 'system' not in globals():
        print("初始化系统...")
        global system
        system = V4SpatialPredictionSystem()
        if not system.load_and_process_data():
            print("❌ 数据加载失败")
            return
        system.train_v4_models()
    
    while True:
        print("\n请输入新站点信息（输入 'quit' 退出）:")
        
        # 获取用户输入
        name = input("站点名称: ").strip()
        if name.lower() == 'quit':
            break
        
        try:
            lon = float(input("经度: ").strip())
            lat = float(input("纬度: ").strip())
            hour = int(input("预测时间（0-23小时）: ").strip())
            
            if not (0 <= hour <= 23):
                print("❌ 时间必须在0-23之间")
                continue
            
            # 进行预测
            print(f"\n正在预测 '{name}' 在 {hour}:00 的分流影响...")
            result = system.predict_new_station_impact([lon, lat], name, hour)
            
            if result:
                print("✅ 预测完成")
                
                # 询问是否保存结果
                save = input("是否保存结果到文件？(y/n): ").strip().lower()
                if save == 'y':
                    filename = f"prediction_{name.replace(' ', '_')}_{hour}h.json"
                    import json
                    with open(filename, 'w', encoding='utf-8') as f:
                        # 转换为可序列化格式
                        serializable_result = {
                            k: (float(v) if isinstance(v, (int, float, np.number)) else 
                                [str(item) for item in v] if isinstance(v, list) else 
                                {sk: float(sv) if isinstance(sv, (int, float, np.number)) else sv 
                                 for sk, sv in v.items()} if isinstance(v, dict) else str(v))
                            for k, v in result.items()
                        }
                        json.dump(serializable_result, f, indent=2, ensure_ascii=False)
                    print(f"📁 结果已保存到 {filename}")
            
        except ValueError:
            print("❌ 输入格式错误，请输入有效的数字")
        except Exception as e:
            print(f"❌ 预测过程中出错: {e}")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == 'interactive':
        interactive_prediction()
    else:
        success = demo_position_coding()
        
        if success:
            # 询问是否进入交互模式
            interactive = input("\n是否进入交互式预测模式？(y/n): ").strip().lower()
            if interactive == 'y':
                interactive_prediction()
        
        print("\n演示结束")
