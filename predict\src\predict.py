import os
import argparse
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import pandas as pd
import geopandas as gpd
import matplotlib.pyplot as plt
from tqdm import tqdm
import matplotlib
from matplotlib.font_manager import FontProperties
from datetime import datetime
import gc
import json
import traceback

# 设置中文字体
# 添加中文字体支持
try:
    # 尝试设置微软雅黑
    font_path = 'C:/Windows/Fonts/msyh.ttc'  # 微软雅黑字体路径
    if os.path.exists(font_path):
        font = FontProperties(fname=font_path)
        plt.rcParams['font.family'] = ['Microsoft YaHei']
        print("已设置中文字体: 微软雅黑")
    else:
        # 尝试其他中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'SimSun', 'Microsoft YaHei', 'KaiTi', 'FangSong']
        plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
        print("已设置中文字体: SimHei/SimSun")
except Exception as e:
    print(f"设置中文字体时出错: {str(e)}")

from data_loader import MetroDataLoader
from models import MetroFlowModel
from utils import (
    create_directory, timer, evaluate_predictions, plot_prediction_comparison,
    save_results_to_csv, save_results_to_shapefile, prepare_prediction_comparison,
    visualize_flow_on_map, log_message, normalize_predictions, plot_training_curves
)

# 配置GPU设备
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

# 默认参数设置 - 可直接在这里修改而不需要命令行参数
DEFAULT_DATA_DIR = 'C:/Users/<USER>/Desktop/接驳'
DEFAULT_MODEL_DIR = './output'
DEFAULT_OUTPUT_DIR = './results'
DEFAULT_HIDDEN_DIM = 256  # 增加隐藏层维度
DEFAULT_USE_FP16 = False
DEFAULT_MODEL_PREFIX = 'best'
DEFAULT_EPOCHS = 10     # 定义训练轮数
DEFAULT_BATCH_SIZE = 64
DEFAULT_LR = 0.0005    # 降低学习率以提高稳定性
DEFAULT_EARLY_STOPPING = 15
DEFAULT_TRAIN_MODEL = True  # 是否训练模型
DEFAULT_NUM_LAYERS = 4      # 增加模型层数
DEFAULT_DROPOUT = 0.3       # 增加dropout以防止过拟合

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='地铁流量预测训练与预测')
    parser.add_argument('--data_dir', type=str, default=DEFAULT_DATA_DIR,
                        help='数据目录路径')
    parser.add_argument('--model_dir', type=str, default=DEFAULT_MODEL_DIR,
                        help='模型目录路径')
    parser.add_argument('--output_dir', type=str, default=DEFAULT_OUTPUT_DIR,
                        help='输出目录路径')
    parser.add_argument('--hidden_dim', type=int, default=DEFAULT_HIDDEN_DIM,
                        help='隐藏层维度')
    parser.add_argument('--use_fp16', action='store_true', default=DEFAULT_USE_FP16,
                        help='使用半精度浮点数')
    parser.add_argument('--model_prefix', type=str, default=DEFAULT_MODEL_PREFIX,
                        help='模型文件前缀，可选值: best或final')
    parser.add_argument('--epochs', type=int, default=DEFAULT_EPOCHS,
                        help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=DEFAULT_BATCH_SIZE,
                        help='批量大小')
    parser.add_argument('--lr', type=float, default=DEFAULT_LR,
                        help='学习率')
    parser.add_argument('--early_stopping', type=int, default=DEFAULT_EARLY_STOPPING,
                        help='早停轮数')
    parser.add_argument('--train_model', action='store_true', default=DEFAULT_TRAIN_MODEL,
                        help='是否训练模型')
    parser.add_argument('--num_layers', type=int, default=DEFAULT_NUM_LAYERS,
                        help='模型层数')
    parser.add_argument('--dropout', type=float, default=DEFAULT_DROPOUT,
                        help='Dropout比例')
    return parser.parse_args()

@timer
def train_in_flow_model(model, data_loader, args, log_file=None):
    """
    训练进站流量预测模型
    
    参数:
    model: 模型
    data_loader: 数据加载器
    args: 命令行参数
    log_file: 日志文件路径
    
    返回:
    训练历史记录
    """
    log_message("开始训练进站流量预测模型...", log_file)
    
    # 获取训练和验证数据，确保在正确的设备上
    train_data = data_loader.get_grid_station_graph().to(device)
    
    # 获取训练和验证标签
    train_indices = data_loader.in_data[data_loader.in_data['dataset'] == 'train'].index
    val_indices = data_loader.in_data[data_loader.in_data['dataset'] == 'val'].index
    
    # 创建栅格ID到索引的映射
    grid_ids = [f"{row['station']}_{row['hour']}" for _, row in data_loader.in_data.iterrows()]
    unique_grid_ids = list(set(grid_ids))
    grid_id_to_idx = {grid_id: i for i, grid_id in enumerate(unique_grid_ids)}
    
    # 获取训练和验证样本在图中的索引
    train_node_indices = []
    for idx in train_indices:
        row = data_loader.in_data.loc[idx]
        grid_id = f"{row['station']}_{row['hour']}"
        if grid_id in grid_id_to_idx:
            train_node_indices.append(grid_id_to_idx[grid_id])
    
    val_node_indices = []
    for idx in val_indices:
        row = data_loader.in_data.loc[idx]
        grid_id = f"{row['station']}_{row['hour']}"
        if grid_id in grid_id_to_idx:
            val_node_indices.append(grid_id_to_idx[grid_id])
    
    # 使用PyTorch张量，以便我们可以用它来索引
    train_node_indices = torch.tensor(train_node_indices, dtype=torch.long).to(device)
    val_node_indices = torch.tensor(val_node_indices, dtype=torch.long).to(device)
    
    # 获取标签的均值和标准差，用于标准化
    train_mean = data_loader.in_data.loc[train_indices, 'count'].mean()
    train_std = data_loader.in_data.loc[train_indices, 'count'].std()
    
    # 标准化目标值以提高模型性能
    train_targets = torch.tensor(
        (data_loader.in_data.loc[train_indices, 'count'].values - train_mean) / train_std, 
        dtype=torch.float32
    ).to(device).unsqueeze(1)
    
    val_targets = torch.tensor(
        (data_loader.in_data.loc[val_indices, 'count'].values - train_mean) / train_std, 
        dtype=torch.float32
    ).to(device).unsqueeze(1)
    
    # 打印信息以帮助调试
    log_message(f"训练节点索引数量: {len(train_node_indices)}", log_file)
    log_message(f"验证节点索引数量: {len(val_node_indices)}", log_file)
    log_message(f"训练目标数量: {len(train_targets)}", log_file)
    log_message(f"验证目标数量: {len(val_targets)}", log_file)
    
    # 训练历史记录
    history = {
        'train_loss': [],
        'train_mae': [],
        'val_loss': [],
        'val_mae': []
    }
    
    # 早停设置
    best_val_loss = float('inf')
    best_epoch = 0
    patience = args.early_stopping
    
    # 使用cosine学习率调度器以提高收敛性能
    optimizer = torch.optim.Adam(model.in_model.parameters(), lr=args.lr, weight_decay=1e-5)
    scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=args.epochs)
    
    # 使用Huber Loss以减少对异常值的敏感性
    criterion = nn.SmoothL1Loss()
    
    # 训练循环
    for epoch in range(args.epochs):
        # 训练
        model.in_model.train()
        
        # 前向传播
        predictions = model.in_model(train_data)
        
        # 安全地获取对应训练样本的预测结果
        train_predictions = predictions[train_node_indices]
        
        # 确认维度匹配
        if train_predictions.shape[0] != train_targets.shape[0]:
            log_message(f"警告: 预测结果维度 ({train_predictions.shape[0]}) 与目标维度 ({train_targets.shape[0]}) 不匹配", log_file)
            # 调整维度以匹配较小的一个
            min_size = min(train_predictions.shape[0], train_targets.shape[0])
            train_predictions = train_predictions[:min_size]
            train_targets_subset = train_targets[:min_size]
            loss = criterion(train_predictions, train_targets_subset)
        else:
            loss = criterion(train_predictions, train_targets)
        
        # 反向传播
        optimizer.zero_grad()
        loss.backward()
        
        # 梯度裁剪以防止梯度爆炸
        nn.utils.clip_grad_norm_(model.in_model.parameters(), max_norm=1.0)
        
        optimizer.step()
        
        # 计算MAE
        mae = torch.mean(torch.abs(train_predictions - train_targets[:len(train_predictions)])).item()
        
        # 验证
        model.in_model.eval()
        with torch.no_grad():
            val_preds = model.in_model(train_data)
            # 安全地获取对应验证样本的预测结果
            val_predictions = val_preds[val_node_indices]
            
            # 确认维度匹配
            if val_predictions.shape[0] != val_targets.shape[0]:
                log_message(f"警告: 验证预测结果维度 ({val_predictions.shape[0]}) 与验证目标维度 ({val_targets.shape[0]}) 不匹配", log_file)
                # 调整维度以匹配较小的一个
                min_size = min(val_predictions.shape[0], val_targets.shape[0])
                val_predictions = val_predictions[:min_size]
                val_targets_subset = val_targets[:min_size]
                val_loss = criterion(val_predictions, val_targets_subset).item()
                val_mae = torch.mean(torch.abs(val_predictions - val_targets_subset)).item()
            else:
                val_loss = criterion(val_predictions, val_targets).item()
                val_mae = torch.mean(torch.abs(val_predictions - val_targets)).item()
        
        # 更新学习率
        scheduler.step()
        
        # 记录历史
        history['train_loss'].append(loss.item())
        history['train_mae'].append(mae)
        history['val_loss'].append(val_loss)
        history['val_mae'].append(val_mae)
        
        # 打印进度
        log_message(
            f"Epoch {epoch+1}/{args.epochs} - "
            f"Train Loss: {loss.item():.4f}, Train MAE: {mae:.4f}, "
            f"Val Loss: {val_loss:.4f}, Val MAE: {val_mae:.4f}, "
            f"LR: {scheduler.get_last_lr()[0]:.6f}",
            log_file
        )
        
        # 早停检查
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            best_epoch = epoch
            # 保存最佳模型
            model.save_models(os.path.join(args.model_dir, args.model_prefix))
        elif epoch - best_epoch > patience:
            log_message(f"早停！在 {patience} 轮内验证损失没有改善。", log_file)
            break
    
    # 加载最佳模型
    model_path_prefix = os.path.join(args.model_dir, args.model_prefix)
    model.load_models(model_path_prefix)
    
    # 保存训练曲线
    plot_training_curves(
        history['train_loss'], history['val_loss'],
        history['train_mae'], history['val_mae'],
        save_path=os.path.join(args.output_dir, 'in_flow_training_curves.png')
    )
    
    # 保存均值和标准差供预测时使用
    model.in_model.register_buffer('mean', torch.tensor(train_mean, dtype=torch.float32))
    model.in_model.register_buffer('std', torch.tensor(train_std, dtype=torch.float32))
    
    return history, train_mean, train_std

@timer
def train_out_flow_model(model, data_loader, args, log_file=None):
    """
    训练出站流量预测模型
    
    参数:
    model: 模型
    data_loader: 数据加载器
    args: 命令行参数
    log_file: 日志文件路径
    
    返回:
    训练历史记录
    """
    log_message("开始训练出站流量预测模型...", log_file)
    
    # 获取训练和验证数据，确保在正确的设备上
    train_data = data_loader.get_grid_station_graph().to(device)
    
    # 获取训练和验证标签
    train_indices = data_loader.out_data[data_loader.out_data['dataset'] == 'train'].index
    val_indices = data_loader.out_data[data_loader.out_data['dataset'] == 'val'].index
    
    # 创建栅格ID到索引的映射
    grid_ids = [f"{row['station']}_{row['hour']}" for _, row in data_loader.out_data.iterrows()]
    unique_grid_ids = list(set(grid_ids))
    grid_id_to_idx = {grid_id: i for i, grid_id in enumerate(unique_grid_ids)}
    
    # 获取训练和验证样本在图中的索引
    train_node_indices = []
    for idx in train_indices:
        row = data_loader.out_data.loc[idx]
        grid_id = f"{row['station']}_{row['hour']}"
        if grid_id in grid_id_to_idx:
            train_node_indices.append(grid_id_to_idx[grid_id])
    
    val_node_indices = []
    for idx in val_indices:
        row = data_loader.out_data.loc[idx]
        grid_id = f"{row['station']}_{row['hour']}"
        if grid_id in grid_id_to_idx:
            val_node_indices.append(grid_id_to_idx[grid_id])
    
    # 使用PyTorch张量，以便我们可以用它来索引
    train_node_indices = torch.tensor(train_node_indices, dtype=torch.long).to(device)
    val_node_indices = torch.tensor(val_node_indices, dtype=torch.long).to(device)
    
    # 获取标签的均值和标准差，用于标准化
    train_mean = data_loader.out_data.loc[train_indices, 'count'].mean()
    train_std = data_loader.out_data.loc[train_indices, 'count'].std()
    
    # 标准化目标值以提高模型性能
    train_targets = torch.tensor(
        (data_loader.out_data.loc[train_indices, 'count'].values - train_mean) / train_std, 
        dtype=torch.float32
    ).to(device).unsqueeze(1)
    
    val_targets = torch.tensor(
        (data_loader.out_data.loc[val_indices, 'count'].values - train_mean) / train_std, 
        dtype=torch.float32
    ).to(device).unsqueeze(1)
    
    # 打印信息以帮助调试
    log_message(f"训练节点索引数量: {len(train_node_indices)}", log_file)
    log_message(f"验证节点索引数量: {len(val_node_indices)}", log_file)
    log_message(f"训练目标数量: {len(train_targets)}", log_file)
    log_message(f"验证目标数量: {len(val_targets)}", log_file)
    
    # 训练历史记录
    history = {
        'train_loss': [],
        'train_mae': [],
        'val_loss': [],
        'val_mae': []
    }
    
    # 早停设置
    best_val_loss = float('inf')
    best_epoch = 0
    patience = args.early_stopping
    
    # 使用cosine学习率调度器以提高收敛性能
    optimizer = torch.optim.Adam(model.out_model.parameters(), lr=args.lr, weight_decay=1e-5)
    scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=args.epochs)
    
    # 使用Huber Loss以减少对异常值的敏感性
    criterion = nn.SmoothL1Loss()
    
    # 训练循环
    for epoch in range(args.epochs):
        # 训练
        model.out_model.train()
        
        # 前向传播
        predictions = model.out_model(train_data)
        
        # 安全地获取对应训练样本的预测结果
        train_predictions = predictions[train_node_indices]
        
        # 确认维度匹配
        if train_predictions.shape[0] != train_targets.shape[0]:
            log_message(f"警告: 预测结果维度 ({train_predictions.shape[0]}) 与目标维度 ({train_targets.shape[0]}) 不匹配", log_file)
            # 调整维度以匹配较小的一个
            min_size = min(train_predictions.shape[0], train_targets.shape[0])
            train_predictions = train_predictions[:min_size]
            train_targets_subset = train_targets[:min_size]
            loss = criterion(train_predictions, train_targets_subset)
        else:
            loss = criterion(train_predictions, train_targets)
        
        # 反向传播
        optimizer.zero_grad()
        loss.backward()
        
        # 梯度裁剪以防止梯度爆炸
        nn.utils.clip_grad_norm_(model.out_model.parameters(), max_norm=1.0)
        
        optimizer.step()
        
        # 计算MAE
        mae = torch.mean(torch.abs(train_predictions - train_targets[:len(train_predictions)])).item()
        
        # 验证
        model.out_model.eval()
        with torch.no_grad():
            val_preds = model.out_model(train_data)
            # 安全地获取对应验证样本的预测结果
            val_predictions = val_preds[val_node_indices]
            
            # 确认维度匹配
            if val_predictions.shape[0] != val_targets.shape[0]:
                log_message(f"警告: 验证预测结果维度 ({val_predictions.shape[0]}) 与验证目标维度 ({val_targets.shape[0]}) 不匹配", log_file)
                # 调整维度以匹配较小的一个
                min_size = min(val_predictions.shape[0], val_targets.shape[0])
                val_predictions = val_predictions[:min_size]
                val_targets_subset = val_targets[:min_size]
                val_loss = criterion(val_predictions, val_targets_subset).item()
                val_mae = torch.mean(torch.abs(val_predictions - val_targets_subset)).item()
            else:
                val_loss = criterion(val_predictions, val_targets).item()
                val_mae = torch.mean(torch.abs(val_predictions - val_targets)).item()
        
        # 更新学习率
        scheduler.step()
        
        # 记录历史
        history['train_loss'].append(loss.item())
        history['train_mae'].append(mae)
        history['val_loss'].append(val_loss)
        history['val_mae'].append(val_mae)
        
        # 打印进度
        log_message(
            f"Epoch {epoch+1}/{args.epochs} - "
            f"Train Loss: {loss.item():.4f}, Train MAE: {mae:.4f}, "
            f"Val Loss: {val_loss:.4f}, Val MAE: {val_mae:.4f}, "
            f"LR: {scheduler.get_last_lr()[0]:.6f}",
            log_file
        )
        
        # 早停检查
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            best_epoch = epoch
            # 保存最佳模型
            model.save_models(os.path.join(args.model_dir, args.model_prefix))
        elif epoch - best_epoch > patience:
            log_message(f"早停！在 {patience} 轮内验证损失没有改善。", log_file)
            break
    
    # 加载最佳模型
    model_path_prefix = os.path.join(args.model_dir, args.model_prefix)
    model.load_models(model_path_prefix)
    
    # 保存训练曲线
    plot_training_curves(
        history['train_loss'], history['val_loss'],
        history['train_mae'], history['val_mae'],
        save_path=os.path.join(args.output_dir, 'out_flow_training_curves.png')
    )
    
    # 保存均值和标准差供预测时使用
    model.out_model.register_buffer('mean', torch.tensor(train_mean, dtype=torch.float32))
    model.out_model.register_buffer('std', torch.tensor(train_std, dtype=torch.float32))
    
    return history, train_mean, train_std

@timer
def train_od_flow_model(model, data_loader, args, log_file=None):
    """
    训练OD流量预测模型
    
    参数:
    model: 模型
    data_loader: 数据加载器
    args: 命令行参数
    log_file: 日志文件路径
    
    返回:
    训练历史记录
    """
    log_message("开始训练OD流量预测模型...", log_file)
    
    # 获取训练和验证数据，确保在正确的设备上
    train_data = data_loader.get_station_station_graph().to(device)
    
    # 获取训练和验证标签
    train_indices = data_loader.od_data[data_loader.od_data['dataset'] == 'train'].index
    val_indices = data_loader.od_data[data_loader.od_data['dataset'] == 'val'].index
    
    # 创建OD对到索引的映射
    edge_pairs = [(row['o_rawname'], row['d_rawname']) for _, row in data_loader.od_data.iterrows()]
    unique_edge_pairs = list(set(edge_pairs))
    edge_pair_to_idx = {pair: i for i, pair in enumerate(unique_edge_pairs)}
    
    # 获取训练和验证样本在图中的索引
    train_edge_indices = []
    for idx in train_indices:
        row = data_loader.od_data.loc[idx]
        edge_pair = (row['o_rawname'], row['d_rawname'])
        if edge_pair in edge_pair_to_idx:
            train_edge_indices.append(edge_pair_to_idx[edge_pair])
    
    val_edge_indices = []
    for idx in val_indices:
        row = data_loader.od_data.loc[idx]
        edge_pair = (row['o_rawname'], row['d_rawname'])
        if edge_pair in edge_pair_to_idx:
            val_edge_indices.append(edge_pair_to_idx[edge_pair])
    
    # 使用PyTorch张量，以便我们可以用它来索引
    train_edge_indices = torch.tensor(train_edge_indices, dtype=torch.long).to(device)
    val_edge_indices = torch.tensor(val_edge_indices, dtype=torch.long).to(device)
    
    # 获取标签的均值和标准差，用于标准化
    train_mean = data_loader.od_data.loc[train_indices, 'trip'].mean()
    train_std = data_loader.od_data.loc[train_indices, 'trip'].std()
    
    # 标准化目标值以提高模型性能
    train_targets = torch.tensor(
        (data_loader.od_data.loc[train_indices, 'trip'].values - train_mean) / train_std, 
        dtype=torch.float32
    ).to(device).unsqueeze(1)
    
    val_targets = torch.tensor(
        (data_loader.od_data.loc[val_indices, 'trip'].values - train_mean) / train_std, 
        dtype=torch.float32
    ).to(device).unsqueeze(1)
    
    # 打印信息以帮助调试
    log_message(f"训练边索引数量: {len(train_edge_indices)}", log_file)
    log_message(f"验证边索引数量: {len(val_edge_indices)}", log_file)
    log_message(f"训练目标数量: {len(train_targets)}", log_file)
    log_message(f"验证目标数量: {len(val_targets)}", log_file)
    
    # 训练历史记录
    history = {
        'train_loss': [],
        'train_mae': [],
        'val_loss': [],
        'val_mae': []
    }
    
    # 早停设置
    best_val_loss = float('inf')
    best_epoch = 0
    patience = args.early_stopping
    
    # 使用cosine学习率调度器以提高收敛性能
    optimizer = torch.optim.Adam(model.od_model.parameters(), lr=args.lr, weight_decay=1e-5)
    scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=args.epochs)
    
    # 使用Huber Loss以减少对异常值的敏感性
    criterion = nn.SmoothL1Loss()
    
    # 训练循环
    for epoch in range(args.epochs):
        # 训练
        model.od_model.train()
        
        # 前向传播
        predictions = model.od_model(train_data)
        
        # 安全地获取对应训练样本的预测结果
        try:
            train_predictions = predictions[train_edge_indices]
            
            # 确认维度匹配
            if train_predictions.shape[0] != train_targets.shape[0]:
                log_message(f"警告: 预测结果维度 ({train_predictions.shape[0]}) 与目标维度 ({train_targets.shape[0]}) 不匹配", log_file)
                # 调整维度以匹配较小的一个
                min_size = min(train_predictions.shape[0], train_targets.shape[0])
                train_predictions = train_predictions[:min_size]
                train_targets_subset = train_targets[:min_size]
                loss = criterion(train_predictions, train_targets_subset)
            else:
                loss = criterion(train_predictions, train_targets)
        except Exception as e:
            log_message(f"训练中出现错误: {str(e)}", log_file)
            log_message(f"预测张量形状: {predictions.shape}, 训练边索引范围: 0-{max(train_edge_indices).item() if len(train_edge_indices) > 0 else 'N/A'}", log_file)
            # 使用最小的有效索引子集
            valid_indices = torch.where(train_edge_indices < predictions.shape[0])[0]
            if len(valid_indices) > 0:
                train_edge_indices_valid = train_edge_indices[valid_indices]
                train_predictions = predictions[train_edge_indices_valid]
                train_targets_valid = train_targets[valid_indices]
                loss = criterion(train_predictions, train_targets_valid)
            else:
                log_message("没有有效的训练索引，跳过此轮", log_file)
                continue
        
        # 反向传播
        optimizer.zero_grad()
        loss.backward()
        
        # 梯度裁剪以防止梯度爆炸
        nn.utils.clip_grad_norm_(model.od_model.parameters(), max_norm=1.0)
        
        optimizer.step()
        
        # 计算MAE (使用最小的共同长度)
        min_len = min(train_predictions.shape[0], train_targets.shape[0])
        mae = torch.mean(torch.abs(train_predictions[:min_len] - train_targets[:min_len])).item()
        
        # 验证
        model.od_model.eval()
        with torch.no_grad():
            val_preds = model.od_model(train_data)
            try:
                # 安全地获取对应验证样本的预测结果
                val_predictions = val_preds[val_edge_indices]
                
                # 确认维度匹配
                if val_predictions.shape[0] != val_targets.shape[0]:
                    log_message(f"警告: 验证预测结果维度 ({val_predictions.shape[0]}) 与验证目标维度 ({val_targets.shape[0]}) 不匹配", log_file)
                    # 调整维度以匹配较小的一个
                    min_size = min(val_predictions.shape[0], val_targets.shape[0])
                    val_predictions = val_predictions[:min_size]
                    val_targets_subset = val_targets[:min_size]
                    val_loss = criterion(val_predictions, val_targets_subset).item()
                    val_mae = torch.mean(torch.abs(val_predictions - val_targets_subset)).item()
                else:
                    val_loss = criterion(val_predictions, val_targets).item()
                    val_mae = torch.mean(torch.abs(val_predictions - val_targets)).item()
            except Exception as e:
                log_message(f"验证中出现错误: {str(e)}", log_file)
                log_message(f"预测张量形状: {val_preds.shape}, 验证边索引范围: 0-{max(val_edge_indices).item() if len(val_edge_indices) > 0 else 'N/A'}", log_file)
                # 使用最小的有效索引子集
                valid_indices = torch.where(val_edge_indices < val_preds.shape[0])[0]
                if len(valid_indices) > 0:
                    val_edge_indices_valid = val_edge_indices[valid_indices]
                    val_predictions = val_preds[val_edge_indices_valid]
                    val_targets_valid = val_targets[valid_indices]
                    val_loss = criterion(val_predictions, val_targets_valid).item()
                    val_mae = torch.mean(torch.abs(val_predictions - val_targets_valid)).item()
                else:
                    val_loss = float('inf')
                    val_mae = float('inf')
                    log_message("没有有效的验证索引", log_file)
        
        # 更新学习率
        scheduler.step()
        
        # 记录历史
        history['train_loss'].append(loss.item())
        history['train_mae'].append(mae)
        history['val_loss'].append(val_loss)
        history['val_mae'].append(val_mae)
        
        # 打印进度
        log_message(
            f"Epoch {epoch+1}/{args.epochs} - "
            f"Train Loss: {loss.item():.4f}, Train MAE: {mae:.4f}, "
            f"Val Loss: {val_loss:.4f}, Val MAE: {val_mae:.4f}, "
            f"LR: {scheduler.get_last_lr()[0]:.6f}",
            log_file
        )
        
        # 早停检查
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            best_epoch = epoch
            # 保存最佳模型
            model.save_models(os.path.join(args.model_dir, args.model_prefix))
        elif epoch - best_epoch > patience:
            log_message(f"早停！在 {patience} 轮内验证损失没有改善。", log_file)
            break
    
    # 加载最佳模型
    model_path_prefix = os.path.join(args.model_dir, args.model_prefix)
    model.load_models(model_path_prefix)
    
    # 保存训练曲线
    plot_training_curves(
        history['train_loss'], history['val_loss'],
        history['train_mae'], history['val_mae'],
        save_path=os.path.join(args.output_dir, 'od_flow_training_curves.png')
    )
    
    # 保存均值和标准差供预测时使用
    model.od_model.register_buffer('mean', torch.tensor(train_mean, dtype=torch.float32))
    model.od_model.register_buffer('std', torch.tensor(train_std, dtype=torch.float32))
    
    return history, train_mean, train_std

@timer
def predict_in_flow(model, data_loader, args):
    """
    预测进站流量
    
    参数:
    model: 模型
    data_loader: 数据加载器
    args: 命令行参数
    
    返回:
    添加了预测结果的GeoDataFrame
    """
    log_message("预测进站流量...")
    
    # 获取测试数据并确保在正确的设备上
    test_data = data_loader.get_grid_station_graph().to(device)
    test_indices = data_loader.in_data[data_loader.in_data['dataset'] == 'test'].index
    
    # 创建栅格ID到索引的映射
    grid_ids = [f"{row['station']}_{row['hour']}" for _, row in data_loader.in_data.iterrows()]
    unique_grid_ids = list(set(grid_ids))
    grid_id_to_idx = {grid_id: i for i, grid_id in enumerate(unique_grid_ids)}
    
    # 获取测试样本在图中的索引
    test_node_indices = []
    for idx in test_indices:
        row = data_loader.in_data.loc[idx]
        grid_id = f"{row['station']}_{row['hour']}"
        if grid_id in grid_id_to_idx:
            test_node_indices.append(grid_id_to_idx[grid_id])
    
    # 预测
    with torch.no_grad():
        model.in_model.eval()  # 确保模型处于评估模式
        all_predictions = model.predict_in_flow(test_data)
        # 将预测结果转移到CPU并转换为numpy数组
        all_predictions = all_predictions.cpu().numpy()
        
        # 对预测结果进行正则化处理（确保非负）
        all_predictions = normalize_predictions(all_predictions)
        
        # 提取测试样本对应的预测结果
        predictions = np.zeros((len(test_indices), 1))
        for i, idx in enumerate(test_indices):
            row = data_loader.in_data.loc[idx]
            grid_id = f"{row['station']}_{row['hour']}"
            if grid_id in grid_id_to_idx:
                node_idx = grid_id_to_idx[grid_id]
                if node_idx < len(all_predictions):
                    predictions[i] = all_predictions[node_idx]
    
    # 将预测结果添加到原始数据中
    in_data_copy = data_loader.in_data.copy()
    in_data_copy.loc[test_indices, 'prediction'] = predictions
    
    # 计算评估指标
    y_true = data_loader.in_data.loc[test_indices, 'count'].values
    metrics = evaluate_predictions(y_true, predictions.flatten())
    
    log_message(f"进站流量预测指标: MAE={metrics['MAE']:.4f}, MSE={metrics['MSE']:.4f}, RMSE={metrics['RMSE']:.4f}, R2={metrics['R2']:.4f}")
    
    return in_data_copy

@timer
def predict_out_flow(model, data_loader, args):
    """
    预测出站流量
    
    参数:
    model: 模型
    data_loader: 数据加载器
    args: 命令行参数
    
    返回:
    添加了预测结果的GeoDataFrame
    """
    log_message("预测出站流量...")
    
    # 获取测试数据并确保在正确的设备上
    test_data = data_loader.get_grid_station_graph().to(device)
    test_indices = data_loader.out_data[data_loader.out_data['dataset'] == 'test'].index
    
    # 创建栅格ID到索引的映射
    grid_ids = [f"{row['station']}_{row['hour']}" for _, row in data_loader.out_data.iterrows()]
    unique_grid_ids = list(set(grid_ids))
    grid_id_to_idx = {grid_id: i for i, grid_id in enumerate(unique_grid_ids)}
    
    # 获取测试样本在图中的索引
    test_node_indices = []
    for idx in test_indices:
        row = data_loader.out_data.loc[idx]
        grid_id = f"{row['station']}_{row['hour']}"
        if grid_id in grid_id_to_idx:
            test_node_indices.append(grid_id_to_idx[grid_id])
    
    # 预测
    with torch.no_grad():
        model.out_model.eval()  # 确保模型处于评估模式
        all_predictions = model.predict_out_flow(test_data)
        # 将预测结果转移到CPU并转换为numpy数组
        all_predictions = all_predictions.cpu().numpy()
        
        # 对预测结果进行正则化处理（确保非负）
        all_predictions = normalize_predictions(all_predictions)
        
        # 提取测试样本对应的预测结果
        predictions = np.zeros((len(test_indices), 1))
        for i, idx in enumerate(test_indices):
            row = data_loader.out_data.loc[idx]
            grid_id = f"{row['station']}_{row['hour']}"
            if grid_id in grid_id_to_idx:
                node_idx = grid_id_to_idx[grid_id]
                if node_idx < len(all_predictions):
                    predictions[i] = all_predictions[node_idx]
    
    # 将预测结果添加到原始数据中
    out_data_copy = data_loader.out_data.copy()
    out_data_copy.loc[test_indices, 'prediction'] = predictions
    
    # 计算评估指标
    y_true = data_loader.out_data.loc[test_indices, 'count'].values
    metrics = evaluate_predictions(y_true, predictions.flatten())
    
    log_message(f"出站流量预测指标: MAE={metrics['MAE']:.4f}, MSE={metrics['MSE']:.4f}, RMSE={metrics['RMSE']:.4f}, R2={metrics['R2']:.4f}")
    
    return out_data_copy

@timer
def predict_od_flow(model, data_loader, args):
    """
    预测OD流量
    
    参数:
    model: 模型
    data_loader: 数据加载器
    args: 命令行参数
    
    返回:
    添加了预测结果的DataFrame
    """
    log_message("预测OD流量...")
    
    # 获取测试数据并确保在正确的设备上
    test_data = data_loader.get_station_station_graph().to(device)
    test_indices = data_loader.od_data[data_loader.od_data['dataset'] == 'test'].index
    
    # 创建OD对到索引的映射
    edge_pairs = [(row['o_rawname'], row['d_rawname']) for _, row in data_loader.od_data.iterrows()]
    unique_edge_pairs = list(set(edge_pairs))
    edge_pair_to_idx = {pair: i for i, pair in enumerate(unique_edge_pairs)}
    
    # 预测
    with torch.no_grad():
        model.od_model.eval()  # 确保模型处于评估模式
        all_predictions = model.predict_od_flow(test_data)
        # 将预测结果转移到CPU并转换为numpy数组
        all_predictions = all_predictions.cpu().numpy()
        
        # 对预测结果进行正则化处理（确保非负）
        all_predictions = normalize_predictions(all_predictions)
        
        # 提取测试样本对应的预测结果
        predictions = np.zeros((len(test_indices), 1))
        for i, idx in enumerate(test_indices):
            row = data_loader.od_data.loc[idx]
            edge_pair = (row['o_rawname'], row['d_rawname'])
            if edge_pair in edge_pair_to_idx:
                edge_idx = edge_pair_to_idx[edge_pair]
                if edge_idx < len(all_predictions):
                    predictions[i] = all_predictions[edge_idx]
    
    # 将预测结果添加到原始数据中
    od_data_copy = data_loader.od_data.copy()
    od_data_copy.loc[test_indices, 'prediction'] = predictions
    
    # 计算评估指标
    y_true = data_loader.od_data.loc[test_indices, 'trip'].values
    metrics = evaluate_predictions(y_true, predictions.flatten())
    
    log_message(f"OD流量预测指标: MAE={metrics['MAE']:.4f}, MSE={metrics['MSE']:.4f}, RMSE={metrics['RMSE']:.4f}, R2={metrics['R2']:.4f}")
    
    return od_data_copy

def main():
    """主函数"""
    # 如果是直接运行文件（而不是通过命令行参数），使用默认参数
    if len(sys.argv) == 1:
        # 创建一个对象来存储默认参数
        class Args:
            def __init__(self):
                self.data_dir = DEFAULT_DATA_DIR
                self.model_dir = DEFAULT_MODEL_DIR
                self.output_dir = DEFAULT_OUTPUT_DIR
                self.hidden_dim = DEFAULT_HIDDEN_DIM
                self.use_fp16 = DEFAULT_USE_FP16
                self.model_prefix = DEFAULT_MODEL_PREFIX
                self.epochs = DEFAULT_EPOCHS
                self.batch_size = DEFAULT_BATCH_SIZE
                self.lr = DEFAULT_LR
                self.early_stopping = DEFAULT_EARLY_STOPPING
                self.train_model = DEFAULT_TRAIN_MODEL
                self.num_layers = DEFAULT_NUM_LAYERS
                self.dropout = DEFAULT_DROPOUT
        
        args = Args()
        print("使用默认参数运行...")
    else:
        # 解析命令行参数
        args = parse_args()
    
    # 创建输出目录
    create_directory(args.output_dir)
    create_directory(args.model_dir)
    
    # 日志文件
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_file = os.path.join(args.output_dir, f'metro_flow_{timestamp}.log')
    
    # 记录基本信息
    log_message(f"设备: {device}", log_file)
    log_message(f"模型目录: {args.model_dir}", log_file)
    log_message(f"输出目录: {args.output_dir}", log_file)
    log_message(f"模型前缀: {args.model_prefix}", log_file)
    if args.train_model:
        log_message(f"训练轮数: {args.epochs}", log_file)
        log_message(f"批量大小: {args.batch_size}", log_file)
        log_message(f"学习率: {args.lr}", log_file)
        log_message(f"隐藏层维度: {args.hidden_dim}", log_file)
        log_message(f"模型层数: {args.num_layers}", log_file)
        log_message(f"Dropout比例: {args.dropout}", log_file)
    
    # 加载数据
    data_loader = MetroDataLoader(args.data_dir, use_fp16=args.use_fp16)
    data_loader.load_all_data()
    data_loader.preprocess_data()
    
    # 获取模型维度
    in_node_features = data_loader.grid_station_graph.x.shape[1]
    out_node_features = data_loader.grid_station_graph.x.shape[1]
    od_node_features = data_loader.station_station_graph.x.shape[1]
    od_edge_features = data_loader.station_station_graph.edge_attr.shape[1]
    
    # 创建模型
    model = MetroFlowModel(
        in_node_features, out_node_features, od_node_features, od_edge_features,
        hidden_dim=args.hidden_dim, dropout=args.dropout, num_layers=args.num_layers,
        use_fp16=args.use_fp16
    )
    
    # 训练或加载模型
    if args.train_model:
        log_message("开始训练模型...", log_file)
        
        # 训练进站流量模型
        in_history, in_mean, in_std = train_in_flow_model(model, data_loader, args, log_file)
        
        # 训练出站流量模型
        out_history, out_mean, out_std = train_out_flow_model(model, data_loader, args, log_file)
        
        # 训练OD流量模型
        od_history, od_mean, od_std = train_od_flow_model(model, data_loader, args, log_file)
        
        # 保存模型
        model_path_prefix = os.path.join(args.model_dir, args.model_prefix)
        model.save_models(model_path_prefix)
        
        log_message("模型训练完成！", log_file)
    else:
        # 加载模型
        model_path_prefix = os.path.join(args.model_dir, args.model_prefix)
        model.load_models(model_path_prefix)
        log_message("加载预训练模型...", log_file)
    
    # 预测
    log_message("开始预测...", log_file)
    in_data_with_predictions = predict_in_flow(model, data_loader, args)
    out_data_with_predictions = predict_out_flow(model, data_loader, args)
    od_data_with_predictions = predict_od_flow(model, data_loader, args)
    
    # 保存预测结果
    # 进站流量
    in_output_path = os.path.join(args.output_dir, 'in_500_predictions_with_coords.shp')
    save_results_to_shapefile(in_data_with_predictions, in_output_path)
    
    # 出站流量
    out_output_path = os.path.join(args.output_dir, 'out_500_predictions_with_coords.shp')
    save_results_to_shapefile(out_data_with_predictions, out_output_path)
    
    # OD流量
    od_output_path = os.path.join(args.output_dir, 'od_predictions.csv')
    save_results_to_csv(od_data_with_predictions, od_output_path)
    
    # 绘制预测对比图
    in_true = in_data_with_predictions[in_data_with_predictions['dataset'] == 'test']['count'].values
    in_pred = in_data_with_predictions[in_data_with_predictions['dataset'] == 'test']['prediction'].values
    
    out_true = out_data_with_predictions[out_data_with_predictions['dataset'] == 'test']['count'].values
    out_pred = out_data_with_predictions[out_data_with_predictions['dataset'] == 'test']['prediction'].values
    
    od_true = od_data_with_predictions[od_data_with_predictions['dataset'] == 'test']['trip'].values
    od_pred = od_data_with_predictions[od_data_with_predictions['dataset'] == 'test']['prediction'].values
    
    # 进站流量对比图
    plot_prediction_comparison(
        in_true, in_pred, 
        title='进站流量预测对比',
        save_path=os.path.join(args.output_dir, 'in_prediction_scatter.png')
    )
    
    # 出站流量对比图
    plot_prediction_comparison(
        out_true, out_pred, 
        title='出站流量预测对比',
        save_path=os.path.join(args.output_dir, 'out_prediction_scatter.png')
    )
    
    # OD流量对比图
    plot_prediction_comparison(
        od_true, od_pred, 
        title='OD流量预测对比',
        save_path=os.path.join(args.output_dir, 'od_prediction_scatter.png')
    )
    
    # 准备总体预测比较结果
    comparison_df = prepare_prediction_comparison(in_data_with_predictions, out_data_with_predictions, od_data_with_predictions)
    save_results_to_csv(comparison_df, os.path.join(args.output_dir, 'prediction_comparison.csv'))
    
    # 绘制总体预测对比图
    plt.figure(figsize=(15, 10))
    
    types = ['In', 'Out', 'OD']
    colors = ['blue', 'green', 'red']
    
    for t, c in zip(types, colors):
        type_data = comparison_df[comparison_df['Type'] == t]
        plt.scatter(type_data['True_Value'], type_data['Predicted_Value'], alpha=0.5, label=t, color=c)
    
    # 绘制理想的对角线
    min_val = min(comparison_df['True_Value'].min(), comparison_df['Predicted_Value'].min())
    max_val = max(comparison_df['True_Value'].max(), comparison_df['Predicted_Value'].max())
    plt.plot([min_val, max_val], [min_val, max_val], 'r--')
    
    plt.xlabel('真实值')
    plt.ylabel('预测值')
    plt.title('预测值与真实值对比')
    plt.legend()
    plt.grid(True)
    plt.tight_layout()
    plt.savefig(os.path.join(args.output_dir, 'prediction_scatter.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    log_message("预测完成！", log_file)

if __name__ == "__main__":
    import sys
    main() 