import os
import torch
import numpy as np
import pandas as pd
from tqdm import tqdm

from data_loader import MetroDataLoader
from models import MetroFlowModel
from utils import log_message, create_directory

# 配置GPU设备
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

def debug_data_structure(data_loader):
    """调试数据结构"""
    print("\n==== 数据结构调试 ====")
    
    # 检查进站数据
    test_indices_in = data_loader.in_data[data_loader.in_data['dataset'] == 'test'].index
    print(f"进站测试样本数量: {len(test_indices_in)}")
    
    # 检查出站数据
    test_indices_out = data_loader.out_data[data_loader.out_data['dataset'] == 'test'].index
    print(f"出站测试样本数量: {len(test_indices_out)}")
    
    # 检查OD数据
    test_indices_od = data_loader.od_data[data_loader.od_data['dataset'] == 'test'].index
    print(f"OD测试样本数量: {len(test_indices_od)}")
    
    # 检查图结构
    print("\n==== 图结构 ====")
    grid_station_graph = data_loader.get_grid_station_graph()
    station_station_graph = data_loader.get_station_station_graph()
    
    print(f"栅格-站点图节点数: {grid_station_graph.x.shape[0]}")
    print(f"栅格-站点图特征维度: {grid_station_graph.x.shape[1]}")
    print(f"栅格-站点图边数: {grid_station_graph.edge_index.shape[1]}")
    
    print(f"站点-站点图节点数: {station_station_graph.x.shape[0]}")
    print(f"站点-站点图特征维度: {station_station_graph.x.shape[1]}")
    print(f"站点-站点图边数: {station_station_graph.edge_index.shape[1]}")
    
    # 检查不同数据集中的站点分布
    in_stations_train = set(data_loader.in_data[data_loader.in_data['dataset'] == 'train']['station'])
    in_stations_val = set(data_loader.in_data[data_loader.in_data['dataset'] == 'val']['station'])
    in_stations_test = set(data_loader.in_data[data_loader.in_data['dataset'] == 'test']['station'])
    
    print(f"\n进站数据训练集站点数: {len(in_stations_train)}")
    print(f"进站数据验证集站点数: {len(in_stations_val)}")
    print(f"进站数据测试集站点数: {len(in_stations_test)}")
    
    # 检查栅格-站点对应关系
    print("\n==== 栅格-站点对应关系 ====")
    # 获取所有唯一的站点
    all_stations = data_loader.in_data['station'].unique()
    print(f"总站点数: {len(all_stations)}")
    
    # 从进站数据中获取栅格信息
    grid_ids = [f"{row['station']}_{row['hour']}" for _, row in data_loader.in_data.iterrows()]
    unique_grid_ids = list(set(grid_ids))
    print(f"总栅格数: {len(unique_grid_ids)}")
    
    # 检查测试集栅格
    test_grid_ids = [f"{row['station']}_{row['hour']}" for _, row in data_loader.in_data[data_loader.in_data['dataset'] == 'test'].iterrows()]
    unique_test_grid_ids = list(set(test_grid_ids))
    print(f"测试集栅格数: {len(unique_test_grid_ids)}")
    
    return {
        'in_test_count': len(test_indices_in),
        'out_test_count': len(test_indices_out),
        'od_test_count': len(test_indices_od),
        'grid_station_nodes': grid_station_graph.x.shape[0],
        'station_station_nodes': station_station_graph.x.shape[0],
        'unique_grid_count': len(unique_grid_ids),
        'test_grid_count': len(unique_test_grid_ids)
    }

def debug_model_predictions(model, data_loader):
    """调试模型预测"""
    print("\n==== 模型预测调试 ====")
    
    # 获取测试数据
    grid_station_graph = data_loader.get_grid_station_graph()
    station_station_graph = data_loader.get_station_station_graph()
    
    # 进站流量预测
    print("\n-- 进站流量预测 --")
    test_indices_in = data_loader.in_data[data_loader.in_data['dataset'] == 'test'].index
    print(f"进站测试样本数量: {len(test_indices_in)}")
    
    with torch.no_grad():
        in_predictions = model.predict_in_flow(grid_station_graph)
        in_predictions = in_predictions.cpu().numpy()
    
    print(f"进站预测结果形状: {in_predictions.shape}")
    
    # 出站流量预测
    print("\n-- 出站流量预测 --")
    test_indices_out = data_loader.out_data[data_loader.out_data['dataset'] == 'test'].index
    print(f"出站测试样本数量: {len(test_indices_out)}")
    
    with torch.no_grad():
        out_predictions = model.predict_out_flow(grid_station_graph)
        out_predictions = out_predictions.cpu().numpy()
    
    print(f"出站预测结果形状: {out_predictions.shape}")
    
    # OD流量预测
    print("\n-- OD流量预测 --")
    test_indices_od = data_loader.od_data[data_loader.od_data['dataset'] == 'test'].index
    print(f"OD测试样本数量: {len(test_indices_od)}")
    
    with torch.no_grad():
        od_predictions = model.predict_od_flow(station_station_graph)
        od_predictions = od_predictions.cpu().numpy()
    
    print(f"OD预测结果形状: {od_predictions.shape}")
    
    return {
        'in_test_count': len(test_indices_in),
        'in_pred_shape': in_predictions.shape,
        'out_test_count': len(test_indices_out),
        'out_pred_shape': out_predictions.shape,
        'od_test_count': len(test_indices_od),
        'od_pred_shape': od_predictions.shape
    }

def debug_grid_node_mapping(data_loader):
    """调试栅格-节点映射"""
    print("\n==== 栅格-节点映射调试 ====")
    
    # 从进站数据中获取栅格信息
    grid_ids = [f"{row['station']}_{row['hour']}" for _, row in data_loader.in_data.iterrows()]
    unique_grid_ids = list(set(grid_ids))
    grid_id_to_idx = {grid_id: i for i, grid_id in enumerate(unique_grid_ids)}
    
    # 获取所有唯一的站点
    stations = data_loader.in_data['station'].unique()
    num_grids = len(unique_grid_ids)
    station_to_idx = {station: i + num_grids for i, station in enumerate(stations)}
    
    print(f"栅格数量: {len(grid_id_to_idx)}")
    print(f"站点数量: {len(station_to_idx)}")
    print(f"栅格-站点图总节点数: {len(grid_id_to_idx) + len(station_to_idx)}")
    
    # 检查栅格-站点连接
    test_indices = data_loader.in_data[data_loader.in_data['dataset'] == 'test'].index
    test_stations = set(data_loader.in_data.loc[test_indices, 'station'])
    test_hours = set(data_loader.in_data.loc[test_indices, 'hour'])
    
    print(f"测试集站点数: {len(test_stations)}")
    print(f"测试集小时数: {len(test_hours)}")
    print(f"理论测试集栅格数 (站点×小时): {len(test_stations) * len(test_hours)}")
    
    # 检查模型是如何生成预测结果的
    print("\n在模型中的预测过程:")
    print("1. 模型接收整个图作为输入")
    print("2. 对图中所有节点生成预测结果")
    print("3. 从中选择对应测试样本的结果")
    
    print("\n关键问题: 测试样本在图中的索引位置与预测结果的对应关系")
    
    # 输出一些测试样本的信息
    print("\n测试样本示例:")
    for i, idx in enumerate(test_indices[:5]):
        row = data_loader.in_data.loc[idx]
        grid_id = f"{row['station']}_{row['hour']}"
        grid_idx = grid_id_to_idx.get(grid_id, -1)
        print(f"样本 {i+1}: 站点={row['station']}, 小时={row['hour']}, 图中索引={grid_idx}")
    
    return {
        'grid_count': len(grid_id_to_idx),
        'station_count': len(station_to_idx),
        'total_nodes': len(grid_id_to_idx) + len(station_to_idx),
        'test_stations': len(test_stations),
        'test_hours': len(test_hours),
        'theoretical_test_grids': len(test_stations) * len(test_hours)
    }

def analyze_prediction_mismatch(data_loader, model_predictions):
    """分析预测结果与测试样本不匹配的原因"""
    print("\n==== 预测结果与测试样本不匹配分析 ====")
    
    # 理解不匹配的核心原因
    print("\n不匹配的核心原因:")
    print("1. 模型预测时是对整个图的所有节点进行预测，而不是仅对测试样本")
    print("2. 图中的节点不仅包含测试样本对应的节点，还包含其他节点")
    print("3. 需要找到测试样本在图中的对应节点位置，才能正确提取预测结果")
    
    # 计算正确的映射关系
    grid_ids = [f"{row['station']}_{row['hour']}" for _, row in data_loader.in_data.iterrows()]
    unique_grid_ids = list(set(grid_ids))
    grid_id_to_idx = {grid_id: i for i, grid_id in enumerate(unique_grid_ids)}
    
    # 测试集样本在图中的索引位置
    test_indices = data_loader.in_data[data_loader.in_data['dataset'] == 'test'].index
    test_grid_indices = []
    
    for idx in test_indices:
        row = data_loader.in_data.loc[idx]
        grid_id = f"{row['station']}_{row['hour']}"
        grid_idx = grid_id_to_idx.get(grid_id)
        if grid_idx is not None:
            test_grid_indices.append(grid_idx)
    
    print(f"\n测试样本数量: {len(test_indices)}")
    print(f"找到对应图节点的测试样本数量: {len(test_grid_indices)}")
    
    # 解决方案
    print("\n解决方案:")
    print("1. 创建测试样本ID到图节点索引的映射")
    print("2. 使用映射从模型预测结果中提取对应测试样本的预测值")
    print("3. 或者在构建图时保持测试样本的索引顺序")
    
    return {
        'test_count': len(test_indices),
        'matched_count': len(test_grid_indices)
    }

def propose_solution():
    """提出解决方案"""
    print("\n==== 解决方案 ====")
    print("根据分析，预测结果与测试样本数量不匹配的原因是:")
    print("1. 模型预测的是图中所有节点的结果，包括栅格节点和站点节点")
    print("2. 但我们只需要测试集样本对应的栅格节点的预测结果")
    
    print("\n建议的解决方案:")
    print("方案1: 使用索引映射(修改predict.py)")
    print("""
def predict_in_flow(model, data_loader, args):
    # 获取测试数据
    test_data = data_loader.get_grid_station_graph()
    test_indices = data_loader.in_data[data_loader.in_data['dataset'] == 'test'].index
    
    # 创建栅格ID到索引的映射
    grid_ids = [f"{row['station']}_{row['hour']}" for _, row in data_loader.in_data.iterrows()]
    unique_grid_ids = list(set(grid_ids))
    grid_id_to_idx = {grid_id: i for i, grid_id in enumerate(unique_grid_ids)}
    
    # 获取测试样本在图中的索引
    test_node_indices = []
    for idx in test_indices:
        row = data_loader.in_data.loc[idx]
        grid_id = f"{row['station']}_{row['hour']}"
        if grid_id in grid_id_to_idx:
            test_node_indices.append(grid_id_to_idx[grid_id])
    
    # 预测
    with torch.no_grad():
        all_predictions = model.predict_in_flow(test_data)
        all_predictions = all_predictions.cpu().numpy()
        
        # 提取测试样本对应的预测结果
        predictions = np.zeros((len(test_indices), 1))
        for i, (idx, node_idx) in enumerate(zip(test_indices, test_node_indices)):
            if i < len(test_node_indices):
                predictions[i] = all_predictions[node_idx]
    
    # 将预测结果添加到原始数据中
    in_data_copy = data_loader.in_data.copy()
    in_data_copy.loc[test_indices, 'prediction'] = predictions
    
    return in_data_copy
    """)
    
    print("\n方案2: 修改数据加载器，保持测试样本的顺序与图节点一致")
    print("这需要修改data_loader.py中的_build_grid_station_graph方法")
    
    print("\n推荐使用方案1，因为它更灵活且不需要修改数据加载器的核心逻辑")

def main():
    """主函数"""
    print("开始调试地铁站流量预测系统...")
    
    # 加载数据
    data_dir = 'C:/Users/<USER>/Desktop/接驳'
    data_loader = MetroDataLoader(data_dir, use_fp16=True)
    data_loader.load_all_data()
    data_loader.preprocess_data()
    
    # 调试数据结构
    data_info = debug_data_structure(data_loader)
    
    # 创建模型
    in_node_features = data_loader.grid_station_graph.x.shape[1]
    out_node_features = data_loader.grid_station_graph.x.shape[1]
    od_node_features = data_loader.station_station_graph.x.shape[1]
    od_edge_features = data_loader.station_station_graph.edge_attr.shape[1]
    
    model = MetroFlowModel(
        in_node_features, out_node_features, od_node_features, od_edge_features,
        hidden_dim=128, use_fp16=True
    )
    
    # 调试模型预测
    pred_info = debug_model_predictions(model, data_loader)
    
    # 调试栅格-节点映射
    mapping_info = debug_grid_node_mapping(data_loader)
    
    # 分析预测结果与测试样本不匹配的原因
    mismatch_info = analyze_prediction_mismatch(data_loader, pred_info)
    
    # 提出解决方案
    propose_solution()
    
    print("\n调试完成！")

if __name__ == "__main__":
    main() 