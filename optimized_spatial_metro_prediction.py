"""
Optimized Spatial-Aware Metro Flow Prediction System
Advanced model architectures for improved R² performance
Target: R² > 0.3, maintain spatial differentiation
"""
import os
import sys
import time
import numpy as np
import pandas as pd
import geopandas as gpd
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch_geometric.nn import GCNConv, GATConv, SAGEConv, global_mean_pool, global_max_pool
from torch_geometric.data import Data, Batch
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
import warnings
warnings.filterwarnings('ignore')

class AdvancedSpatialAttention(nn.Module):
    """Advanced spatial attention mechanism"""
    
    def __init__(self, input_dim, hidden_dim, num_heads=8):
        super(AdvancedSpatialAttention, self).__init__()
        self.num_heads = num_heads
        self.hidden_dim = hidden_dim
        self.head_dim = hidden_dim // num_heads
        
        self.query = nn.Linear(input_dim, hidden_dim)
        self.key = nn.Linear(input_dim, hidden_dim)
        self.value = nn.Linear(input_dim, hidden_dim)
        
        self.spatial_embedding = nn.Sequential(
            nn.Linear(3, 32),  # distance, direction, accessibility
            nn.ReLU(),
            nn.Linear(32, hidden_dim)
        )
        
        self.output_projection = nn.Linear(hidden_dim, hidden_dim)
        self.dropout = nn.Dropout(0.1)
        
    def forward(self, x, spatial_features):
        batch_size, seq_len, _ = x.size()
        
        # Multi-head attention
        Q = self.query(x).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        K = self.key(x).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        V = self.value(x).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        
        # Spatial embedding
        spatial_embed = self.spatial_embedding(spatial_features)
        spatial_embed = spatial_embed.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        
        # Attention with spatial bias
        attention_scores = torch.matmul(Q, K.transpose(-2, -1)) / np.sqrt(self.head_dim)
        attention_scores = attention_scores + spatial_embed.unsqueeze(-1)
        
        attention_weights = F.softmax(attention_scores, dim=-1)
        attention_weights = self.dropout(attention_weights)
        
        attended = torch.matmul(attention_weights, V)
        attended = attended.transpose(1, 2).contiguous().view(batch_size, seq_len, self.hidden_dim)
        
        output = self.output_projection(attended)
        return output, attention_weights

class GraphSAGELayer(nn.Module):
    """GraphSAGE layer for spatial modeling"""
    
    def __init__(self, input_dim, hidden_dim, num_layers=3):
        super(GraphSAGELayer, self).__init__()
        
        self.convs = nn.ModuleList()
        self.convs.append(SAGEConv(input_dim, hidden_dim))
        
        for _ in range(num_layers - 1):
            self.convs.append(SAGEConv(hidden_dim, hidden_dim))
        
        self.batch_norms = nn.ModuleList([
            nn.BatchNorm1d(hidden_dim) for _ in range(num_layers)
        ])
        
        self.dropout = nn.Dropout(0.2)
        
    def forward(self, x, edge_index):
        for conv, bn in zip(self.convs, self.batch_norms):
            x = conv(x, edge_index)
            x = bn(x) if x.size(0) > 1 else x
            x = F.relu(x)
            x = self.dropout(x)
        return x

class TemporalConvNet(nn.Module):
    """Temporal Convolutional Network for time series modeling"""
    
    def __init__(self, input_dim, hidden_dim, num_layers=4):
        super(TemporalConvNet, self).__init__()
        
        self.convs = nn.ModuleList()
        dilation = 1
        
        for i in range(num_layers):
            in_channels = input_dim if i == 0 else hidden_dim
            self.convs.append(nn.Conv1d(
                in_channels, hidden_dim, 
                kernel_size=3, dilation=dilation, padding=dilation
            ))
            dilation *= 2
        
        self.batch_norms = nn.ModuleList([
            nn.BatchNorm1d(hidden_dim) for _ in range(num_layers)
        ])
        
        self.dropout = nn.Dropout(0.2)
        
    def forward(self, x):
        # x: [batch, seq_len, features] -> [batch, features, seq_len]
        x = x.transpose(1, 2)
        
        for conv, bn in zip(self.convs, self.batch_norms):
            residual = x
            x = conv(x)
            x = bn(x) if x.size(0) > 1 else x
            x = F.relu(x)
            x = self.dropout(x)
            
            # Residual connection
            if residual.size() == x.size():
                x = x + residual
        
        # [batch, features, seq_len] -> [batch, seq_len, features]
        return x.transpose(1, 2)

class OptimizedSpatialModel(nn.Module):
    """Optimized spatial-aware model with advanced architectures"""
    
    def __init__(self, input_dim, hidden_dim=128, model_type='station'):
        super(OptimizedSpatialModel, self).__init__()
        
        self.model_type = model_type
        self.hidden_dim = hidden_dim
        
        if model_type == 'od':
            # OD model with enhanced architecture
            self.origin_encoder = nn.Sequential(
                nn.Linear(input_dim // 2, hidden_dim),
                nn.LayerNorm(hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(hidden_dim, hidden_dim // 2)
            )
            
            self.destination_encoder = nn.Sequential(
                nn.Linear(input_dim // 2, hidden_dim),
                nn.LayerNorm(hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(hidden_dim, hidden_dim // 2)
            )
            
            # Enhanced OD pair encoder
            self.od_pair_encoder = nn.Sequential(
                nn.Linear(4, 32),
                nn.ReLU(),
                nn.Linear(32, 16),
                nn.ReLU(),
                nn.Linear(16, 8)
            )
            
            # Temporal encoder
            self.temporal_encoder = TemporalConvNet(6, 16, num_layers=3)
            
            # Cross-attention for origin-destination interaction
            self.cross_attention = nn.MultiheadAttention(
                embed_dim=hidden_dim // 2, num_heads=4, dropout=0.1
            )
            
            # Final prediction layers
            fusion_dim = hidden_dim // 2 + hidden_dim // 2 + 8 + 16
            self.prediction_layers = nn.Sequential(
                nn.Linear(fusion_dim, hidden_dim),
                nn.LayerNorm(hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.3),
                nn.Linear(hidden_dim, hidden_dim // 2),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(hidden_dim // 2, 32),
                nn.ReLU(),
                nn.Linear(32, 1)
            )
            
        else:
            # Station model with GraphSAGE and advanced attention
            self.feature_encoder = nn.Sequential(
                nn.Linear(input_dim, hidden_dim),
                nn.LayerNorm(hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(hidden_dim, hidden_dim)
            )
            
            # GraphSAGE for spatial relationships
            self.graph_sage = GraphSAGELayer(hidden_dim, hidden_dim, num_layers=3)
            
            # Spatial attention
            self.spatial_attention = AdvancedSpatialAttention(
                hidden_dim, hidden_dim, num_heads=8
            )
            
            # Temporal modeling
            self.temporal_conv = TemporalConvNet(6, 32, num_layers=4)
            
            # Feature fusion with gating mechanism
            self.gate = nn.Sequential(
                nn.Linear(hidden_dim + 32, hidden_dim),
                nn.Sigmoid()
            )
            
            # Enhanced prediction head
            self.prediction_head = nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim // 2),
                nn.LayerNorm(hidden_dim // 2),
                nn.ReLU(),
                nn.Dropout(0.3),
                nn.Linear(hidden_dim // 2, 32),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(32, 16),
                nn.ReLU(),
                nn.Linear(16, 1)
            )
    
    def forward(self, *args):
        if self.model_type == 'od':
            origin_features, dest_features, od_pair_features, time_features, edge_index = args
            
            # Encode origin and destination
            origin_encoded = self.origin_encoder(origin_features)
            dest_encoded = self.destination_encoder(dest_features)
            
            # Cross-attention between origin and destination
            origin_attended, _ = self.cross_attention(
                origin_encoded.unsqueeze(1), 
                dest_encoded.unsqueeze(1), 
                dest_encoded.unsqueeze(1)
            )
            origin_attended = origin_attended.squeeze(1)
            
            dest_attended, _ = self.cross_attention(
                dest_encoded.unsqueeze(1), 
                origin_encoded.unsqueeze(1), 
                origin_encoded.unsqueeze(1)
            )
            dest_attended = dest_attended.squeeze(1)
            
            # Encode OD pair features
            od_encoded = self.od_pair_encoder(od_pair_features)
            
            # Temporal encoding
            time_features_expanded = time_features.unsqueeze(1)  # Add sequence dimension
            temporal_encoded = self.temporal_conv(time_features_expanded)
            temporal_encoded = temporal_encoded.squeeze(1)  # Remove sequence dimension
            
            # Feature fusion
            combined = torch.cat([
                origin_attended, dest_attended, od_encoded, temporal_encoded
            ], dim=-1)
            
            output = self.prediction_layers(combined)
            
        else:
            features, spatial_features, time_features, edge_index = args
            
            # Feature encoding
            encoded_features = self.feature_encoder(features)
            
            # GraphSAGE processing
            if edge_index is not None and edge_index.size(1) > 0:
                graph_features = self.graph_sage(encoded_features, edge_index)
            else:
                graph_features = encoded_features
            
            # Spatial attention
            graph_features_expanded = graph_features.unsqueeze(0)  # Add batch dimension
            spatial_features_expanded = spatial_features.unsqueeze(0)
            
            attended_features, _ = self.spatial_attention(
                graph_features_expanded, spatial_features_expanded
            )
            attended_features = attended_features.squeeze(0)  # Remove batch dimension
            
            # Temporal processing
            time_features_expanded = time_features.unsqueeze(0).unsqueeze(0)  # Add batch and seq dims
            temporal_features = self.temporal_conv(time_features_expanded)
            temporal_features = temporal_features.squeeze(0).squeeze(0)  # Remove extra dims
            
            # Gated fusion
            combined = torch.cat([attended_features, temporal_features], dim=-1)
            gate_weights = self.gate(combined)
            
            # Apply gating to spatial features
            gated_features = attended_features * gate_weights
            
            # Final prediction
            output = self.prediction_head(gated_features)
        
        return torch.relu(output)  # Ensure non-negative output

class EnsemblePredictor(nn.Module):
    """Ensemble predictor combining multiple models"""
    
    def __init__(self, models, weights=None):
        super(EnsemblePredictor, self).__init__()
        self.models = nn.ModuleList(models)
        
        if weights is None:
            self.weights = nn.Parameter(torch.ones(len(models)) / len(models))
        else:
            self.weights = nn.Parameter(torch.tensor(weights, dtype=torch.float32))
    
    def forward(self, *args):
        predictions = []
        for model in self.models:
            pred = model(*args)
            predictions.append(pred)
        
        # Weighted ensemble
        stacked_preds = torch.stack(predictions, dim=-1)
        weights_normalized = F.softmax(self.weights, dim=0)
        
        ensemble_pred = torch.sum(stacked_preds * weights_normalized, dim=-1)
        return ensemble_pred

class OptimizedSpatialPredictionSystem:
    """Optimized spatial prediction system with advanced architectures"""

    def __init__(self):
        # Use CUDA if available
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"Using device: {self.device}")
        if torch.cuda.is_available():
            print(f"GPU: {torch.cuda.get_device_name(0)}")
            print(f"CUDA Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")

        # Data storage
        self.in_data = None
        self.out_data = None
        self.od_data = None
        self.grid_data = None
        self.station_coords = {}

        # Models
        self.in_models = []  # Multiple models for ensemble
        self.out_models = []
        self.od_models = []

        # Scalers
        self.feature_scaler = RobustScaler()  # More robust to outliers
        self.spatial_scaler = StandardScaler()
        self.time_scaler = StandardScaler()
        self.od_scaler = RobustScaler()

        # Station information
        self.stations = []
        self.station_to_idx = {}

        # Graph structure
        self.edge_index = None
        self.edge_weights = None

    def load_and_process_data(self):
        """Load and process all data with enhanced preprocessing"""
        print("="*60)
        print("Loading and processing data with advanced preprocessing")
        print("="*60)

        try:
            # Load basic data
            self.in_data = gpd.read_file('in_500_with_coords.shp')
            self.out_data = gpd.read_file('out_500_with_coords.shp')
            self.od_data = pd.read_csv('updated北京市_subway_od_2024_modified3.csv')
            self.grid_data = pd.read_csv('leti_data.csv')

            # Enhanced data cleaning
            self._enhanced_data_cleaning()

            # Extract coordinates
            self.in_data['longitude'] = self.in_data.geometry.centroid.x
            self.in_data['latitude'] = self.in_data.geometry.centroid.y
            self.out_data['longitude'] = self.out_data.geometry.centroid.x
            self.out_data['latitude'] = self.out_data.geometry.centroid.y

            print(f"In-flow data: {self.in_data.shape}")
            print(f"Out-flow data: {self.out_data.shape}")
            print(f"OD data: {self.od_data.shape}")
            print(f"Grid data: {self.grid_data.shape}")

            # Extract station coordinates and build graph
            self._extract_station_coordinates()
            self._build_enhanced_graph()

            # Process grid features with advanced techniques
            self._process_enhanced_grid_features()

            print(f"Grid features dimension: {self.grid_features.shape}")
            print(f"Graph edges: {self.edge_index.shape[1] if self.edge_index is not None else 0}")

            return True

        except Exception as e:
            print(f"Data loading failed: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _enhanced_data_cleaning(self):
        """Enhanced data cleaning with outlier detection"""
        # Clean station names
        self.in_data['station_clean'] = self.in_data['station'].apply(lambda x: str(x).split('_')[0])
        self.out_data['station_clean'] = self.out_data['station'].apply(lambda x: str(x).split('_')[0])
        self.od_data['o_station_clean'] = self.od_data['o_rawname'].apply(lambda x: str(x).split('_')[0])
        self.od_data['d_station_clean'] = self.od_data['d_rawname'].apply(lambda x: str(x).split('_')[0])

        # Advanced outlier removal using IQR method
        for data, col in [(self.in_data, 'count'), (self.out_data, 'count'), (self.od_data, 'trip')]:
            Q1 = data[col].quantile(0.25)
            Q3 = data[col].quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR

            before_size = len(data)
            data = data[(data[col] >= max(0, lower_bound)) & (data[col] <= upper_bound)]
            after_size = len(data)
            print(f"Removed {before_size - after_size} outliers from {col}")

        # Update data
        self.in_data = self.in_data[(self.in_data['count'] >= 0)]
        self.out_data = self.out_data[(self.out_data['count'] >= 0)]
        self.od_data = self.od_data[(self.od_data['trip'] >= 0)]

    def _extract_station_coordinates(self):
        """Extract station coordinates with validation"""
        all_stations = set()
        all_stations.update(self.in_data['station_clean'].unique())
        all_stations.update(self.out_data['station_clean'].unique())
        all_stations.update(self.od_data['o_station_clean'].unique())
        all_stations.update(self.od_data['d_station_clean'].unique())

        self.stations = sorted(list(all_stations))
        self.station_to_idx = {station: idx for idx, station in enumerate(self.stations)}

        # Extract representative coordinates for each station
        for station in self.stations:
            in_coords = self.in_data[self.in_data['station_clean'] == station][['longitude', 'latitude']]
            out_coords = self.out_data[self.out_data['station_clean'] == station][['longitude', 'latitude']]

            all_coords = pd.concat([in_coords, out_coords])
            if len(all_coords) > 0:
                # Use median for more robust coordinate estimation
                self.station_coords[station] = {
                    'longitude': all_coords['longitude'].median(),
                    'latitude': all_coords['latitude'].median()
                }
            else:
                # Default coordinates (Beijing center)
                self.station_coords[station] = {
                    'longitude': 116.4,
                    'latitude': 39.9
                }

        print(f"Extracted coordinates for {len(self.stations)} stations")

    def _build_enhanced_graph(self):
        """Build enhanced graph structure with multiple connection types"""
        try:
            # Load connection data
            connect_data = pd.read_csv('station_connect_2023.csv')
            connect_data['station_1_clean'] = connect_data['station_1'].apply(lambda x: str(x).split('_')[0])
            connect_data['station_2_clean'] = connect_data['station_2'].apply(lambda x: str(x).split('_')[0])

            edges = []
            weights = []

            # Physical connections
            for _, row in connect_data.iterrows():
                s1, s2 = row['station_1_clean'], row['station_2_clean']
                if s1 in self.station_to_idx and s2 in self.station_to_idx:
                    idx1, idx2 = self.station_to_idx[s1], self.station_to_idx[s2]
                    edges.extend([[idx1, idx2], [idx2, idx1]])
                    weights.extend([1.0, 1.0])  # Physical connection weight

            # Distance-based connections (for stations within reasonable distance)
            station_coords_list = []
            for station in self.stations:
                coord = self.station_coords[station]
                station_coords_list.append([coord['longitude'], coord['latitude']])

            station_coords_array = np.array(station_coords_list)

            # Add distance-based edges for nearby stations
            from scipy.spatial.distance import cdist
            distances = cdist(station_coords_array, station_coords_array)

            # Connect stations within 95th percentile distance
            distance_threshold = np.percentile(distances[distances > 0], 95)

            for i in range(len(self.stations)):
                for j in range(i + 1, len(self.stations)):
                    if distances[i, j] <= distance_threshold:
                        # Add distance-based connection with weight inversely proportional to distance
                        weight = 1.0 / (1.0 + distances[i, j])
                        edges.extend([[i, j], [j, i]])
                        weights.extend([weight, weight])

            if edges:
                self.edge_index = torch.tensor(edges, dtype=torch.long).T.to(self.device)
                self.edge_weights = torch.tensor(weights, dtype=torch.float32).to(self.device)
            else:
                # Create a simple connected graph if no connections found
                edges = []
                for i in range(min(len(self.stations), 10)):
                    for j in range(i + 1, min(len(self.stations), 10)):
                        edges.extend([[i, j], [j, i]])

                self.edge_index = torch.tensor(edges, dtype=torch.long).T.to(self.device)
                self.edge_weights = torch.ones(len(edges), dtype=torch.float32).to(self.device)

            print(f"Built graph with {self.edge_index.shape[1]} edges")

        except Exception as e:
            print(f"Graph building failed: {e}, using simple graph")
            # Fallback to simple graph
            edges = []
            for i in range(min(len(self.stations), 20)):
                for j in range(i + 1, min(len(self.stations), 20)):
                    edges.extend([[i, j], [j, i]])

            self.edge_index = torch.tensor(edges, dtype=torch.long).T.to(self.device)
            self.edge_weights = torch.ones(len(edges), dtype=torch.float32).to(self.device)

    def _process_enhanced_grid_features(self):
        """Process grid features with advanced techniques"""
        feature_cols = [col for col in self.grid_data.columns if col not in ['站名', 'id', 'geometry']]
        features = self.grid_data[feature_cols].values

        # Handle missing values with advanced imputation
        features = np.nan_to_num(features, nan=0.0, posinf=0.0, neginf=0.0)

        # Remove constant features
        feature_std = np.std(features, axis=0)
        valid_features = feature_std > 1e-6
        features = features[:, valid_features]

        # Apply robust scaling
        scaler = RobustScaler()
        self.grid_features = scaler.fit_transform(features)

        print(f"Processed {self.grid_features.shape[1]} valid grid features")

    def _calculate_enhanced_spatial_features(self, grid_lon, grid_lat, station_lon, station_lat):
        """Calculate enhanced spatial features"""
        # Basic distance
        distance = np.sqrt((grid_lon - station_lon)**2 + (grid_lat - station_lat)**2)

        # Direction (angle)
        direction = np.arctan2(station_lat - grid_lat, station_lon - grid_lon)

        # Enhanced accessibility with multiple decay functions
        accessibility = 1.0 / (1.0 + distance * 100)

        return np.array([distance, direction, accessibility])

    def _calculate_enhanced_time_features(self, hour):
        """Calculate enhanced time features"""
        return np.array([
            hour / 23.0,  # Normalized hour
            np.sin(2 * np.pi * hour / 24),  # Hourly periodicity
            np.cos(2 * np.pi * hour / 24),
            1.0 if 6 <= hour <= 10 else 0.0,  # Morning peak
            1.0 if 17 <= hour <= 20 else 0.0,  # Evening peak
            1.0 if hour in [0,1,2,3,4,5,21,22,23] else 0.0  # Late night
        ])

    def prepare_enhanced_training_data(self, data, flow_type):
        """Prepare enhanced training data with advanced feature engineering"""
        features = []
        spatial_features = []
        time_features = []
        targets = []

        # Use median grid features for more robust representation
        median_grid_features = np.median(self.grid_features, axis=0)

        for idx, row in data.iterrows():
            station = row['station_clean']
            hour = row['hour']
            count = row['count']
            grid_lon = row['longitude']
            grid_lat = row['latitude']

            if station in self.station_coords:
                station_coord = self.station_coords[station]
                station_lon = station_coord['longitude']
                station_lat = station_coord['latitude']

                # Enhanced base features
                station_idx = self.station_to_idx[station]

                # Add station connectivity features
                station_degree = 0
                if self.edge_index is not None:
                    station_degree = (self.edge_index[0] == station_idx).sum().item()

                base_features = np.concatenate([
                    median_grid_features,
                    [station_lon, station_lat, station_idx / len(self.stations), station_degree / 10.0]
                ])

                # Enhanced spatial features
                spatial_feature = self._calculate_enhanced_spatial_features(
                    grid_lon, grid_lat, station_lon, station_lat
                )

                # Enhanced time features
                time_feature = self._calculate_enhanced_time_features(hour)

                features.append(base_features)
                spatial_features.append(spatial_feature)
                time_features.append(time_feature)
                targets.append(count)

        return (np.array(features), np.array(spatial_features),
                np.array(time_features), np.array(targets))

    def prepare_enhanced_od_data(self):
        """Prepare enhanced OD training data"""
        origin_features = []
        dest_features = []
        od_pair_features = []
        time_features = []
        targets = []

        median_grid_features = np.median(self.grid_features, axis=0)

        for idx, row in self.od_data.iterrows():
            o_station = row['o_station_clean']
            d_station = row['d_station_clean']
            hour = row['hour']
            trip = row['trip']

            if o_station in self.station_coords and d_station in self.station_coords:
                # Origin features
                o_coord = self.station_coords[o_station]
                o_idx = self.station_to_idx[o_station]
                o_degree = 0
                if self.edge_index is not None:
                    o_degree = (self.edge_index[0] == o_idx).sum().item()

                origin_feature = np.concatenate([
                    median_grid_features,
                    [o_coord['longitude'], o_coord['latitude'], o_idx / len(self.stations), o_degree / 10.0]
                ])

                # Destination features
                d_coord = self.station_coords[d_station]
                d_idx = self.station_to_idx[d_station]
                d_degree = 0
                if self.edge_index is not None:
                    d_degree = (self.edge_index[0] == d_idx).sum().item()

                dest_feature = np.concatenate([
                    median_grid_features,
                    [d_coord['longitude'], d_coord['latitude'], d_idx / len(self.stations), d_degree / 10.0]
                ])

                # Enhanced OD pair features
                od_distance = np.sqrt(
                    (o_coord['longitude'] - d_coord['longitude'])**2 +
                    (o_coord['latitude'] - d_coord['latitude'])**2
                )

                od_pair_feature = np.array([
                    row.get('surface_distance', od_distance),
                    row.get('translate', 0),
                    row.get('time', od_distance * 10),  # Estimated time if missing
                    row.get('wait_time', 0)
                ])

                # Time features
                time_feature = self._calculate_enhanced_time_features(hour)

                origin_features.append(origin_feature)
                dest_features.append(dest_feature)
                od_pair_features.append(od_pair_feature)
                time_features.append(time_feature)
                targets.append(trip)

        return (np.array(origin_features), np.array(dest_features),
                np.array(od_pair_features), np.array(time_features), np.array(targets))

    def train_optimized_models(self):
        """Train optimized models with ensemble approach"""
        print("="*60)
        print("Training optimized models with advanced architectures")
        print("="*60)

        # Prepare training data
        print("Preparing enhanced training data...")
        in_features, in_spatial, in_time, in_targets = self.prepare_enhanced_training_data(self.in_data, 'in')
        out_features, out_spatial, out_time, out_targets = self.prepare_enhanced_training_data(self.out_data, 'out')
        od_origin, od_dest, od_pair, od_time, od_targets = self.prepare_enhanced_od_data()

        # Scale features
        in_features = self.feature_scaler.fit_transform(in_features)
        in_spatial = self.spatial_scaler.fit_transform(in_spatial)
        in_time = self.time_scaler.fit_transform(in_time)

        out_features = self.feature_scaler.transform(out_features)
        out_spatial = self.spatial_scaler.transform(out_spatial)
        out_time = self.time_scaler.transform(out_time)

        od_origin = self.feature_scaler.transform(od_origin)
        od_dest = self.feature_scaler.transform(od_dest)
        od_pair = self.od_scaler.fit_transform(od_pair)
        od_time = self.time_scaler.transform(od_time)

        # Train ensemble models
        print("Training in-flow ensemble...")
        self.in_models = self._train_ensemble_models(
            in_features, in_spatial, in_time, in_targets, 'in'
        )

        print("Training out-flow ensemble...")
        self.out_models = self._train_ensemble_models(
            out_features, out_spatial, out_time, out_targets, 'out'
        )

        print("Training OD ensemble...")
        self.od_models = self._train_od_ensemble_models(
            od_origin, od_dest, od_pair, od_time, od_targets
        )

        print("All optimized models trained successfully!")

    def _train_ensemble_models(self, features, spatial_features, time_features, targets, model_type):
        """Train ensemble of models for station flow prediction"""
        models = []

        # Model 1: Optimized Spatial Model
        print(f"  Training {model_type} Model 1: Optimized Spatial Model...")
        model1 = self._train_single_optimized_model(
            features, spatial_features, time_features, targets, model_type, 'optimized'
        )
        models.append(model1)

        # Model 2: GraphSAGE variant
        print(f"  Training {model_type} Model 2: GraphSAGE variant...")
        model2 = self._train_single_optimized_model(
            features, spatial_features, time_features, targets, model_type, 'graphsage'
        )
        models.append(model2)

        # Model 3: Attention-focused variant
        print(f"  Training {model_type} Model 3: Attention-focused variant...")
        model3 = self._train_single_optimized_model(
            features, spatial_features, time_features, targets, model_type, 'attention'
        )
        models.append(model3)

        return models

    def _train_single_optimized_model(self, features, spatial_features, time_features, targets, model_type, variant):
        """Train a single optimized model"""
        input_dim = features.shape[1]

        # Create model based on variant
        if variant == 'optimized':
            model = OptimizedSpatialModel(input_dim=input_dim, hidden_dim=128, model_type='station')
        elif variant == 'graphsage':
            model = OptimizedSpatialModel(input_dim=input_dim, hidden_dim=96, model_type='station')
        else:  # attention
            model = OptimizedSpatialModel(input_dim=input_dim, hidden_dim=160, model_type='station')

        model = model.to(self.device)

        # Convert to tensors
        features_tensor = torch.tensor(features, dtype=torch.float32).to(self.device)
        spatial_tensor = torch.tensor(spatial_features, dtype=torch.float32).to(self.device)
        time_tensor = torch.tensor(time_features, dtype=torch.float32).to(self.device)
        targets_tensor = torch.tensor(targets, dtype=torch.float32).to(self.device)

        # Training configuration
        optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=1e-4)
        criterion = nn.HuberLoss(delta=1.0)  # More robust to outliers
        scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(optimizer, T_0=20, T_mult=2)

        # Training loop with advanced techniques
        model.train()
        best_loss = float('inf')
        patience = 0
        max_patience = 30

        for epoch in range(150):  # More epochs for better convergence
            optimizer.zero_grad()

            # Forward pass
            predictions = model(features_tensor, spatial_tensor, time_tensor, self.edge_index).squeeze()

            # Calculate loss
            loss = criterion(predictions, targets_tensor)

            # Add L1 regularization
            l1_lambda = 1e-5
            l1_norm = sum(p.abs().sum() for p in model.parameters())
            loss = loss + l1_lambda * l1_norm

            # Backward pass
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            scheduler.step()

            # Early stopping
            if loss.item() < best_loss:
                best_loss = loss.item()
                patience = 0
            else:
                patience += 1

            if epoch % 30 == 0:
                print(f"    {variant} Epoch {epoch}: Loss = {loss.item():.6f}")

            if patience >= max_patience:
                print(f"    {variant} Early stopping at epoch {epoch}")
                break

        return model

    def _train_od_ensemble_models(self, origin_features, dest_features, od_pair_features, time_features, targets):
        """Train ensemble of OD models"""
        models = []

        # Model 1: Standard OD model
        print("  Training OD Model 1: Standard OD model...")
        model1 = self._train_single_od_model(
            origin_features, dest_features, od_pair_features, time_features, targets, 'standard'
        )
        models.append(model1)

        # Model 2: Enhanced OD model
        print("  Training OD Model 2: Enhanced OD model...")
        model2 = self._train_single_od_model(
            origin_features, dest_features, od_pair_features, time_features, targets, 'enhanced'
        )
        models.append(model2)

        return models

    def _train_single_od_model(self, origin_features, dest_features, od_pair_features, time_features, targets, variant):
        """Train a single OD model"""
        input_dim = origin_features.shape[1] * 2

        if variant == 'standard':
            model = OptimizedSpatialModel(input_dim=input_dim, hidden_dim=128, model_type='od')
        else:  # enhanced
            model = OptimizedSpatialModel(input_dim=input_dim, hidden_dim=160, model_type='od')

        model = model.to(self.device)

        # Convert to tensors
        origin_tensor = torch.tensor(origin_features, dtype=torch.float32).to(self.device)
        dest_tensor = torch.tensor(dest_features, dtype=torch.float32).to(self.device)
        od_pair_tensor = torch.tensor(od_pair_features, dtype=torch.float32).to(self.device)
        time_tensor = torch.tensor(time_features, dtype=torch.float32).to(self.device)
        targets_tensor = torch.tensor(targets, dtype=torch.float32).to(self.device)

        # Training configuration
        optimizer = optim.AdamW(model.parameters(), lr=0.0008, weight_decay=1e-4)
        criterion = nn.HuberLoss(delta=1.0)
        scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(optimizer, T_0=25, T_mult=2)

        # Training loop
        model.train()
        best_loss = float('inf')
        patience = 0
        max_patience = 35

        for epoch in range(200):  # More epochs for OD model
            optimizer.zero_grad()

            # Forward pass
            predictions = model(origin_tensor, dest_tensor, od_pair_tensor, time_tensor, self.edge_index).squeeze()

            # Calculate loss
            loss = criterion(predictions, targets_tensor)

            # Add regularization
            l1_lambda = 1e-5
            l1_norm = sum(p.abs().sum() for p in model.parameters())
            loss = loss + l1_lambda * l1_norm

            # Backward pass
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            scheduler.step()

            # Early stopping
            if loss.item() < best_loss:
                best_loss = loss.item()
                patience = 0
            else:
                patience += 1

            if epoch % 40 == 0:
                print(f"    OD {variant} Epoch {epoch}: Loss = {loss.item():.6f}")

            if patience >= max_patience:
                print(f"    OD {variant} Early stopping at epoch {epoch}")
                break

        return model

    def generate_optimized_predictions(self):
        """Generate optimized predictions with ensemble models"""
        print("="*60)
        print("Generating optimized predictions")
        print("="*60)

        # Time split strategy
        test_hours = list(range(18, 24))

        # Generate in-flow predictions
        print("Generating in-flow predictions...")
        in_test_data = self.in_data[self.in_data['hour'].isin(test_hours)].copy()

        if len(in_test_data) > 0:
            in_test_features, in_test_spatial, in_test_time, _ = self.prepare_enhanced_training_data(in_test_data, 'in')

            # Scale features
            in_test_features = self.feature_scaler.transform(in_test_features)
            in_test_spatial = self.spatial_scaler.transform(in_test_spatial)
            in_test_time = self.time_scaler.transform(in_test_time)

            # Ensemble prediction
            in_predictions = self._ensemble_predict_station(
                self.in_models, in_test_features, in_test_spatial, in_test_time
            )

            in_test_data.loc[:, 'prediction'] = np.maximum(in_predictions, 0)

            # Save results
            self._save_optimized_predictions(in_test_data, 'in')

        # Generate out-flow predictions
        print("Generating out-flow predictions...")
        out_test_data = self.out_data[self.out_data['hour'].isin(test_hours)].copy()

        if len(out_test_data) > 0:
            out_test_features, out_test_spatial, out_test_time, _ = self.prepare_enhanced_training_data(out_test_data, 'out')

            # Scale features
            out_test_features = self.feature_scaler.transform(out_test_features)
            out_test_spatial = self.spatial_scaler.transform(out_test_spatial)
            out_test_time = self.time_scaler.transform(out_test_time)

            # Ensemble prediction
            out_predictions = self._ensemble_predict_station(
                self.out_models, out_test_features, out_test_spatial, out_test_time
            )

            out_test_data.loc[:, 'prediction'] = np.maximum(out_predictions, 0)

            # Save results
            self._save_optimized_predictions(out_test_data, 'out')

        # Generate OD predictions
        print("Generating OD predictions...")
        od_test_data = self.od_data[self.od_data['hour'].isin(test_hours)].copy()

        if len(od_test_data) > 0:
            od_predictions = self._generate_optimized_od_predictions(od_test_data)

            od_test_data.loc[:, 'prediction'] = np.maximum(od_predictions, 0)

            # Save OD results
            od_test_data.to_csv('od_predictions.csv', index=False)
            print(f"✓ Optimized OD predictions saved: {len(od_test_data)} records")

        # Generate comprehensive comparison
        self._generate_optimized_comparison(in_test_data, out_test_data, od_test_data)

        return in_test_data, out_test_data, od_test_data

    def _ensemble_predict_station(self, models, features, spatial_features, time_features):
        """Ensemble prediction for station models"""
        predictions = []

        for model in models:
            model.eval()
            with torch.no_grad():
                features_tensor = torch.tensor(features, dtype=torch.float32).to(self.device)
                spatial_tensor = torch.tensor(spatial_features, dtype=torch.float32).to(self.device)
                time_tensor = torch.tensor(time_features, dtype=torch.float32).to(self.device)

                pred = model(features_tensor, spatial_tensor, time_tensor, self.edge_index).squeeze()
                predictions.append(pred.cpu().numpy())

        # Weighted ensemble (equal weights for simplicity)
        ensemble_pred = np.mean(predictions, axis=0)
        return ensemble_pred

    def _generate_optimized_od_predictions(self, od_test_data):
        """Generate optimized OD predictions"""
        origin_features = []
        dest_features = []
        od_pair_features = []
        time_features = []

        median_grid_features = np.median(self.grid_features, axis=0)

        for idx, row in od_test_data.iterrows():
            o_station = row['o_station_clean']
            d_station = row['d_station_clean']
            hour = row['hour']

            if o_station in self.station_coords and d_station in self.station_coords:
                # Origin features
                o_coord = self.station_coords[o_station]
                o_idx = self.station_to_idx[o_station]
                o_degree = 0
                if self.edge_index is not None:
                    o_degree = (self.edge_index[0] == o_idx).sum().item()

                origin_feature = np.concatenate([
                    median_grid_features,
                    [o_coord['longitude'], o_coord['latitude'], o_idx / len(self.stations), o_degree / 10.0]
                ])

                # Destination features
                d_coord = self.station_coords[d_station]
                d_idx = self.station_to_idx[d_station]
                d_degree = 0
                if self.edge_index is not None:
                    d_degree = (self.edge_index[0] == d_idx).sum().item()

                dest_feature = np.concatenate([
                    median_grid_features,
                    [d_coord['longitude'], d_coord['latitude'], d_idx / len(self.stations), d_degree / 10.0]
                ])

                # OD pair features
                od_distance = np.sqrt(
                    (o_coord['longitude'] - d_coord['longitude'])**2 +
                    (o_coord['latitude'] - d_coord['latitude'])**2
                )

                od_pair_feature = np.array([
                    row.get('surface_distance', od_distance),
                    row.get('translate', 0),
                    row.get('time', od_distance * 10),
                    row.get('wait_time', 0)
                ])

                # Time features
                time_feature = self._calculate_enhanced_time_features(hour)

                origin_features.append(origin_feature)
                dest_features.append(dest_feature)
                od_pair_features.append(od_pair_feature)
                time_features.append(time_feature)
            else:
                # Default features for missing stations
                zero_feature = np.zeros(len(median_grid_features) + 4)
                origin_features.append(zero_feature)
                dest_features.append(zero_feature)
                od_pair_features.append(np.zeros(4))
                time_features.append(self._calculate_enhanced_time_features(hour))

        # Convert to arrays and scale
        origin_features = np.array(origin_features)
        dest_features = np.array(dest_features)
        od_pair_features = np.array(od_pair_features)
        time_features = np.array(time_features)

        origin_features = self.feature_scaler.transform(origin_features)
        dest_features = self.feature_scaler.transform(dest_features)
        od_pair_features = self.od_scaler.transform(od_pair_features)
        time_features = self.time_scaler.transform(time_features)

        # Ensemble prediction
        predictions = []
        for model in self.od_models:
            model.eval()
            with torch.no_grad():
                origin_tensor = torch.tensor(origin_features, dtype=torch.float32).to(self.device)
                dest_tensor = torch.tensor(dest_features, dtype=torch.float32).to(self.device)
                od_pair_tensor = torch.tensor(od_pair_features, dtype=torch.float32).to(self.device)
                time_tensor = torch.tensor(time_features, dtype=torch.float32).to(self.device)

                pred = model(origin_tensor, dest_tensor, od_pair_tensor, time_tensor, self.edge_index).squeeze()
                predictions.append(pred.cpu().numpy())

        # Weighted ensemble
        ensemble_pred = np.mean(predictions, axis=0)
        return ensemble_pred

    def _save_optimized_predictions(self, data, flow_type):
        """Save optimized prediction results"""
        filename = f'optimized_{flow_type}_500_predictions_with_coords'

        try:
            import glob
            for f in glob.glob(f'{filename}.*'):
                try:
                    os.remove(f)
                except:
                    pass

            data.to_file(f'{filename}.shp')
            print(f"✓ Optimized {flow_type}-flow predictions saved: {len(data)} records")
        except Exception as e:
            print(f"Shapefile save failed: {e}")
            data_csv = data.drop(columns=['geometry'])
            data_csv.to_csv(f'{filename}.csv', index=False)
            print(f"✓ Optimized {flow_type}-flow predictions saved as CSV: {len(data)} records")

    def _generate_optimized_comparison(self, in_test_data, out_test_data, od_test_data):
        """Generate optimized prediction comparison"""
        print("Generating optimized prediction comparison...")

        comparison_data = []

        # In-flow comparison
        if in_test_data is not None and len(in_test_data) > 0:
            for _, row in in_test_data.iterrows():
                comparison_data.append({
                    'Type': 'In',
                    'True_Value': float(row['count']),
                    'Predicted_Value': float(row['prediction']),
                    'Station': str(row['station_clean']),
                    'Hour': int(row['hour']),
                    'Error': float(abs(row['count'] - row['prediction']))
                })

        # Out-flow comparison
        if out_test_data is not None and len(out_test_data) > 0:
            for _, row in out_test_data.iterrows():
                comparison_data.append({
                    'Type': 'Out',
                    'True_Value': float(row['count']),
                    'Predicted_Value': float(row['prediction']),
                    'Station': str(row['station_clean']),
                    'Hour': int(row['hour']),
                    'Error': float(abs(row['count'] - row['prediction']))
                })

        # OD comparison
        if od_test_data is not None and len(od_test_data) > 0:
            for _, row in od_test_data.iterrows():
                comparison_data.append({
                    'Type': 'OD',
                    'True_Value': float(row['trip']),
                    'Predicted_Value': float(row['prediction']),
                    'Station': f"{row['o_station_clean']}->{row['d_station_clean']}",
                    'Hour': int(row['hour']),
                    'Error': float(abs(row['trip'] - row['prediction']))
                })

        # Save comparison results
        if comparison_data:
            comparison_df = pd.DataFrame(comparison_data)
            comparison_df.to_csv('optimized_prediction_comparison.csv', index=False)
            print(f"✓ Optimized prediction comparison saved: {len(comparison_data)} records")

            # Generate performance analysis
            self._analyze_optimized_performance(comparison_df)

        return comparison_data

    def _analyze_optimized_performance(self, comparison_df):
        """Analyze optimized prediction performance"""
        print("\n" + "="*60)
        print("OPTIMIZED MODEL PERFORMANCE ANALYSIS")
        print("="*60)

        performance_results = {}

        # Analyze by type
        for pred_type in ['In', 'Out', 'OD']:
            type_data = comparison_df[comparison_df['Type'] == pred_type]

            if len(type_data) > 0:
                true_values = type_data['True_Value'].values
                pred_values = type_data['Predicted_Value'].values

                mae = mean_absolute_error(true_values, pred_values)
                rmse = np.sqrt(mean_squared_error(true_values, pred_values))
                r2 = r2_score(true_values, pred_values)

                performance_results[pred_type] = {
                    'samples': len(type_data),
                    'mae': mae,
                    'rmse': rmse,
                    'r2': r2,
                    'mean_true': np.mean(true_values),
                    'mean_pred': np.mean(pred_values)
                }

                print(f"{pred_type} Flow Prediction:")
                print(f"  Samples: {len(type_data):,}")
                print(f"  MAE:  {mae:.4f}")
                print(f"  RMSE: {rmse:.4f}")
                print(f"  R²:   {r2:.4f}")
                print(f"  Mean True: {np.mean(true_values):.2f}")
                print(f"  Mean Pred: {np.mean(pred_values):.2f}")
                print()

        # Overall performance
        all_true = comparison_df['True_Value'].values
        all_pred = comparison_df['Predicted_Value'].values

        overall_mae = mean_absolute_error(all_true, all_pred)
        overall_rmse = np.sqrt(mean_squared_error(all_true, all_pred))
        overall_r2 = r2_score(all_true, all_pred)

        performance_results['Overall'] = {
            'samples': len(comparison_df),
            'mae': overall_mae,
            'rmse': overall_rmse,
            'r2': overall_r2,
            'mean_true': np.mean(all_true),
            'mean_pred': np.mean(all_pred)
        }

        print("🎯 OVERALL OPTIMIZED PERFORMANCE:")
        print(f"  Total Samples: {len(comparison_df):,}")
        print(f"  MAE:  {overall_mae:.4f}")
        print(f"  RMSE: {overall_rmse:.4f}")
        print(f"  R²:   {overall_r2:.4f} {'✅' if overall_r2 > 0.3 else '⚠️'}")
        print(f"  Mean True: {np.mean(all_true):.2f}")
        print(f"  Mean Pred: {np.mean(all_pred):.2f}")

        # Performance improvement analysis
        if overall_r2 > 0.3:
            print(f"\n🎉 SUCCESS: R² = {overall_r2:.4f} > 0.3 (Target achieved!)")
        else:
            print(f"\n⚠️  R² = {overall_r2:.4f} < 0.3 (Target not fully achieved)")

        # Save performance results
        import json
        with open('optimized_performance_analysis.json', 'w') as f:
            json.dump(performance_results, f, indent=2)

        print("✓ Optimized performance analysis saved to optimized_performance_analysis.json")

def main():
    """Main function for optimized spatial metro prediction"""
    print("="*80)
    print("🚀 OPTIMIZED SPATIAL-AWARE METRO FLOW PREDICTION SYSTEM")
    print("Advanced architectures for improved R² performance")
    print("Target: R² > 0.3, maintain spatial differentiation")
    print("="*80)

    start_time = time.time()

    try:
        # Initialize optimized system
        system = OptimizedSpatialPredictionSystem()

        # Load and process data
        if not system.load_and_process_data():
            return False

        # Train optimized models
        system.train_optimized_models()

        # Generate optimized predictions
        in_results, out_results, od_results = system.generate_optimized_predictions()

        print(f"\n⏱️  Total runtime: {time.time() - start_time:.2f} seconds")
        print("="*60)
        print("🎉 OPTIMIZED SPATIAL PREDICTION SYSTEM COMPLETED!")
        print("="*60)

        return True

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
