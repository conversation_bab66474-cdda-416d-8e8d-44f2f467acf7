"""
GPU优化的训练器
使用混合精度训练、梯度累积等技术
"""
import torch
import torch.nn as nn
import torch.optim as optim
from torch.cuda.amp import GradScaler, autocast
import numpy as np
import gc
from model_gpu_optimized import GPUOptimizedTGCN
from memory_monitor import MemoryMonitor

# 禁用torch.compile以避免Triton依赖
import torch._dynamo
torch._dynamo.config.suppress_errors = True

class GPUOptimizedTrainer:
    # ... 其余代码保持不变
    def __init__(self, config):
        self.config = config
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # 混合精度训练
        self.use_amp = True and torch.cuda.is_available()
        self.scaler = GradScaler() if self.use_amp else None
        
        # 梯度累积
        self.accumulation_steps = getattr(config, 'ACCUMULATION_STEPS', 4)
        
        print(f"🚀 GPU优化训练器初始化:")
        print(f"   设备: {self.device}")
        print(f"   混合精度: {'✅' if self.use_amp else '❌'}")
        print(f"   梯度累积步数: {self.accumulation_steps}")
        
        # 内存监控
        self.memory_monitor = MemoryMonitor()
    
    def train_multitask_model(self, X, in_flows, out_flows, od_flows, edge_index, edge_weights,
                             train_indices, test_indices, common_stations):
        """GPU优化的多任务训练"""
        print("🚀 开始GPU优化训练...")
        
        # 开始内存监控
        self.memory_monitor.start_monitoring()
        
        try:
            # 数据准备和优化
            train_data = self._prepare_optimized_data(
                X, in_flows, out_flows, edge_index, edge_weights, train_indices, test_indices
            )
            
            # 模型初始化
            model = self._initialize_optimized_model(X, train_indices)
            
            # 训练循环
            model = self._train_with_optimizations(model, train_data)
            
            return model
            
        finally:
            # 停止监控并生成报告
            self.memory_monitor.stop_monitoring()
    
    def _prepare_optimized_data(self, X, in_flows, out_flows, edge_index, edge_weights, 
                               train_indices, test_indices):
        """数据预处理和优化"""
        print("📊 准备GPU优化数据...")
        
        # 智能数据分割（基于GPU内存）
        if torch.cuda.is_available():
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        else:
            gpu_memory = 8
        
        # 根据GPU内存调整批处理大小
        if gpu_memory < 4:  # < 4GB
            max_nodes_per_batch = 30
            max_time_steps = 8
        elif gpu_memory < 8:  # 4-8GB
            max_nodes_per_batch = 50
            max_time_steps = 12
        else:  # > 8GB
            max_nodes_per_batch = 100
            max_time_steps = 18
        
        # 限制数据大小
        if len(train_indices) > max_nodes_per_batch:
            train_indices = train_indices[:max_nodes_per_batch]
            print(f"   训练节点数量限制为: {len(train_indices)}")
        
        if len(test_indices) > max_nodes_per_batch:
            test_indices = test_indices[:max_nodes_per_batch]
            print(f"   测试节点数量限制为: {len(test_indices)}")
        
        if X.shape[0] > max_time_steps:
            X = X[-max_time_steps:]
            in_flows = in_flows[-max_time_steps:]
            out_flows = out_flows[-max_time_steps:]
            print(f"   时间步数限制为: {X.shape[0]}")
        
        # 图简化 - 保留最重要的边
        edge_index, edge_weights = self._simplify_graph_smart(
            edge_index, edge_weights, max_edges_per_node=15
        )
        
        # 数据类型优化
        if self.use_amp:
            # 混合精度：输入用float16，权重用float32
            X = X.half()
            in_flows = in_flows.half()
            out_flows = out_flows.half()
            edge_weights = edge_weights.half()
        else:
            X = X.float()
            in_flows = in_flows.float()
            out_flows = out_flows.float()
            edge_weights = edge_weights.float()
        
        # 移动到GPU
        X = X.to(self.device, non_blocking=True)
        in_flows = in_flows.to(self.device, non_blocking=True)
        out_flows = out_flows.to(self.device, non_blocking=True)
        edge_index = edge_index.to(self.device, non_blocking=True)
        edge_weights = edge_weights.to(self.device, non_blocking=True)
        
        # 转换indices为tensor以便GPU操作
        train_indices_tensor = torch.tensor(train_indices, dtype=torch.long, device=self.device)
        test_indices_tensor = torch.tensor(test_indices, dtype=torch.long, device=self.device)
        
        # 重新映射边
        train_edge_index, train_edge_weights = self._remap_edges_efficient(
            edge_index, edge_weights, train_indices_tensor
        )
        test_edge_index, test_edge_weights = self._remap_edges_efficient(
            edge_index, edge_weights, test_indices_tensor
        )
        
        return {
            'X_train': X[:, train_indices].unsqueeze(0),
            'X_test': X[:, test_indices].unsqueeze(0),
            'in_train': in_flows[-1, train_indices].unsqueeze(0),
            'in_test': in_flows[-1, test_indices].unsqueeze(0),
            'out_train': out_flows[-1, train_indices].unsqueeze(0),
            'out_test': out_flows[-1, test_indices].unsqueeze(0),
            'edge_index_train': train_edge_index,
            'edge_weights_train': train_edge_weights,
            'edge_index_test': test_edge_index,
            'edge_weights_test': test_edge_weights
        }
    
    def _simplify_graph_smart(self, edge_index, edge_weights, max_edges_per_node=15):
        """智能图简化"""
        print(f"🔗 图简化: 原始边数 {edge_index.shape[1]}")
        
        num_nodes = edge_index.max().item() + 1
        target_edges = max_edges_per_node * num_nodes
        
        if edge_index.shape[1] <= target_edges:
            return edge_index, edge_weights
        
        # 保留权重最大的边
        _, top_indices = torch.topk(edge_weights, min(target_edges, len(edge_weights)))
        simplified_edge_index = edge_index[:, top_indices]
        simplified_edge_weights = edge_weights[top_indices]
        
        # 确保图连通性：为每个节点至少保留一条边
        node_has_edge = torch.zeros(num_nodes, dtype=torch.bool, device=edge_index.device)
        src_nodes = simplified_edge_index[0]
        dst_nodes = simplified_edge_index[1]
        node_has_edge[src_nodes] = True
        node_has_edge[dst_nodes] = True
        
        # 为没有边的节点添加自环
        isolated_nodes = torch.where(~node_has_edge)[0]
        if len(isolated_nodes) > 0:
            self_loops = torch.stack([isolated_nodes, isolated_nodes], dim=0)
            self_weights = torch.ones(len(isolated_nodes), device=edge_weights.device, dtype=edge_weights.dtype)
            
            simplified_edge_index = torch.cat([simplified_edge_index, self_loops], dim=1)
            simplified_edge_weights = torch.cat([simplified_edge_weights, self_weights], dim=0)
        
        print(f"   简化后边数: {simplified_edge_index.shape[1]}")
        return simplified_edge_index, simplified_edge_weights
    
    def _initialize_optimized_model(self, X, train_indices):
        """初始化优化模型"""
        # 动态调整模型大小
        input_dim = X.shape[-1]
        num_nodes = len(train_indices)
        
        # 根据数据规模调整隐藏维度
        if num_nodes > 80:
            hidden_dim = 32
        elif num_nodes > 40:
            hidden_dim = 48
        else:
            hidden_dim = 64
            
        hidden_dim = min(hidden_dim, self.config.HIDDEN_DIM)
        
        model = GPUOptimizedTGCN(
            input_dim=input_dim,
            hidden_dim=hidden_dim,
            num_nodes=num_nodes,
            output_tasks=['in_flow', 'out_flow'],  # 专注于主要任务
            use_checkpoint=True,  # 启用梯度检查点
            num_heads=4,
            dropout=0.1
        ).to(self.device)
        
        # 编译模型（PyTorch 2.0+）
        if hasattr(torch, 'compile') and torch.cuda.is_available():
            try:
                model = torch.compile(model, mode="reduce-overhead")
                print("✅ 模型编译成功")
            except Exception as e:
                print(f"⚠️  模型编译失败: {e}")
        
        # 计算模型大小
        model_size = sum(p.numel() for p in model.parameters()) * 4 / 1024**2  # MB
        print(f"📈 模型大小: {model_size:.1f} MB")
        print(f"📊 隐藏维度: {hidden_dim}")
        
        return model
    
    def _train_with_optimizations(self, model, train_data):
        """优化训练循环"""
        # 优化器设置
        optimizer = optim.AdamW(
            model.parameters(), 
            lr=self.config.LEARNING_RATE,
            weight_decay=1e-4,
            eps=1e-8,
            fused=True if torch.cuda.is_available() else False  # 融合优化器
        )
        
        # 学习率调度器
        scheduler = optim.lr_scheduler.OneCycleLR(
            optimizer,
            max_lr=self.config.LEARNING_RATE,
            total_steps=self.config.NUM_EPOCHS,
            pct_start=0.1,
            anneal_strategy='cos'
        )
        
        # 损失函数
        criterion = nn.SmoothL1Loss()  # 更稳定的损失函数
        
        best_loss = float('inf')
        patience_counter = 0
        patience = 20
        
        print("🎯 开始训练循环...")
        
        for epoch in range(self.config.NUM_EPOCHS):
            # 训练阶段
            model.train()
            total_loss = 0
            
            # 梯度累积循环
            optimizer.zero_grad()
            
            for step in range(self.accumulation_steps):
                with autocast(enabled=self.use_amp):
                    outputs = model(
                        train_data['X_train'], 
                        train_data['edge_index_train'], 
                        train_data['edge_weights_train']
                    )
                    
                    # 计算损失
                    loss = 0
                    loss_count = 0
                    
                    if 'in_flow' in outputs:
                        in_loss = criterion(outputs['in_flow'], train_data['in_train'])
                        loss += in_loss
                        loss_count += 1
                    
                    if 'out_flow' in outputs:
                        out_loss = criterion(outputs['out_flow'], train_data['out_train'])
                        loss += out_loss
                        loss_count += 1
                    
                    # 平均损失
                    if loss_count > 0:
                        loss = loss / loss_count
                    
                    # 梯度累积
                    loss = loss / self.accumulation_steps
                
                # 反向传播
                if self.use_amp:
                    self.scaler.scale(loss).backward()
                else:
                    loss.backward()
                
                total_loss += loss.item()
            
            # 优化器步骤
            if self.use_amp:
                # 梯度裁剪
                self.scaler.unscale_(optimizer)
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                
                self.scaler.step(optimizer)
                self.scaler.update()
            else:
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
            
            scheduler.step()
            
            # 验证阶段
            if epoch % 5 == 0:
                model.eval()
                with torch.no_grad(), autocast(enabled=self.use_amp):
                    val_outputs = model(
                        train_data['X_test'], 
                        train_data['edge_index_test'], 
                        train_data['edge_weights_test']
                    )
                    
                    val_loss = 0
                    val_count = 0
                    
                    if 'in_flow' in val_outputs:
                        val_loss += criterion(val_outputs['in_flow'], train_data['in_test'])
                        val_count += 1
                    
                    if 'out_flow' in val_outputs:
                        val_loss += criterion(val_outputs['out_flow'], train_data['out_test'])
                        val_count += 1
                    
                    if val_count > 0:
                        val_loss = val_loss / val_count
                
                print(f'Epoch {epoch+1}/{self.config.NUM_EPOCHS}:')
                print(f'  Train Loss: {total_loss:.4f}')
                print(f'  Val Loss: {val_loss.item():.4f}')
                print(f'  LR: {scheduler.get_last_lr()[0]:.6f}')
                
                # GPU内存使用
                if torch.cuda.is_available():
                    memory_used = torch.cuda.memory_allocated() / 1024**2
                    memory_total = torch.cuda.get_device_properties(0).total_memory / 1024**2
                    print(f'  GPU内存: {memory_used:.0f}/{memory_total:.0f} MB ({memory_used/memory_total*100:.1f}%)')
                
                # 早停检查
                if val_loss < best_loss:
                    best_loss = val_loss
                    patience_counter = 0
                    torch.save(model.state_dict(), 'best_gpu_optimized_model.pth')
                    print("  💾 保存最佳模型")
                else:
                    patience_counter += 1
                
                if patience_counter >= patience:
                    print(f"🛑 早停于第 {epoch+1} 轮")
                    break
            
            # 定期清理内存
            if epoch % 10 == 0:
                gc.collect()
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
        
        # 加载最佳模型
        if torch.cuda.is_available():
            model.load_state_dict(torch.load('best_gpu_optimized_model.pth'))
        else:
            model.load_state_dict(torch.load('best_gpu_optimized_model.pth', map_location='cpu'))
        
        print("✅ GPU优化训练完成！")
        return model
    
    def _remap_edges_efficient(self, edge_index, edge_weights, node_indices):
        """高效的边重映射"""
        # 创建映射字典
        old_to_new = torch.zeros(edge_index.max().item() + 1, dtype=torch.long, device=self.device) - 1
        old_to_new[node_indices] = torch.arange(len(node_indices), device=self.device)
        
        # 筛选边
        src_mask = torch.isin(edge_index[0], node_indices)
        dst_mask = torch.isin(edge_index[1], node_indices)
        edge_mask = src_mask & dst_mask
        
        if edge_mask.sum() == 0:
            # 如果没有有效边，创建自环
            n_nodes = len(node_indices)
            self_loop_index = torch.stack([torch.arange(n_nodes, device=self.device), 
                                         torch.arange(n_nodes, device=self.device)], dim=0)
            self_loop_weights = torch.ones(n_nodes, device=self.device, dtype=edge_weights.dtype)
            return self_loop_index, self_loop_weights
        
        # 重映射边索引
        sub_edge_index = edge_index[:, edge_mask]
        sub_edge_weights = edge_weights[edge_mask]
        
        remapped_edge_index = torch.stack([
            old_to_new[sub_edge_index[0]],
            old_to_new[sub_edge_index[1]]
        ], dim=0)
        
        return remapped_edge_index, sub_edge_weights 