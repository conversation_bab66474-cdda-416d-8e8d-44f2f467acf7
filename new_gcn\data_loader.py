import pandas as pd
import geopandas as gpd
import numpy as np
from shapely.geometry import Point
from sklearn.preprocessing import StandardScaler, LabelEncoder
import torch
from config import Config

class DataLoader:
    def __init__(self):
        self.config = Config()
        self.station_encoder = LabelEncoder()
        self.scaler = StandardScaler()
        self.station_coords = {}
        self.station_features = {}
        self.grid_coords = {}
        
    def load_shapefile_data(self, shapefile_path, data_type='in'):
        """加载shapefile数据并提取地理信息"""
        print(f"正在加载{data_type}站数据...")
        gdf = gpd.read_file(shapefile_path)
        
        # 提取栅格中心坐标作为栅格经纬度
        if 'geometry' in gdf.columns:
            gdf['grid_lon'] = gdf.geometry.centroid.x
            gdf['grid_lat'] = gdf.geometry.centroid.y
        
        # 确保数据类型正确
        if self.config.USE_FLOAT16:
            numeric_cols = gdf.select_dtypes(include=[np.number]).columns
            for col in numeric_cols:
                if col not in ['hour']:  # 保持hour为整数
                    gdf[col] = gdf[col].astype(np.float16)
        
        print(f"{data_type}站数据形状: {gdf.shape}")
        return gdf
    
    def load_od_data(self):
        """加载OD数据"""
        print("正在加载OD数据...")
        od_data = pd.read_csv(self.config.OD_PATH)
        
        if self.config.USE_FLOAT16:
            numeric_cols = od_data.select_dtypes(include=[np.number]).columns
            for col in numeric_cols:
                if col not in ['hour']:
                    od_data[col] = od_data[col].astype(np.float16)
        
        print(f"OD数据形状: {od_data.shape}")
        return od_data
    
    def load_station_features(self):
        """加载站点特征数据"""
        print("正在加载站点特征数据...")
        features_df = pd.read_csv(self.config.STATION_FEATURES_PATH)
        
        # 除站名外的所有列都是特征列
        feature_cols = [col for col in features_df.columns if col != '站名']
        
        if self.config.USE_FLOAT16:
            for col in feature_cols:
                features_df[col] = features_df[col].astype(np.float16)
        
        print(f"站点特征数据形状: {features_df.shape}")
        return features_df
    
    def extract_station_coordinates(self, in_data, out_data):
        """提取地铁站点坐标"""
        print("正在提取站点坐标...")
        coords = {}
        
        # 从进站数据提取坐标
        if 'longitude' in in_data.columns and 'latitude' in in_data.columns:
            for _, row in in_data.drop_duplicates('station').iterrows():
                coords[row['station']] = (row['longitude'], row['latitude'])
        
        # 从出站数据补充坐标
        if 'longitude' in out_data.columns and 'latitude' in out_data.columns:
            for _, row in out_data.drop_duplicates('station').iterrows():
                if row['station'] not in coords:
                    coords[row['station']] = (row['longitude'], row['latitude'])
        
        print(f"提取到 {len(coords)} 个站点坐标")
        return coords
    
    def create_time_features(self, hour):
        """创建时间特征"""
        # 创建周期性时间特征
        hour_sin = np.sin(2 * np.pi * hour / 24)
        hour_cos = np.cos(2 * np.pi * hour / 24)
        
        # 创建时段特征
        is_morning_peak = ((hour >= 7) & (hour <= 9)).astype(int)
        is_evening_peak = ((hour >= 17) & (hour <= 19)).astype(int)
        is_night = ((hour >= 22) | (hour <= 6)).astype(int)
        is_weekend = 0  # 这里可以根据实际日期来设置
        
        return np.column_stack([hour_sin, hour_cos, is_morning_peak, 
                               is_evening_peak, is_night, is_weekend])
    
    def process_flow_data(self, flow_data, flow_type='in'):
        """处理流量数据"""
        print(f"正在处理{flow_type}站流量数据...")
        
        # 创建时间特征
        time_features = self.create_time_features(flow_data['hour'].values)
        
        # 创建站点-时间矩阵
        stations = flow_data['station'].unique()
        hours = sorted(flow_data['hour'].unique())
        
        # 编码站点
        if not hasattr(self.station_encoder, 'classes_'):
            self.station_encoder.fit(stations)
        
        flow_matrix = np.zeros((len(hours), len(stations)))
        time_feature_matrix = np.zeros((len(hours), len(stations), time_features.shape[1]))
        
        for i, hour in enumerate(hours):
            hour_data = flow_data[flow_data['hour'] == hour]
            for _, row in hour_data.iterrows():
                station_idx = np.where(stations == row['station'])[0]
                if len(station_idx) > 0:
                    flow_matrix[i, station_idx[0]] = row['count']
                    time_feature_matrix[i, station_idx[0]] = time_features[
                        flow_data['hour'] == hour].iloc[0]
        
        return flow_matrix, time_feature_matrix, stations
    
    def balance_flow_data(self, in_flow, out_flow):
        """平衡进出站流量数据"""
        print("正在平衡进出站流量...")
        
        # 计算总流量
        total_in = np.sum(in_flow)
        total_out = np.sum(out_flow)
        
        # 计算平衡因子
        balance_factor = (total_in + total_out) / (2 * total_out)
        
        # 应用平衡因子
        balanced_out_flow = out_flow * balance_factor
        
        print(f"平衡前: 进站总量={total_in:.2f}, 出站总量={total_out:.2f}")
        print(f"平衡后: 进站总量={total_in:.2f}, 出站总量={np.sum(balanced_out_flow):.2f}")
        
        return in_flow, balanced_out_flow
    
    def create_grid_station_connections(self, grid_coords, station_coords, threshold=None):
        """创建栅格-站点连接，基于距离阈值"""
        if threshold is None:
            threshold = self.config.SPATIAL_THRESHOLD
        
        print(f"正在创建栅格-站点连接 (阈值: {threshold})...")
        connections = {}
        
        for grid_id, (grid_lon, grid_lat) in grid_coords.items():
            connections[grid_id] = []
            for station, (station_lon, station_lat) in station_coords.items():
                # 计算距离 (简化的欧几里得距离)
                distance = np.sqrt((grid_lon - station_lon)**2 + (grid_lat - station_lat)**2)
                if distance < threshold:
                    connections[grid_id].append((station, distance))
        
        # 移除没有连接的栅格
        connections = {k: v for k, v in connections.items() if v}
        
        print(f"创建了 {len(connections)} 个栅格-站点连接")
        return connections
    
    def load_all_data(self):
        """加载所有数据"""
        print("开始加载所有数据...")
        
        # 加载基础数据
        in_data = self.load_shapefile_data(self.config.IN_STATION_PATH, 'in')
        out_data = self.load_shapefile_data(self.config.OUT_STATION_PATH, 'out')
        od_data = self.load_od_data()
        station_features = self.load_station_features()
        
        # 提取坐标
        station_coords = self.extract_station_coordinates(in_data, out_data)
        
        # 处理流量数据
        in_flow, in_time_features, in_stations = self.process_flow_data(in_data, 'in')
        out_flow, out_time_features, out_stations = self.process_flow_data(out_data, 'out')
        
        # 获取共同站点
        common_stations = list(set(in_stations) & set(out_stations) & set(station_coords.keys()))
        print(f"共同站点数量: {len(common_stations)}")
        
        # 平衡流量数据
        in_flow_balanced, out_flow_balanced = self.balance_flow_data(in_flow, out_flow)
        
        # 准备返回数据
        data_dict = {
            'in_data': in_data,
            'out_data': out_data,
            'od_data': od_data,
            'station_features': station_features,
            'station_coords': station_coords,
            'in_flow': in_flow_balanced,
            'out_flow': out_flow_balanced,
            'in_time_features': in_time_features,
            'out_time_features': out_time_features,
            'common_stations': common_stations
        }
        
        print("数据加载完成!")
        return data_dict 