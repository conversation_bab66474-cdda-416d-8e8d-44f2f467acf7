# -*- coding: utf-8 -*-
"""
Created on Tue Apr 15 10:38:20 2025
Modified T-GCN Implementation for Traffic Prediction

@author: <PERSON><PERSON><PERSON><PERSON>
"""

import os

os.environ["DGLBACKEND"] = "pytorch"

import torch
import torch.nn as nn
import torch.optim as optim
import pandas as pd
import geopandas as gpd
import numpy as np
import dgl
from dgl.nn import GraphConv
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import mean_absolute_error, mean_squared_error
from tqdm import tqdm

# 设置随机种子和设备
torch.manual_seed(42)
np.random.seed(42)
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"Using device: {device}")


# 数据加载
def load_data(in_shp, out_shp, od_csv, features_csv):
    in_gdf = gpd.read_file(in_shp)
    out_gdf = gpd.read_file(out_shp)
    od_df = pd.read_csv(od_csv)
    features_df = pd.read_csv(features_csv)
    print("Data loaded successfully.")
    return in_gdf, out_gdf, od_df, features_df


# 数据预处理
def preprocess_data(in_gdf, out_gdf, od_df, features_df):
    # 去除0点到4点的数据
    in_gdf = in_gdf[~in_gdf['hour'].isin([0, 1, 2, 3, 4])].copy()
    out_gdf = out_gdf[~out_gdf['hour'].isin([0, 1, 2, 3, 4])].copy()
    od_df = od_df[~od_df['hour'].isin([0, 1, 2, 3, 4])].copy()
    features_df = features_df.copy()

    # 处理缺失值（删除）
    in_gdf = in_gdf.dropna(subset=['station', 'hour', 'count'])
    out_gdf = out_gdf.dropna(subset=['station', 'hour', 'count'])
    od_df = od_df.dropna(
        subset=['o_rawname', 'd_rawname', 'hour', 'trip', 'surface_distance', 'translate', 'time', 'wait_time'])
    features_df = features_df.dropna(subset=['站名'])

    # 合并关键字
    all_stations = pd.concat([
        in_gdf['station'], out_gdf['station'],
        od_df['o_rawname'], od_df['d_rawname'],
        features_df['站名']
    ]).dropna().unique()

    station_encoder = LabelEncoder()
    station_encoder.fit(all_stations)

    # 站点的label转化为数字编码
    in_gdf.loc[:, 'station_id'] = station_encoder.transform(in_gdf['station'])
    out_gdf.loc[:, 'station_id'] = station_encoder.transform(out_gdf['station'])
    od_df.loc[:, 'o_station_id'] = station_encoder.transform(od_df['o_rawname'])
    od_df.loc[:, 'd_station_id'] = station_encoder.transform(od_df['d_rawname'])

    # 标准化od特征
    scaler = StandardScaler()
    od_features = ['surface_distance', 'translate', 'time', 'wait_time']
    for col in od_features:
        if od_df[col].std() == 0:
            od_df.loc[:, col] = 0
        else:
            od_df.loc[:, col] = scaler.fit_transform(od_df[[col]]).flatten()

    # 对除了站名，做标准化处理
    feature_cols = [col for col in features_df.columns if col != '站名']
    for col in feature_cols:
        if features_df[col].std() == 0:
            features_df.loc[:, col] = 0
        else:
            features_df.loc[:, col] = scaler.fit_transform(features_df[[col]]).flatten()

    features_df.loc[:, 'station_id'] = features_df['站名'].apply(
        lambda x: station_encoder.transform([x])[0] if x in station_encoder.classes_ else -1
    )

    return in_gdf, out_gdf, od_df, features_df, station_encoder, feature_cols


# 创建时序数据
def create_sequences(data, station_encoder, seq_length=12):
    """
    为T-GCN创建时序数据
    seq_length: 时间序列长度（小时）
    """
    num_stations = len(station_encoder.classes_)

    # 为每个站点创建时间序列
    sequences = []
    targets = []

    # 按时间排序
    data_sorted = data.sort_values(['hour']).copy()

    # 获取所有时间点
    time_points = sorted(data_sorted['hour'].unique())

    # 为每个时间窗口创建序列
    for i in range(len(time_points) - seq_length):
        # 输入序列
        seq_data = np.zeros((seq_length, num_stations))
        # 目标值
        target_data = np.zeros(num_stations)

        # 填充序列数据
        for t in range(seq_length):
            hour_data = data_sorted[data_sorted['hour'] == time_points[i + t]]
            for _, row in hour_data.iterrows():
                station_id = int(row['station_id'])
                seq_data[t, station_id] = row['count']

        # 填充目标数据
        target_hour_data = data_sorted[data_sorted['hour'] == time_points[i + seq_length]]
        for _, row in target_hour_data.iterrows():
            station_id = int(row['station_id'])
            target_data[station_id] = row['count']

        sequences.append(seq_data)
        targets.append(target_data)

    return np.array(sequences), np.array(targets), time_points[seq_length:]


# 创建图结构
def create_graph(od_df, station_encoder):
    """创建站点之间的邻接矩阵"""
    num_stations = len(station_encoder.classes_)
    adj_matrix = np.zeros((num_stations, num_stations))

    # 基于OD数据构建邻接矩阵
    for _, row in od_df.iterrows():
        o_id = int(row['o_station_id'])
        d_id = int(row['d_station_id'])
        # 使用trip数量作为权重（归一化）
        weight = row['trip']
        adj_matrix[o_id, d_id] += weight
        adj_matrix[d_id, o_id] += weight  # 无向图

    # 归一化邻接矩阵
    row_sums = adj_matrix.sum(axis=1)
    row_sums[row_sums == 0] = 1  # 避免除零
    adj_matrix = adj_matrix / row_sums[:, np.newaxis]

    # 添加自环
    adj_matrix += np.eye(num_stations)

    return torch.FloatTensor(adj_matrix).to(device)


# T-GCN单元
class TGCNCell(nn.Module):
    def __init__(self, num_nodes, input_dim, hidden_dim):
        super(TGCNCell, self).__init__()
        self.num_nodes = num_nodes
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim

        # GCN权重矩阵
        self.weight_ih = nn.Parameter(torch.FloatTensor(input_dim, hidden_dim * 3))
        self.weight_hh = nn.Parameter(torch.FloatTensor(hidden_dim, hidden_dim * 3))
        self.bias_ih = nn.Parameter(torch.FloatTensor(hidden_dim * 3))
        self.bias_hh = nn.Parameter(torch.FloatTensor(hidden_dim * 3))

        self.reset_parameters()

    def reset_parameters(self):
        std = 1.0 / (self.hidden_dim) ** 0.5
        for weight in self.parameters():
            weight.data.uniform_(-std, std)

    def forward(self, inputs, hidden, adj_matrix):
        """
        inputs: [batch_size, num_nodes, input_dim]
        hidden: [batch_size, num_nodes, hidden_dim]
        adj_matrix: [num_nodes, num_nodes]
        """
        batch_size = inputs.size(0)

        # 图卷积操作
        # inputs: [batch_size, num_nodes, input_dim] -> [batch_size * num_nodes, input_dim]
        inputs_flat = inputs.view(-1, self.input_dim)
        hidden_flat = hidden.view(-1, self.hidden_dim)

        # 线性变换
        gi = torch.mm(inputs_flat, self.weight_ih) + self.bias_ih
        gh = torch.mm(hidden_flat, self.weight_hh) + self.bias_hh

        # 重塑为 [batch_size, num_nodes, hidden_dim * 3]
        gi = gi.view(batch_size, self.num_nodes, -1)
        gh = gh.view(batch_size, self.num_nodes, -1)

        # 图卷积
        gi = torch.matmul(adj_matrix, gi)
        gh = torch.matmul(adj_matrix, gh)

        # GRU门控机制
        i_r, i_z, i_n = gi.chunk(3, 2)
        h_r, h_z, h_n = gh.chunk(3, 2)

        resetgate = torch.sigmoid(i_r + h_r)
        inputgate = torch.sigmoid(i_z + h_z)
        newgate = torch.tanh(i_n + resetgate * h_n)
        hy = newgate + inputgate * (hidden - newgate)

        return hy


# T-GCN模型
class TGCN(nn.Module):
    def __init__(self, num_nodes, input_dim, hidden_dim, output_dim, num_layers=2):
        super(TGCN, self).__init__()
        self.num_nodes = num_nodes
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.output_dim = output_dim
        self.num_layers = num_layers

        # T-GCN层
        self.tgcn_cells = nn.ModuleList()
        for i in range(num_layers):
            if i == 0:
                self.tgcn_cells.append(TGCNCell(num_nodes, input_dim, hidden_dim))
            else:
                self.tgcn_cells.append(TGCNCell(num_nodes, hidden_dim, hidden_dim))

        # 输出层
        self.output_layer = nn.Linear(hidden_dim, output_dim)
        self.dropout = nn.Dropout(0.3)

    def forward(self, inputs, adj_matrix):
        """
        inputs: [batch_size, seq_length, num_nodes, input_dim]
        adj_matrix: [num_nodes, num_nodes]
        """
        batch_size, seq_length, num_nodes, input_dim = inputs.size()

        # 初始化隐藏状态
        hidden_states = []
        for i in range(self.num_layers):
            hidden_states.append(torch.zeros(batch_size, num_nodes, self.hidden_dim).to(inputs.device))

        # 时间步循环
        for t in range(seq_length):
            input_t = inputs[:, t, :, :]  # [batch_size, num_nodes, input_dim]

            # 通过每一层T-GCN
            for i in range(self.num_layers):
                if i == 0:
                    hidden_states[i] = self.tgcn_cells[i](input_t, hidden_states[i], adj_matrix)
                else:
                    hidden_states[i] = self.tgcn_cells[i](hidden_states[i - 1], hidden_states[i], adj_matrix)

        # 使用最后一层的最终隐藏状态进行预测
        output = self.dropout(hidden_states[-1])
        output = self.output_layer(output)

        return output.squeeze(-1)  # [batch_size, num_nodes]


# 数据集类
class TGCNDataset(torch.utils.data.Dataset):
    def __init__(self, sequences, targets):
        self.sequences = torch.FloatTensor(sequences)
        self.targets = torch.FloatTensor(targets)

    def __len__(self):
        return len(self.sequences)

    def __getitem__(self, idx):
        return self.sequences[idx], self.targets[idx]


# 训练函数
def train_tgcn(model, train_loader, val_loader, adj_matrix, epochs=50):
    optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-5)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=5, factor=0.5)
    criterion = nn.MSELoss()
    best_mae = float('inf')

    for epoch in range(epochs):
        model.train()
        total_loss = 0

        for sequences, targets in tqdm(train_loader, desc=f"Epoch {epoch + 1}"):
            sequences = sequences.to(device)
            targets = targets.to(device)

            # 添加特征维度
            sequences = sequences.unsqueeze(-1)  # [batch_size, seq_length, num_nodes, 1]

            optimizer.zero_grad()
            outputs = model(sequences, adj_matrix)
            loss = criterion(outputs, targets)
            loss.backward()
            optimizer.step()

            total_loss += loss.item()

        # 验证
        model.eval()
        val_mae = 0
        val_count = 0

        with torch.no_grad():
            for sequences, targets in val_loader:
                sequences = sequences.to(device).unsqueeze(-1)
                targets = targets.to(device)

                outputs = model(sequences, adj_matrix)
                val_mae += mean_absolute_error(targets.cpu().numpy().flatten(),
                                               outputs.cpu().numpy().flatten())
                val_count += 1

        val_mae /= val_count
        scheduler.step(val_mae)

        print(f"Epoch {epoch + 1}, Loss: {total_loss / len(train_loader):.4f}, Val MAE: {val_mae:.4f}")

        if val_mae < best_mae:
            best_mae = val_mae
            torch.save(model.state_dict(), 'best_tgcn_model.pth')

    return best_mae


# 评估函数
def evaluate_tgcn(model, test_loader, adj_matrix):
    model.eval()
    all_predictions = []
    all_targets = []

    with torch.no_grad():
        for sequences, targets in test_loader:
            sequences = sequences.to(device).unsqueeze(-1)
            targets = targets.to(device)

            outputs = model(sequences, adj_matrix)

            all_predictions.extend(outputs.cpu().numpy().flatten())
            all_targets.extend(targets.cpu().numpy().flatten())

    mae = mean_absolute_error(all_targets, all_predictions)
    rmse = np.sqrt(mean_squared_error(all_targets, all_predictions))

    return mae, rmse, all_predictions, all_targets


# 主执行函数
def main():
    # 数据路径
    in_shp = "C:\\Users\\<USER>\\Desktop\\接驳\\in_500.shp"
    out_shp = "C:\\Users\\<USER>\\Desktop\\接驳\\out_500.shp"
    od_csv = "C:\\Users\\<USER>\\Desktop\\接驳\\updated_北京市_subway_od_2024_modified3.csv"
    features_csv = "C:\\Users\\<USER>\\Desktop\\接驳\\station_features_result.csv"

    # 加载和预处理数据
    in_gdf, out_gdf, od_df, features_df = load_data(in_shp, out_shp, od_csv, features_csv)
    in_gdf, out_gdf, od_df, features_df, station_encoder, feature_cols = preprocess_data(
        in_gdf, out_gdf, od_df, features_df
    )

    # 合并进站和出站数据
    in_gdf['flow_type'] = 'in'
    out_gdf['flow_type'] = 'out'
    combined_flow = pd.concat([in_gdf[['station_id', 'hour', 'count']],
                               out_gdf[['station_id', 'hour', 'count']]])

    # 创建时序数据
    seq_length = 12  # 使用12小时的历史数据预测下一小时
    sequences, targets, time_points = create_sequences(combined_flow, station_encoder, seq_length)

    print(f"Created {len(sequences)} sequences with length {seq_length}")
    print(f"Sequence shape: {sequences.shape}, Target shape: {targets.shape}")

    # 创建图结构
    adj_matrix = create_graph(od_df, station_encoder)

    # 数据分割
    train_size = int(0.7 * len(sequences))
    val_size = int(0.15 * len(sequences))

    train_sequences = sequences[:train_size]
    train_targets = targets[:train_size]
    val_sequences = sequences[train_size:train_size + val_size]
    val_targets = targets[train_size:train_size + val_size]
    test_sequences = sequences[train_size + val_size:]
    test_targets = targets[train_size + val_size:]

    # 创建数据加载器
    train_dataset = TGCNDataset(train_sequences, train_targets)
    val_dataset = TGCNDataset(val_sequences, val_targets)
    test_dataset = TGCNDataset(test_sequences, test_targets)

    train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=32, shuffle=True)
    val_loader = torch.utils.data.DataLoader(val_dataset, batch_size=32, shuffle=False)
    test_loader = torch.utils.data.DataLoader(test_dataset, batch_size=32, shuffle=False)

    # 创建模型
    num_nodes = len(station_encoder.classes_)
    model = TGCN(
        num_nodes=num_nodes,
        input_dim=1,  # 流量数据
        hidden_dim=64,
        output_dim=1,
        num_layers=2
    ).to(device)

    print(f"Model created with {num_nodes} nodes")
    print(f"Total parameters: {sum(p.numel() for p in model.parameters())}")

    # 训练模型
    best_mae = train_tgcn(model, train_loader, val_loader, adj_matrix, epochs=50)

    # 加载最佳模型并测试
    model.load_state_dict(torch.load('best_tgcn_model.pth'))
    test_mae, test_rmse, predictions, targets = evaluate_tgcn(model, test_loader, adj_matrix)

    print(f"Test MAE: {test_mae:.4f}")
    print(f"Test RMSE: {test_rmse:.4f}")

    # 保存预测结果
    results_df = pd.DataFrame({
        'prediction': predictions,
        'target': targets
    })
    results_df.to_csv('tgcn_predictions.csv', index=False)

    return model, test_mae, test_rmse


if __name__ == "__main__":
    model, mae, rmse = main()