#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
地铁流量预测主程序
基于GCN + 时间特征的地铁流量预测系统
"""

import os
import sys
import argparse
import torch
import numpy as np
import pandas as pd
import geopandas as gpd
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 添加模型路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_processor import MetroDataProcessor
from models.gcn_temporal import create_model, MetroFlowGCN
from trainer import MetroFlowTrainer


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='地铁流量预测系统')
    
    # 数据参数
    parser.add_argument('--data_path', type=str, 
                       default='C:/Users/<USER>/Desktop/接驳/',
                       help='数据文件路径')
    parser.add_argument('--distance_threshold', type=float, default=1000,
                       help='栅格-站点连接距离阈值(米)')
    
    # 模型参数
    parser.add_argument('--spatial_hidden_dim', type=int, default=128,
                       help='空间隐藏层维度')
    parser.add_argument('--temporal_embed_dim', type=int, default=64,
                       help='时间嵌入维度')
    parser.add_argument('--num_gcn_layers', type=int, default=3,
                       help='GCN层数')
    parser.add_argument('--dropout', type=float, default=0.2,
                       help='Dropout概率')
    parser.add_argument('--use_float16', action='store_true', default=False,
                       help='使用float16精度')
    
    # 训练参数
    parser.add_argument('--epochs', type=int, default=20,
                       help='训练轮数')
    parser.add_argument('--patience', type=int, default=5,
                       help='早停耐心值')
    parser.add_argument('--batch_size', type=int, default=1,
                       help='批大小')
    parser.add_argument('--learning_rate', type=float, default=0.001,
                       help='学习率')
    
    # 输出参数
    parser.add_argument('--output_dir', type=str, default='./outputs',
                       help='输出目录')
    parser.add_argument('--save_predictions', action='store_true', default=True,
                       help='保存预测结果')
    
    return parser.parse_args()


class DataLoader:
    """简化的数据加载器"""
    def __init__(self, data_dict, batch_size=1):
        self.data = data_dict
        self.batch_size = batch_size
        
    def __iter__(self):
        yield self.data
        
    def __len__(self):
        return 1


def main():
    """主函数"""
    print("=" * 60)
    print("地铁流量预测系统")
    print("基于GCN + 时间特征")
    print("=" * 60)
    
    # 解析参数
    args = parse_args()
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    if torch.cuda.is_available():
        print(f"GPU显存: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    run_dir = os.path.join(args.output_dir, f"run_{timestamp}")
    os.makedirs(run_dir, exist_ok=True)
    
    print(f"输出目录: {run_dir}")
    
    try:
        # 1. 数据处理
        print("\n" + "="*50)
        print("步骤 1: 数据处理")
        print("="*50)
        
        processor = MetroDataProcessor(
            data_path=args.data_path,
            distance_threshold=args.distance_threshold,
            use_float16=args.use_float16
        )
        
        data_info = processor.process_all()
        
        print(f"数据处理完成:")
        print(f"  - 站点数量: {data_info['num_stations']}")
        print(f"  - 特征维度: {data_info['num_features']}")
        print(f"  - 时间步数: {data_info['num_hours']}")
        
        # 2. 创建模型
        print("\n" + "="*50)
        print("步骤 2: 创建模型")
        print("="*50)
        
        model_config = {
            'node_feat_dim': data_info['num_features'],
            'spatial_hidden_dim': args.spatial_hidden_dim,
            'temporal_embed_dim': args.temporal_embed_dim,
            'num_gcn_layers': args.num_gcn_layers,
            'num_prediction_tasks': 3,  # 进站、出站、OD
            'dropout': args.dropout,
            'use_float16': args.use_float16
        }
        
        model = create_model(model_config)
        print(f"模型创建完成:")
        print(f"  - 参数数量: {sum(p.numel() for p in model.parameters()):,}")
        print(f"  - 使用float16: {args.use_float16}")
        
        # 3. 准备数据加载器
        print("\n" + "="*50)
        print("步骤 3: 准备数据")
        print("="*50)
        
        train_data = processor.get_torch_data('train')
        val_data = processor.get_torch_data('val')
        test_data = processor.get_torch_data('test')
        
        train_loader = DataLoader(train_data, args.batch_size)
        val_loader = DataLoader(val_data, args.batch_size)
        test_loader = DataLoader(test_data, args.batch_size)
        
        print("数据加载器创建完成")
        
        # 4. 训练模型
        print("\n" + "="*50)
        print("步骤 4: 训练模型") 
        print("="*50)
        
        trainer = MetroFlowTrainer(model, device)
        
        checkpoint_dir = os.path.join(run_dir, 'checkpoints')
        trainer.train(
            train_loader=train_loader,
            val_loader=val_loader,
            epochs=args.epochs,
            patience=args.patience,
            save_dir=checkpoint_dir
        )
        
        print("模型训练完成")
        
        # 5. 评估模型
        print("\n" + "="*50)
        print("步骤 5: 评估模型")
        print("="*50)
        
        results_dir = os.path.join(run_dir, 'results')
        results = trainer.evaluate(
            test_loader=test_loader,
            save_results=True,
            save_dir=results_dir
        )
        
        # 6. 保存预测结果
        if args.save_predictions:
            print("\n" + "="*50)
            print("步骤 6: 保存预测结果")
            print("="*50)
            
            save_predictions_to_files(
                processor, trainer, test_data, 
                args.data_path, results_dir
            )
        
        # 7. 分析新线路影响（示例）
        print("\n" + "="*50)
        print("步骤 7: 新线路影响分析")
        print("="*50)
        
        analyze_new_line_impact(processor, trainer, results_dir)
        
        print("\n" + "="*60)
        print("所有步骤完成！")
        print(f"结果保存在: {run_dir}")
        print("="*60)
        
    except Exception as e:
        print(f"\n错误发生: {str(e)}")
        import traceback
        traceback.print_exc()


def save_predictions_to_files(processor, trainer, test_data, data_path, output_dir):
    """保存预测结果到文件"""
    print("保存预测结果到文件...")
    
    try:
        # 获取测试集预测结果
        trainer.model.eval()
        with torch.no_grad():
            node_features = test_data['node_features'].to(trainer.device)
            edge_index = test_data['edge_index'].to(trainer.device)
            time_features = test_data['time_features'].to(trainer.device)
            
            predictions_list = []
            targets_list = []
            
            for t in range(len(time_features)):
                current_time = time_features[t:t+1]
                predictions = trainer.model(node_features, edge_index, current_time)
                
                in_flows = test_data['in_flows'][t].numpy()
                out_flows = test_data['out_flows'][t].numpy()
                
                pred_np = predictions[:, :2].cpu().numpy()  # 只取进站、出站预测
                
                predictions_list.append(pred_np)
                targets_list.append(np.column_stack([in_flows, out_flows]))
        
        # 创建预测结果DataFrame
        all_predictions = np.concatenate(predictions_list, axis=0)
        all_targets = np.concatenate(targets_list, axis=0)
        
        # 获取测试集站点名称
        test_stations = [processor.stations[i] for i in processor.test_data['stations']]
        
        # 创建结果数据框
        results_df = pd.DataFrame({
            'station': test_stations * len(processor.test_data['hours']),
            'hour': [h for h in processor.test_data['hours'] for _ in test_stations],
            'true_in': all_targets[:, 0],
            'pred_in': all_predictions[:, 0],
            'true_out': all_targets[:, 1],
            'pred_out': all_predictions[:, 1],
            'mae_in': np.abs(all_targets[:, 0] - all_predictions[:, 0]),
            'mae_out': np.abs(all_targets[:, 1] - all_predictions[:, 1])
        })
        
        # 保存CSV文件
        csv_path = os.path.join(output_dir, 'predictions_comparison.csv')
        results_df.to_csv(csv_path, index=False, encoding='utf-8-sig')
        print(f"预测结果已保存至: {csv_path}")
        
        # 保存汇总统计，确保所有数值都是标准Python类型
        summary_stats = {
            'overall_mae_in': float(np.mean(results_df['mae_in'])),
            'overall_mae_out': float(np.mean(results_df['mae_out'])),
            'overall_mae': float(np.mean([results_df['mae_in'].mean(), results_df['mae_out'].mean()])),
            'station_count': int(len(test_stations)),
            'time_steps': int(len(processor.test_data['hours'])),
            'total_predictions': int(len(results_df))
        }
        
        import json
        with open(os.path.join(output_dir, 'prediction_summary.json'), 'w') as f:
            json.dump(summary_stats, f, indent=2)
        
        print("预测结果汇总:")
        for key, value in summary_stats.items():
            print(f"  {key}: {value}")
            
    except Exception as e:
        print(f"保存预测结果时出错: {str(e)}")


def analyze_new_line_impact(processor, trainer, output_dir):
    """分析新线路对流量的影响"""
    print("分析新线路影响...")
    
    try:
        # 这里是一个简化的示例分析
        # 实际应用中需要根据具体的新线路规划来设计
        
        impact_analysis = {
            'methodology': '基于图结构变化的流量影响分析',
            'assumptions': [
                '新线路会改变站点间的连接关系',
                '影响范围主要集中在新线路附近的站点',
                '流量会根据新的最短路径重新分配'
            ],
            'impact_factors': [
                '站点可达性改善',
                '换乘便利性提升', 
                '周边区域开发潜力'
            ],
            'analysis_steps': [
                '1. 识别受影响的站点',
                '2. 计算连通性变化',
                '3. 预测流量重分配',
                '4. 评估整体影响'
            ]
        }
        
        # 保存分析框架
        import json
        with open(os.path.join(output_dir, 'new_line_impact_framework.json'), 'w') as f:
            json.dump(impact_analysis, f, indent=2, ensure_ascii=False)
        
        print("新线路影响分析框架已保存")
        print("注意: 具体的影响分析需要新线路的详细规划数据")
        
    except Exception as e:
        print(f"新线路影响分析时出错: {str(e)}")


if __name__ == '__main__':
    main() 