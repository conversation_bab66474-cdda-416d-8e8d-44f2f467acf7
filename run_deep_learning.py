"""
运行深度学习地铁流量预测系统
"""
import os
import sys
import time
import argparse
import json
import warnings
warnings.filterwarnings('ignore')

# 添加gcn_metro_flow到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'gcn_metro_flow'))

from gcn_metro_flow.config import MetroFlowConfig
from gcn_metro_flow.deep_trainer import DeepMetroFlowTrainer

def check_dependencies():
    """检查依赖库"""
    required_packages = [
        ('torch', 'torch'), 
        ('pandas', 'pandas'), 
        ('numpy', 'numpy'), 
        ('geopandas', 'geopandas'),
        ('sklearn', 'scikit-learn'), 
        ('torch_geometric', 'torch_geometric')
    ]
    
    missing_packages = []
    
    for import_name, package_name in required_packages:
        try:
            __import__(import_name)
            print(f"✓ {package_name}")
        except ImportError:
            missing_packages.append(package_name)
            print(f"✗ {package_name} - 未安装")
    
    if missing_packages:
        print(f"\n请安装缺失的包: pip install {' '.join(missing_packages)}")
        return False
    
    return True

def create_config(args):
    """创建配置"""
    # 基础配置
    config = MetroFlowConfig(
        config_file=args.config if args.config else None,
        city=args.city
    )
    
    # 命令行参数覆盖
    if args.data_dir:
        config.set('data.base_dir', args.data_dir)
    
    if args.epochs:
        config.set('model.epochs', args.epochs)
    
    if args.batch_size:
        config.set('model.batch_size', args.batch_size)
    
    if args.learning_rate:
        config.set('model.learning_rate', args.learning_rate)
    
    if args.hidden_dim:
        config.set('model.gcn_hidden_dim', args.hidden_dim)
    
    if args.sequence_length:
        config.set('data_processing.sequence_length', args.sequence_length)
    
    if args.prediction_horizon:
        config.set('data_processing.prediction_horizon', args.prediction_horizon)
    
    # GPU设置
    if args.gpu is not None:
        config.set('device.cuda_device', args.gpu)
    
    if args.cpu:
        config.set('device.use_cuda', False)
    
    return config

def run_training(config, args):
    """运行训练"""
    print("="*80)
    print("深度学习地铁流量预测系统")
    print("="*80)
    
    # 检查CUDA
    import torch
    if torch.cuda.is_available() and config.get('device.use_cuda', True):
        device_id = config.get('device.cuda_device', 0)
        print(f"CUDA可用: {torch.cuda.get_device_name(device_id)}")
        print(f"GPU内存: {torch.cuda.get_device_properties(device_id).total_memory / 1e9:.1f} GB")
    else:
        print("使用CPU训练")
    
    start_time = time.time()
    
    try:
        # 初始化训练器
        trainer = DeepMetroFlowTrainer(config)
        
        # 打印模型摘要
        if not args.quiet:
            trainer.print_model_summary()
        
        # 数据准备
        if not args.skip_data_prep:
            trainer.prepare_data()
        
        # 初始化模型
        trainer.initialize_models()
        
        # 训练模型
        if not args.eval_only:
            print("\n开始训练...")
            train_history = trainer.train_models()
            
            # 保存训练历史
            output_files = config.get_output_files()
            with open(output_files['metrics'].replace('.json', '_train_history.json'), 'w') as f:
                json.dump(train_history, f, indent=2)
        
        # 评估模型
        print("\n开始评估...")
        metrics = trainer.evaluate_models()
        
        # 生成预测结果
        if not args.skip_prediction:
            trainer.generate_predictions()
        
        # 打印结果
        print_results(metrics, config)
        
        end_time = time.time()
        print(f"\n总运行时间: {end_time - start_time:.2f} 秒")
        
        return True
        
    except Exception as e:
        print(f"\n错误: {str(e)}")
        if args.debug:
            import traceback
            traceback.print_exc()
        return False

def print_results(metrics, config):
    """打印结果"""
    print("\n" + "="*60)
    print("深度学习预测结果")
    print("="*60)
    
    print(f"\n配置信息:")
    print(f"  城市: {config.city}")
    print(f"  序列长度: {config.get('data_processing.sequence_length', 12)}")
    print(f"  预测时长: {config.get('data_processing.prediction_horizon', 6)}")
    print(f"  隐藏维度: {config.get('model.gcn_hidden_dim', 128)}")
    print(f"  GCN层数: {config.get('model.gcn_layers', 3)}")
    
    print(f"\n预测性能:")
    
    # 进站流量
    in_metrics = metrics.get('in_flow', {})
    print(f"  进站流量:")
    print(f"    MAE:  {in_metrics.get('mae', 0):.4f}")
    print(f"    RMSE: {in_metrics.get('rmse', 0):.4f}")
    print(f"    R²:   {in_metrics.get('r2', 0):.4f}")
    print(f"    MAPE: {in_metrics.get('mape', 0):.2f}%")
    
    # 出站流量
    out_metrics = metrics.get('out_flow', {})
    print(f"  出站流量:")
    print(f"    MAE:  {out_metrics.get('mae', 0):.4f}")
    print(f"    RMSE: {out_metrics.get('rmse', 0):.4f}")
    print(f"    R²:   {out_metrics.get('r2', 0):.4f}")
    print(f"    MAPE: {out_metrics.get('mape', 0):.2f}%")
    
    # 整体性能
    overall_metrics = metrics.get('overall', {})
    print(f"  整体性能:")
    print(f"    MAE:  {overall_metrics.get('mae', 0):.4f}")
    print(f"    RMSE: {overall_metrics.get('rmse', 0):.4f}")
    print(f"    R²:   {overall_metrics.get('r2', 0):.4f}")
    print(f"    MAPE: {overall_metrics.get('mape', 0):.2f}%")
    
    # 输出文件
    output_files = config.get_output_files()
    print(f"\n输出文件:")
    for file_type, filepath in output_files.items():
        if os.path.exists(filepath):
            print(f"  ✓ {file_type}: {os.path.basename(filepath)}")
        else:
            print(f"  ✗ {file_type}: {os.path.basename(filepath)} (未生成)")
    
    print("="*60)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='深度学习地铁流量预测系统')
    
    # 基础参数
    parser.add_argument('--city', type=str, default='beijing', 
                       help='城市名称 (beijing, shanghai, guangzhou)')
    parser.add_argument('--config', type=str, help='配置文件路径')
    parser.add_argument('--data_dir', type=str, help='数据目录路径')
    
    # 模型参数
    parser.add_argument('--epochs', type=int, help='训练轮数')
    parser.add_argument('--batch_size', type=int, help='批次大小')
    parser.add_argument('--learning_rate', type=float, help='学习率')
    parser.add_argument('--hidden_dim', type=int, help='隐藏层维度')
    parser.add_argument('--sequence_length', type=int, help='输入序列长度')
    parser.add_argument('--prediction_horizon', type=int, help='预测时长')
    
    # 设备参数
    parser.add_argument('--gpu', type=int, help='GPU设备ID')
    parser.add_argument('--cpu', action='store_true', help='强制使用CPU')
    
    # 运行模式
    parser.add_argument('--eval_only', action='store_true', help='仅评估，不训练')
    parser.add_argument('--skip_data_prep', action='store_true', help='跳过数据准备')
    parser.add_argument('--skip_prediction', action='store_true', help='跳过预测生成')
    parser.add_argument('--quiet', action='store_true', help='静默模式')
    parser.add_argument('--debug', action='store_true', help='调试模式')
    
    # 快速测试
    parser.add_argument('--quick_test', action='store_true', help='快速测试模式')
    
    args = parser.parse_args()
    
    # 快速测试模式
    if args.quick_test:
        args.epochs = 5
        args.batch_size = 8
        args.sequence_length = 6
        args.prediction_horizon = 3
        args.hidden_dim = 64
        print("快速测试模式：使用较小的参数")
    
    # 检查依赖
    print("检查依赖库...")
    if not check_dependencies():
        return False
    
    # 创建配置
    config = create_config(args)
    
    # 保存配置
    if not args.quiet:
        config_path = f"{config.city}_config.json"
        config.save_config(config_path)
        print(f"配置已保存到: {config_path}")
    
    # 运行训练
    success = run_training(config, args)
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
