import os
import torch
import torch.nn as nn
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, LabelEncoder
import shap
import warnings
warnings.filterwarnings('ignore')
# import matplotlib
# matplotlib.use('Agg')  # 在导入 matplotlib.pyplot 之前设置
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm

# 动态选择可用字体
def find_available_font():
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    preferred_fonts = ['SimHei', 'Microsoft YaHei', 'Noto Sans CJK SC', 'PingFang SC', 'Arial Unicode MS', 'WenQuanYi Zen Hei']
    for font in preferred_fonts:
        if font in available_fonts:
            return font
    return None

font_name = find_available_font()
if font_name:
    plt.rcParams['font.sans-serif'] = [font_name]
    print(f"Using font: {font_name}")
else:
    print("Warning: No suitable Chinese font found. Please install a Chinese font (e.g., <PERSON><PERSON><PERSON><PERSON> or Noto Sans CJK).")
plt.rcParams['axes.unicode_minus'] = True
# # Set font to support Chinese characters
# plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'Arial Unicode MS', 'DejaVu Sans']
# plt.rcParams['axes.unicode_minus'] = True  # Handle minus sign rendering
# 强制使用CPU避免GPU内存不足
device = torch.device("cuda")
print(f"Using device: {device}")

# 设置随机种子
torch.manual_seed(42)
np.random.seed(42)

# 设置matplotlib中文字体
# plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
# plt.rcParams['axes.unicode_minus'] = True

# 轻量级模型定义（只包含必要部分）
class LightweightTrafficModel(nn.Module):
    def __init__(self, num_stations, geo_feature_dim, station_dim=32, hidden_dim=64):
        super().__init__()
        # 减小模型尺寸以节省内存
        self.station_embed = nn.Embedding(num_stations, station_dim)
        self.hour_embed = nn.Embedding(24, 16)
        
        self.geo_encoder = nn.Sequential(
            nn.Linear(geo_feature_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        self.time_encoder = nn.Sequential(
            nn.Linear(2, 16),
            nn.ReLU()
        )
        
        # 简化的OD预测网络
        od_input_dim = station_dim * 2 + hidden_dim * 2 + 4 + 16 + 16
        self.od_predictor = nn.Sequential(
            nn.Linear(od_input_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, 1)
        )
        
        # Move model to the specified device
        self.to(device)
    
    def forward_od_only(self, od_features, hour_features, o_geo_features, d_geo_features, 
                       o_station_ids, d_station_ids, hours):
        """只处理OD预测的前向传播"""
        # Ensure inputs are on the correct device
        od_features = od_features.to(device)
        hour_features = hour_features.to(device)
        o_geo_features = o_geo_features.to(device)
        d_geo_features = d_geo_features.to(device)
        o_station_ids = o_station_ids.to(device)
        d_station_ids = d_station_ids.to(device)
        hours = hours.to(device)
        
        # 获取嵌入
        o_station_emb = self.station_embed(o_station_ids)
        d_station_emb = self.station_embed(d_station_ids)
        hour_emb = self.hour_embed(hours)
        
        # 处理地理特征
        o_geo_emb = self.geo_encoder(o_geo_features)
        d_geo_emb = self.geo_encoder(d_geo_features)
        
        # 处理时间特征
        time_emb = self.time_encoder(hour_features)
        
        # 合并所有特征
        combined = torch.cat([
            o_station_emb, d_station_emb, o_geo_emb, d_geo_emb,
            od_features, hour_emb, time_emb
        ], dim=-1)
        
        # 预测
        prediction = self.od_predictor(combined)
        return torch.relu(prediction.squeeze(-1))

def load_and_prepare_data(sample_ratio=0.1):
    """加载并准备数据用于SHAP分析"""
    
    # 这里需要根据您的实际文件路径调整
    od_csv = "C:\\Users\\<USER>\\Desktop\\接驳\\updated_北京市_subway_od_2024_modified3.csv"
    features_csv = "C:\\Users\\<USER>\\Desktop\\接驳\\station_features_result.csv"
    
    # 加载数据
    print("Loading data...")
    od_df = pd.read_csv(od_csv)
    features_df = pd.read_csv(features_csv)
    
    # 过滤数据
    od_df = od_df[~od_df['hour'].isin([0, 1, 2, 3, 4])].copy()
    
    # 根据sample_ratio采样数据
    sample_size = int(len(od_df) * sample_ratio)
    od_sample = od_df.sample(n=min(sample_size, len(od_df)), random_state=42)
    
    print(f"Original data size: {len(od_df)}")
    print(f"Sample size ({sample_ratio*100}%): {len(od_sample)}")
    
    # 站点编码
    all_stations = pd.concat([
        od_sample['o_rawname'], od_sample['d_rawname'], features_df['站名']
    ]).dropna().unique()
    
    station_encoder = LabelEncoder()
    station_encoder.fit(all_stations)
    
    od_sample.loc[:, 'o_station_id'] = station_encoder.transform(od_sample['o_rawname'])
    od_sample.loc[:, 'd_station_id'] = station_encoder.transform(od_sample['d_rawname'])
    
    # 特征标准化 - 确保数据类型为数值
    scaler_od = StandardScaler()
    od_features = ['surface_distance', 'translate', 'time', 'wait_time']
    # 确保数值类型
    for col in od_features:
        od_sample[col] = pd.to_numeric(od_sample[col], errors='coerce')
    od_sample.loc[:, od_features] = scaler_od.fit_transform(od_sample[od_features])
    
    scaler_features = StandardScaler()
    feature_cols = [col for col in features_df.columns if col != '站名']
    # 确保所有特征都是数值类型
    for col in feature_cols:
        features_df[col] = pd.to_numeric(features_df[col], errors='coerce')
    features_df[feature_cols] = features_df[feature_cols].fillna(0)  # 填充NaN
    features_df.loc[:, feature_cols] = scaler_features.fit_transform(features_df[feature_cols])
    features_df.loc[:, 'station_id'] = features_df['站名'].apply(
        lambda x: station_encoder.transform([x])[0] if x in station_encoder.classes_ else -1
    )
    
    # 时间编码
    def encode_hour(hour):
        hour = hour % 24
        sin = np.sin(2 * np.pi * hour / 24)
        cos = np.cos(2 * np.pi * hour / 24)
        return [sin, cos]
    
    # 确保hour列是数值类型
    od_sample['hour'] = pd.to_numeric(od_sample['hour'], errors='coerce')
    
    hour_encoded = od_sample['hour'].apply(encode_hour).tolist()
    od_sample.loc[:, 'hour_sin'] = [x[0] for x in hour_encoded]
    od_sample.loc[:, 'hour_cos'] = [x[1] for x in hour_encoded]
    
    return od_sample, features_df, station_encoder, feature_cols, scaler_od, scaler_features

def create_lightweight_model(num_stations, geo_feature_dim):
    """创建轻量级模型并加载预训练权重（如果存在）"""
    model = LightweightTrafficModel(num_stations, geo_feature_dim).to(device)
    
    # 尝试加载预训练权重
    if os.path.exists('best_model.pth'):
        try:
            full_state_dict = torch.load('best_model.pth', map_location=device)
            model_state = model.state_dict()
            filtered_dict = {}
            
            for name, param in full_state_dict.items():
                if name in model_state and param.shape == model_state[name].shape:
                    filtered_dict[name] = param
                    print(f"Loaded: {name}")
            
            model_state.update(filtered_dict)
            model.load_state_dict(model_state)
            print(f"Successfully loaded {len(filtered_dict)} layers from pretrained model")
            
        except Exception as e:
            print(f"Failed to load pretrained weights: {e}")
            print("Using randomly initialized model")
    else:
        print("No pretrained model found, using randomly initialized model")
    
    model.eval()
    return model

def prepare_shap_data(od_sample, features_df, feature_cols, max_samples=1000):
    """准备SHAP分析所需的数据"""
    
    # 限制样本数量以节省内存
    if len(od_sample) > max_samples:
        od_sample = od_sample.sample(n=max_samples, random_state=42)
    
    print(f"Preparing SHAP data for {len(od_sample)} samples...")
    
    # 准备特征矩阵
    feature_matrix = []
    sample_info = []  # 保存原始信息用于预测
    
    for idx, row in od_sample.iterrows():
        # OD特征
        od_feats = row[['surface_distance', 'translate', 'time', 'wait_time']].values.astype(np.float64)
        
        # 时间特征
        hour_feats = row[['hour_sin', 'hour_cos']].values.astype(np.float64)
        
        # 地理特征
        o_station_id = int(row['o_station_id'])
        d_station_id = int(row['d_station_id'])
        
        o_geo = features_df[features_df['station_id'] == o_station_id][feature_cols].values
        d_geo = features_df[features_df['station_id'] == d_station_id][feature_cols].values
        
        o_geo = o_geo[0].astype(np.float64) if len(o_geo) > 0 else np.zeros(len(feature_cols), dtype=np.float64)
        d_geo = d_geo[0].astype(np.float64) if len(d_geo) > 0 else np.zeros(len(feature_cols), dtype=np.float64)
        
        # 合并所有特征
        features = np.concatenate([od_feats, hour_feats, o_geo, d_geo]).astype(np.float64)
        feature_matrix.append(features)
        
        # 保存样本信息
        sample_info.append({
            'o_station_id': o_station_id,
            'd_station_id': d_station_id,
            'hour': int(row['hour']),
            'actual_flow': row.get('passenger_flow', 0)
        })
    
    feature_matrix = np.array(feature_matrix, dtype=np.float64)
    
    # 构建特征名称
    feature_names = (['surface_distance', 'translate', 'time', 'wait_time'] + 
                    ['hour_sin', 'hour_cos'] + 
                    [f'o_{col}' for col in feature_cols] +
                    [f'd_{col}' for col in feature_cols])
    
    # 检查数据质量
    if np.any(np.isnan(feature_matrix)) or np.any(np.isinf(feature_matrix)):
        print("Warning: Found NaN or inf values in feature matrix, replacing with 0")
        feature_matrix = np.nan_to_num(feature_matrix, nan=0.0, posinf=0.0, neginf=0.0)
    
    print(f"Feature matrix shape: {feature_matrix.shape}")
    print(f"Number of features: {len(feature_names)}")
    
    return feature_matrix, feature_names, sample_info

def create_model_wrapper(model, sample_info, feature_cols):
    """创建模型包装器函数"""
    def model_wrapper(X):
        if not isinstance(X, np.ndarray):
            X = np.array(X, dtype=np.float64)
        
        if X.ndim == 1:
            X = X.reshape(1, -1)
        
        predictions = []
        
        for i, features in enumerate(X):
            features = np.array(features, dtype=np.float64)
            sample_idx = i % len(sample_info)
            sample = sample_info[sample_idx]
            
            try:
                # 分解特征
                od_features = torch.tensor(features[:4], dtype=torch.float32).unsqueeze(0).to(device)
                hour_features = torch.tensor(features[4:6], dtype=torch.float32).unsqueeze(0).to(device)
                o_geo_features = torch.tensor(features[6:6+len(feature_cols)], dtype=torch.float32).unsqueeze(0).to(device)
                d_geo_features = torch.tensor(features[6+len(feature_cols):], dtype=torch.float32).unsqueeze(0).to(device)
                
                o_station_ids = torch.tensor([sample['o_station_id']], dtype=torch.long).to(device)
                d_station_ids = torch.tensor([sample['d_station_id']], dtype=torch.long).to(device)
                hours = torch.tensor([sample['hour']], dtype=torch.long).to(device)
                
                # 模型预测
                with torch.no_grad():
                    pred = model.forward_od_only(
                        od_features, hour_features, o_geo_features, d_geo_features,
                        o_station_ids, d_station_ids, hours
                    )
                    predictions.append(float(pred.cpu().item()))  # Move result back to CPU for SHAP
            
            except Exception as e:
                print(f"Error in prediction for sample {i}: {e}")
                predictions.append(0.0)
        
        return np.array(predictions, dtype=np.float64)
    
    return model_wrapper

def comprehensive_shap_analysis(model, feature_matrix, feature_names, sample_info, feature_cols):
    """进行全面的SHAP分析"""
    
    print("Starting comprehensive SHAP analysis...")
    
    # 创建模型包装器
    model_wrapper = create_model_wrapper(model, sample_info, feature_cols)
    
    # 测试模型包装器
    print("Testing model wrapper...")
    test_pred = model_wrapper(feature_matrix[:5])
    print(f"Test predictions: {test_pred}")
    
    # 准备SHAP数据
    print(len(feature_matrix))
    background_size = min(10, len(feature_matrix) // 4)
    explainer_size = min(20, len(feature_matrix))
    
    background_data = feature_matrix[:background_size]
    explain_data = feature_matrix[:explainer_size]
    
    print(f"Background samples: {background_size}")
    print(f"Explanation samples: {explainer_size}")
    
    # 创建SHAP解释器
    explainer = shap.KernelExplainer(model_wrapper, background_data, link="identity")
    
    # 计算SHAP值
    print("Computing SHAP values...")
    shap_values = explainer.shap_values(explain_data, nsamples=100)
    
    # 获取基础值和预测值
    expected_value = explainer.expected_value
    predictions = model_wrapper(explain_data)
    
    print("SHAP analysis completed!")
    print(f"SHAP values shape: {shap_values.shape}")
    print(f"Expected value: {expected_value}")
    
    return shap_values, expected_value, predictions, explain_data

def create_publication_plots(shap_values, expected_value, predictions, feature_matrix, 
                           feature_names, sample_info):
    """创建论文级别的SHAP可视化图表"""
    
    print("Creating publication-quality SHAP plots...")
    
    # Ensure feature_matrix has the same number of rows as shap_values
    feature_matrix = feature_matrix[:len(shap_values)]
    
    # 设置图表样式
    plt.style.use('seaborn-v0_8')
    
    # 1. Summary Plot (最重要的图)
    plt.figure(figsize=(12, 8))
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Noto Sans CJK SC', 'PingFang SC']
    plt.rcParams['axes.unicode_minus'] = True
    shap.summary_plot(shap_values, feature_matrix, feature_names=feature_names, 
                     plot_type="dot", show=False)
    plt.title('SHAP Summary Plot - Feature Importance and Impact', fontsize=16, pad=20)
    plt.tight_layout()
    plt.savefig('shap_summary_plot.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. Bar Plot - 特征重要性排序
    plt.figure(figsize=(10, 8))
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Noto Sans CJK SC', 'PingFang SC']
    plt.rcParams['axes.unicode_minus'] = True
    shap.summary_plot(shap_values, feature_matrix, feature_names=feature_names, 
                     plot_type="bar", show=False)
    plt.title('Feature Importance Ranking', fontsize=16, pad=20)
    plt.tight_layout()
    plt.savefig('shap_bar_plot.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 3. Waterfall Plot - 单个预测的解释
    plt.figure(figsize=(10, 8))
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Noto Sans CJK SC', 'PingFang SC']
    plt.rcParams['axes.unicode_minus'] = True
    shap.waterfall_plot(shap.Explanation(values=shap_values[0], 
                                       base_values=expected_value,
                                       data=feature_matrix[0],
                                       feature_names=feature_names), show=False)
    plt.title('SHAP Waterfall Plot - Individual Prediction Explanation', fontsize=16, pad=20)
    plt.tight_layout()
    plt.savefig('shap_waterfall_plot.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 4. Force Plot - 转换为静态图
    try:
        # 创建force plot但保存为静态图
        plt.figure(figsize=(14, 6))
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Noto Sans CJK SC', 'PingFang SC']
        plt.rcParams['axes.unicode_minus'] = True
        
        # 手动创建force plot的静态版本
        sample_idx = 0
        base_value = expected_value
        sample_shap_values = shap_values[sample_idx]
        sample_features = feature_matrix[sample_idx]
        
        # 计算累积效应
        prediction = base_value + np.sum(sample_shap_values)
        
        # 排序特征按绝对SHAP值
        sorted_indices = np.argsort(np.abs(sample_shap_values))[::-1][:15]  # 只显示前15个特征
        
        # 创建水平条形图
        y_pos = np.arange(len(sorted_indices))
        colors = ['red' if x < 0 else 'blue' for x in sample_shap_values[sorted_indices]]
        
        plt.barh(y_pos, sample_shap_values[sorted_indices], color=colors, alpha=0.7)
        plt.yticks(y_pos, [feature_names[i] for i in sorted_indices])
        plt.xlabel('SHAP Value')
        plt.title(f'Feature Contributions to Prediction\nBase Value: {base_value:.3f}, Prediction: {prediction:.3f}')
        plt.axvline(x=0, color='black', linestyle='-', alpha=0.3)
        plt.tight_layout()
        plt.savefig('shap_force_plot_static.png', dpi=300, bbox_inches='tight')
        plt.close()
        
    except Exception as e:
        print(f"Failed to create force plot: {e}")
    
    # 5. Partial Dependence Plots for top features
    feature_importance = np.abs(shap_values).mean(0)
    top_feature_indices = np.argsort(feature_importance)[-6:]  # 前6个重要特征
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    axes = axes.ravel()
    
    for i, feature_idx in enumerate(top_feature_indices):
        feature_name = feature_names[feature_idx]
        feature_values = feature_matrix[:, feature_idx]
        shap_values_feature = shap_values[:, feature_idx]
        
        # 创建散点图显示特征值与SHAP值的关系
        axes[i].scatter(feature_values, shap_values_feature, alpha=0.6, s=20)
        axes[i].set_xlabel(f'Feature Value: {feature_name}')
        axes[i].set_ylabel('SHAP Value')
        axes[i].set_title(f'Partial Dependence: {feature_name}')
        axes[i].grid(True, alpha=0.3)
        
        # 添加趋势线
        from scipy import stats
        slope, intercept, r_value, p_value, std_err = stats.linregress(feature_values, shap_values_feature)
        line_x = np.linspace(feature_values.min(), feature_values.max(), 100)
        line_y = slope * line_x + intercept
        axes[i].plot(line_x, line_y, 'r-', alpha=0.8, linewidth=2)
        axes[i].text(0.05, 0.95, f'R²={r_value**2:.3f}', transform=axes[i].transAxes, 
                    bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    plt.suptitle('Partial Dependence Plots for Top 6 Features', fontsize=16)
    plt.tight_layout()
    plt.savefig('shap_partial_dependence.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 6. 特征交互热图
    plt.figure(figsize=(12, 10))
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Noto Sans CJK SC', 'PingFang SC']
    plt.rcParams['axes.unicode_minus'] = True
    
    # 选择前20个最重要的特征进行交互分析
    top_n = min(20, len(feature_names))
    top_indices = np.argsort(feature_importance)[-top_n:]
    
    # 计算特征之间的相关性
    selected_shap = shap_values[:, top_indices]
    correlation_matrix = np.corrcoef(selected_shap.T)
    
    im = plt.imshow(correlation_matrix, cmap='RdBu_r', vmin=-1, vmax=1)
    plt.colorbar(im, label='SHAP Value Correlation')
    
    # 添加标签
    selected_names = [feature_names[i] for i in top_indices]
    plt.xticks(range(len(selected_names)), selected_names, rotation=45, ha='right')
    plt.yticks(range(len(selected_names)), selected_names)
    plt.title('SHAP Value Correlation Matrix (Top 20 Features)', fontsize=16, pad=20)
    plt.tight_layout()
    plt.savefig('shap_correlation_matrix.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 7. 保存详细的数值结果
    feature_importance_df = pd.DataFrame({
        'feature': feature_names,
        'mean_abs_shap': feature_importance,
        'mean_shap': np.mean(shap_values, axis=0),
        'std_shap': np.std(shap_values, axis=0)
    }).sort_values('mean_abs_shap', ascending=False)
    
    feature_importance_df.to_csv('detailed_shap_analysis.csv', index=False)
    
    # 保存SHAP值矩阵
    shap_df = pd.DataFrame(shap_values, columns=feature_names)
    shap_df['prediction'] = predictions
    shap_df['expected_value'] = expected_value
    shap_df.to_csv('complete_shap_values.csv', index=False)
    
    print("\nPublication-quality plots created:")
    print("1. shap_summary_plot.png - Main summary plot")
    print("2. shap_bar_plot.png - Feature importance bar chart")
    print("3. shap_waterfall_plot.png - Individual prediction explanation")
    print("4. shap_force_plot_static.png - Feature contributions")
    print("5. shap_partial_dependence.png - Partial dependence plots")
    print("6. shap_correlation_matrix.png - Feature correlation matrix")
    print("7. detailed_shap_analysis.csv - Numerical results")
    print("8. complete_shap_values.csv - Complete SHAP values matrix")
    
    return feature_importance_df

def analyze_feature_categories(feature_importance_df, feature_names):
    """分析不同类别特征的重要性"""
    
    print("\n=== Feature Category Analysis ===")
    
    # 定义特征类别
    categories = {
        'OD Features': ['surface_distance', 'translate', 'time', 'wait_time'],
        'Time Features': ['hour_sin', 'hour_cos'],
        'Origin Geographic Features': [f for f in feature_names if f.startswith('o_')],
        'Destination Geographic Features': [f for f in feature_names if f.startswith('d_')]
    }
    
    category_analysis = {}
    
    for category, features in categories.items():
        category_features = feature_importance_df[
            feature_importance_df['feature'].isin(features)
        ]
        
        if not category_features.empty:
            total_importance = category_features['mean_abs_shap'].sum()
            avg_importance = category_features['mean_abs_shap'].mean()
            top_feature = category_features.iloc[0]
            
            category_analysis[category] = {
                'total_importance': total_importance,
                'average_importance': avg_importance,
                'num_features': len(category_features),
                'top_feature': top_feature['feature'],
                'top_feature_importance': top_feature['mean_abs_shap']
            }
            
            print(f"\n{category}:")
            print(f"  Total Importance: {total_importance:.4f}")
            print(f"  Average Importance: {avg_importance:.4f}")
            print(f"  Number of Features: {len(category_features)}")
            print(f"  Most Important: {top_feature['feature']} ({top_feature['mean_abs_shap']:.4f})")
    
    # 创建类别重要性图表
    plt.figure(figsize=(12, 6))
    categories_names = list(category_analysis.keys())
    total_importances = [category_analysis[cat]['total_importance'] for cat in categories_names]
    
    plt.bar(categories_names, total_importances, color=['skyblue', 'lightgreen', 'lightcoral', 'plum'])
    plt.xlabel('Feature Category')
    plt.ylabel('Total SHAP Importance')
    plt.title('Feature Importance by Category')
    plt.xticks(rotation=45, ha='right')
    plt.tight_layout()
    plt.savefig('shap_category_importance.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 保存类别分析结果
    category_df = pd.DataFrame(category_analysis).T
    category_df.to_csv('feature_category_analysis.csv')
    
    return category_analysis

def main():
    """主执行函数"""
    print("=== 开始全面SHAP分析 ===")
    
    # 设置数据采样比例（可调整）
    SAMPLE_RATIO = 0.01  # 使用5%的数据，可以根据需要调整
    
    # 加载数据
    print("Loading and preparing data...")
    od_sample, features_df, station_encoder, feature_cols, scaler_od, scaler_features = load_and_prepare_data(
        sample_ratio=SAMPLE_RATIO
    )
    
    print(f"Sample size: {len(od_sample)}")
    print(f"Number of stations: {len(station_encoder.classes_)}")
    print(f"Number of geo features: {len(feature_cols)}")
    
    # 创建模型
    print("Creating lightweight model...")
    model = create_lightweight_model(len(station_encoder.classes_), len(feature_cols))
    
    # 准备SHAP数据
    feature_matrix, feature_names, sample_info = prepare_shap_data(
        od_sample, features_df, feature_cols, max_samples=5000
    )
    
    # 执行SHAP分析
    shap_values, expected_value, predictions, explain_data = comprehensive_shap_analysis(
        model, feature_matrix, feature_names, sample_info, feature_cols
    )
    
    # 创建论文级别的图表
    feature_importance_df = create_publication_plots(
        shap_values, expected_value, predictions, feature_matrix, feature_names, sample_info
    )
    
    # 分析特征类别
    category_analysis = analyze_feature_categories(feature_importance_df, feature_names)
    
    print("\n=== 分析完成 ===")
    print(f"共处理了 {len(explain_data)} 个样本")
    print(f"生成了 {len(feature_names)} 个特征的SHAP值")
    print("所有图表和分析结果已保存到当前目录")

if __name__ == "__main__":
    main()