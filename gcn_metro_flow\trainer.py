"""
训练框架 - 分层验证策略
"""
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, mean_squared_error
import time
import os
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

from config import MODEL_CONFIG, DEVICE_CONFIG, LOG_CONFIG
from models import MetroFlowGCN

class MetroFlowTrainer:
    """地铁流量预测训练器"""
    
    def __init__(self, data_loader, graph_builder):
        self.data_loader = data_loader
        self.graph_builder = graph_builder
        self.device = self._setup_device()
        
        # 初始化模型
        grid_feature_dim = self.data_loader.grid_features['features'].shape[1] if self.data_loader.grid_features else 51
        self.model = MetroFlowGCN(grid_feature_dim).to(self.device)
        
        # 优化器
        self.optimizer = optim.Adam(
            self.model.parameters(),
            lr=MODEL_CONFIG['learning_rate'],
            weight_decay=MODEL_CONFIG['weight_decay']
        )
        
        # 学习率调度器
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='min', patience=10, factor=0.5
        )
        
        # 训练历史
        self.train_history = defaultdict(list)
        self.best_val_loss = float('inf')
        self.patience_counter = 0
        
    def _setup_device(self):
        """设置设备"""
        if DEVICE_CONFIG['use_cuda'] and torch.cuda.is_available():
            device = torch.device(f"cuda:{DEVICE_CONFIG['cuda_device']}")
            print(f"Using GPU: {device}")
        else:
            device = torch.device('cpu')
            print("Using CPU")
        return device
    
    def train_with_hierarchical_validation(self):
        """分层验证策略训练"""
        print("Starting hierarchical validation training...")
        
        # 1. 时间分割 + 空间分割
        train_stations, val_stations = self._spatial_split()
        
        # 2. 留一法验证（对验证集中的每个站点）
        all_results = []
        
        for i, val_station in enumerate(val_stations):
            print(f"\n=== Leave-One-Out Validation {i+1}/{len(val_stations)}: {val_station} ===")
            
            # 当前验证站点
            current_val_stations = [val_station]
            current_train_stations = train_stations + [s for s in val_stations if s != val_station]
            
            # 训练模型
            model_results = self._train_single_fold(current_train_stations, current_val_stations)
            all_results.append(model_results)
            
            # 内存清理
            torch.cuda.empty_cache() if torch.cuda.is_available() else None
        
        # 汇总结果
        final_results = self._aggregate_results(all_results)
        return final_results
    
    def _spatial_split(self):
        """空间分割：80%站点训练，20%站点验证"""
        stations = self.data_loader.stations
        train_stations, val_stations = train_test_split(
            stations,
            test_size=MODEL_CONFIG['val_ratio'],
            random_state=42
        )
        
        print(f"Spatial split: {len(train_stations)} train stations, {len(val_stations)} val stations")
        return train_stations, val_stations
    
    def _train_single_fold(self, train_stations, val_stations):
        """训练单个折叠"""
        # 重新初始化模型
        grid_feature_dim = self.data_loader.grid_features['features'].shape[1] if self.data_loader.grid_features else 51
        self.model = MetroFlowGCN(grid_feature_dim).to(self.device)
        self.optimizer = optim.Adam(
            self.model.parameters(),
            lr=MODEL_CONFIG['learning_rate'],
            weight_decay=MODEL_CONFIG['weight_decay']
        )
        
        # 准备数据
        train_data, val_data = self._prepare_fold_data(train_stations, val_stations)
        
        # 训练循环
        best_val_loss = float('inf')
        patience_counter = 0
        
        for epoch in range(MODEL_CONFIG['epochs']):
            # 训练
            train_loss, train_metrics = self._train_epoch(train_data)
            
            # 验证
            val_loss, val_metrics = self._validate_epoch(val_data)
            
            # 学习率调度
            self.scheduler.step(val_loss)
            
            # 早停检查
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                # 保存最佳模型
                torch.save(self.model.state_dict(), 'best_model_fold.pth')
            else:
                patience_counter += 1
            
            # 打印进度
            if epoch % LOG_CONFIG['print_every'] == 0:
                print(f"Epoch {epoch}: Train Loss={train_loss:.4f}, Val Loss={val_loss:.4f}")
                print(f"Train MAE: In={train_metrics['in_mae']:.4f}, Out={train_metrics['out_mae']:.4f}")
                print(f"Val MAE: In={val_metrics['in_mae']:.4f}, Out={val_metrics['out_mae']:.4f}")
            
            # 早停
            if patience_counter >= MODEL_CONFIG['early_stopping_patience']:
                print(f"Early stopping at epoch {epoch}")
                break
        
        # 加载最佳模型进行最终评估
        self.model.load_state_dict(torch.load('best_model_fold.pth'))
        final_results = self._final_evaluation(val_data, val_stations)
        
        return final_results
    
    def _prepare_fold_data(self, train_stations, val_stations):
        """准备折叠数据"""
        train_data = []
        val_data = []
        
        # 时间分割：18点前训练，18点后测试
        train_hours = list(range(0, MODEL_CONFIG['time_split_hour']))
        val_hours = list(range(MODEL_CONFIG['time_split_hour'], 24))
        
        # 准备训练数据
        for hour in train_hours:
            graph_data = self.graph_builder.graphs_by_hour[hour]
            od_data = self.graph_builder.get_od_data_for_hour(hour)
            
            # 过滤站点
            filtered_graph, filtered_od = self._filter_data_by_stations(
                graph_data, od_data, train_stations
            )
            
            if filtered_graph is not None:
                train_data.append({
                    'graph': filtered_graph,
                    'od': filtered_od,
                    'hour': hour
                })
        
        # 准备验证数据
        for hour in val_hours:
            graph_data = self.graph_builder.graphs_by_hour[hour]
            od_data = self.graph_builder.get_od_data_for_hour(hour)
            
            # 过滤站点
            filtered_graph, filtered_od = self._filter_data_by_stations(
                graph_data, od_data, val_stations
            )
            
            if filtered_graph is not None:
                val_data.append({
                    'graph': filtered_graph,
                    'od': filtered_od,
                    'hour': hour
                })
        
        return train_data, val_data
    
    def _filter_data_by_stations(self, graph_data, od_data, stations):
        """根据站点过滤数据"""
        # 获取站点索引
        station_indices = [self.graph_builder.station_to_idx[s] for s in stations 
                          if s in self.graph_builder.station_to_idx]
        
        if len(station_indices) == 0:
            return None, None
        
        # 过滤图数据
        filtered_graph = graph_data.clone()
        
        # 创建站点掩码
        node_mask = torch.zeros(graph_data.num_nodes, dtype=torch.bool)
        node_mask[station_indices] = True
        
        # 过滤节点特征和标签
        filtered_graph.x = graph_data.x[node_mask]
        filtered_graph.y = graph_data.y[node_mask]
        
        # 过滤边（保留两端都在选定站点中的边）
        edge_mask = node_mask[graph_data.edge_index[0]] & node_mask[graph_data.edge_index[1]]
        filtered_graph.edge_index = graph_data.edge_index[:, edge_mask]
        if graph_data.edge_attr is not None:
            filtered_graph.edge_attr = graph_data.edge_attr[edge_mask]
        
        # 重新映射节点索引
        old_to_new = {old_idx: new_idx for new_idx, old_idx in enumerate(station_indices)}
        for i in range(filtered_graph.edge_index.size(1)):
            filtered_graph.edge_index[0, i] = old_to_new[filtered_graph.edge_index[0, i].item()]
            filtered_graph.edge_index[1, i] = old_to_new[filtered_graph.edge_index[1, i].item()]
        
        filtered_graph.num_nodes = len(station_indices)
        
        # 过滤OD数据
        filtered_od = None
        if od_data is not None:
            od_mask = torch.isin(od_data['od_pairs'][:, 0], torch.tensor(station_indices)) & \
                     torch.isin(od_data['od_pairs'][:, 1], torch.tensor(station_indices))
            
            if od_mask.sum() > 0:
                filtered_od = {
                    'od_pairs': od_data['od_pairs'][od_mask],
                    'od_flows': od_data['od_flows'][od_mask],
                    'od_features': od_data['od_features'][od_mask]
                }
                
                # 重新映射OD对索引
                for i in range(filtered_od['od_pairs'].size(0)):
                    filtered_od['od_pairs'][i, 0] = old_to_new[filtered_od['od_pairs'][i, 0].item()]
                    filtered_od['od_pairs'][i, 1] = old_to_new[filtered_od['od_pairs'][i, 1].item()]
        
        return filtered_graph, filtered_od
    
    def _train_epoch(self, train_data):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        all_predictions = {'in_flow': [], 'out_flow': []}
        all_targets = {'in_flow': [], 'out_flow': []}
        
        for batch_data in train_data:
            graph = batch_data['graph'].to(self.device)
            od_data = batch_data['od']
            
            if od_data is not None:
                od_data = {k: v.to(self.device) for k, v in od_data.items()}
            
            # 前向传播
            predictions = self.model(graph, od_data)
            
            # 计算损失
            targets = {
                'in_flow': graph.y[:, 0],
                'out_flow': graph.y[:, 1]
            }
            
            od_targets = od_data['od_flows'] if od_data is not None else None
            losses = self.model.compute_loss(predictions, targets, od_targets)
            
            # 反向传播
            self.optimizer.zero_grad()
            losses['total_loss'].backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            
            total_loss += losses['total_loss'].item()
            
            # 收集预测和目标
            all_predictions['in_flow'].extend(predictions['in_flow'].detach().cpu().numpy())
            all_predictions['out_flow'].extend(predictions['out_flow'].detach().cpu().numpy())
            all_targets['in_flow'].extend(targets['in_flow'].detach().cpu().numpy())
            all_targets['out_flow'].extend(targets['out_flow'].detach().cpu().numpy())
        
        # 计算指标
        metrics = self._compute_metrics(all_predictions, all_targets)
        avg_loss = total_loss / len(train_data)
        
        return avg_loss, metrics
    
    def _validate_epoch(self, val_data):
        """验证一个epoch"""
        self.model.eval()
        total_loss = 0
        all_predictions = {'in_flow': [], 'out_flow': []}
        all_targets = {'in_flow': [], 'out_flow': []}
        
        with torch.no_grad():
            for batch_data in val_data:
                graph = batch_data['graph'].to(self.device)
                od_data = batch_data['od']
                
                if od_data is not None:
                    od_data = {k: v.to(self.device) for k, v in od_data.items()}
                
                # 前向传播
                predictions = self.model(graph, od_data)
                
                # 计算损失
                targets = {
                    'in_flow': graph.y[:, 0],
                    'out_flow': graph.y[:, 1]
                }
                
                od_targets = od_data['od_flows'] if od_data is not None else None
                losses = self.model.compute_loss(predictions, targets, od_targets)
                
                total_loss += losses['total_loss'].item()
                
                # 收集预测和目标
                all_predictions['in_flow'].extend(predictions['in_flow'].detach().cpu().numpy())
                all_predictions['out_flow'].extend(predictions['out_flow'].detach().cpu().numpy())
                all_targets['in_flow'].extend(targets['in_flow'].detach().cpu().numpy())
                all_targets['out_flow'].extend(targets['out_flow'].detach().cpu().numpy())
        
        # 计算指标
        metrics = self._compute_metrics(all_predictions, all_targets)
        avg_loss = total_loss / len(val_data)
        
        return avg_loss, metrics
    
    def _compute_metrics(self, predictions, targets):
        """计算评估指标"""
        metrics = {}
        
        for flow_type in ['in_flow', 'out_flow']:
            if len(predictions[flow_type]) > 0 and len(targets[flow_type]) > 0:
                pred = np.array(predictions[flow_type])
                true = np.array(targets[flow_type])
                
                metrics[f'{flow_type}_mae'] = mean_absolute_error(true, pred)
                metrics[f'{flow_type}_mse'] = mean_squared_error(true, pred)
                metrics[f'{flow_type}_rmse'] = np.sqrt(metrics[f'{flow_type}_mse'])
            else:
                metrics[f'{flow_type}_mae'] = 0
                metrics[f'{flow_type}_mse'] = 0
                metrics[f'{flow_type}_rmse'] = 0
        
        return metrics
    
    def _final_evaluation(self, val_data, val_stations):
        """最终评估"""
        self.model.eval()
        results = {
            'station': val_stations[0] if len(val_stations) == 1 else 'multiple',
            'predictions': [],
            'targets': [],
            'metrics': {}
        }
        
        with torch.no_grad():
            for batch_data in val_data:
                graph = batch_data['graph'].to(self.device)
                od_data = batch_data['od']
                
                if od_data is not None:
                    od_data = {k: v.to(self.device) for k, v in od_data.items()}
                
                predictions = self.model(graph, od_data)
                
                results['predictions'].append({
                    'hour': batch_data['hour'],
                    'in_flow': predictions['in_flow'].detach().cpu().numpy(),
                    'out_flow': predictions['out_flow'].detach().cpu().numpy(),
                    'od_flow': predictions.get('od_flow', torch.tensor([])).detach().cpu().numpy()
                })
                
                results['targets'].append({
                    'hour': batch_data['hour'],
                    'in_flow': graph.y[:, 0].detach().cpu().numpy(),
                    'out_flow': graph.y[:, 1].detach().cpu().numpy(),
                    'od_flow': od_data['od_flows'].detach().cpu().numpy() if od_data else np.array([])
                })
        
        # 计算总体指标
        all_pred_in = np.concatenate([p['in_flow'] for p in results['predictions']])
        all_pred_out = np.concatenate([p['out_flow'] for p in results['predictions']])
        all_true_in = np.concatenate([t['in_flow'] for t in results['targets']])
        all_true_out = np.concatenate([t['out_flow'] for t in results['targets']])
        
        results['metrics'] = {
            'in_flow_mae': mean_absolute_error(all_true_in, all_pred_in),
            'out_flow_mae': mean_absolute_error(all_true_out, all_pred_out),
            'in_flow_rmse': np.sqrt(mean_squared_error(all_true_in, all_pred_in)),
            'out_flow_rmse': np.sqrt(mean_squared_error(all_true_out, all_pred_out))
        }
        
        return results
    
    def _aggregate_results(self, all_results):
        """汇总所有折叠的结果"""
        print("\n=== Aggregating Results ===")
        
        # 计算平均指标
        avg_metrics = defaultdict(list)
        for result in all_results:
            for metric, value in result['metrics'].items():
                avg_metrics[metric].append(value)
        
        final_metrics = {}
        for metric, values in avg_metrics.items():
            final_metrics[f'avg_{metric}'] = np.mean(values)
            final_metrics[f'std_{metric}'] = np.std(values)
        
        print("Final Results:")
        for metric, value in final_metrics.items():
            print(f"{metric}: {value:.4f}")
        
        return {
            'individual_results': all_results,
            'aggregated_metrics': final_metrics
        }
