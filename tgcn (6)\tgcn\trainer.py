"""
训练和评估模块
负责模型训练、验证和预测结果保存
"""
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
import geopandas as gpd
import matplotlib.pyplot as plt
from sklearn.metrics import mean_absolute_error
from config import Config
from model import TGCN
from utils import ModelUtils

class Trainer:
    def __init__(self, config=None):
        self.config = config or Config()
        self.utils = ModelUtils()
    
    def train_and_evaluate(self, X, flow_matrix, edge_index, train_indices, test_indices, 
                          common_stations, train_stations, test_stations, 
                          station_to_idx, test_station_to_idx, in_data, out_data, od_data):
        """
        训练和评估模型
        """
        print("准备训练数据...")
        
        # 准备数据
        X_train, X_test, y_train, y_test = self._prepare_training_data(
            X, flow_matrix, train_indices, test_indices
        )
        
        # 初始化模型
        model = self._initialize_model(X.shape[2], len(train_indices))
        
        # 训练模型 - 传入indices参数
        all_epoch_loss, all_epoch_mae = self._train_model(
            model, X_train, X_test, y_train, y_test, edge_index, train_indices, test_indices
        )
        
        # 保存训练曲线
        self._save_training_curves(all_epoch_loss, all_epoch_mae)
        
        # 生成预测结果
        self._generate_predictions(
            model, X_test, edge_index, test_indices, common_stations,
            test_stations, in_data, out_data, od_data
        )
        
        print("训练和预测完成！")
    
    def _prepare_training_data(self, X, flow_matrix, train_indices, test_indices):
        """准备训练数据"""
        X_tensor = torch.FloatTensor(X)
        flow_tensor = torch.FloatTensor(flow_matrix)
        
        # 创建训练集和测试集
        X_train = X_tensor[:, train_indices].unsqueeze(0)
        X_test = X_tensor[:, test_indices].unsqueeze(0)
        
        y_train = flow_tensor[:, train_indices].unsqueeze(0)
        y_test = flow_tensor[:, test_indices].unsqueeze(0)
        
        return X_train, X_test, y_train, y_test
    
    def _initialize_model(self, input_dim, num_train_nodes):
        """初始化模型"""
        model = TGCN(
            input_dim=input_dim,
            hidden_dim=self.config.HIDDEN_DIM,
            output_dim=1,
            num_nodes=num_train_nodes
        )
        return model
    
    def _train_model(self, model, X_train, X_test, y_train, y_test, edge_index, train_indices, test_indices):
        """训练模型"""
        # 重新映射训练集的edge_index
        train_edge_index = self._remap_edge_index(edge_index, train_indices)
        test_edge_index = self._remap_edge_index(edge_index, test_indices)
        
        criterion = nn.MSELoss()
        optimizer = optim.Adam(model.parameters(), lr=self.config.LEARNING_RATE)
        
        all_epoch_loss = []
        all_epoch_mae = []
        
        print("开始训练...")
        for epoch in range(self.config.NUM_EPOCHS):
            # 训练阶段
            model.train()
            outputs = model(X_train, train_edge_index)  # 使用重新映射的edge_index
            loss = criterion(outputs, y_train[:, -1])
            
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            # 评估阶段
            model.eval()
            with torch.no_grad():
                test_outputs = model(X_test, test_edge_index)  # 使用重新映射的edge_index
                test_loss = criterion(test_outputs, y_test[:, -1])
                mae = torch.mean(torch.abs(test_outputs - y_test[:, -1]))
                
                all_epoch_loss.append(loss.item())
                all_epoch_mae.append(mae.item())
                
                if (epoch + 1) % 10 == 0:
                    print(f'Epoch {epoch + 1}/{self.config.NUM_EPOCHS}, '
                          f'Loss: {loss.item():.4f}, Test Loss: {test_loss.item():.4f}, '
                          f'MAE: {mae.item():.4f}')
        
        return all_epoch_loss, all_epoch_mae
    
    def _remap_edge_index(self, edge_index, node_indices):
        """
        重新映射edge_index以适应子图
        
        Args:
            edge_index: 原始边索引 [2, num_edges]
            node_indices: 子图节点索引
            
        Returns:
            重新映射的边索引
        """
        # 创建原始索引到新索引的映射
        old_to_new = {old_idx: new_idx for new_idx, old_idx in enumerate(node_indices)}
        
        # 筛选出子图中的边
        mask = torch.isin(edge_index[0], torch.tensor(node_indices)) & \
               torch.isin(edge_index[1], torch.tensor(node_indices))
        
        sub_edge_index = edge_index[:, mask]
        
        # 重新映射索引
        remapped_edge_index = torch.zeros_like(sub_edge_index)
        for i in range(sub_edge_index.shape[1]):
            old_src = sub_edge_index[0, i].item()
            old_dst = sub_edge_index[1, i].item()
            remapped_edge_index[0, i] = old_to_new[old_src]
            remapped_edge_index[1, i] = old_to_new[old_dst]
        
        return remapped_edge_index
    
    def _save_training_curves(self, all_epoch_loss, all_epoch_mae):
        """保存训练曲线"""
        plt.figure(figsize=(12, 5))
        
        plt.subplot(1, 2, 1)
        plt.plot(all_epoch_loss)
        plt.title('Training Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        
        plt.subplot(1, 2, 2)
        plt.plot(all_epoch_mae)
        plt.title('Validation MAE')
        plt.xlabel('Epoch')
        plt.ylabel('MAE')
        
        plt.tight_layout()
        plt.savefig(self.config.OUTPUT_TRAINING_CURVES)
        print(f"训练曲线已保存到: {self.config.OUTPUT_TRAINING_CURVES}")
    
    def _generate_predictions(self, model, X_test, edge_index, test_indices, 
                            common_stations, test_stations, in_data, out_data, od_data):
        """生成并保存预测结果"""
        print("生成预测结果...")
        
        # 重新映射测试集的edge_index
        test_edge_index = self._remap_edge_index(edge_index, test_indices)
        
        model.eval()
        with torch.no_grad():
            test_predictions = model(X_test, test_edge_index).cpu().numpy()[0]  # 使用重新映射的edge_index
            
            # 创建预测DataFrame
            prediction_df = pd.DataFrame({
                'station': [common_stations[i] for i in test_indices],
                'prediction': test_predictions
            })
            
            # 保存各类预测结果
            self._save_in_predictions(in_data, test_stations, prediction_df)
            self._save_out_predictions(out_data, test_stations, prediction_df)
            self._save_od_predictions(od_data, test_stations, prediction_df)
            self._save_prediction_comparison(in_data, out_data, od_data, 
                                           test_stations, prediction_df)
    
    def _save_in_predictions(self, in_data, test_stations, prediction_df):
        """保存进站预测结果"""
        test_in_data = in_data[in_data['station'].isin(test_stations)].copy()
        test_in_data = pd.merge(test_in_data, prediction_df, on='station', how='left')
        
        gdf_in_pred = gpd.GeoDataFrame(test_in_data, geometry='geometry')
        gdf_in_pred.to_file(self.config.OUTPUT_IN_PRED)
        print(f"进站预测结果已保存到: {self.config.OUTPUT_IN_PRED}")
    
    def _save_out_predictions(self, out_data, test_stations, prediction_df):
        """保存出站预测结果"""
        test_out_data = out_data[out_data['station'].isin(test_stations)].copy()
        test_out_data = pd.merge(test_out_data, prediction_df, on='station', how='left')
        
        gdf_out_pred = gpd.GeoDataFrame(test_out_data, geometry='geometry')
        gdf_out_pred.to_file(self.config.OUTPUT_OUT_PRED)
        print(f"出站预测结果已保存到: {self.config.OUTPUT_OUT_PRED}")
    
    def _save_od_predictions(self, od_data, test_stations, prediction_df):
        """保存OD预测结果"""
        od_subset = od_data[
            (od_data['o_rawname'].isin(test_stations)) &
            (od_data['d_rawname'].isin(test_stations))
        ].copy()
        
        # 基于进站预测估计OD流量
        od_predictions = []
        for _, row in od_subset.iterrows():
            o_station = row['o_rawname']
            d_station = row['d_rawname']
            
            o_pred = prediction_df[prediction_df['station'] == o_station]['prediction'].values
            d_pred = prediction_df[prediction_df['station'] == d_station]['prediction'].values
            
            if len(o_pred) > 0 and len(d_pred) > 0:
                od_pred = np.sqrt(o_pred[0] * d_pred[0])
                od_predictions.append({
                    'o_rawname': o_station,
                    'd_rawname': d_station,
                    'prediction': od_pred
                })
        
        od_pred_df = pd.DataFrame(od_predictions)
        od_subset = pd.merge(od_subset, od_pred_df, on=['o_rawname', 'd_rawname'], how='left')
        od_subset.to_csv(self.config.OUTPUT_OD_PRED, index=False)
        print(f"OD预测结果已保存到: {self.config.OUTPUT_OD_PRED}")
    
    def _save_prediction_comparison(self, in_data, out_data, od_data, 
                                  test_stations, prediction_df):
        """保存预测比较结果"""
        prediction_comparison = []
        
        # 进站预测比较
        test_in_data = in_data[in_data['station'].isin(test_stations)].copy()
        test_in_data = pd.merge(test_in_data, prediction_df, on='station', how='left')
        
        for _, row in test_in_data.iterrows():
            if pd.notna(row['prediction']):
                prediction_comparison.append({
                    'Type': 'In',
                    'Station': row['station'],
                    'Hour': row['hour'],
                    'True_Value': row['count'],
                    'Predicted_Value': row['prediction']
                })
        
        # 计算并打印评估指标
        if len(prediction_comparison) > 0:
            in_results = test_in_data.dropna(subset=['prediction'])
            if len(in_results) > 0:
                in_mae = mean_absolute_error(in_results['count'], in_results['prediction'])
                print(f"进站预测 MAE: {in_mae:.4f}")
        
        # 保存比较结果
        pd.DataFrame(prediction_comparison).to_csv(self.config.OUTPUT_COMPARISON, index=False)
        print(f"预测比较结果已保存到: {self.config.OUTPUT_COMPARISON}")



