import os
import argparse
import torch
import numpy as np
import pandas as pd
from tqdm import tqdm
import matplotlib.pyplot as plt
from datetime import datetime
import gc
import torch.nn as nn

from data_loader import MetroDataLoader
from models import MetroFlowModel
from utils import (
    create_directory, timer, evaluate_predictions, plot_prediction_comparison,
    save_results_to_csv, prepare_prediction_comparison, log_message
)

# 配置GPU设备
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='地铁流量预测训练')
    parser.add_argument('--data_dir', type=str, default='C:/Users/<USER>/Desktop/接驳',
                        help='数据目录路径')
    parser.add_argument('--model_dir', type=str, default='./output',
                        help='模型保存目录路径')
    parser.add_argument('--hidden_dim', type=int, default=128,
                        help='隐藏层维度')
    parser.add_argument('--epochs', type=int, default=100,
                        help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=64,
                        help='批量大小')
    parser.add_argument('--lr', type=float, default=0.001,
                        help='学习率')
    parser.add_argument('--use_fp16', action='store_true',
                        help='使用半精度浮点数')
    parser.add_argument('--early_stopping', type=int, default=20,
                        help='早停轮数')
    parser.add_argument('--model_prefix', type=str, default='best',
                        help='模型文件前缀')
    return parser.parse_args()

@timer
def train_model(model, train_loader, val_loader, epochs=10, device=torch.device('cuda' if torch.cuda.is_available() else 'cpu')):
    """
    训练模型
    """
    # 创建优化器 - 使用AdamW并添加权重衰减防止过拟合
    optimizer = torch.optim.AdamW(model.parameters(), lr=0.001, weight_decay=1e-5)
    
    # 创建学习率调度器 - 使用余弦退火调度器提高模型性能
    scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=epochs)
    
    # 损失函数 - 使用Huber Loss减少异常值影响
    criterion = nn.SmoothL1Loss()
    
    # 创建混合精度训练的scaler
    scaler = torch.cuda.amp.GradScaler(enabled=model.use_fp16 if hasattr(model, 'use_fp16') else False)
    
    # 梯度累积步数 - 模拟更大的批次大小而不增加内存使用
    gradient_accumulation_steps = 4
    
    # 记录训练历史
    history = {
        'train_loss': [],
        'val_loss': [],
        'train_mae': [],
        'val_mae': []
    }
    
    # 最佳验证损失
    best_val_loss = float('inf')
    
    # 训练循环
    for epoch in range(epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_mae = 0.0
        train_samples = 0
        
        # 创建进度条
        pbar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{epochs} [Train]")
        
        # 清空缓存开始新的epoch
        torch.cuda.empty_cache()
        
        for i, (data, targets) in enumerate(pbar):
            # 将数据移到设备
            data = data.to(device)
            targets = {k: v.to(device) for k, v in targets.items()}
            
            # 确保在每个batch开始时清理垃圾收集器
            if i % gradient_accumulation_steps == 0:
                gc.collect()
                torch.cuda.empty_cache()
            
            # 前向传播 - 使用混合精度
            with torch.cuda.amp.autocast(enabled=model.use_fp16 if hasattr(model, 'use_fp16') else False):
                try:
                    # 获取预测结果
                    outputs = model(data)
                    
                    # 初始化损失
                    loss = 0.0
                    batch_mae = 0.0
                    
                    # 计算每个任务的损失并累加
                    task_count = 0
                    for task in ['in', 'out', 'od']:
                        if task in outputs and task in targets:
                            task_loss = criterion(outputs[task], targets[task])
                            task_mae = torch.mean(torch.abs(outputs[task] - targets[task]))
                            
                            loss += task_loss
                            batch_mae += task_mae.item()
                            task_count += 1
                    
                    # 计算平均损失和MAE
                    if task_count > 0:
                        loss /= task_count
                        batch_mae /= task_count
                        
                        # 梯度缩放和累积
                        loss = loss / gradient_accumulation_steps
                        scaler.scale(loss).backward()
                        
                        # 更新进度条
                        pbar.set_postfix(loss=loss.item() * gradient_accumulation_steps, mae=batch_mae)
                        
                        # 梯度累积更新
                        if (i + 1) % gradient_accumulation_steps == 0 or (i + 1) == len(train_loader):
                            # 梯度裁剪防止梯度爆炸
                            scaler.unscale_(optimizer)
                            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                            
                            # 使用scaler更新参数
                            scaler.step(optimizer)
                            scaler.update()
                            optimizer.zero_grad(set_to_none=True)  # 更高效的梯度清零
                        
                        # 累积统计
                        train_loss += loss.item() * gradient_accumulation_steps * len(data.x)
                        train_mae += batch_mae * len(data.x)
                        train_samples += len(data.x)
                        
                except RuntimeError as e:
                    print(f"训练批次出错，跳过: {str(e)}")
                    # 重置梯度
                    optimizer.zero_grad(set_to_none=True)
                    # 尝试释放内存
                    torch.cuda.empty_cache()
                    gc.collect()
        
        # 更新学习率
        scheduler.step()
        
        # 计算平均训练损失和MAE
        avg_train_loss = train_loss / train_samples if train_samples > 0 else float('inf')
        avg_train_mae = train_mae / train_samples if train_samples > 0 else float('inf')
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_mae = 0.0
        val_samples = 0
        
        # 清空缓存开始验证
        torch.cuda.empty_cache()
        
        with torch.no_grad():
            for data, targets in tqdm(val_loader, desc=f"Epoch {epoch+1}/{epochs} [Val]"):
                # 将数据移到设备
                data = data.to(device)
                targets = {k: v.to(device) for k, v in targets.items()}
                
                try:
                    # 前向传播
                    outputs = model(data)
                    
                    # 初始化损失
                    batch_loss = 0.0
                    batch_mae = 0.0
                    
                    # 计算每个任务的损失并累加
                    task_count = 0
                    for task in ['in', 'out', 'od']:
                        if task in outputs and task in targets:
                            task_loss = criterion(outputs[task], targets[task])
                            task_mae = torch.mean(torch.abs(outputs[task] - targets[task]))
                            
                            batch_loss += task_loss.item()
                            batch_mae += task_mae.item()
                            task_count += 1
                    
                    # 计算平均损失和MAE
                    if task_count > 0:
                        batch_loss /= task_count
                        batch_mae /= task_count
                        
                        # 累积统计
                        val_loss += batch_loss * len(data.x)
                        val_mae += batch_mae * len(data.x)
                        val_samples += len(data.x)
                    
                except RuntimeError as e:
                    print(f"验证批次出错，跳过: {str(e)}")
                    torch.cuda.empty_cache()
        
        # 计算平均验证损失和MAE
        avg_val_loss = val_loss / val_samples if val_samples > 0 else float('inf')
        avg_val_mae = val_mae / val_samples if val_samples > 0 else float('inf')
        
        # 记录历史
        history['train_loss'].append(avg_train_loss)
        history['val_loss'].append(avg_val_loss)
        history['train_mae'].append(avg_train_mae)
        history['val_mae'].append(avg_val_mae)
        
        # 打印epoch结果
        print(f"Epoch {epoch+1}/{epochs} - Train Loss: {avg_train_loss:.4f}, Train MAE: {avg_train_mae:.4f}, Val Loss: {avg_val_loss:.4f}, Val MAE: {avg_val_mae:.4f}")
        
        # 如果验证损失改善，保存模型
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            # 保存最佳模型
            torch.save(model.state_dict(), 'best_model.pth')
            print(f"模型保存于 'best_model.pth'")
        
        # 清空缓存结束epoch
        torch.cuda.empty_cache()
        gc.collect()
    
    return history

def plot_training_history(history, model_type, save_path):
    """
    绘制训练历史记录图
    
    参数:
    history: 训练历史记录
    model_type: 模型类型
    save_path: 保存路径
    """
    plt.figure(figsize=(12, 10))
    
    # 损失曲线
    plt.subplot(2, 1, 1)
    plt.plot(history['train_loss'], label='训练损失')
    plt.plot(history['val_loss'], label='验证损失')
    plt.title(f'{model_type}模型训练损失')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True)
    
    # MAE曲线
    plt.subplot(2, 1, 2)
    plt.plot(history['train_mae'], label='训练MAE')
    plt.plot(history['val_mae'], label='验证MAE')
    plt.title(f'{model_type}模型训练MAE')
    plt.xlabel('Epoch')
    plt.ylabel('MAE')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig(save_path)
    plt.close()

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 创建输出目录
    create_directory(args.model_dir)
    
    # 日志文件
    log_file = os.path.join(args.model_dir, 'training.log')
    
    # 记录基本信息
    log_message(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", log_file)
    log_message(f"设备: {device}", log_file)
    log_message(f"数据目录: {args.data_dir}", log_file)
    log_message(f"模型目录: {args.model_dir}", log_file)
    log_message(f"隐藏层维度: {args.hidden_dim}", log_file)
    log_message(f"训练轮数: {args.epochs}", log_file)
    log_message(f"批量大小: {args.batch_size}", log_file)
    log_message(f"学习率: {args.lr}", log_file)
    log_message(f"使用FP16: {args.use_fp16}", log_file)
    log_message(f"早停轮数: {args.early_stopping}", log_file)
    
    # 加载数据
    data_loader = MetroDataLoader(args.data_dir, use_fp16=args.use_fp16)
    data_loader.load_all_data()
    data_loader.preprocess_data()
    
    # 获取模型维度
    in_node_features = data_loader.grid_station_graph.x.shape[1]
    out_node_features = data_loader.grid_station_graph.x.shape[1]
    od_node_features = data_loader.station_station_graph.x.shape[1]
    od_edge_features = data_loader.station_station_graph.edge_attr.shape[1]
    
    # 创建模型
    model = MetroFlowModel(
        in_node_features, out_node_features, od_node_features, od_edge_features,
        hidden_dim=args.hidden_dim, use_fp16=args.use_fp16
    )
    
    # 训练模型
    history = train_model(model, data_loader.train_loader, data_loader.val_loader, args.epochs, device)
    plot_training_history(history, '地铁流量', os.path.join(args.model_dir, 'training_history.png'))
    
    # 保存最终模型
    torch.save(model.state_dict(), os.path.join(args.model_dir, 'final_model.pth'))
    
    log_message(f"训练完成！最终模型已保存至 {args.model_dir}", log_file)
    log_message(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", log_file)

if __name__ == "__main__":
    main() 