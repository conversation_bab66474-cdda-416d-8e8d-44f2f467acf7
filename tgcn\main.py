"""
主入口文件
协调各个模块完成整个T-GCN训练和预测流程
"""
from config import Config
from data_loader import DataLoader
from data_preprocessor import DataPreprocessor
from trainer import Trainer
from utils import ModelUtils

def main():
    """主函数"""
    # 初始化配置
    config = Config()
    
    # 设置随机种子
    ModelUtils.set_random_seed(config.RANDOM_SEED)
    
    # 初始化各个模块
    data_loader = DataLoader(config)
    preprocessor = DataPreprocessor(config)
    trainer = Trainer(config)
    
    print("=" * 50)
    print("T-GCN 地铁客流预测系统")
    print("=" * 50)
    
    try:
        # 加载数据
        in_data, out_data, od_data, station_features = data_loader.load_data()
        
        # 预处理数据
        (X, flow_matrix, edge_index, train_indices, test_indices,
         common_stations, train_stations, test_stations,
         station_to_idx, test_station_to_idx, 
         in_data, out_data, od_data) = preprocessor.preprocess_data(
            in_data, out_data, od_data, station_features
        )
        
        # 训练和评估
        trainer.train_and_evaluate(
            X, flow_matrix, edge_index, train_indices, test_indices,
            common_stations, train_stations, test_stations,
            station_to_idx, test_station_to_idx, in_data, out_data, od_data
        )
        
        print("=" * 50)
        print("程序执行完成！")
        print("=" * 50)
        
    except Exception as e:
        print(f"程序执行出错: {str(e)}")
        raise

if __name__ == "__main__":
    main()



