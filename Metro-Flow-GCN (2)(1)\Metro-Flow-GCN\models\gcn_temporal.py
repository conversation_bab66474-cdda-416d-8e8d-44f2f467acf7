import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import GCNConv, global_mean_pool
from torch_geometric.data import Data, Batch
import numpy as np


class SpatialGCN(nn.Module):
    """空间图卷积网络模块"""
    def __init__(self, input_dim, hidden_dim, output_dim, num_layers=3, dropout=0.2):
        super(SpatialGCN, self).__init__()
        self.num_layers = num_layers
        self.dropout = dropout
        
        # GCN层
        self.gcn_layers = nn.ModuleList()
        self.gcn_layers.append(GCNConv(input_dim, hidden_dim))
        
        for _ in range(num_layers - 2):
            self.gcn_layers.append(GCNConv(hidden_dim, hidden_dim))
        
        self.gcn_layers.append(GCNConv(hidden_dim, output_dim))
        
        # Batch Normalization
        self.batch_norms = nn.ModuleList()
        for _ in range(num_layers - 1):
            self.batch_norms.append(nn.BatchNorm1d(hidden_dim))
        
    def forward(self, x, edge_index, batch=None):
        # x: [num_nodes, input_dim]
        # edge_index: [2, num_edges]
        
        for i, (conv, bn) in enumerate(zip(self.gcn_layers[:-1], self.batch_norms)):
            x = conv(x, edge_index)
            x = bn(x)
            x = F.relu(x)
            x = F.dropout(x, p=self.dropout, training=self.training)
        
        # 最后一层不使用激活函数和dropout
        x = self.gcn_layers[-1](x, edge_index)
        
        return x


class TemporalEncoder(nn.Module):
    """时间特征编码器"""
    def __init__(self, time_dim=24, embed_dim=64):
        super(TemporalEncoder, self).__init__()
        self.time_dim = time_dim
        self.embed_dim = embed_dim
        
        # 时间嵌入
        self.hour_embed = nn.Embedding(24, embed_dim // 2)
        self.day_embed = nn.Embedding(7, embed_dim // 4)  # 一周7天
        self.month_embed = nn.Embedding(12, embed_dim // 4)  # 12个月
        
        # 时间特征融合
        self.temporal_fusion = nn.Linear(embed_dim, embed_dim)
        
    def forward(self, time_features):
        # time_features: [batch_size, 3] (hour, day_of_week, month)
        batch_size = time_features.size(0)
        
        hour_emb = self.hour_embed(time_features[:, 0].long())  # [batch_size, embed_dim//2]
        day_emb = self.day_embed(time_features[:, 1].long())    # [batch_size, embed_dim//4]
        month_emb = self.month_embed(time_features[:, 2].long()) # [batch_size, embed_dim//4]
        
        # 拼接时间特征
        temporal_emb = torch.cat([hour_emb, day_emb, month_emb], dim=1)  # [batch_size, embed_dim]
        temporal_emb = self.temporal_fusion(temporal_emb)
        
        return temporal_emb


class MetroFlowGCN(nn.Module):
    """地铁流量预测的GCN模型"""
    def __init__(self, 
                 node_feat_dim,
                 spatial_hidden_dim=128,
                 temporal_embed_dim=64,
                 num_gcn_layers=3,
                 num_prediction_tasks=3,  # 进站、出站、OD
                 dropout=0.2,
                 use_float16=False):
        super(MetroFlowGCN, self).__init__()
        
        self.node_feat_dim = node_feat_dim
        self.spatial_hidden_dim = spatial_hidden_dim
        self.temporal_embed_dim = temporal_embed_dim
        self.num_prediction_tasks = num_prediction_tasks
        self.use_float16 = use_float16
        
        # 时间编码器
        self.temporal_encoder = TemporalEncoder(embed_dim=temporal_embed_dim)
        
        # 空间特征预处理
        self.node_feature_proj = nn.Linear(node_feat_dim, spatial_hidden_dim)
        
        # 空间GCN
        total_input_dim = spatial_hidden_dim + temporal_embed_dim
        self.spatial_gcn = SpatialGCN(
            input_dim=total_input_dim,
            hidden_dim=spatial_hidden_dim,
            output_dim=spatial_hidden_dim,
            num_layers=num_gcn_layers,
            dropout=dropout
        )
        
        # 预测头
        self.prediction_heads = nn.ModuleList()
        for _ in range(num_prediction_tasks):
            self.prediction_heads.append(
                nn.Sequential(
                    nn.Linear(spatial_hidden_dim, spatial_hidden_dim // 2),
                    nn.ReLU(),
                    nn.Dropout(dropout),
                    nn.Linear(spatial_hidden_dim // 2, 1)
                )
            )
        
        # 流量平衡约束层
        self.balance_layer = nn.Linear(num_prediction_tasks, num_prediction_tasks)
        
    def forward(self, node_features, edge_index, time_features, batch=None):
        """
        Args:
            node_features: [num_nodes, node_feat_dim] 节点特征
            edge_index: [2, num_edges] 边索引
            time_features: [batch_size, 3] 时间特征 (hour, day_of_week, month)
            batch: 批次索引
        """
        batch_size = time_features.size(0)
        num_nodes = node_features.size(0)
        
        # 检查输入数据
        if torch.isnan(node_features).any() or torch.isinf(node_features).any():
            print("警告: 输入节点特征包含NaN或Inf值")
            node_features = torch.where(torch.isnan(node_features) | torch.isinf(node_features), 
                                       torch.zeros_like(node_features), node_features)
        
        if torch.isnan(time_features).any() or torch.isinf(time_features).any():
            print("警告: 输入时间特征包含NaN或Inf值")
            time_features = torch.where(torch.isnan(time_features) | torch.isinf(time_features), 
                                       torch.zeros_like(time_features), time_features)
        
        # 节点特征投影
        node_feat_proj = self.node_feature_proj(node_features)  # [num_nodes, spatial_hidden_dim]
        
        # 检查投影后的特征
        if torch.isnan(node_feat_proj).any() or torch.isinf(node_feat_proj).any():
            print("警告: 节点特征投影后包含NaN或Inf值")
            node_feat_proj = torch.where(torch.isnan(node_feat_proj) | torch.isinf(node_feat_proj), 
                                        torch.zeros_like(node_feat_proj), node_feat_proj)
        
        # 时间特征编码
        temporal_emb = self.temporal_encoder(time_features)  # [batch_size, temporal_embed_dim]
        
        # 检查时间编码
        if torch.isnan(temporal_emb).any() or torch.isinf(temporal_emb).any():
            print("警告: 时间编码包含NaN或Inf值")
            temporal_emb = torch.where(torch.isnan(temporal_emb) | torch.isinf(temporal_emb), 
                                      torch.zeros_like(temporal_emb), temporal_emb)
        
        # 将时间特征广播到所有节点
        if batch is None:
            # 单图情况，所有节点使用相同的时间特征
            temporal_emb_expanded = temporal_emb[0].unsqueeze(0).repeat(num_nodes, 1)
        else:
            # 批处理情况
            temporal_emb_expanded = temporal_emb[batch]
        
        # 拼接空间和时间特征
        combined_features = torch.cat([node_feat_proj, temporal_emb_expanded], dim=1)
        
        # 检查拼接后的特征
        if torch.isnan(combined_features).any() or torch.isinf(combined_features).any():
            print("警告: 拼接后特征包含NaN或Inf值")
            combined_features = torch.where(torch.isnan(combined_features) | torch.isinf(combined_features), 
                                           torch.zeros_like(combined_features), combined_features)
        
        # 空间GCN处理
        spatial_repr = self.spatial_gcn(combined_features, edge_index, batch)
        
        # 检查GCN输出
        if torch.isnan(spatial_repr).any() or torch.isinf(spatial_repr).any():
            print("警告: GCN输出包含NaN或Inf值")
            spatial_repr = torch.where(torch.isnan(spatial_repr) | torch.isinf(spatial_repr), 
                                      torch.zeros_like(spatial_repr), spatial_repr)
        
        # 多任务预测
        predictions = []
        for head in self.prediction_heads:
            pred = head(spatial_repr)  # [num_nodes, 1]
            # 检查预测头输出
            if torch.isnan(pred).any() or torch.isinf(pred).any():
                print("警告: 预测头输出包含NaN或Inf值")
                pred = torch.where(torch.isnan(pred) | torch.isinf(pred), 
                                  torch.zeros_like(pred), pred)
            predictions.append(pred)
        
        predictions = torch.cat(predictions, dim=1)  # [num_nodes, num_prediction_tasks]
        
        # 流量平衡约束
        balanced_predictions = self.balance_layer(predictions)
        
        # 检查平衡层输出
        if torch.isnan(balanced_predictions).any() or torch.isinf(balanced_predictions).any():
            print("警告: 平衡层输出包含NaN或Inf值")
            balanced_predictions = torch.where(torch.isnan(balanced_predictions) | torch.isinf(balanced_predictions), 
                                              torch.zeros_like(balanced_predictions), balanced_predictions)
        
        # 确保预测值为正
        balanced_predictions = F.relu(balanced_predictions)
        
        # 最终检查
        if torch.isnan(balanced_predictions).any() or torch.isinf(balanced_predictions).any():
            print("警告: 最终输出包含NaN或Inf值，使用零值替换")
            balanced_predictions = torch.where(torch.isnan(balanced_predictions) | torch.isinf(balanced_predictions), 
                                              torch.zeros_like(balanced_predictions), balanced_predictions)
        
        if self.use_float16:
            balanced_predictions = balanced_predictions.half()
        
        return balanced_predictions
    
    def predict_grid_station_flow(self, grid_features, station_features, 
                                  grid_station_edges, time_features):
        """预测栅格到站点的流量"""
        # 合并栅格和站点特征
        combined_features = torch.cat([grid_features, station_features], dim=0)
        
        # 使用模型进行预测
        predictions = self.forward(combined_features, grid_station_edges, time_features)
        
        # 分离栅格和站点的预测结果
        num_grids = grid_features.size(0)
        grid_predictions = predictions[:num_grids]
        station_predictions = predictions[num_grids:]
        
        return grid_predictions, station_predictions


class GridStationConnector(nn.Module):
    """栅格-站点连接器，用于建立栅格和站点之间的连接"""
    def __init__(self, distance_threshold=1000):  # 1km阈值
        super(GridStationConnector, self).__init__()
        self.distance_threshold = distance_threshold
        
    def build_grid_station_graph(self, grid_coords, station_coords):
        """
        构建栅格-站点图
        Args:
            grid_coords: [num_grids, 2] 栅格坐标 (经度, 纬度)
            station_coords: [num_stations, 2] 站点坐标 (经度, 纬度)
        """
        num_grids = grid_coords.size(0)
        num_stations = station_coords.size(0)
        
        # 计算栅格与站点之间的距离
        grid_coords = grid_coords.unsqueeze(1)  # [num_grids, 1, 2]
        station_coords = station_coords.unsqueeze(0)  # [1, num_stations, 2]
        
        # 欧几里得距离计算
        distances = torch.sqrt(torch.sum((grid_coords - station_coords) ** 2, dim=2))
        
        # 找到距离小于阈值的连接
        valid_connections = distances < self.distance_threshold
        grid_indices, station_indices = torch.where(valid_connections)
        
        # 调整站点索引（因为它们在合并后的特征矩阵中的位置）
        station_indices_adjusted = station_indices + num_grids
        
        # 构建边索引
        edge_index = torch.stack([
            torch.cat([grid_indices, station_indices_adjusted]),
            torch.cat([station_indices_adjusted, grid_indices])
        ])
        
        return edge_index, distances[valid_connections]


def create_model(config):
    """创建模型实例"""
    model = MetroFlowGCN(
        node_feat_dim=config['node_feat_dim'],
        spatial_hidden_dim=config.get('spatial_hidden_dim', 128),
        temporal_embed_dim=config.get('temporal_embed_dim', 64),
        num_gcn_layers=config.get('num_gcn_layers', 3),
        num_prediction_tasks=config.get('num_prediction_tasks', 3),
        dropout=config.get('dropout', 0.2),
        use_float16=config.get('use_float16', False)
    )
    
    if config.get('use_float16', False):
        model = model.half()
    
    return model 