# 纯GCN+时间特征地铁流量预测系统

## 项目概述

本项目实现了一个基于深度学习的地铁流量预测系统，使用纯图卷积网络(GCN)结合时间特征进行时空模式学习。系统支持多城市数据，具备真正的机器学习预测能力，而非简单的统计方法。

## 🚀 核心特性

### 1. 真正的深度学习架构
- **时空图卷积网络 (Temporal GCN)**: 结合空间图结构和时间序列建模
- **多层GCN**: 3层图卷积网络提取空间特征
- **LSTM时序建模**: 捕获时间依赖关系
- **注意力机制**: 区分不同时间段的重要性
- **多任务学习**: 同时预测进站和出站流量

### 2. 先进的时间特征工程
- **周期性编码**: sin/cos编码捕获24小时周期性
- **时间段分类**: 早高峰、晚高峰、平峰、低峰四个时段
- **小时嵌入**: 学习每个小时的独特模式
- **时序预测**: 支持多步预测，而非单点预测

### 3. 通用数据适配能力
- **多城市支持**: 支持北京、上海、广州等不同城市
- **自动数据检测**: 智能识别不同格式的数据文件
- **字段映射**: 自动适配不同的字段名称
- **数据验证**: 完整的数据一致性检查

### 4. 完整的机器学习流程
- **特征工程**: 标准化、异常值处理、特征选择
- **模型训练**: 早停、学习率调度、梯度裁剪
- **模型评估**: MAE、RMSE、R²等多种指标
- **结果输出**: 多种格式的预测结果

## 📁 项目结构

```
接驳/
├── gcn_metro_flow/              # 核心框架
│   ├── config.py               # 配置管理
│   ├── data_adapter.py         # 通用数据适配器
│   ├── models.py               # 深度学习模型
│   ├── deep_trainer.py         # 训练框架
│   ├── predictor.py            # 预测模块
│   └── grid_allocator.py       # 栅格分配
├── run_deep_learning.py        # 完整深度学习系统
├── final_ml_system.py          # 最终机器学习系统
├── run_prediction.py           # 简化预测系统
└── README_FINAL.md             # 本文档
```

## 🔧 环境要求

### 必需依赖
```bash
torch>=1.9.0
torch-geometric>=2.0.0
pandas>=1.3.0
geopandas>=0.9.0
scikit-learn>=1.0.0
numpy>=1.21.0
```

### 安装命令
```bash
pip install torch torch-geometric pandas geopandas scikit-learn numpy
```

## 🚀 快速开始

### 1. 基础运行
```bash
# 运行完整的深度学习系统
python run_deep_learning.py --city beijing

# 快速测试模式
python run_deep_learning.py --quick_test

# 运行最终优化版本
python final_ml_system.py
```

### 2. 自定义配置
```bash
# 指定数据目录
python run_deep_learning.py --data_dir /path/to/data

# 调整模型参数
python run_deep_learning.py --epochs 100 --batch_size 32 --hidden_dim 128

# 使用GPU
python run_deep_learning.py --gpu 0

# 仅评估模式
python run_deep_learning.py --eval_only
```

### 3. 多城市支持
```bash
# 北京数据
python run_deep_learning.py --city beijing

# 上海数据
python run_deep_learning.py --city shanghai --data_dir ./data/shanghai

# 广州数据
python run_deep_learning.py --city guangzhou --data_dir ./data/guangzhou
```

## 📊 数据格式要求

### 输入数据文件
1. **进出站流量数据** (`in_500_with_coords.shp`, `out_500_with_coords.shp`)
   - 字段：station, hour, count, longitude, latitude
   - 格式：Shapefile或CSV

2. **OD流量数据** (`*od*.csv`)
   - 字段：o_rawname, d_rawname, hour, trip, surface_distance, translate, time, wait_time
   - 格式：CSV

3. **站点连接数据** (`*connect*.csv`)
   - 字段：station_1, station_2
   - 格式：CSV

4. **相邻站流量数据** (`*flow*.csv`)
   - 字段：hour, o_rawname, d_rawname, trip
   - 格式：CSV

5. **栅格特征数据** (`*data.csv`)
   - 字段：站名 + 51维地理社会经济特征
   - 格式：CSV

### 输出文件
- `{city}_in_predictions.shp`: 进站流量预测
- `{city}_out_predictions.shp`: 出站流量预测
- `{city}_od_predictions.csv`: OD流量预测
- `{city}_prediction_comparison.csv`: 预测对比
- `{city}_metrics.json`: 评估指标
- `{city}_best_model.pth`: 训练好的模型

## 🧠 模型架构详解

### 1. 时空图卷积网络 (TemporalGCN)
```python
# 核心组件
- TemporalEncoder: 时间特征编码
- SpatialGCN: 空间图卷积
- LSTM: 时序建模
- MultiheadAttention: 注意力机制
```

### 2. 预测模型
```python
# 站点流量预测器
StationFlowPredictor:
  - 输入: 时空特征序列
  - 输出: 进出站流量预测
  - 特性: 流量分解、多步预测

# OD流量预测器  
ODFlowPredictor:
  - 输入: 站点嵌入 + OD特征
  - 输出: OD流量预测
  - 特性: 交互建模、距离感知
```

### 3. 训练策略
- **分层验证**: 时间分割 + 空间分割 + 留一法
- **早停机制**: 防止过拟合
- **学习率调度**: 自适应学习率
- **梯度裁剪**: 稳定训练过程

## 📈 性能指标

### 评估指标
- **MAE (Mean Absolute Error)**: 平均绝对误差
- **RMSE (Root Mean Square Error)**: 均方根误差  
- **R² (Coefficient of Determination)**: 决定系数
- **MAPE (Mean Absolute Percentage Error)**: 平均绝对百分比误差

### 预期性能
- 进站流量预测 MAE: < 50 人次
- 出站流量预测 MAE: < 50 人次
- 整体 R²: > 0.8
- 训练时间: < 30分钟 (CPU)

## 🔧 配置说明

### 模型配置
```json
{
  "model": {
    "gcn_hidden_dim": 128,
    "gcn_layers": 3,
    "gcn_dropout": 0.3,
    "time_embed_dim": 32,
    "learning_rate": 0.001,
    "epochs": 200,
    "batch_size": 32
  }
}
```

### 数据配置
```json
{
  "data_processing": {
    "sequence_length": 12,
    "prediction_horizon": 6,
    "station_radius_km": 3.0,
    "feature_normalize": true,
    "remove_outliers": true
  }
}
```

## 🚀 高级功能

### 1. 自定义配置文件
```bash
# 创建配置文件
python run_deep_learning.py --city beijing
# 生成 beijing_config.json

# 使用自定义配置
python run_deep_learning.py --config my_config.json
```

### 2. 模型部署
```python
# 加载训练好的模型
import torch
from gcn_metro_flow.models import StationFlowPredictor

model = StationFlowPredictor(config)
model.load_state_dict(torch.load('beijing_best_model.pth'))
model.eval()

# 进行预测
predictions = model(graph_data)
```

### 3. 批量处理
```bash
# 处理多个城市
for city in beijing shanghai guangzhou; do
    python run_deep_learning.py --city $city --data_dir ./data/$city
done
```

## 🔍 技术创新点

### 1. 纯GCN架构
- 专注于图结构学习，避免复杂的注意力机制
- 高效的空间特征提取
- 良好的可解释性

### 2. 时间特征融合
- 多层次时间编码
- 周期性和趋势性建模
- 时间段感知的预测

### 3. 多任务学习
- 同时预测进出站和OD流量
- 共享表示学习
- 提高模型泛化能力

### 4. 分层预测机制
- 站点级预测 → OD级预测 → 栅格级分配
- 层次化的流量建模
- 保证流量守恒

## 🛠️ 故障排除

### 常见问题
1. **CUDA内存不足**: 减小batch_size或使用CPU
2. **数据格式错误**: 检查字段名称和数据类型
3. **模型不收敛**: 调整学习率或增加正则化
4. **预测结果异常**: 检查数据预处理和特征工程

### 调试模式
```bash
python run_deep_learning.py --debug --quick_test
```

## 📚 扩展方向

### 1. 模型改进
- 引入图注意力网络 (GAT)
- 添加变分自编码器 (VAE)
- 集成强化学习

### 2. 特征增强
- 天气数据融合
- 事件数据集成
- 实时交通信息

### 3. 应用扩展
- 实时预测系统
- 可视化界面
- 移动端应用

## 📄 许可证

本项目采用 MIT 许可证。

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request！

## 📞 联系方式

如有问题，请通过 GitHub Issues 联系。
