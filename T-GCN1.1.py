import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
import geopandas as gpd
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, mean_squared_error
import networkx as nx
from scipy.spatial.distance import cdist
from torch_geometric.nn import GCNConv, ChebConv
from torch_geometric.data import Data
import matplotlib.pyplot as plt
import seaborn as sns
from shapely.geometry import Point
import warnings
warnings.filterwarnings('ignore')

# 设置设备和数据类型
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
dtype = torch.float16  # 使用float16降低内存使用

class TemporalGCN(nn.Module):
    """
    T-GCN模型，结合图卷积网络(GCN)和门控循环单元(GRU)
    """
    def __init__(self, node_num, dim_in, dim_out, cheb_k=3, embed_dim=64, num_layers=2, dropout=0.3):
        super(TemporalGCN, self).__init__()
        self.node_num = node_num
        self.dim_in = dim_in
        self.dim_out = dim_out
        self.cheb_k = cheb_k
        self.embed_dim = embed_dim
        self.num_layers = num_layers
        
        # 空间特征提取 - 使用Chebyshev图卷积
        self.spatial_conv = ChebConv(dim_in, embed_dim, cheb_k)
        
        # 时间特征提取 - 多层GRU
        self.temporal_conv = nn.GRU(
            input_size=embed_dim,
            hidden_size=embed_dim,
            num_layers=num_layers,
            dropout=dropout if num_layers > 1 else 0,
            batch_first=True
        )
        
        # 特征融合层
        self.feature_fusion = nn.Sequential(
            nn.Linear(embed_dim, embed_dim * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim * 2, embed_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 输出层
        self.output_layer = nn.Linear(embed_dim, dim_out)
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.GRU):
                for name, param in m.named_parameters():
                    if 'weight' in name:
                        nn.init.xavier_uniform_(param)
                    elif 'bias' in name:
                        nn.init.constant_(param, 0)
    
    def forward(self, x, edge_index, edge_weight=None):
        # x: (batch_size, time_steps, node_num, features)
        batch_size, time_steps, node_num, features = x.shape
        
        # 存储每个时间步的空间特征
        spatial_features = []
        
        for t in range(time_steps):
            # 获取当前时间步的数据
            x_t = x[:, t, :, :].reshape(-1, features)  # (batch_size * node_num, features)
            
            # 空间卷积
            spatial_out = self.spatial_conv(x_t, edge_index, edge_weight)
            spatial_out = torch.relu(spatial_out)
            
            # 重塑为 (batch_size, node_num, embed_dim)
            spatial_out = spatial_out.reshape(batch_size, node_num, -1)
            spatial_features.append(spatial_out)
        
        # 合并时间维度: (batch_size, time_steps, node_num, embed_dim)
        spatial_features = torch.stack(spatial_features, dim=1)
        
        # 时间卷积 - 对每个节点分别处理
        temporal_outputs = []
        for i in range(node_num):
            node_features = spatial_features[:, :, i, :]  # (batch_size, time_steps, embed_dim)
            temporal_out, _ = self.temporal_conv(node_features)
            temporal_outputs.append(temporal_out[:, -1, :])  # 取最后一个时间步
        
        # 合并节点维度: (batch_size, node_num, embed_dim)
        temporal_output = torch.stack(temporal_outputs, dim=1)
        
        # 特征融合
        fused_features = self.feature_fusion(temporal_output)
        
        # 输出预测
        output = self.output_layer(fused_features)
        
        return output

class SubwayPredictor:
    """地铁客流预测器"""
    
    def __init__(self, data_path="C:\\Users\\<USER>\\Desktop\\接驳\\"):
        self.data_path = data_path
        self.scaler_in = StandardScaler()
        self.scaler_out = StandardScaler()
        self.scaler_od = StandardScaler()
        self.device = device
        self.dtype = dtype
        
    def load_data(self):
        """加载所有数据文件"""
        print("正在加载数据...")
        
        # 加载进站数据
        self.in_data = gpd.read_file(f"{self.data_path}in_500_with_coords.shp")
        print(f"进站数据形状: {self.in_data.shape}")
        
        # 加载出站数据
        self.out_data = gpd.read_file(f"{self.data_path}out_500_with_coords.shp")
        print(f"出站数据形状: {self.out_data.shape}")
        
        # 加载OD数据
        self.od_data = pd.read_csv(f"{self.data_path}updated_北京市_subway_od_2024_modified.csv")
        print(f"OD数据形状: {self.od_data.shape}")
        
        # 加载站点特征数据
        self.station_features = pd.read_csv(f"{self.data_path}station_features_result.csv")
        print(f"站点特征数据形状: {self.station_features.shape}")
        
    def extract_grid_info(self):
        """从shp文件中提取栅格地理信息"""
        print("提取栅格地理信息...")
        
        # 从进出站数据中提取栅格中心坐标
        grid_coords = {}
        
        for gdf, name in [(self.in_data, 'in'), (self.out_data, 'out')]:
            for idx, row in gdf.iterrows():
                if hasattr(row.geometry, 'centroid'):
                    # 获取栅格中心点
                    centroid = row.geometry.centroid
                    station = row['station']
                    if station not in grid_coords:
                        grid_coords[station] = {
                            'longitude': centroid.x,
                            'latitude': centroid.y
                        }
        
        self.grid_coords = grid_coords
        print(f"提取到 {len(grid_coords)} 个栅格的地理信息")
        
    def preprocess_data(self):
        """数据预处理"""
        print("开始数据预处理...")
        
        # 提取栅格信息
        self.extract_grid_info()
        
        # 获取共同站点
        in_stations = set(self.in_data['station'].unique())
        out_stations = set(self.out_data['station'].unique())
        feature_stations = set(self.station_features['站名'].unique())
        
        self.common_stations = list(in_stations & out_stations & feature_stations)
        print(f"共同站点数量: {len(self.common_stations)}")
        
        # 过滤数据
        self.in_data = self.in_data[self.in_data['station'].isin(self.common_stations)]
        self.out_data = self.out_data[self.out_data['station'].isin(self.common_stations)]
        self.od_data = self.od_data[
            (self.od_data['o_rawname'].isin(self.common_stations)) &
            (self.od_data['d_rawname'].isin(self.common_stations))
        ]
        
        # 创建站点到索引的映射
        self.station_to_idx = {station: idx for idx, station in enumerate(self.common_stations)}
        self.idx_to_station = {idx: station for station, idx in self.station_to_idx.items()}
        
        # 准备特征数据
        self.prepare_features()
        
        # 构建图结构
        self.build_graph()
        
    def prepare_features(self):
        """准备特征数据"""
        print("准备特征数据...")
        
        # 处理站点特征
        station_features_dict = {}
        feature_cols = [col for col in self.station_features.columns if col != '站名']
        
        for _, row in self.station_features.iterrows():
            station = row['站名']
            if station in self.common_stations:
                features = row[feature_cols].values.astype(np.float32)
                station_features_dict[station] = features
        
        # 创建特征矩阵
        self.node_features = np.zeros((len(self.common_stations), len(feature_cols)), dtype=np.float32)
        for i, station in enumerate(self.common_stations):
            if station in station_features_dict:
                self.node_features[i] = station_features_dict[station]
        
        # 标准化特征
        self.node_features = StandardScaler().fit_transform(self.node_features)
        
        print(f"节点特征矩阵形状: {self.node_features.shape}")
        
    def build_graph(self):
        """构建图结构"""
        print("构建图结构...")
        
        # 获取站点坐标
        coords = []
        for station in self.common_stations:
            if station in self.grid_coords:
                coords.append([self.grid_coords[station]['longitude'], 
                              self.grid_coords[station]['latitude']])
            else:
                # 从原始数据中查找坐标
                coord_found = False
                for gdf in [self.in_data, self.out_data]:
                    station_data = gdf[gdf['station'] == station]
                    if not station_data.empty and 'longitude' in station_data.columns:
                        coords.append([station_data.iloc[0]['longitude'], 
                                     station_data.iloc[0]['latitude']])
                        coord_found = True
                        break
                
                if not coord_found:
                    coords.append([0, 0])  # 默认坐标
        
        coords = np.array(coords)
        
        # 计算距离矩阵
        distances = cdist(coords, coords, metric='euclidean')
        
        # 构建邻接矩阵（使用距离阈值或k近邻）
        k = min(10, len(self.common_stations) - 1)  # k近邻
        adjacency = np.zeros((len(self.common_stations), len(self.common_stations)))
        
        for i in range(len(self.common_stations)):
            # 找到k个最近邻居
            nearest_indices = np.argsort(distances[i])[1:k+1]  # 排除自己
            for j in nearest_indices:
                weight = 1.0 / (1.0 + distances[i, j])  # 距离越近权重越大
                adjacency[i, j] = weight
                adjacency[j, i] = weight  # 对称图
        
        # 转换为PyTorch格式
        edge_index = []
        edge_weight = []
        
        for i in range(len(self.common_stations)):
            for j in range(len(self.common_stations)):
                if adjacency[i, j] > 0:
                    edge_index.append([i, j])
                    edge_weight.append(adjacency[i, j])
        
        self.edge_index = torch.tensor(edge_index, dtype=torch.long).t().contiguous()
        self.edge_weight = torch.tensor(edge_weight, dtype=self.dtype)
        
        print(f"图结构: {len(self.common_stations)} 个节点, {len(edge_index)} 条边")
        
    def create_sequences(self, data, target_col, sequence_length=12):
        """创建时序数据"""
        sequences = []
        targets = []
        
        # 按站点分组
        for station in self.common_stations:
            station_data = data[data['station'] == station].sort_values('hour')
            if len(station_data) < sequence_length + 1:
                continue
                
            values = station_data[target_col].values
            
            for i in range(len(values) - sequence_length):
                seq = values[i:i+sequence_length]
                target = values[i+sequence_length]
                sequences.append(seq)
                targets.append(target)
        
        return np.array(sequences), np.array(targets)
    
    def prepare_model_data(self, sequence_length=12):
        """准备模型训练数据"""
        print("准备模型训练数据...")
        
        # 创建时序数据
        print("创建进站时序数据...")
        in_sequences, in_targets = self.create_sequences(self.in_data, 'count', sequence_length)
        
        print("创建出站时序数据...")
        out_sequences, out_targets = self.create_sequences(self.out_data, 'count', sequence_length)
        
        # 数据标准化
        in_sequences_scaled = self.scaler_in.fit_transform(in_sequences.reshape(-1, 1)).reshape(in_sequences.shape)
        in_targets_scaled = self.scaler_in.transform(in_targets.reshape(-1, 1)).flatten()
        
        out_sequences_scaled = self.scaler_out.fit_transform(out_sequences.reshape(-1, 1)).reshape(out_sequences.shape)
        out_targets_scaled = self.scaler_out.transform(out_targets.reshape(-1, 1)).flatten()
        
        # 转换为PyTorch张量
        self.in_sequences = torch.tensor(in_sequences_scaled, dtype=self.dtype)
        self.in_targets = torch.tensor(in_targets_scaled, dtype=self.dtype)
        self.out_sequences = torch.tensor(out_sequences_scaled, dtype=self.dtype)
        self.out_targets = torch.tensor(out_targets_scaled, dtype=self.dtype)
        
        print(f"进站序列形状: {self.in_sequences.shape}")
        print(f"出站序列形状: {self.out_sequences.shape}")
        
        # 数据分割
        self.split_data()
        
    def split_data(self, test_size=0.2):
        """分割训练和测试数据"""
        # 进站数据分割
        indices = np.arange(len(self.in_sequences))
        train_idx, test_idx = train_test_split(indices, test_size=test_size, random_state=42)
        
        self.in_train_seq = self.in_sequences[train_idx]
        self.in_train_targets = self.in_targets[train_idx]
        self.in_test_seq = self.in_sequences[test_idx]
        self.in_test_targets = self.in_targets[test_idx]
        
        # 出站数据分割
        indices = np.arange(len(self.out_sequences))
        train_idx, test_idx = train_test_split(indices, test_size=test_size, random_state=42)
        
        self.out_train_seq = self.out_sequences[train_idx]
        self.out_train_targets = self.out_targets[train_idx]
        self.out_test_seq = self.out_sequences[test_idx]
        self.out_test_targets = self.out_targets[test_idx]
        
    def train_model(self, model, train_sequences, train_targets, val_sequences, val_targets, 
                   epochs=100, batch_size=32):
        """训练模型"""
        model.to(self.device)
        optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-5)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
        criterion = nn.MSELoss()
        
        train_losses = []
        val_losses = []
        train_maes = []
        val_maes = []
        
        best_val_loss = float('inf')
        patience_counter = 0
        early_stopping_patience = 20
        
        for epoch in range(epochs):
            model.train()
            epoch_train_loss = 0
            epoch_train_mae = 0
            num_batches = 0
            
            # 训练
            for i in range(0, len(train_sequences), batch_size):
                batch_seq = train_sequences[i:i+batch_size].to(self.device)
                batch_targets = train_targets[i:i+batch_size].to(self.device)
                
                # 重塑数据为 (batch_size, time_steps, node_num, features)
                batch_size_actual = batch_seq.shape[0]
                time_steps = batch_seq.shape[1]

                # 扩展序列数据以匹配节点数
                batch_seq_expanded = batch_seq.unsqueeze(2).repeat(
                    1, 1, len(self.common_stations), 1
                )
                
                optimizer.zero_grad()
                outputs = model(batch_seq_expanded, self.edge_index.to(self.device), 
                              self.edge_weight.to(self.device))
                
                # 取第一个节点的输出（或者可以改为平均）
                outputs = outputs[:, 0, 0]  # (batch_size,)
                
                loss = criterion(outputs, batch_targets)
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                epoch_train_loss += loss.item()
                epoch_train_mae += mean_absolute_error(
                    batch_targets.cpu().detach().numpy(),
                    outputs.cpu().detach().numpy()
                )
                num_batches += 1
            
            # 验证
            model.eval()
            val_loss = 0
            val_mae = 0
            val_batches = 0
            
            with torch.no_grad():
                for i in range(0, len(val_sequences), batch_size):
                    batch_seq = val_sequences[i:i+batch_size].to(self.device)
                    batch_targets = val_targets[i:i+batch_size].to(self.device)
                    
                    batch_size_actual = batch_seq.shape[0]
                    time_steps = batch_seq.shape[1]
                    
                    batch_seq_expanded = batch_seq.unsqueeze(2).repeat(
                        1, 1, len(self.common_stations), 1
                    )
                    
                    outputs = model(batch_seq_expanded, self.edge_index.to(self.device),
                                  self.edge_weight.to(self.device))
                    outputs = outputs[:, 0, 0]
                    
                    loss = criterion(outputs, batch_targets)
                    val_loss += loss.item()
                    val_mae += mean_absolute_error(
                        batch_targets.cpu().numpy(),
                        outputs.cpu().numpy()
                    )
                    val_batches += 1
            
            # 记录指标
            avg_train_loss = epoch_train_loss / num_batches
            avg_val_loss = val_loss / val_batches
            avg_train_mae = epoch_train_mae / num_batches
            avg_val_mae = val_mae / val_batches
            
            train_losses.append(avg_train_loss)
            val_losses.append(avg_val_loss)
            train_maes.append(avg_train_mae)
            val_maes.append(avg_val_mae)
            
            scheduler.step(avg_val_loss)
            
            # 早停检查
            if avg_val_loss < best_val_loss:
                best_val_loss = avg_val_loss
                patience_counter = 0
                # 保存最佳模型
                torch.save(model.state_dict(), 'best_model.pth')
            else:
                patience_counter += 1
            
            if patience_counter >= early_stopping_patience:
                print(f"早停于第 {epoch+1} 轮")
                break
            
            if (epoch + 1) % 10 == 0:
                print(f'Epoch {epoch+1}/{epochs}:')
                print(f'  训练Loss: {avg_train_loss:.4f}, 训练MAE: {avg_train_mae:.4f}')
                print(f'  验证Loss: {avg_val_loss:.4f}, 验证MAE: {avg_val_mae:.4f}')
                print(f'  学习率: {optimizer.param_groups[0]["lr"]:.6f}')
        
        # 加载最佳模型
        model.load_state_dict(torch.load('best_model.pth'))
        
        return train_losses, val_losses, train_maes, val_maes
    
    def predict_and_save(self):
        """预测并保存结果"""
        print("开始预测并保存结果...")
        
        # 创建模型
        node_num = len(self.common_stations)
        feature_dim = self.node_features.shape[1]
        
        # 训练进站预测模型
        print("训练进站预测模型...")
        in_model = TemporalGCN(
            node_num=node_num,
            dim_in=1,  # 单特征输入
            dim_out=1,
            cheb_k=3,
            embed_dim=64,
            num_layers=2,
            dropout=0.3
        )
        
        # 分割验证集
        val_size = len(self.in_train_seq) // 5
        in_val_seq = self.in_train_seq[-val_size:]
        in_val_targets = self.in_train_targets[-val_size:]
        in_train_seq = self.in_train_seq[:-val_size]
        in_train_targets = self.in_train_targets[:-val_size]
        
        in_train_losses, in_val_losses, in_train_maes, in_val_maes = self.train_model(
            in_model, in_train_seq, in_train_targets, in_val_seq, in_val_targets
        )
        
        # 训练出站预测模型
        print("训练出站预测模型...")
        out_model = TemporalGCN(
            node_num=node_num,
            dim_in=1,
            dim_out=1,
            cheb_k=3,
            embed_dim=64,
            num_layers=2,
            dropout=0.3
        )
        
        val_size = len(self.out_train_seq) // 5
        out_val_seq = self.out_train_seq[-val_size:]
        out_val_targets = self.out_train_targets[-val_size:]
        out_train_seq = self.out_train_seq[:-val_size]
        out_train_targets = self.out_train_targets[:-val_size]
        
        out_train_losses, out_val_losses, out_train_maes, out_val_maes = self.train_model(
            out_model, out_train_seq, out_train_targets, out_val_seq, out_val_targets
        )
        
        # 进行预测
        print("进行预测...")
        self.make_predictions(in_model, out_model)
        
        # 绘制训练曲线
        self.plot_training_curves(in_train_losses, in_val_losses, in_train_maes, in_val_maes, "进站")
        self.plot_training_curves(out_train_losses, out_val_losses, out_train_maes, out_val_maes, "出站")
    
    def make_predictions(self, in_model, out_model):
        """进行预测"""
        in_model.eval()
        out_model.eval()
        
        with torch.no_grad():
            # 进站预测
            batch_size = 32
            in_predictions = []
            
            for i in range(0, len(self.in_test_seq), batch_size):
                batch_seq = self.in_test_seq[i:i+batch_size].to(self.device)
                batch_size_actual = batch_seq.shape[0]
                time_steps = batch_seq.shape[1]
                
                batch_seq_expanded = batch_seq.unsqueeze(2).repeat(
                    1, 1, len(self.common_stations), 1
                )
                
                outputs = in_model(batch_seq_expanded, self.edge_index.to(self.device),
                                 self.edge_weight.to(self.device))
                outputs = outputs[:, 0, 0]
                in_predictions.extend(outputs.cpu().numpy())
            
            # 出站预测
            out_predictions = []
            
            for i in range(0, len(self.out_test_seq), batch_size):
                batch_seq = self.out_test_seq[i:i+batch_size].to(self.device)
                batch_size_actual = batch_seq.shape[0]
                time_steps = batch_seq.shape[1]
                
                batch_seq_expanded = batch_seq.unsqueeze(2).repeat(
                    1, 1, len(self.common_stations), 1
                )
                
                outputs = out_model(batch_seq_expanded, self.edge_index.to(self.device),
                                  self.edge_weight.to(self.device))
                outputs = outputs[:, 0, 0]
                out_predictions.extend(outputs.cpu().numpy())
        
        # 反标准化
        in_predictions = self.scaler_in.inverse_transform(
            np.array(in_predictions).reshape(-1, 1)
        ).flatten()
        in_true = self.scaler_in.inverse_transform(
            self.in_test_targets.cpu().numpy().reshape(-1, 1)
        ).flatten()
        
        out_predictions = self.scaler_out.inverse_transform(
            np.array(out_predictions).reshape(-1, 1)
        ).flatten()
        out_true = self.scaler_out.inverse_transform(
            self.out_test_targets.cpu().numpy().reshape(-1, 1)
        ).flatten()
        
        # 计算评估指标
        in_mae = mean_absolute_error(in_true, in_predictions)
        in_mse = mean_squared_error(in_true, in_predictions)
        out_mae = mean_absolute_error(out_true, out_predictions)
        out_mse = mean_squared_error(out_true, out_predictions)
        
        print(f"\n最终预测结果:")
        print(f"进站预测 - MAE: {in_mae:.4f}, MSE: {in_mse:.4f}, RMSE: {np.sqrt(in_mse):.4f}")
        print(f"出站预测 - MAE: {out_mae:.4f}, MSE: {out_mse:.4f}, RMSE: {np.sqrt(out_mse):.4f}")
        
        # 保存预测结果
        self.save_predictions(in_predictions, in_true, out_predictions, out_true)
    
    def save_predictions(self, in_pred, in_true, out_pred, out_true):
        """保存预测结果"""
        print("保存预测结果...")
        
        # 创建预测比较数据
        comparison_data = []
        
        for i, (true_val, pred_val) in enumerate(zip(in_true, in_pred)):
            comparison_data.append({
                'Type': 'In',
                'True_Value': float(true_val),
                'Predicted_Value': float(pred_val)
            })
        
        for i, (true_val, pred_val) in enumerate(zip(out_true, out_pred)):
            comparison_data.append({
                'Type': 'Out', 
                'True_Value': float(true_val),
                'Predicted_Value': float(pred_val)
            })
        
        comparison_df = pd.DataFrame(comparison_data)
        comparison_df.to_csv(f"{self.data_path}prediction_comparison.csv", index=False)
        
        print("预测结果已保存!")
    
    def plot_training_curves(self, train_losses, val_losses, train_maes, val_maes, model_type):
        """绘制训练曲线"""
        fig, ((ax1, ax2)) = plt.subplots(1, 2, figsize=(15, 5))
        
        # 损失曲线
        ax1.plot(train_losses, label='训练损失', color='blue')
        ax1.plot(val_losses, label='验证损失', color='red')
        ax1.set_title(f'{model_type}模型损失曲线')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.legend()
        ax1.grid(True)
        
        # MAE曲线
        ax2.plot(train_maes, label='训练MAE', color='green')
        ax2.plot(val_maes, label='验证MAE', color='orange')
        ax2.set_title(f'{model_type}模型MAE曲线')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('MAE')
        ax2.legend()
        ax2.grid(True)
        
        plt.tight_layout()
        plt.savefig(f"{self.data_path}{model_type}_training_curves.png", dpi=300, bbox_inches='tight')
        plt.show()
    
    def run_complete_pipeline(self):
        """运行完整的预测流程"""
        print("开始运行T-GCN地铁客流预测系统...")
        print("="*60)
        
        # 1. 加载数据
        self.load_data()
        print("="*60)
        
        # 2. 数据预处理
        self.preprocess_data()
        print("="*60)
        
        # 3. 准备模型数据
        self.prepare_model_data(sequence_length=12)
        print("="*60)
        
        # 4. 训练模型并预测
        self.predict_and_save()
        print("="*60)
        
        print("T-GCN地铁客流预测完成!")

# OD流量预测器类
class ODFlowPredictor:
    """OD流量预测器"""
    
    def __init__(self, data_path="C:\\Users\\<USER>\\Desktop\\接驳\\"):
        self.data_path = data_path
        self.scaler = StandardScaler()
        self.device = device
        self.dtype = dtype
        
    def load_and_preprocess_od_data(self, od_data, common_stations):
        """加载和预处理OD数据"""
        print("预处理OD数据...")
        
        # 过滤共同站点
        od_filtered = od_data[
            (od_data['o_rawname'].isin(common_stations)) &
            (od_data['d_rawname'].isin(common_stations))
        ].copy()
        
        # 创建站点映射
        station_to_idx = {station: idx for idx, station in enumerate(common_stations)}
        
        # 添加起点和终点索引
        od_filtered['origin_idx'] = od_filtered['o_rawname'].map(station_to_idx)
        od_filtered['dest_idx'] = od_filtered['d_rawname'].map(station_to_idx)
        
        # 按时间和OD对聚合
        od_grouped = od_filtered.groupby(['origin_idx', 'dest_idx', 'hour']).agg({
            'trip': 'sum',
            'surface_distance': 'mean',
            'translate': 'mean',
            'time': 'mean',
            'wait_time': 'mean'
        }).reset_index()
        
        return od_grouped
    
    def create_od_features(self, od_data):
        """创建OD特征"""
        features = []
        targets = []
        
        # 为每个OD对创建特征
        for _, row in od_data.iterrows():
            feature = [
                row['origin_idx'],
                row['dest_idx'], 
                row['hour'],
                row['surface_distance'],
                row['translate'],
                row['time'],
                row['wait_time']
            ]
            features.append(feature)
            targets.append(row['trip'])
        
        return np.array(features), np.array(targets)
    
    def train_od_model(self, features, targets, common_stations, edge_index, edge_weight):
        """训练OD预测模型"""
        print("训练OD流量预测模型...")
        
        # 数据分割
        X_train, X_test, y_train, y_test = train_test_split(
            features, targets, test_size=0.2, random_state=42
        )
        
        # 标准化
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        y_train_scaled = StandardScaler().fit_transform(y_train.reshape(-1, 1)).flatten()
        y_test_scaled = StandardScaler().fit_transform(y_test.reshape(-1, 1)).flatten()
        
        # 创建OD预测模型
        class ODTemporalGCN(nn.Module):
            def __init__(self, node_num, feature_dim, hidden_dim=128, output_dim=1):
                super(ODTemporalGCN, self).__init__()
                self.node_num = node_num
                
                # 特征编码器
                self.feature_encoder = nn.Sequential(
                    nn.Linear(feature_dim, hidden_dim),
                    nn.ReLU(),
                    nn.Dropout(0.3),
                    nn.Linear(hidden_dim, hidden_dim//2),
                    nn.ReLU(),
                    nn.Dropout(0.3)
                )
                
                # 图卷积层
                self.gcn1 = GCNConv(hidden_dim//2, hidden_dim//2)
                self.gcn2 = GCNConv(hidden_dim//2, hidden_dim//4)
                
                # 输出层
                self.output_layer = nn.Sequential(
                    nn.Linear(hidden_dim//4 * 2, hidden_dim//4),  # *2因为concat origin和dest
                    nn.ReLU(),
                    nn.Dropout(0.3),
                    nn.Linear(hidden_dim//4, output_dim)
                )
                
            def forward(self, x, edge_index, edge_weight=None):
                batch_size = x.shape[0]
                
                # 特征编码
                encoded = self.feature_encoder(x[:, 2:])  # 除去origin_idx和dest_idx
                
                # 创建节点特征矩阵
                node_features = torch.zeros(self.node_num, encoded.shape[1], 
                                          device=encoded.device, dtype=encoded.dtype)
                
                # 聚合同一节点的特征
                for i in range(batch_size):
                    origin_idx = int(x[i, 0].item())
                    dest_idx = int(x[i, 1].item())
                    if origin_idx < self.node_num:
                        node_features[origin_idx] += encoded[i] / batch_size
                    if dest_idx < self.node_num:
                        node_features[dest_idx] += encoded[i] / batch_size
                
                # 图卷积
                h1 = torch.relu(self.gcn1(node_features, edge_index, edge_weight))
                h2 = torch.relu(self.gcn2(h1, edge_index, edge_weight))
                
                # 为每个OD对提取特征
                od_features = []
                for i in range(batch_size):
                    origin_idx = int(x[i, 0].item())
                    dest_idx = int(x[i, 1].item())
                    
                    if origin_idx < self.node_num and dest_idx < self.node_num:
                        od_feat = torch.cat([h2[origin_idx], h2[dest_idx]], dim=0)
                    else:
                        od_feat = torch.zeros(h2.shape[1] * 2, device=h2.device, dtype=h2.dtype)
                    
                    od_features.append(od_feat)
                
                od_features = torch.stack(od_features)
                
                # 输出预测
                output = self.output_layer(od_features)
                return output.squeeze()
        
        # 创建模型
        od_model = ODTemporalGCN(
            node_num=len(common_stations),
            feature_dim=X_train_scaled.shape[1] - 2,  # 减去origin_idx和dest_idx
            hidden_dim=128
        ).to(self.device)
        
        # 训练设置
        optimizer = optim.Adam(od_model.parameters(), lr=0.001, weight_decay=1e-5)
        criterion = nn.MSELoss()
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
        
        # 转换为tensor
        X_train_tensor = torch.tensor(X_train_scaled, dtype=self.dtype).to(self.device)
        y_train_tensor = torch.tensor(y_train_scaled, dtype=self.dtype).to(self.device)
        X_test_tensor = torch.tensor(X_test_scaled, dtype=self.dtype).to(self.device)
        y_test_tensor = torch.tensor(y_test_scaled, dtype=self.dtype).to(self.device)
        
        # 训练循环
        train_losses = []
        test_losses = []
        train_maes = []
        test_maes = []
        
        batch_size = 512
        epochs = 100
        
        for epoch in range(epochs):
            od_model.train()
            epoch_train_loss = 0
            epoch_train_mae = 0
            num_batches = 0
            
            # 训练
            for i in range(0, len(X_train_tensor), batch_size):
                batch_x = X_train_tensor[i:i+batch_size]
                batch_y = y_train_tensor[i:i+batch_size]
                
                optimizer.zero_grad()
                outputs = od_model(batch_x, edge_index.to(self.device), edge_weight.to(self.device))
                loss = criterion(outputs, batch_y)
                loss.backward()
                torch.nn.utils.clip_grad_norm_(od_model.parameters(), max_norm=1.0)
                optimizer.step()
                
                epoch_train_loss += loss.item()
                epoch_train_mae += mean_absolute_error(
                    batch_y.cpu().detach().numpy(),
                    outputs.cpu().detach().numpy()
                )
                num_batches += 1
            
            # 测试
            od_model.eval()
            with torch.no_grad():
                test_outputs = od_model(X_test_tensor, edge_index.to(self.device), edge_weight.to(self.device))
                test_loss = criterion(test_outputs, y_test_tensor).item()
                test_mae = mean_absolute_error(
                    y_test_tensor.cpu().numpy(),
                    test_outputs.cpu().numpy()
                )
            
            # 记录指标
            avg_train_loss = epoch_train_loss / num_batches
            train_losses.append(avg_train_loss)
            test_losses.append(test_loss)
            train_maes.append(epoch_train_mae / num_batches)
            test_maes.append(test_mae)
            
            scheduler.step(test_loss)
            
            if (epoch + 1) % 20 == 0:
                print(f'OD模型 Epoch {epoch+1}/{epochs}:')
                print(f'  训练Loss: {avg_train_loss:.4f}, 训练MAE: {train_maes[-1]:.4f}')
                print(f'  测试Loss: {test_loss:.4f}, 测试MAE: {test_mae:.4f}')
        
        # 最终预测
        od_model.eval()
        with torch.no_grad():
            final_predictions = od_model(X_test_tensor, edge_index.to(self.device), edge_weight.to(self.device))
            final_predictions = final_predictions.cpu().numpy()
        
        # 反标准化（这里需要保存标准化器）
        print(f"\nOD流量预测完成!")
        print(f"最终测试MAE: {test_maes[-1]:.4f}")
        print(f"最终测试Loss: {test_losses[-1]:.4f}")
        
        # 保存OD预测结果
        od_results = pd.DataFrame({
            'True_Value': y_test,
            'Predicted_Value': final_predictions,
            'Origin_Idx': X_test[:, 0],
            'Dest_Idx': X_test[:, 1],
            'Hour': X_test[:, 2]
        })
        od_results.to_csv(f"{self.data_path}od_predictions.csv", index=False)
        
        return train_losses, test_losses, train_maes, test_maes

# 主执行函数
def main():
    """主函数"""
    print("启动T-GCN地铁客流预测系统")
    print("GPU可用:", torch.cuda.is_available())
    if torch.cuda.is_available():
        print("GPU设备:", torch.cuda.get_device_name())
        print("GPU内存:", torch.cuda.get_device_properties(0).total_memory / 1024**3, "GB")
    
    try:
        # 创建预测器实例
        predictor = SubwayPredictor()
        
        # 运行完整流程
        predictor.run_complete_pipeline()
        
        # 运行OD预测
        print("\n" + "="*60)
        print("开始OD流量预测...")
        
        od_predictor = ODFlowPredictor(predictor.data_path)
        od_data_processed = od_predictor.load_and_preprocess_od_data(
            predictor.od_data, predictor.common_stations
        )
        
        od_features, od_targets = od_predictor.create_od_features(od_data_processed)
        
        od_train_losses, od_test_losses, od_train_maes, od_test_maes = od_predictor.train_od_model(
            od_features, od_targets, predictor.common_stations, 
            predictor.edge_index, predictor.edge_weight
        )
        
        # 绘制OD训练曲线
        predictor.plot_training_curves(od_train_losses, od_test_losses, 
                                     od_train_maes, od_test_maes, "OD流量")
        
        print("\n" + "="*60)
        print("所有预测任务完成!")
        print("结果文件已保存到:", predictor.data_path)
        print("- prediction_comparison.csv: 进出站预测比较")
        print("- od_predictions.csv: OD流量预测结果")
        print("- *_training_curves.png: 训练过程曲线图")
        
    except Exception as e:
        print(f"执行出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()