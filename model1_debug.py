# -*- coding: utf-8 -*-
"""
Script to load best_model.pth and generate predictions with metrics for grid-to-station,
station-to-station, and station-to-grid flows.
"""

import os
os.environ["DGLBACKEND"] = "pytorch"

import torch
import pandas as pd
import geopandas as gpd
import numpy as np
import dgl
from torch.nn import TransformerEncoder, TransformerEncoderLayer
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import mean_absolute_error, mean_squared_error
from tqdm import tqdm

# 设置随机种子和设备
torch.manual_seed(42)
np.random.seed(42)
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"Using device: {device}")

# 数据加载
def load_data(in_shp, out_shp, od_csv, features_csv):
    in_gdf = gpd.read_file(in_shp)
    out_gdf = gpd.read_file(out_shp)
    od_df = pd.read_csv(od_csv)
    features_df = pd.read_csv(features_csv)
    print("Data loaded successfully.")
    return in_gdf, out_gdf, od_df, features_df

# 数据预处理
def preprocess_data(in_gdf, out_gdf, od_df, features_df):
    in_gdf = in_gdf[~in_gdf['hour'].isin([0, 1, 2, 3, 4])].copy()
    out_gdf = out_gdf[~out_gdf['hour'].isin([0, 1, 2, 3, 4])].copy()
    od_df = od_df[~od_df['hour'].isin([0, 1, 2, 3, 4])].copy()
    features_df = features_df.copy()
    
    in_gdf = in_gdf.dropna(subset=['station', 'hour', 'count'])
    out_gdf = out_gdf.dropna(subset=['station', 'hour', 'count'])
    od_df = od_df.dropna(subset=['o_rawname', 'd_rawname', 'hour', 'trip', 'surface_distance', 'translate', 'time', 'wait_time'])
    features_df = features_df.dropna(subset=['站名'])
    
    all_stations = pd.concat([
        in_gdf['station'], out_gdf['station'],
        od_df['o_rawname'], od_df['d_rawname'],
        features_df['站名']
    ]).dropna().unique()
    
    station_encoder = LabelEncoder()
    station_encoder.fit(all_stations)
    
    in_gdf.loc[:, 'station_id'] = station_encoder.transform(in_gdf['station'])
    out_gdf.loc[:, 'station_id'] = station_encoder.transform(out_gdf['station'])
    od_df.loc[:, 'o_station_id'] = station_encoder.transform(od_df['o_rawname'])
    od_df.loc[:, 'd_station_id'] = station_encoder.transform(od_df['d_rawname'])
    
    scaler = StandardScaler()
    od_features = ['surface_distance', 'translate', 'time', 'wait_time']
    for col in od_features:
        if od_df[col].std() == 0:
            od_df.loc[:, col] = 0
        else:
            od_df.loc[:, col] = scaler.fit_transform(od_df[[col]]).flatten()
    
    feature_cols = [col for col in features_df.columns if col != '站名']
    for col in feature_cols:
        if features_df[col].std() == 0:
            features_df.loc[:, col] = 0
        else:
            features_df.loc[:, col] = scaler.fit_transform(features_df[[col]]).flatten()
    
    features_df.loc[:, 'station_id'] = features_df['站名'].apply(
        lambda x: station_encoder.transform([x])[0] if x in station_encoder.classes_ else -1
    )
    
    def encode_hour(hour):
        hour = hour % 24
        sin = np.sin(2 * np.pi * hour / 24)
        cos = np.cos(2 * np.pi * hour / 24)
        return sin, cos
    
    in_gdf.loc[:, ['hour_sin', 'hour_cos']] = pd.DataFrame(
        in_gdf['hour'].apply(encode_hour).tolist(), index=in_gdf.index, columns=['hour_sin', 'hour_cos']
    )
    out_gdf.loc[:, ['hour_sin', 'hour_cos']] = pd.DataFrame(
        out_gdf['hour'].apply(encode_hour).tolist(), index=out_gdf.index, columns=['hour_sin', 'hour_cos']
    )
    od_df.loc[:, ['hour_sin', 'hour_cos']] = pd.DataFrame(
        od_df['hour'].apply(encode_hour).tolist(), index=od_df.index, columns=['hour_sin', 'hour_cos']
    )
    
    return in_gdf, out_gdf, od_df, features_df, station_encoder, feature_cols

# 创建图
def create_graph(in_gdf, out_gdf, od_df, station_encoder):
    num_nodes = len(station_encoder.classes_)
    src, dst = [], []
    for _, row in od_df.iterrows():
        src.append(row['o_station_id'])
        dst.append(row['d_station_id'])
    g = dgl.graph((src, dst), num_nodes=num_nodes).to(device)
    g = dgl.add_self_loop(g)
    return g

# 数据集
class TrafficDataset(torch.utils.data.Dataset):
    def __init__(self, in_gdf, out_gdf, od_df, features_df, feature_cols):
        self.in_gdf = in_gdf
        self.out_gdf = out_gdf
        self.od_df = od_df
        self.features_df = features_df
        self.feature_cols = feature_cols
        
    def __len__(self):
        return len(self.in_gdf) + len(self.out_gdf) + len(self.od_df)
    
    def __getitem__(self, idx):
        if idx < len(self.in_gdf):
            row = self.in_gdf.iloc[idx]
            station_id = row['station_id']
            geo_features = self.features_df[self.features_df['station_id'] == station_id][self.feature_cols].values
            geo_features = geo_features[0] if len(geo_features) > 0 else np.zeros(len(self.feature_cols))
            return {
                'type': 'in',
                'station_id': torch.tensor(station_id, dtype=torch.long),
                'hour': torch.tensor([row['hour_sin'], row['hour_cos']], dtype=torch.float),
                'geo_features': torch.tensor(geo_features, dtype=torch.float),
                'count': torch.tensor(row['count'], dtype=torch.float),
                'index': idx,
                'geometry': row['geometry'],
                'hour_raw': row['hour']
            }
        elif idx < len(self.in_gdf) + len(self.out_gdf):
            row = self.out_gdf.iloc[idx - len(self.in_gdf)]
            station_id = row['station_id']
            geo_features = self.features_df[self.features_df['station_id'] == station_id][self.feature_cols].values
            geo_features = geo_features[0] if len(geo_features) > 0 else np.zeros(len(self.feature_cols))
            return {
                'type': 'out',
                'station_id': torch.tensor(station_id, dtype=torch.long),
                'hour': torch.tensor([row['hour_sin'], row['hour_cos']], dtype=torch.float),
                'geo_features': torch.tensor(geo_features, dtype=torch.float),
                'count': torch.tensor(row['count'], dtype=torch.float),
                'index': idx - len(self.in_gdf),
                'geometry': row['geometry'],
                'hour_raw': row['hour']
            }
        else:
            row = self.od_df.iloc[idx - len(self.in_gdf) - len(self.out_gdf)]
            o_station_id = row['o_station_id']
            d_station_id = row['d_station_id']
            o_geo = self.features_df[self.features_df['station_id'] == o_station_id][self.feature_cols].values
            d_geo = self.features_df[self.features_df['station_id'] == d_station_id][self.feature_cols].values
            o_geo = o_geo[0] if len(o_geo) > 0 else np.zeros(len(self.feature_cols))
            d_geo = d_geo[0] if len(d_geo) > 0 else np.zeros(len(self.feature_cols))
            return {
                'type': 'od',
                'o_station_id': torch.tensor(o_station_id, dtype=torch.long),
                'd_station_id': torch.tensor(d_station_id, dtype=torch.long),
                'hour': torch.tensor([row['hour_sin'], row['hour_cos']], dtype=torch.float),
                'o_geo_features': torch.tensor(o_geo, dtype=torch.float),
                'd_geo_features': torch.tensor(d_geo, dtype=torch.float),
                'od_features': torch.tensor([
                    row['surface_distance'], row['translate'], row['time'], row['wait_time']
                ], dtype=torch.float),
                'trip': torch.tensor(row['trip'], dtype=torch.float),
                'index': idx - len(self.in_gdf) - len(self.out_gdf),
                'o_rawname': row['o_rawname'],
                'd_rawname': row['d_rawname'],
                'hour_raw': row['hour']
            }

def collate_fn(batch):
    in_batch = [x for x in batch if x['type'] == 'in']
    out_batch = [x for x in batch if x['type'] == 'out']
    od_batch = [x for x in batch if x['type'] == 'od']
    return in_batch, out_batch, od_batch

# 模型定义
class TrafficModel(torch.nn.Module):
    def __init__(self, num_stations, geo_feature_dim, station_dim=64, time_dim=2, hidden_dim=128):
        super().__init__()
        self.num_stations = num_stations
        self.station_dim = station_dim
        self.hidden_dim = hidden_dim
        self.time_dim = time_dim
        
        self.station_embed = torch.nn.Embedding(num_stations, station_dim)
        self.geo_embed = torch.nn.Linear(geo_feature_dim, hidden_dim)
        self.time_embed = torch.nn.Linear(time_dim, time_dim)
        
        self.gnn = dgl.nn.GraphConv(station_dim + hidden_dim + time_dim, hidden_dim)
        transformer_layer = TransformerEncoderLayer(
            d_model=hidden_dim, nhead=8, batch_first=True
        )
        self.transformer = TransformerEncoder(transformer_layer, num_layers=4)
        self.fc_grid = torch.nn.Linear(hidden_dim, 1)
        
        self.dnn = torch.nn.Sequential(
            torch.nn.Linear(station_dim*2 + hidden_dim*2 + 4 + time_dim, 512),
            torch.nn.ReLU(),
            torch.nn.Dropout(0.3),
            torch.nn.Linear(512, 256),
            torch.nn.ReLU(),
            torch.nn.Dropout(0.3),
            torch.nn.Linear(256, hidden_dim)
        )
        self.attention = torch.nn.MultiheadAttention(embed_dim=hidden_dim, num_heads=8)
        self.fc_od = torch.nn.Linear(hidden_dim, 1)
        
    def forward(self, in_batch, out_batch, od_batch, graph):
        count_pred_in, count_pred_out, trip_pred = [], [], []
        
        node_features = torch.zeros(
            (self.num_stations, self.station_dim + self.hidden_dim + self.time_dim),
            device=device
        )
        
        if in_batch:
            station_ids = torch.tensor([x['station_id'] for x in in_batch], dtype=torch.long, device=device)
            hours = torch.stack([x['hour'] for x in in_batch]).to(device)
            geo_features = torch.stack([x['geo_features'] for x in in_batch]).to(device)
            
            station_emb = self.station_embed(station_ids)
            geo_emb = self.geo_embed(geo_features)
            time_emb = self.time_embed(hours)
            batch_features = torch.cat([station_emb, geo_emb, time_emb], dim=-1)
            
            node_features[station_ids] = batch_features
            gnn_out = self.gnn(graph, node_features)
            transformer_out = self.transformer(gnn_out.unsqueeze(1)).squeeze(1)
            count_pred_in = torch.relu(self.fc_grid(transformer_out[station_ids])).squeeze(-1)
            if torch.isnan(count_pred_in).any():
                count_pred_in = torch.nan_to_num(count_pred_in, nan=0.0)
        
        if out_batch:
            station_ids = torch.tensor([x['station_id'] for x in out_batch], dtype=torch.long, device=device)
            hours = torch.stack([x['hour'] for x in out_batch]).to(device)
            geo_features = torch.stack([x['geo_features'] for x in out_batch]).to(device)
            
            station_emb = self.station_embed(station_ids)
            geo_emb = self.geo_embed(geo_features)
            time_emb = self.time_embed(hours)
            batch_features = torch.cat([station_emb, geo_emb, time_emb], dim=-1)
            
            node_features[station_ids] = batch_features
            gnn_out = self.gnn(graph, node_features)
            transformer_out = self.transformer(gnn_out.unsqueeze(1)).squeeze(1)
            count_pred_out = torch.relu(self.fc_grid(transformer_out[station_ids])).squeeze(-1)
            if torch.isnan(count_pred_out).any():
                count_pred_out = torch.nan_to_num(count_pred_out, nan=0.0)
        
        if od_batch:
            o_station_ids = torch.tensor([x['o_station_id'] for x in od_batch], dtype=torch.long, device=device)
            d_station_ids = torch.tensor([x['d_station_id'] for x in od_batch], dtype=torch.long, device=device)
            hours = torch.stack([x['hour'] for x in od_batch]).to(device)
            o_geo = torch.stack([x['o_geo_features'] for x in od_batch]).to(device)
            d_geo = torch.stack([x['d_geo_features'] for x in od_batch]).to(device)
            od_features = torch.stack([x['od_features'] for x in od_batch]).to(device)
            
            o_emb = self.station_embed(o_station_ids)
            d_emb = self.station_embed(d_station_ids)
            o_geo_emb = self.geo_embed(o_geo)
            d_geo_emb = self.geo_embed(d_geo)
            time_emb = self.time_embed(hours)
            
            combined = torch.cat([o_emb, d_emb, o_geo_emb, d_geo_emb, od_features, time_emb], dim=-1)
            dnn_out = self.dnn(combined)
            attn_out, _ = self.attention(dnn_out.unsqueeze(0), dnn_out.unsqueeze(0), dnn_out.unsqueeze(0))
            trip_pred = torch.relu(self.fc_od(attn_out.squeeze(0))).squeeze(-1)
            if torch.isnan(trip_pred).any():
                trip_pred = torch.nan_to_num(trip_pred, nan=0.0)
        
        return count_pred_in, count_pred_out, trip_pred

# 主程序
in_shp = "C:\\Users\\<USER>\\Desktop\\接驳\\in_500.shp"
out_shp = "C:\\Users\\<USER>\\Desktop\\接驳\\out_500.shp"
od_csv = "C:\\Users\\<USER>\\Desktop\\接驳\\updated_北京市_subway_od_2024_modified3.csv"
features_csv = "C:\\Users\\<USER>\\Desktop\\接驳\\station_features_result.csv"

# 加载和预处理数据
in_gdf, out_gdf, od_df, features_df = load_data(in_shp, out_shp, od_csv, features_csv)
in_gdf, out_gdf, od_df, features_df, station_encoder, feature_cols = preprocess_data(
    in_gdf, out_gdf, od_df, features_df
)

# 使用全数据集作为测试集以覆盖所有数据
test_in = in_gdf
test_out = out_gdf
test_od = od_df

# 创建测试数据集和加载器
test_dataset = TrafficDataset(test_in, test_out, test_od, features_df, feature_cols)
test_loader = torch.utils.data.DataLoader(
    test_dataset, batch_size=128, shuffle=False, collate_fn=collate_fn
)

# 创建图
graph = create_graph(in_gdf, out_gdf, od_df, station_encoder)

# 加载模型
model = TrafficModel(
    num_stations=len(station_encoder.classes_),
    geo_feature_dim=len(feature_cols)
).to(device)
model.load_state_dict(torch.load('best_model.pth'))
model.eval()

# 收集预测结果
in_results = []
out_results = []
od_results = []

with torch.no_grad():
    for in_batch, out_batch, od_batch in tqdm(test_loader, desc="Predicting"):
        count_pred_in, count_pred_out, trip_pred = model(in_batch, out_batch, od_batch, graph)
        
        if in_batch:
            pred = np.maximum(count_pred_in.cpu().numpy(), 0)
            for i, item in enumerate(in_batch):
                in_results.append({
                    'geometry': item['geometry'],
                    'hour': item['hour_raw'],
                    'true': item['count'].item(),
                    'pred': pred[i],
                    'station': station_encoder.inverse_transform([item['station_id'].item()])[0]
                })
        
        if out_batch:
            pred = np.maximum(count_pred_out.cpu().numpy(), 0)
            for i, item in enumerate(out_batch):
                out_results.append({
                    'geometry': item['geometry'],
                    'hour': item['hour_raw'],
                    'true': item['count'].item(),
                    'pred': pred[i],
                    'station': station_encoder.inverse_transform([item['station_id'].item()])[0]
                })
        
        if od_batch:
            pred = np.maximum(trip_pred.cpu().numpy(), 0)
            for i, item in enumerate(od_batch):
                od_results.append({
                    'hour': item['hour_raw'],
                    'o_rawname': item['o_rawname'],
                    'd_rawname': item['d_rawname'],
                    'true': item['trip'].item(),
                    'pred': pred[i]
                })

# 第一部分：栅格到地铁站（in）
in_results_df = pd.DataFrame(in_results)
in_results_df['abs_error'] = (in_results_df['true'] - in_results_df['pred']).abs()
in_results_df['squared_error'] = (in_results_df['true'] - in_results_df['pred']) ** 2

# 按 WKT 分组并计算指标
in_metrics = in_results_df.groupby(in_results_df['geometry'].apply(lambda x: x.wkt)).agg({
    'true': 'sum',
    'pred': 'sum',
    'abs_error': 'mean',
    'squared_error': lambda x: np.sqrt(np.mean(x))
}).reset_index().rename(
    columns={'geometry': 'wkt', 'abs_error': 'MAE', 'squared_error': 'RMSE', 
             'pred': 'pred_total_flow', 'true': 'true_total_flow'}
)

# 合并几何信息
geometry_map = in_results_df[['geometry']].drop_duplicates()
geometry_map['wkt'] = geometry_map['geometry'].apply(lambda x: x.wkt)
in_metrics = in_metrics.merge(geometry_map, on='wkt', how='left')

# 创建 GeoDataFrame
in_gdf_metrics = gpd.GeoDataFrame(
    in_metrics,
    geometry=in_metrics['geometry'],
    crs=test_in.crs
)

# 保存 shapefile 和 csv
in_gdf_metrics.drop(columns=['wkt']).to_file('in_grid_metrics.shp')
in_metrics.drop(columns=['geometry']).to_csv('in_grid_metrics.csv', index=False)

# 第三部分：地铁站到栅格（out）
out_results_df = pd.DataFrame(out_results)
out_results_df['abs_error'] = (out_results_df['true'] - out_results_df['pred']).abs()
out_results_df['squared_error'] = (out_results_df['true'] - out_results_df['pred']) ** 2

# 按 WKT 分组并计算指标
out_metrics = out_results_df.groupby(out_results_df['geometry'].apply(lambda x: x.wkt)).agg({
    'true': 'sum',
    'pred': 'sum',
    'abs_error': 'mean',
    'squared_error': lambda x: np.sqrt(np.mean(x))
}).reset_index().rename(
    columns={'geometry': 'wkt', 'abs_error': 'MAE', 'squared_error': 'RMSE', 
             'pred': 'pred_total_flow', 'true': 'true_total_flow'}
)

# 合并几何信息
geometry_map = out_results_df[['geometry']].drop_duplicates()
geometry_map['wkt'] = geometry_map['geometry'].apply(lambda x: x.wkt)
out_metrics = out_metrics.merge(geometry_map, on='wkt', how='left')

# 创建 GeoDataFrame
out_gdf_metrics = gpd.GeoDataFrame(
    out_metrics,
    geometry=out_metrics['geometry'],
    crs=test_out.crs
)

# 保存 shapefile 和 csv
out_gdf_metrics.drop(columns=['wkt']).to_file('out_grid_metrics.shp')
out_metrics.drop(columns=['geometry']).to_csv('out_grid_metrics.csv', index=False)

# 第二部分：地铁站间（od）
od_results_df = pd.DataFrame(od_results)
od_results_df['abs_error'] = (od_results_df['true'] - od_results_df['pred']).abs()
od_results_df['squared_error'] = (od_results_df['true'] - od_results_df['pred']) ** 2

# 集计文件：按起点站计算
od_metrics = od_results_df.groupby('o_rawname').agg({
    'true': 'sum',
    'pred': 'sum',
    'abs_error': 'mean',
    'squared_error': lambda x: np.sqrt(np.mean(x))
}).reset_index().rename(
    columns={'o_rawname': 'station', 'abs_error': 'MAE', 'squared_error': 'RMSE', 
             'pred': 'pred_total_flow', 'true': 'true_total_flow'}
)

# 保存集计 csv
od_metrics.to_csv('od_station_metrics.csv', index=False)

# 非集计文件：完整预测结果
od_results_df = od_results_df[['hour', 'o_rawname', 'd_rawname', 'true', 'pred']].rename(
    columns={'o_rawname': 'origin', 'd_rawname': 'destination', 'true': 'true_flow', 'pred': 'pred_flow'}
)
od_results_df.to_csv('od_detailed_predictions.csv', index=False)

print("All outputs generated successfully.")