import os
import argparse
import torch
import numpy as np
import pandas as pd
import geopandas as gpd
import matplotlib.pyplot as plt
from tqdm import tqdm

from data_loader import MetroDataLoader
from models import MetroFlowModel
from utils import (
    create_directory, timer, evaluate_predictions, plot_prediction_comparison,
    save_results_to_csv, save_results_to_shapefile, prepare_prediction_comparison,
    visualize_flow_on_map, log_message
)

# 配置GPU设备
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='地铁流量预测')
    parser.add_argument('--data_dir', type=str, default='C:/Users/<USER>/Desktop/接驳',
                        help='数据目录路径')
    parser.add_argument('--model_dir', type=str, default='./output',
                        help='模型目录路径')
    parser.add_argument('--output_dir', type=str, default='./results',
                        help='输出目录路径')
    parser.add_argument('--hidden_dim', type=int, default=128,
                        help='隐藏层维度')
    parser.add_argument('--use_fp16', action='store_true',
                        help='使用半精度浮点数')
    parser.add_argument('--model_prefix', type=str, default='best',
                        help='模型文件前缀，可选值: best或final')
    return parser.parse_args()

@timer
def predict_in_flow(model, data_loader, args):
    """
    预测进站流量
    
    参数:
    model: 模型
    data_loader: 数据加载器
    args: 命令行参数
    
    返回:
    添加了预测结果的GeoDataFrame
    """
    log_message("预测进站流量...")
    
    # 获取测试数据
    test_data = data_loader.get_grid_station_graph()
    test_indices = data_loader.in_data[data_loader.in_data['dataset'] == 'test'].index
    
    # 创建栅格ID到索引的映射
    grid_ids = [f"{row['station']}_{row['hour']}" for _, row in data_loader.in_data.iterrows()]
    unique_grid_ids = list(set(grid_ids))
    grid_id_to_idx = {grid_id: i for i, grid_id in enumerate(unique_grid_ids)}
    
    # 获取测试样本在图中的索引
    test_node_indices = []
    for idx in test_indices:
        row = data_loader.in_data.loc[idx]
        grid_id = f"{row['station']}_{row['hour']}"
        if grid_id in grid_id_to_idx:
            test_node_indices.append(grid_id_to_idx[grid_id])
    
    # 预测
    with torch.no_grad():
        all_predictions = model.predict_in_flow(test_data)
        all_predictions = all_predictions.cpu().numpy()
        
        # 提取测试样本对应的预测结果
        predictions = np.zeros((len(test_indices), 1))
        for i, idx in enumerate(test_indices):
            row = data_loader.in_data.loc[idx]
            grid_id = f"{row['station']}_{row['hour']}"
            if grid_id in grid_id_to_idx:
                node_idx = grid_id_to_idx[grid_id]
                if node_idx < len(all_predictions):
                    predictions[i] = all_predictions[node_idx]
    
    # 将预测结果添加到原始数据中
    in_data_copy = data_loader.in_data.copy()
    in_data_copy.loc[test_indices, 'prediction'] = predictions
    
    # 计算评估指标
    y_true = data_loader.in_data.loc[test_indices, 'count'].values
    metrics = evaluate_predictions(y_true, predictions.flatten())
    
    log_message(f"进站流量预测指标: MAE={metrics['MAE']:.4f}, MSE={metrics['MSE']:.4f}, RMSE={metrics['RMSE']:.4f}, R2={metrics['R2']:.4f}")
    
    return in_data_copy

@timer
def predict_out_flow(model, data_loader, args):
    """
    预测出站流量
    
    参数:
    model: 模型
    data_loader: 数据加载器
    args: 命令行参数
    
    返回:
    添加了预测结果的GeoDataFrame
    """
    log_message("预测出站流量...")
    
    # 获取测试数据
    test_data = data_loader.get_grid_station_graph()
    test_indices = data_loader.out_data[data_loader.out_data['dataset'] == 'test'].index
    
    # 创建栅格ID到索引的映射
    grid_ids = [f"{row['station']}_{row['hour']}" for _, row in data_loader.out_data.iterrows()]
    unique_grid_ids = list(set(grid_ids))
    grid_id_to_idx = {grid_id: i for i, grid_id in enumerate(unique_grid_ids)}
    
    # 获取测试样本在图中的索引
    test_node_indices = []
    for idx in test_indices:
        row = data_loader.out_data.loc[idx]
        grid_id = f"{row['station']}_{row['hour']}"
        if grid_id in grid_id_to_idx:
            test_node_indices.append(grid_id_to_idx[grid_id])
    
    # 预测
    with torch.no_grad():
        all_predictions = model.predict_out_flow(test_data)
        all_predictions = all_predictions.cpu().numpy()
        
        # 提取测试样本对应的预测结果
        predictions = np.zeros((len(test_indices), 1))
        for i, idx in enumerate(test_indices):
            row = data_loader.out_data.loc[idx]
            grid_id = f"{row['station']}_{row['hour']}"
            if grid_id in grid_id_to_idx:
                node_idx = grid_id_to_idx[grid_id]
                if node_idx < len(all_predictions):
                    predictions[i] = all_predictions[node_idx]
    
    # 将预测结果添加到原始数据中
    out_data_copy = data_loader.out_data.copy()
    out_data_copy.loc[test_indices, 'prediction'] = predictions
    
    # 计算评估指标
    y_true = data_loader.out_data.loc[test_indices, 'count'].values
    metrics = evaluate_predictions(y_true, predictions.flatten())
    
    log_message(f"出站流量预测指标: MAE={metrics['MAE']:.4f}, MSE={metrics['MSE']:.4f}, RMSE={metrics['RMSE']:.4f}, R2={metrics['R2']:.4f}")
    
    return out_data_copy

@timer
def predict_od_flow(model, data_loader, args):
    """
    预测OD流量
    
    参数:
    model: 模型
    data_loader: 数据加载器
    args: 命令行参数
    
    返回:
    添加了预测结果的DataFrame
    """
    log_message("预测OD流量...")
    
    # 获取测试数据
    test_data = data_loader.get_station_station_graph()
    test_indices = data_loader.od_data[data_loader.od_data['dataset'] == 'test'].index
    
    # 创建OD对到索引的映射
    edge_pairs = [(row['o_rawname'], row['d_rawname']) for _, row in data_loader.od_data.iterrows()]
    unique_edge_pairs = list(set(edge_pairs))
    edge_pair_to_idx = {pair: i for i, pair in enumerate(unique_edge_pairs)}
    
    # 预测
    with torch.no_grad():
        all_predictions = model.predict_od_flow(test_data)
        all_predictions = all_predictions.cpu().numpy()
        
        # 提取测试样本对应的预测结果
        predictions = np.zeros((len(test_indices), 1))
        for i, idx in enumerate(test_indices):
            row = data_loader.od_data.loc[idx]
            edge_pair = (row['o_rawname'], row['d_rawname'])
            if edge_pair in edge_pair_to_idx:
                edge_idx = edge_pair_to_idx[edge_pair]
                if edge_idx < len(all_predictions):
                    predictions[i] = all_predictions[edge_idx]
    
    # 将预测结果添加到原始数据中
    od_data_copy = data_loader.od_data.copy()
    od_data_copy.loc[test_indices, 'prediction'] = predictions
    
    # 计算评估指标
    y_true = data_loader.od_data.loc[test_indices, 'trip'].values
    metrics = evaluate_predictions(y_true, predictions.flatten())
    
    log_message(f"OD流量预测指标: MAE={metrics['MAE']:.4f}, MSE={metrics['MSE']:.4f}, RMSE={metrics['RMSE']:.4f}, R2={metrics['R2']:.4f}")
    
    return od_data_copy

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 创建输出目录
    create_directory(args.output_dir)
    
    # 日志文件
    log_file = os.path.join(args.output_dir, 'prediction.log')
    
    # 记录基本信息
    log_message(f"设备: {device}", log_file)
    log_message(f"模型目录: {args.model_dir}", log_file)
    log_message(f"输出目录: {args.output_dir}", log_file)
    log_message(f"模型前缀: {args.model_prefix}", log_file)
    
    # 加载数据
    data_loader = MetroDataLoader(args.data_dir, use_fp16=args.use_fp16)
    data_loader.load_all_data()
    data_loader.preprocess_data()
    
    # 获取模型维度
    in_node_features = data_loader.grid_station_graph.x.shape[1]
    out_node_features = data_loader.grid_station_graph.x.shape[1]
    od_node_features = data_loader.station_station_graph.x.shape[1]
    od_edge_features = data_loader.station_station_graph.edge_attr.shape[1]
    
    # 创建模型
    model = MetroFlowModel(
        in_node_features, out_node_features, od_node_features, od_edge_features,
        hidden_dim=args.hidden_dim, use_fp16=args.use_fp16
    )
    
    # 加载模型
    model_path_prefix = os.path.join(args.model_dir, args.model_prefix)
    model.load_models(model_path_prefix)
    
    # 预测
    in_data_with_predictions = predict_in_flow(model, data_loader, args)
    out_data_with_predictions = predict_out_flow(model, data_loader, args)
    od_data_with_predictions = predict_od_flow(model, data_loader, args)
    
    # 保存预测结果
    # 进站流量
    in_output_path = os.path.join(args.output_dir, 'in_500_predictions_with_coords.shp')
    save_results_to_shapefile(in_data_with_predictions, in_output_path)
    
    # 出站流量
    out_output_path = os.path.join(args.output_dir, 'out_500_predictions_with_coords.shp')
    save_results_to_shapefile(out_data_with_predictions, out_output_path)
    
    # OD流量
    od_output_path = os.path.join(args.output_dir, 'od_predictions.csv')
    save_results_to_csv(od_data_with_predictions, od_output_path)
    
    # 绘制预测对比图
    in_true = in_data_with_predictions[in_data_with_predictions['dataset'] == 'test']['count'].values
    in_pred = in_data_with_predictions[in_data_with_predictions['dataset'] == 'test']['prediction'].values
    
    out_true = out_data_with_predictions[out_data_with_predictions['dataset'] == 'test']['count'].values
    out_pred = out_data_with_predictions[out_data_with_predictions['dataset'] == 'test']['prediction'].values
    
    od_true = od_data_with_predictions[od_data_with_predictions['dataset'] == 'test']['trip'].values
    od_pred = od_data_with_predictions[od_data_with_predictions['dataset'] == 'test']['prediction'].values
    
    # 进站流量对比图
    plot_prediction_comparison(
        in_true, in_pred, 
        title='进站流量预测对比',
        save_path=os.path.join(args.output_dir, 'in_prediction_scatter.png')
    )
    
    # 出站流量对比图
    plot_prediction_comparison(
        out_true, out_pred, 
        title='出站流量预测对比',
        save_path=os.path.join(args.output_dir, 'out_prediction_scatter.png')
    )
    
    # OD流量对比图
    plot_prediction_comparison(
        od_true, od_pred, 
        title='OD流量预测对比',
        save_path=os.path.join(args.output_dir, 'od_prediction_scatter.png')
    )
    
    # 准备总体预测比较结果
    comparison_df = prepare_prediction_comparison(in_data_with_predictions, out_data_with_predictions, od_data_with_predictions)
    save_results_to_csv(comparison_df, os.path.join(args.output_dir, 'prediction_comparison.csv'))
    
    # 绘制总体预测对比图
    plt.figure(figsize=(15, 10))
    
    types = ['In', 'Out', 'OD']
    colors = ['blue', 'green', 'red']
    
    for t, c in zip(types, colors):
        type_data = comparison_df[comparison_df['Type'] == t]
        plt.scatter(type_data['True_Value'], type_data['Predicted_Value'], alpha=0.5, label=t, color=c)
    
    # 绘制理想的对角线
    min_val = min(comparison_df['True_Value'].min(), comparison_df['Predicted_Value'].min())
    max_val = max(comparison_df['True_Value'].max(), comparison_df['Predicted_Value'].max())
    plt.plot([min_val, max_val], [min_val, max_val], 'r--')
    
    plt.xlabel('True Values')
    plt.ylabel('Predicted Values')
    plt.title('预测值与真实值对比')
    plt.legend()
    plt.grid(True)
    plt.tight_layout()
    plt.savefig(os.path.join(args.output_dir, 'prediction_scatter.png'))
    plt.close()
    
    log_message("预测完成！", log_file)

if __name__ == "__main__":
    main() 