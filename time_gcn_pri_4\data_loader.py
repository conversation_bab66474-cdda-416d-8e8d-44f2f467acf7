import pandas as pd
import numpy as np
import geopandas as gpd
import torch
import torch.nn.functional as F
from torch_geometric.data import Data
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
from geopy.distance import geodesic
import warnings
warnings.filterwarnings('ignore')

class MetroDataLoader:
    def __init__(self, base_path="C:\\Users\\<USER>\\Desktop\\接驳\\"):
        self.base_path = base_path
        self.scaler = StandardScaler()
        self.station_encoder = LabelEncoder()
        self.station_to_id = {}
        self.id_to_station = {}
        
    def clean_station_name(self, name):
        """清理站点名称，去除下划线后的内容"""
        if pd.isna(name):
            return name
        return str(name).split('_')[0]
    
    def load_flow_data(self):
        """加载进出站流量数据"""
        print("Loading flow data...")
        
        # 加载进站数据
        in_data = gpd.read_file(f"{self.base_path}in_500_with_coords.shp")
        print(f"In station data shape: {in_data.shape}")
        print(f"In station columns: {in_data.columns.tolist()}")
        print(f"In station data sample:\n{in_data.head()}")
        
        # 加载出站数据
        out_data = gpd.read_file(f"{self.base_path}out_500_with_coords.shp")
        print(f"Out station data shape: {out_data.shape}")
        print(f"Out station columns: {out_data.columns.tolist()}")
        
        return in_data, out_data
    
    def load_od_data(self):
        """加载OD数据"""
        print("Loading OD data...")
        od_data = pd.read_csv(f"{self.base_path}updated_北京市_subway_od_2024_modified3.csv")
        print(f"OD data shape: {od_data.shape}")
        print(f"OD data columns: {od_data.columns.tolist()}")
        print(f"OD data sample:\n{od_data.head()}")
        
        return od_data
    
    def load_grid_features(self):
        """加载栅格特征数据"""
        print("Loading grid features...")
        grid_features = pd.read_csv(f"{self.base_path}leti_data.csv")
        print(f"Grid features shape: {grid_features.shape}")
        print(f"Grid features columns: {grid_features.columns.tolist()}")
        print(f"Grid features sample:\n{grid_features.head()}")
        
        return grid_features
    
    def load_station_connections(self):
        """加载地铁站连接数据"""
        print("Loading station connections...")
        connections = pd.read_csv(f"{self.base_path}station_connect_2023.csv")
        print(f"Station connections shape: {connections.shape}")
        print(f"Station connections columns: {connections.columns.tolist()}")
        print(f"Station connections sample:\n{connections.head()}")
        
        # 清理站点名称
        connections['station_1'] = connections['station_1'].apply(self.clean_station_name)
        connections['station_2'] = connections['station_2'].apply(self.clean_station_name)
        
        return connections
    
    def load_subway_flow(self):
        """加载地铁相邻站流量数据"""
        print("Loading subway flow data...")
        flow_data = pd.read_csv(f"{self.base_path}subway_flow_24h_results_hourly_stats.csv")
        print(f"Subway flow data shape: {flow_data.shape}")
        print(f"Subway flow data columns: {flow_data.columns.tolist()}")
        print(f"Subway flow data sample:\n{flow_data.head()}")
        
        return flow_data
    
    def create_station_mapping(self, in_data, out_data, connections, flow_data):
        """创建站点映射"""
        print("Creating station mapping...")
        
        # 收集所有站点名称
        all_stations = set()
        all_stations.update(in_data['station'].unique())
        all_stations.update(out_data['station'].unique())
        all_stations.update(connections['station_1'].unique())
        all_stations.update(connections['station_2'].unique())
        all_stations.update(flow_data['o_rawname'].unique())
        all_stations.update(flow_data['d_rawname'].unique())
        
        all_stations = list(all_stations)
        all_stations = [s for s in all_stations if pd.notna(s)]
        
        # 创建映射
        self.station_to_id = {station: idx for idx, station in enumerate(all_stations)}
        self.id_to_station = {idx: station for station, idx in self.station_to_id.items()}
        
        print(f"Total number of stations: {len(all_stations)}")
        print(f"Sample stations: {list(all_stations)[:10]}")
        
        return all_stations
    
    def aggregate_grid_features_to_stations(self, grid_features, stations_coords, radius_km=3):
        """将栅格特征聚合到站点"""
        print("Aggregating grid features to stations...")
        
        station_features = {}
        feature_columns = [col for col in grid_features.columns if col != '站名']
        
        for station in stations_coords.index:
            station_lat = stations_coords.loc[station, 'latitude']
            station_lon = stations_coords.loc[station, 'longitude']
            
            # 计算距离并筛选3km内的栅格
            nearby_grids = []
            for _, grid in grid_features.iterrows():
                # 这里假设栅格数据中也有经纬度信息，如果没有需要从shp文件中读取
                # 暂时使用站名匹配，实际应该使用地理坐标
                if grid['站名'] == station:
                    nearby_grids.append(grid[feature_columns].values)
            
            if nearby_grids:
                # 聚合特征（这里使用均值）
                station_features[station] = np.mean(nearby_grids, axis=0)
            else:
                # 如果没有找到附近栅格，使用零向量
                station_features[station] = np.zeros(len(feature_columns))
        
        station_features_df = pd.DataFrame.from_dict(station_features, orient='index', 
                                                   columns=feature_columns)
        print(f"Station features shape: {station_features_df.shape}")
        
        return station_features_df
    
    def create_graph_data(self, connections, flow_data, station_features, hour):
        """为特定小时创建图数据"""
        
        # 获取该小时的流量数据
        hour_flow = flow_data[flow_data['hour'] == hour]
        
        # 创建边索引
        edge_index = []
        edge_weights = []
        
        # 基于连接关系创建边
        for _, row in connections.iterrows():
            station1 = row['station_1']
            station2 = row['station_2']
            
            if station1 in self.station_to_id and station2 in self.station_to_id:
                id1 = self.station_to_id[station1]
                id2 = self.station_to_id[station2]
                
                # 查找对应的流量
                flow = hour_flow[
                    (hour_flow['o_rawname'] == station1) & 
                    (hour_flow['d_rawname'] == station2)
                ]['trip'].values
                
                weight = flow[0] if len(flow) > 0 else 1.0
                
                edge_index.append([id1, id2])
                edge_weights.append(weight)
        
        # 转换为张量
        edge_index = torch.tensor(edge_index, dtype=torch.long).t().contiguous()
        edge_weights = torch.tensor(edge_weights, dtype=torch.float)
        
        # 准备节点特征
        node_features = []
        for station in self.id_to_station.values():
            if station in station_features.index:
                features = station_features.loc[station].values
            else:
                features = np.zeros(station_features.shape[1])
            
            # 添加时间特征
            time_features = [
                np.sin(2 * np.pi * hour / 24),  # 小时的正弦编码
                np.cos(2 * np.pi * hour / 24),  # 小时的余弦编码
                hour / 23.0  # 归一化小时
            ]
            
            combined_features = np.concatenate([features, time_features])
            node_features.append(combined_features)
        
        node_features = torch.tensor(node_features, dtype=torch.float)
        
        return Data(x=node_features, edge_index=edge_index, edge_attr=edge_weights)
    
    def prepare_training_data(self, in_data, out_data, od_data, test_ratio=0.2):
        """准备训练数据"""
        print("Preparing training data...")
        
        # 获取所有站点
        all_stations = list(self.station_to_id.keys())
        
        # 80%的站点用于训练，20%用于测试
        train_stations, test_stations = train_test_split(
            all_stations, test_size=test_ratio, random_state=42
        )
        
        # 分离训练和测试数据
        train_in = in_data[in_data['station'].isin(train_stations)]
        test_in = in_data[in_data['station'].isin(test_stations)]
        
        train_out = out_data[out_data['station'].isin(train_stations)]
        test_out = out_data[out_data['station'].isin(test_stations)]
        
        train_od = od_data[
            od_data['o_rawname'].isin(train_stations) & 
            od_data['d_rawname'].isin(train_stations)
        ]
        test_od = od_data[
            od_data['o_rawname'].isin(test_stations) | 
            od_data['d_rawname'].isin(test_stations)
        ]
        
        print(f"Train stations: {len(train_stations)}")
        print(f"Test stations: {len(test_stations)}")
        print(f"Train in data: {len(train_in)}")
        print(f"Test in data: {len(test_in)}")
        print(f"Train out data: {len(train_out)}")
        print(f"Test out data: {len(test_out)}")
        print(f"Train OD data: {len(train_od)}")
        print(f"Test OD data: {len(test_od)}")
        
        return {
            'train_stations': train_stations,
            'test_stations': test_stations,
            'train_in': train_in,
            'test_in': test_in,
            'train_out': train_out,
            'test_out': test_out,
            'train_od': train_od,
            'test_od': test_od
        }
    
    def load_all_data(self):
        """加载所有数据"""
        print("Loading all data...")
        
        # 加载各种数据
        in_data, out_data = self.load_flow_data()
        od_data = self.load_od_data()
        grid_features = self.load_grid_features()
        connections = self.load_station_connections()
        flow_data = self.load_subway_flow()
        
        # 创建站点映射
        all_stations = self.create_station_mapping(in_data, out_data, connections, flow_data)
        
        # 提取站点坐标
        station_coords = in_data.groupby('station')[['longitude', 'latitude']].first()
        
        # 聚合栅格特征到站点
        station_features = self.aggregate_grid_features_to_stations(grid_features, station_coords)
        
        # 准备训练数据
        data_split = self.prepare_training_data(in_data, out_data, od_data)
        
        return {
            'in_data': in_data,
            'out_data': out_data,
            'od_data': od_data,
            'grid_features': grid_features,
            'connections': connections,
            'flow_data': flow_data,
            'station_features': station_features,
            'station_coords': station_coords,
            'data_split': data_split
        }
