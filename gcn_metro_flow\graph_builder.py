"""
图构建模块 - 构建地铁网络图
"""
import numpy as np
import pandas as pd
import torch
from torch_geometric.data import Data
from torch_geometric.utils import to_undirected
import networkx as nx
from collections import defaultdict

from config import DATA_CONFIG, MODEL_CONFIG

class MetroGraphBuilder:
    """地铁网络图构建器"""
    
    def __init__(self, data_loader):
        self.data_loader = data_loader
        self.station_to_idx = {}
        self.idx_to_station = {}
        self.graphs_by_hour = {}
        
    def build_graphs(self):
        """构建每小时的地铁网络图"""
        print("Building metro network graphs...")
        
        # 创建站点索引映射
        self._create_station_mapping()
        
        # 为每个小时构建图
        for hour in range(24):
            self.graphs_by_hour[hour] = self._build_hourly_graph(hour)
        
        print(f"Built graphs for {len(self.graphs_by_hour)} hours")
        return self.graphs_by_hour
    
    def _create_station_mapping(self):
        """创建站点到索引的映射"""
        stations = self.data_loader.stations
        self.station_to_idx = {station: idx for idx, station in enumerate(stations)}
        self.idx_to_station = {idx: station for idx, station in enumerate(stations)}
        print(f"Created mapping for {len(stations)} stations")
    
    def _build_hourly_graph(self, hour):
        """构建指定小时的图"""
        num_stations = len(self.data_loader.stations)
        
        # 初始化邻接矩阵
        adj_matrix = np.zeros((num_stations, num_stations))
        edge_weights = defaultdict(float)
        
        # 1. 添加基础连接边（从station_connect数据）
        self._add_connection_edges(adj_matrix, edge_weights)
        
        # 2. 添加流量权重（从subway_flow数据）
        self._add_flow_weights(adj_matrix, edge_weights, hour)
        
        # 3. 构建PyTorch Geometric图数据
        graph_data = self._create_graph_data(adj_matrix, edge_weights, hour)
        
        return graph_data
    
    def _add_connection_edges(self, adj_matrix, edge_weights):
        """添加基础连接边"""
        if self.data_loader.station_connections is None:
            print("Warning: No station connection data available")
            return
        
        connections = self.data_loader.station_connections
        added_edges = 0
        
        for _, row in connections.iterrows():
            station1 = row['station_1_clean']
            station2 = row['station_2_clean']
            
            if station1 in self.station_to_idx and station2 in self.station_to_idx:
                idx1 = self.station_to_idx[station1]
                idx2 = self.station_to_idx[station2]
                
                # 添加双向边
                adj_matrix[idx1, idx2] = 1.0
                adj_matrix[idx2, idx1] = 1.0
                
                # 初始化边权重
                edge_weights[(idx1, idx2)] = 1.0
                edge_weights[(idx2, idx1)] = 1.0
                
                added_edges += 1
        
        print(f"Added {added_edges} connection edges")
    
    def _add_flow_weights(self, adj_matrix, edge_weights, hour):
        """添加流量权重"""
        if self.data_loader.subway_flow_data is None:
            print("Warning: No subway flow data available")
            return
        
        # 过滤指定小时的数据
        hour_data = self.data_loader.subway_flow_data[
            self.data_loader.subway_flow_data['hour'] == hour
        ]
        
        updated_edges = 0
        
        for _, row in hour_data.iterrows():
            o_station = row['o_station_clean']
            d_station = row['d_station_clean']
            flow = row['trip']
            
            if (o_station in self.station_to_idx and 
                d_station in self.station_to_idx and 
                flow > 0):
                
                o_idx = self.station_to_idx[o_station]
                d_idx = self.station_to_idx[d_station]
                
                # 更新边权重（使用流量值）
                if adj_matrix[o_idx, d_idx] > 0:  # 只更新已存在的边
                    edge_weights[(o_idx, d_idx)] = flow
                    updated_edges += 1
        
        print(f"Updated {updated_edges} edges with flow weights for hour {hour}")
    
    def _create_graph_data(self, adj_matrix, edge_weights, hour):
        """创建PyTorch Geometric图数据"""
        # 获取边索引
        edge_indices = np.where(adj_matrix > 0)
        edge_index = torch.tensor([edge_indices[0], edge_indices[1]], dtype=torch.long)
        
        # 获取边权重
        edge_attr = []
        for i in range(len(edge_indices[0])):
            src, dst = edge_indices[0][i], edge_indices[1][i]
            weight = edge_weights.get((src, dst), 1.0)
            edge_attr.append([weight])
        
        edge_attr = torch.tensor(edge_attr, dtype=torch.float)
        
        # 节点特征：站点特征 + 时间特征
        node_features = self._create_node_features(hour)
        
        # 节点标签：进出站流量
        node_labels = self._create_node_labels(hour)
        
        # 创建图数据
        graph_data = Data(
            x=node_features,
            edge_index=edge_index,
            edge_attr=edge_attr,
            y=node_labels,
            hour=hour,
            num_nodes=len(self.data_loader.stations)
        )
        
        return graph_data
    
    def _create_node_features(self, hour):
        """创建节点特征"""
        num_stations = len(self.data_loader.stations)
        
        # 站点地理特征
        if self.data_loader.station_features is not None:
            geo_features = torch.tensor(self.data_loader.station_features, dtype=torch.float)
        else:
            geo_features = torch.zeros(num_stations, MODEL_CONFIG['geo_feature_dim'])
        
        # 时间特征
        time_features = self._create_time_features(hour, num_stations)
        
        # 合并特征
        node_features = torch.cat([geo_features, time_features], dim=1)
        
        return node_features
    
    def _create_time_features(self, hour, num_stations):
        """创建时间特征"""
        # 小时编码（sin/cos编码）
        hour_sin = np.sin(2 * np.pi * hour / 24)
        hour_cos = np.cos(2 * np.pi * hour / 24)
        
        # 时间段编码
        time_period = self._get_time_period(hour)
        
        # 为所有站点创建相同的时间特征
        time_features = np.array([hour_sin, hour_cos, time_period])
        time_features = np.tile(time_features, (num_stations, 1))
        
        return torch.tensor(time_features, dtype=torch.float)
    
    def _get_time_period(self, hour):
        """获取时间段编码"""
        if 6 <= hour < 10:
            return 1.0  # 早高峰
        elif 17 <= hour < 20:
            return 2.0  # 晚高峰
        elif 10 <= hour < 17:
            return 3.0  # 平峰
        else:
            return 0.0  # 低峰
    
    def _create_node_labels(self, hour):
        """创建节点标签（进出站流量）"""
        num_stations = len(self.data_loader.stations)
        
        # 初始化标签矩阵 [进站流量, 出站流量]
        labels = np.zeros((num_stations, 2))
        
        # 填充进站流量
        if self.data_loader.in_flow_data is not None:
            hour_in_data = self.data_loader.in_flow_data[
                self.data_loader.in_flow_data['hour'] == hour
            ]
            
            for _, row in hour_in_data.iterrows():
                station = row['station_clean']
                if station in self.station_to_idx:
                    idx = self.station_to_idx[station]
                    labels[idx, 0] += row['count']
        
        # 填充出站流量
        if self.data_loader.out_flow_data is not None:
            hour_out_data = self.data_loader.out_flow_data[
                self.data_loader.out_flow_data['hour'] == hour
            ]
            
            for _, row in hour_out_data.iterrows():
                station = row['station_clean']
                if station in self.station_to_idx:
                    idx = self.station_to_idx[station]
                    labels[idx, 1] += row['count']
        
        return torch.tensor(labels, dtype=torch.float)
    
    def get_od_data_for_hour(self, hour):
        """获取指定小时的OD数据"""
        if self.data_loader.od_flow_data is None:
            return None
        
        hour_od_data = self.data_loader.od_flow_data[
            self.data_loader.od_flow_data['hour'] == hour
        ]
        
        # 转换为张量格式
        od_pairs = []
        od_flows = []
        od_features = []
        
        for _, row in hour_od_data.iterrows():
            o_station = row['o_station_clean']
            d_station = row['d_station_clean']
            
            if (o_station in self.station_to_idx and 
                d_station in self.station_to_idx):
                
                o_idx = self.station_to_idx[o_station]
                d_idx = self.station_to_idx[d_station]
                
                od_pairs.append([o_idx, d_idx])
                od_flows.append(row['trip'])
                
                # OD特征：距离、换乘次数、时间、等待时间
                od_feat = [
                    row.get('surface_distance', 0),
                    row.get('translate', 0),
                    row.get('time', 0),
                    row.get('wait_time', 0)
                ]
                od_features.append(od_feat)
        
        if len(od_pairs) == 0:
            return None
        
        return {
            'od_pairs': torch.tensor(od_pairs, dtype=torch.long),
            'od_flows': torch.tensor(od_flows, dtype=torch.float),
            'od_features': torch.tensor(od_features, dtype=torch.float)
        }
