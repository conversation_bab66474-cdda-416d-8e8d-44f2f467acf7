#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简单的导入测试脚本
"""

print("开始测试基本导入...")

try:
    import sys
    print(f"✓ Python版本: {sys.version}")
except Exception as e:
    print(f"✗ sys导入失败: {e}")

try:
    import numpy as np
    print(f"✓ NumPy版本: {np.__version__}")
except Exception as e:
    print(f"✗ NumPy导入失败: {e}")

try:
    import pandas as pd
    print(f"✓ Pandas版本: {pd.__version__}")
except Exception as e:
    print(f"✗ Pandas导入失败: {e}")

try:
    import torch
    print(f"✓ PyTorch版本: {torch.__version__}")
    print(f"✓ CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"✓ GPU设备: {torch.cuda.get_device_name(0)}")
except Exception as e:
    print(f"✗ PyTorch导入失败: {e}")

try:
    import torch_geometric
    print(f"✓ PyTorch Geometric可用")
except Exception as e:
    print(f"✗ PyTorch Geometric导入失败: {e}")

try:
    import geopandas
    print(f"✓ GeoPandas可用")
except Exception as e:
    print(f"✗ GeoPandas导入失败: {e}")

print("导入测试完成！") 