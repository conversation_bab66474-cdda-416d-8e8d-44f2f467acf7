"""
T-GCN项目配置文件
包含数据路径、模型参数、训练参数等配置
"""
import os

class Config:
    """配置类"""
    
    # ================== 基础路径配置 ==================
    BASE_PATH = "C:\\Users\\<USER>\\Desktop\\接驳\\"
    
    # ================== 数据文件路径 ==================
    IN_FILE = os.path.join(BASE_PATH, "in_500_with_coords.shp")
    OUT_FILE = os.path.join(BASE_PATH, "out_500_with_coords.shp")
    OD_FILE = os.path.join(BASE_PATH, "updated_北京市_subway_od_2024_modified3.csv")
    FEATURES_FILE = os.path.join(BASE_PATH, "station_features_result.csv")
    
    # ================== 输出文件路径 ==================
    OUTPUT_IN_PRED = os.path.join(BASE_PATH, "in_500_predictions_with_coords.shp")
    OUTPUT_OUT_PRED = os.path.join(BASE_PATH, "out_500_predictions_with_coords.shp")
    OUTPUT_OD_PRED = os.path.join(BASE_PATH, "od_predictions.csv")
    OUTPUT_COMPARISON = os.path.join(BASE_PATH, "prediction_comparison.csv")
    OUTPUT_TRAINING_CURVES = os.path.join(BASE_PATH, "training_curves.png")
    
    # ================== 数据处理参数 ==================
    TRAIN_RATIO = 0.8           # 训练集比例
    TIME_STEPS = 24             # 时间步数(小时)
    
    # ================== 模型参数 ==================
    HIDDEN_DIM = 32             # 隐藏层维度
    OUTPUT_DIM = 1              # 输出维度
    
    # ================== 训练参数 ==================
    NUM_EPOCHS = 100           # 训练轮数
    LEARNING_RATE = 0.01        # 学习率
    BATCH_SIZE = 1              # 批次大小
    
    # ================== 其他参数 ==================
    RANDOM_SEED = 42            # 随机种子
    USE_GPU = True              # 是否使用GPU
    DEVICE = "cuda" if USE_GPU else "cpu"
