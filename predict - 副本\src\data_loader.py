import os
import numpy as np
import pandas as pd
import geopandas as gpd
from tqdm import tqdm
from shapely.geometry import Point
from sklearn.preprocessing import StandardScaler, MinMaxScaler
import torch
from torch_geometric.data import Data
import torch_geometric.transforms as T
from scipy.spatial.distance import cdist

# 配置GPU设备
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

class MetroDataLoader:
    def __init__(self, data_dir, use_fp16=True):
        """
        数据加载器，负责加载和预处理地铁流量数据
        
        参数:
        data_dir: 数据目录路径
        use_fp16: 是否使用半精度浮点数以节省内存
        """
        self.data_dir = data_dir
        self.use_fp16 = use_fp16
        self.dtype = torch.float16 if use_fp16 else torch.float32
        
        # 数据路径
        self.in_data_path = os.path.join(data_dir, 'in_500_with_coords.shp')
        self.out_data_path = os.path.join(data_dir, 'out_500_with_coords.shp')
        self.od_data_path = os.path.join(data_dir, 'updated北京市_subway_od_2024_modified3.csv')
        self.station_features_path = os.path.join(data_dir, 'station_features_result.csv')
        
        # 数据存储
        self.in_data = None
        self.out_data = None
        self.od_data = None
        self.station_features = None
        self.grid_data = None  # 栅格数据
        self.grid_coords = None  # 栅格坐标
        self.station_coords = None  # 站点坐标
        
        # 数据预处理
        self.feature_scaler = StandardScaler()
        self.count_scaler = StandardScaler()
        
        # 图数据
        self.grid_station_graph = None  # 栅格-站点图
        self.station_station_graph = None  # 站点-站点图
        
    def load_all_data(self):
        """加载所有数据"""
        print("加载进站数据...")
        self.in_data = gpd.read_file(self.in_data_path)
        
        # 确保经纬度为数值类型
        if 'longitude' in self.in_data.columns:
            self.in_data['longitude'] = pd.to_numeric(self.in_data['longitude'], errors='coerce')
        if 'latitude' in self.in_data.columns:
            self.in_data['latitude'] = pd.to_numeric(self.in_data['latitude'], errors='coerce')
        
        print("加载出站数据...")
        self.out_data = gpd.read_file(self.out_data_path)
        
        # 确保经纬度为数值类型
        if 'longitude' in self.out_data.columns:
            self.out_data['longitude'] = pd.to_numeric(self.out_data['longitude'], errors='coerce')
        if 'latitude' in self.out_data.columns:
            self.out_data['latitude'] = pd.to_numeric(self.out_data['latitude'], errors='coerce')
        
        print("加载OD数据...")
        self.od_data = pd.read_csv(self.od_data_path)
        
        print("加载站点特征数据...")
        self.station_features = pd.read_csv(self.station_features_path)
        
        # 提取站点和栅格坐标
        self._extract_coordinates()
        
        return self
    
    def _extract_coordinates(self):
        """从数据中提取栅格和站点的坐标"""
        # 提取站点坐标
        station_coords = {}
        if 'longitude' in self.in_data.columns and 'latitude' in self.in_data.columns:
            for _, row in self.in_data.drop_duplicates('station').iterrows():
                try:
                    # 确保坐标是浮点数
                    longitude = float(row['longitude'])
                    latitude = float(row['latitude'])
                    station_coords[row['station']] = (longitude, latitude)
                except (ValueError, TypeError):
                    print(f"警告: 站点 {row['station']} 的坐标无法转换为浮点数，将跳过")
        
        self.station_coords = station_coords
        
        # 从进站数据中提取栅格坐标（取栅格中心点）
        grid_coords = {}
        for _, row in self.in_data.iterrows():
            if hasattr(row.geometry, 'centroid'):
                try:
                    # 确保坐标是浮点数
                    x = float(row.geometry.centroid.x)
                    y = float(row.geometry.centroid.y)
                    grid_id = f"{row['station']}_{row['hour']}"
                    grid_coords[grid_id] = (x, y)
                except (ValueError, TypeError):
                    print(f"警告: 栅格 {grid_id} 的坐标无法转换为浮点数，将跳过")
        
        self.grid_coords = grid_coords
        
        print(f"提取了 {len(station_coords)} 个站点和 {len(grid_coords)} 个栅格的坐标")
    
    def preprocess_data(self, test_ratio=0.2, val_ratio=0.1, random_state=42):
        """
        数据预处理和划分训练/验证/测试集
        
        参数:
        test_ratio: 测试集比例
        val_ratio: 验证集比例
        random_state: 随机种子
        """
        print("数据预处理...")
        
        # 处理进站数据
        self._preprocess_inout_data(self.in_data, is_in=True)
        
        # 处理出站数据
        self._preprocess_inout_data(self.out_data, is_in=False)
        
        # 处理OD数据
        self._preprocess_od_data()
        
        # 处理站点特征数据
        self._preprocess_station_features()
        
        # 划分数据集
        self._split_datasets_by_station(test_ratio, val_ratio, random_state)
        
        # 构建图
        self._build_graphs()
        
        return self
    
    def _preprocess_inout_data(self, data, is_in=True):
        """预处理进站或出站数据"""
        data_type = "进站" if is_in else "出站"
        print(f"处理{data_type}数据...")
        
        # 提取时间特征
        data['hour_sin'] = np.sin(2 * np.pi * data['hour'] / 24)
        data['hour_cos'] = np.cos(2 * np.pi * data['hour'] / 24)
        
        # 工作日/周末特征（这里简化处理，实际应根据日期确定）
        # 在实际应用中，应从日期推导这个特征
        data['is_weekend'] = 0  # 示例，实际应用中根据日期确定
        
        # 确保经纬度特征存在
        if 'longitude' not in data.columns or 'latitude' not in data.columns:
            print(f"警告: {data_type}数据中未找到经纬度特征，将使用几何中心点")
            data['longitude'] = data.geometry.centroid.x
            data['latitude'] = data.geometry.centroid.y
        
        # 确保经纬度为数值类型
        data['longitude'] = pd.to_numeric(data['longitude'], errors='coerce')
        data['latitude'] = pd.to_numeric(data['latitude'], errors='coerce')
        
        # 存储处理后的数据
        if is_in:
            self.in_data = data
        else:
            self.out_data = data
    
    def _preprocess_od_data(self):
        """预处理OD数据"""
        print("处理OD数据...")
        
        # 提取时间特征
        self.od_data['hour_sin'] = np.sin(2 * np.pi * self.od_data['hour'] / 24)
        self.od_data['hour_cos'] = np.cos(2 * np.pi * self.od_data['hour'] / 24)
        
        # 确保必要的特征存在
        required_features = ['surface_distance', 'translate', 'time', 'wait_time']
        for feature in required_features:
            if feature not in self.od_data.columns:
                print(f"警告: OD数据中未找到{feature}特征，将使用默认值0")
                self.od_data[feature] = 0
            else:
                # 确保特征为数值类型
                self.od_data[feature] = pd.to_numeric(self.od_data[feature], errors='coerce').fillna(0)
    
    def _preprocess_station_features(self):
        """预处理站点特征数据"""
        print("处理站点特征数据...")
        
        # 找出数值型特征
        numeric_features = self.station_features.select_dtypes(include=[np.number]).columns.tolist()
        
        # 确保所有特征都是数值型
        for col in self.station_features.columns:
            if col not in numeric_features and col != '站名':
                print(f"将特征 {col} 转换为数值型")
                self.station_features[col] = pd.to_numeric(self.station_features[col], errors='coerce').fillna(0)
                if col not in numeric_features:
                    numeric_features.append(col)
        
        # 标准化数值特征
        self.station_features[numeric_features] = self.feature_scaler.fit_transform(
            self.station_features[numeric_features]
        )
        
        # 处理NaN值
        self.station_features.fillna(0, inplace=True)
    
    def _split_datasets_by_station(self, test_ratio=0.2, val_ratio=0.1, random_state=42):
        """
        基于地铁站划分训练/验证/测试集
        
        参数:
        test_ratio: 测试集比例
        val_ratio: 验证集比例
        random_state: 随机种子
        """
        print("基于地铁站划分数据集...")
        
        # 设置随机种子
        np.random.seed(random_state)
        
        # 获取所有唯一的站点
        in_stations = self.in_data['station'].unique()
        out_stations = self.out_data['station'].unique()
        od_stations = set(self.od_data['o_rawname'].unique()) | set(self.od_data['d_rawname'].unique())
        
        # 获取所有站点的并集
        all_stations = list(set(in_stations) | set(out_stations) | od_stations)
        total_stations = len(all_stations)
        
        # 随机打乱站点顺序
        np.random.shuffle(all_stations)
        
        # 划分站点
        test_size = int(test_ratio * total_stations)
        val_size = int(val_ratio * total_stations)
        
        test_stations = set(all_stations[:test_size])
        val_stations = set(all_stations[test_size:test_size+val_size])
        train_stations = set(all_stations[test_size+val_size:])
        
        print(f"站点划分: 训练集 {len(train_stations)}个站, 验证集 {len(val_stations)}个站, 测试集 {len(test_stations)}个站")
        
        # 划分进站数据
        self.in_data['dataset'] = 'train'
        self.in_data.loc[self.in_data['station'].isin(val_stations), 'dataset'] = 'val'
        self.in_data.loc[self.in_data['station'].isin(test_stations), 'dataset'] = 'test'
        
        # 划分出站数据
        self.out_data['dataset'] = 'train'
        self.out_data.loc[self.out_data['station'].isin(val_stations), 'dataset'] = 'val'
        self.out_data.loc[self.out_data['station'].isin(test_stations), 'dataset'] = 'test'
        
        # 划分OD数据
        self.od_data['dataset'] = 'train'
        # OD数据如果起点或终点站在验证集中，则归为验证集
        val_mask = (self.od_data['o_rawname'].isin(val_stations)) | (self.od_data['d_rawname'].isin(val_stations))
        self.od_data.loc[val_mask, 'dataset'] = 'val'
        
        # OD数据如果起点或终点站在测试集中，则归为测试集
        test_mask = (self.od_data['o_rawname'].isin(test_stations)) | (self.od_data['d_rawname'].isin(test_stations))
        self.od_data.loc[test_mask, 'dataset'] = 'test'
        
        # 打印划分结果
        print(f"进站数据集划分: 训练集 {sum(self.in_data['dataset'] == 'train')}, 验证集 {sum(self.in_data['dataset'] == 'val')}, 测试集 {sum(self.in_data['dataset'] == 'test')}")
        print(f"出站数据集划分: 训练集 {sum(self.out_data['dataset'] == 'train')}, 验证集 {sum(self.out_data['dataset'] == 'val')}, 测试集 {sum(self.out_data['dataset'] == 'test')}")
        print(f"OD数据集划分: 训练集 {sum(self.od_data['dataset'] == 'train')}, 验证集 {sum(self.od_data['dataset'] == 'val')}, 测试集 {sum(self.od_data['dataset'] == 'test')}")
    
    def _build_graphs(self):
        """构建用于图神经网络的图结构"""
        print("构建图结构...")
        
        # 构建栅格-站点图
        self._build_grid_station_graph()
        
        # 构建站点-站点图
        self._build_station_station_graph()
    
    def _build_grid_station_graph(self):
        """构建栅格-站点图"""
        print("构建栅格-站点图...")
        
        # 提取所有唯一的站点
        stations = self.in_data['station'].unique()
        num_stations = len(stations)
        
        # 从进站数据中获取栅格信息
        grid_ids = [f"{row['station']}_{row['hour']}" for _, row in self.in_data.iterrows()]
        unique_grid_ids = list(set(grid_ids))
        num_grids = len(unique_grid_ids)
        
        # 创建节点映射
        grid_id_to_idx = {grid_id: i for i, grid_id in enumerate(unique_grid_ids)}
        station_to_idx = {station: i + num_grids for i, station in enumerate(stations)}
        
        # 创建节点特征
        node_features = []
        
        # 栅格节点特征
        for grid_id in unique_grid_ids:
            station, hour = grid_id.split('_')
            hour = int(hour)
            
            # 基本特征：时间（正弦和余弦编码）
            hour_sin = np.sin(2 * np.pi * hour / 24)
            hour_cos = np.cos(2 * np.pi * hour / 24)
            
            # 提取栅格坐标
            if grid_id in self.grid_coords:
                grid_x, grid_y = self.grid_coords[grid_id]
                # 确保坐标是浮点数
                grid_x = float(grid_x)
                grid_y = float(grid_y)
            else:
                # 如果找不到坐标，使用默认值
                grid_x, grid_y = 0.0, 0.0
            
            # 构建特征向量
            grid_features = [hour_sin, hour_cos, grid_x, grid_y]
            
            # 添加站点相关特征（如有）
            if station in self.station_coords:
                station_x, station_y = self.station_coords[station]
                # 确保坐标是浮点数
                station_x = float(station_x)
                station_y = float(station_y)
                # 计算与站点的距离
                distance = np.sqrt((grid_x - station_x)**2 + (grid_y - station_y)**2)
                grid_features.append(distance)
            else:
                grid_features.append(0.0)  # 默认距离
            
            node_features.append(grid_features)
        
        # 站点节点特征
        for station in stations:
            station_features = []
            
            # 获取站点坐标
            if station in self.station_coords:
                station_x, station_y = self.station_coords[station]
                # 确保坐标是浮点数
                station_x = float(station_x)
                station_y = float(station_y)
                station_features.extend([station_x, station_y])
            else:
                station_features.extend([0.0, 0.0])  # 默认坐标
            
            # 添加站点特性（如果存在）
            if self.station_features is not None and '站名' in self.station_features.columns:
                station_df = self.station_features[self.station_features['站名'] == station]
                if not station_df.empty:
                    # 添加站点所有特征（除了站名）
                    numeric_features = station_df.select_dtypes(include=[np.number]).iloc[0].values
                    # 确保所有特征都是浮点数
                    numeric_features = [float(val) for val in numeric_features]
                    station_features.extend(numeric_features)
                else:
                    # 如果找不到站点特征，添加0
                    num_features = len(self.station_features.columns) - 1  # 减去站名列
                    station_features.extend([0.0] * num_features)
            
            node_features.append(station_features)
        
        # 由于不同节点的特征维度可能不同，使用填充统一维度
        max_features = max(len(features) for features in node_features)
        padded_features = [
            np.pad(features, (0, max_features - len(features)), 'constant')
            for features in node_features
        ]
        
        # 构建边索引
        edge_index = []
        
        # 为每个栅格连接到相应的站点
        for grid_id in unique_grid_ids:
            station = grid_id.split('_')[0]
            if station in station_to_idx and grid_id in grid_id_to_idx:
                grid_idx = grid_id_to_idx[grid_id]
                station_idx = station_to_idx[station]
                
                # 添加双向边
                edge_index.append([grid_idx, station_idx])  # 栅格 -> 站点
                edge_index.append([station_idx, grid_idx])  # 站点 -> 栅格
        
        if not edge_index:
            print("警告: 没有找到栅格-站点连接，图将为空")
            edge_index = [[0, 0], [0, 0]]  # 添加一个自环以避免空图
        
        # 将特征转换为张量
        x = torch.tensor(padded_features, dtype=self.dtype)
        edge_index = torch.tensor(edge_index, dtype=torch.long).t().contiguous()
        
        # 创建图数据
        self.grid_station_graph = Data(x=x, edge_index=edge_index)
        
        print(f"栅格-站点图构建完成：{num_grids}个栅格节点，{num_stations}个站点节点，特征维度={max_features}")
    
    def _build_station_station_graph(self):
        """构建站点-站点图"""
        print("构建站点-站点图...")
        
        # 提取所有唯一的站点
        stations = list(set(self.od_data['o_rawname'].unique()) | set(self.od_data['d_rawname'].unique()))
        num_stations = len(stations)
        
        # 创建站点映射
        station_to_idx = {station: i for i, station in enumerate(stations)}
        
        # 创建节点特征
        node_features = []
        
        for station in stations:
            station_features = []
            
            # 获取站点坐标
            if station in self.station_coords:
                station_x, station_y = self.station_coords[station]
                # 确保坐标是浮点数
                station_x = float(station_x)
                station_y = float(station_y)
                station_features.extend([station_x, station_y])
            else:
                station_features.extend([0.0, 0.0])  # 默认坐标
            
            # 添加站点特性（如果存在）
            if self.station_features is not None and '站名' in self.station_features.columns:
                station_df = self.station_features[self.station_features['站名'] == station]
                if not station_df.empty:
                    # 添加站点所有特征（除了站名）
                    numeric_features = station_df.select_dtypes(include=[np.number]).iloc[0].values
                    # 确保所有特征都是浮点数
                    numeric_features = [float(val) for val in numeric_features]
                    station_features.extend(numeric_features)
                else:
                    # 如果找不到站点特征，添加0
                    num_features = len(self.station_features.columns) - 1  # 减去站名列
                    station_features.extend([0.0] * num_features)
            
            node_features.append(station_features)
        
        # 由于不同节点的特征维度可能不同，使用填充统一维度
        max_features = max(len(features) for features in node_features)
        padded_features = [
            np.pad(features, (0, max_features - len(features)), 'constant')
            for features in node_features
        ]
        
        # 构建边索引和边特征
        edge_index = []
        edge_attr = []
        
        # 根据OD数据构建边
        for _, row in tqdm(self.od_data.iterrows(), desc="构建站点间边", total=len(self.od_data)):
            o_station = row['o_rawname']
            d_station = row['d_rawname']
            
            if o_station in station_to_idx and d_station in station_to_idx:
                o_idx = station_to_idx[o_station]
                d_idx = station_to_idx[d_station]
                
                # 添加边
                edge_index.append([o_idx, d_idx])
                
                # 边特征：距离、换乘次数、时间、等待时间
                try:
                    edge_features = [
                        float(row.get('surface_distance', 0)),
                        float(row.get('translate', 0)),
                        float(row.get('time', 0)),
                        float(row.get('wait_time', 0))
                    ]
                except (ValueError, TypeError):
                    edge_features = [0.0, 0.0, 0.0, 0.0]
                
                edge_attr.append(edge_features)
        
        if not edge_index:
            print("警告: 没有找到站点-站点连接，图将为空")
            edge_index = [[0, 0]]  # 添加一个自环以避免空图
            edge_attr = [[0.0, 0.0, 0.0, 0.0]]
        
        # 将特征转换为张量
        x = torch.tensor(padded_features, dtype=self.dtype)
        edge_index = torch.tensor(edge_index, dtype=torch.long).t().contiguous()
        edge_attr = torch.tensor(edge_attr, dtype=self.dtype)
        
        # 创建图数据
        self.station_station_graph = Data(x=x, edge_index=edge_index, edge_attr=edge_attr)
        
        print(f"站点-站点图构建完成：{num_stations}个站点节点，{len(edge_index[0])}条边，节点特征维度={max_features}，边特征维度={len(edge_attr[0]) if len(edge_attr) > 0 else 0}")
    
    def get_in_data_loader(self, batch_size=64, shuffle=True):
        """获取进站数据的数据加载器"""
        # 实现进站数据的批处理加载逻辑
        pass
    
    def get_out_data_loader(self, batch_size=64, shuffle=True):
        """获取出站数据的数据加载器"""
        # 实现出站数据的批处理加载逻辑
        pass
    
    def get_od_data_loader(self, batch_size=64, shuffle=True):
        """获取OD数据的数据加载器"""
        # 实现OD数据的批处理加载逻辑
        pass
    
    def get_grid_station_graph(self):
        """获取栅格-站点图"""
        return self.grid_station_graph
    
    def get_station_station_graph(self):
        """获取站点-站点图"""
        return self.station_station_graph 