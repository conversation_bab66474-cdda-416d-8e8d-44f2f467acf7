import os
import torch
import torch.nn as nn
import torch.optim as optim
import pandas as pd
import geopandas as gpd
import numpy as np
from torch.nn import TransformerEncoder, TransformerEncoderLayer
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import mean_absolute_error, mean_squared_error
from tqdm import tqdm
import matplotlib.pyplot as plt
import shap
from torch.utils.data import DataLoader, Dataset
import warnings
warnings.filterwarnings('ignore')

# 设置随机种子和设备
torch.manual_seed(42)
np.random.seed(42)
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"Using device: {device}")

# 数据加载
def load_data(in_shp, out_shp, od_csv, features_csv):
    in_gdf = gpd.read_file(in_shp)
    out_gdf = gpd.read_file(out_shp)
    od_df = pd.read_csv(od_csv)
    features_df = pd.read_csv(features_csv)
    print("Data loaded successfully.")
    return in_gdf, out_gdf, od_df, features_df

# 数据预处理
def preprocess_data(in_gdf, out_gdf, od_df, features_df):
    # 过滤0-4点数据
    in_gdf = in_gdf[~in_gdf['hour'].isin([0, 1, 2, 3, 4])].copy()
    out_gdf = out_gdf[~out_gdf['hour'].isin([0, 1, 2, 3, 4])].copy()
    od_df = od_df[~od_df['hour'].isin([0, 1, 2, 3, 4])].copy()
    features_df = features_df.copy()
    
    # 站点编码
    all_stations = pd.concat([
        in_gdf['station'], out_gdf['station'],
        od_df['o_rawname'], od_df['d_rawname'],
        features_df['站名']
    ]).dropna().unique()
    
    station_encoder = LabelEncoder()
    station_encoder.fit(all_stations)
    
    in_gdf.loc[:, 'station_id'] = station_encoder.transform(in_gdf['station'])
    out_gdf.loc[:, 'station_id'] = station_encoder.transform(out_gdf['station'])
    od_df.loc[:, 'o_station_id'] = station_encoder.transform(od_df['o_rawname'])
    od_df.loc[:, 'd_station_id'] = station_encoder.transform(od_df['d_rawname'])
    
    # 特征标准化
    scaler_od = StandardScaler()
    scaler_features = StandardScaler()
    
    od_features = ['surface_distance', 'translate', 'time', 'wait_time']
    od_df.loc[:, od_features] = scaler_od.fit_transform(od_df[od_features])
    
    feature_cols = [col for col in features_df.columns if col != '站名']
    features_df.loc[:, feature_cols] = scaler_features.fit_transform(features_df[feature_cols])
    features_df.loc[:, 'station_id'] = features_df['站名'].apply(
        lambda x: station_encoder.transform([x])[0] if x in station_encoder.classes_ else -1
    )
    
    # 时间编码
    def encode_hour(hour):
        hour = hour % 24
        sin = np.sin(2 * np.pi * hour / 24)
        cos = np.cos(2 * np.pi * hour / 24)
        return sin, cos
    
    for df in [in_gdf, out_gdf, od_df]:
        df.loc[:, ['hour_sin', 'hour_cos']] = pd.DataFrame(
            df['hour'].apply(encode_hour).tolist(), 
            index=df.index, 
            columns=['hour_sin', 'hour_cos']
        )
    
    return in_gdf, out_gdf, od_df, features_df, station_encoder, feature_cols, scaler_od, scaler_features

# 改进的数据集类
class ImprovedTrafficDataset(Dataset):
    def __init__(self, in_gdf, out_gdf, od_df, features_df, feature_cols, mode='all'):
        self.in_gdf = in_gdf if mode in ['all', 'in'] else pd.DataFrame()
        self.out_gdf = out_gdf if mode in ['all', 'out'] else pd.DataFrame()
        self.od_df = od_df if mode in ['all', 'od'] else pd.DataFrame()
        self.features_df = features_df
        self.feature_cols = feature_cols
        self.mode = mode
        
        # 创建单独的索引映射
        self.data_list = []
        
        for idx, row in self.in_gdf.iterrows():
            self.data_list.append(('in', idx))
        for idx, row in self.out_gdf.iterrows():
            self.data_list.append(('out', idx))
        for idx, row in self.od_df.iterrows():
            self.data_list.append(('od', idx))
        
    def __len__(self):
        return len(self.data_list)
    
    def __getitem__(self, idx):
        data_type, data_idx = self.data_list[idx]
        
        if data_type == 'in':
            row = self.in_gdf.loc[data_idx]
            station_id = row['station_id']
            geo_features = self.features_df[self.features_df['station_id'] == station_id][self.feature_cols].values
            geo_features = geo_features[0] if len(geo_features) > 0 else np.zeros(len(self.feature_cols))
            
            return {
                'type': 'in',
                'station_id': torch.tensor(station_id, dtype=torch.long),
                'hour_features': torch.tensor([row['hour_sin'], row['hour_cos']], dtype=torch.float),
                'geo_features': torch.tensor(geo_features, dtype=torch.float),
                'hour': torch.tensor(row['hour'], dtype=torch.long),
                'target': torch.tensor(row['count'], dtype=torch.float)
            }
            
        elif data_type == 'out':
            row = self.out_gdf.loc[data_idx]
            station_id = row['station_id']
            geo_features = self.features_df[self.features_df['station_id'] == station_id][self.feature_cols].values
            geo_features = geo_features[0] if len(geo_features) > 0 else np.zeros(len(self.feature_cols))
            
            return {
                'type': 'out',
                'station_id': torch.tensor(station_id, dtype=torch.long),
                'hour_features': torch.tensor([row['hour_sin'], row['hour_cos']], dtype=torch.float),
                'geo_features': torch.tensor(geo_features, dtype=torch.float),
                'hour': torch.tensor(row['hour'], dtype=torch.long),
                'target': torch.tensor(row['count'], dtype=torch.float)
            }
            
        else:  # od
            row = self.od_df.loc[data_idx]
            o_station_id = row['o_station_id']
            d_station_id = row['d_station_id']
            
            o_geo = self.features_df[self.features_df['station_id'] == o_station_id][self.feature_cols].values
            d_geo = self.features_df[self.features_df['station_id'] == d_station_id][self.feature_cols].values
            o_geo = o_geo[0] if len(o_geo) > 0 else np.zeros(len(self.feature_cols))
            d_geo = d_geo[0] if len(d_geo) > 0 else np.zeros(len(self.feature_cols))
            
            return {
                'type': 'od',
                'o_station_id': torch.tensor(o_station_id, dtype=torch.long),
                'd_station_id': torch.tensor(d_station_id, dtype=torch.long),
                'hour_features': torch.tensor([row['hour_sin'], row['hour_cos']], dtype=torch.float),
                'o_geo_features': torch.tensor(o_geo, dtype=torch.float),
                'd_geo_features': torch.tensor(d_geo, dtype=torch.float),
                'od_features': torch.tensor([
                    row['surface_distance'], row['translate'], row['time'], row['wait_time']
                ], dtype=torch.float),
                'hour': torch.tensor(row['hour'], dtype=torch.long),
                'target': torch.tensor(row['trip'], dtype=torch.float)
            }

def collate_fn(batch):
    in_batch = [x for x in batch if x['type'] == 'in']
    out_batch = [x for x in batch if x['type'] == 'out']
    od_batch = [x for x in batch if x['type'] == 'od']
    return in_batch, out_batch, od_batch

# 改进的模型架构
class ImprovedTrafficModel(nn.Module):
    def __init__(self, num_stations, geo_feature_dim, station_dim=64, hidden_dim=128):
        super().__init__()
        self.num_stations = num_stations
        self.station_dim = station_dim
        self.hidden_dim = hidden_dim
        
        # 嵌入层
        self.station_embed = nn.Embedding(num_stations, station_dim)
        self.hour_embed = nn.Embedding(24, 32)
        
        # 特征编码器
        self.geo_encoder = nn.Sequential(
            nn.Linear(geo_feature_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        self.time_encoder = nn.Sequential(
            nn.Linear(2, 32),
            nn.LayerNorm(32),
            nn.ReLU()
        )
        
        # 站点级别的特征融合
        station_feature_dim = station_dim + hidden_dim + 32 + 32
        self.station_fusion = nn.Sequential(
            nn.Linear(station_feature_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2)
        )
        
        # Transformer编码器用于站点间交互
        encoder_layer = TransformerEncoderLayer(
            d_model=hidden_dim, 
            nhead=8, 
            dim_feedforward=hidden_dim*2,
            dropout=0.1,
            batch_first=True
        )
        self.station_transformer = TransformerEncoder(encoder_layer, num_layers=3)
        
        # 进出站预测头
        self.in_predictor = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.LayerNorm(hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim // 2, 1)
        )
        
        self.out_predictor = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.LayerNorm(hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim // 2, 1)
        )
        
        # OD流预测网络
        od_input_dim = station_dim * 2 + hidden_dim * 2 + 4 + 32 + 32
        self.od_encoder = nn.Sequential(
            nn.Linear(od_input_dim, hidden_dim * 2),
            nn.LayerNorm(hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2)
        )
        
        # OD级别的注意力机制
        self.od_attention = nn.MultiheadAttention(
            embed_dim=hidden_dim, 
            num_heads=8, 
            dropout=0.1,
            batch_first=True
        )
        
        self.od_predictor = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.LayerNorm(hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim // 2, 1)
        )
        
    def forward(self, in_batch, out_batch, od_batch):
        # 全局站点特征初始化
        station_features = torch.zeros(self.num_stations, self.hidden_dim, device=device)
        
        # 处理进站数据
        if in_batch:
            station_ids = torch.stack([x['station_id'] for x in in_batch]).to(device)
            hour_features = torch.stack([x['hour_features'] for x in in_batch]).to(device)
            geo_features = torch.stack([x['geo_features'] for x in in_batch]).to(device)
            hours = torch.stack([x['hour'] for x in in_batch]).to(device)
            
            # 特征编码
            station_emb = self.station_embed(station_ids)
            hour_emb = self.hour_embed(hours)
            geo_emb = self.geo_encoder(geo_features)
            time_emb = self.time_encoder(hour_features)
            
            # 特征融合
            combined_features = torch.cat([station_emb, geo_emb, hour_emb, time_emb], dim=-1)
            station_repr = self.station_fusion(combined_features)
            
            # 更新全局站点特征
            station_features[station_ids] = station_repr
        
        # 处理出站数据
        if out_batch:
            station_ids = torch.stack([x['station_id'] for x in out_batch]).to(device)
            hour_features = torch.stack([x['hour_features'] for x in out_batch]).to(device)
            geo_features = torch.stack([x['geo_features'] for x in out_batch]).to(device)
            hours = torch.stack([x['hour'] for x in out_batch]).to(device)
            
            station_emb = self.station_embed(station_ids)
            hour_emb = self.hour_embed(hours)
            geo_emb = self.geo_encoder(geo_features)
            time_emb = self.time_encoder(hour_features)
            
            combined_features = torch.cat([station_emb, geo_emb, hour_emb, time_emb], dim=-1)
            station_repr = self.station_fusion(combined_features)
            
            station_features[station_ids] = station_repr
        
        # 站点间交互
        enhanced_features = self.station_transformer(station_features.unsqueeze(0)).squeeze(0)
        
        # 预测
        predictions = {}
        
        if in_batch:
            station_ids = torch.stack([x['station_id'] for x in in_batch]).to(device)
            in_pred = self.in_predictor(enhanced_features[station_ids])
            predictions['in'] = torch.relu(in_pred.squeeze(-1))
        
        if out_batch:
            station_ids = torch.stack([x['station_id'] for x in out_batch]).to(device)
            out_pred = self.out_predictor(enhanced_features[station_ids])
            predictions['out'] = torch.relu(out_pred.squeeze(-1))
        
        if od_batch:
            o_station_ids = torch.stack([x['o_station_id'] for x in od_batch]).to(device)
            d_station_ids = torch.stack([x['d_station_id'] for x in od_batch]).to(device)
            hour_features = torch.stack([x['hour_features'] for x in od_batch]).to(device)
            o_geo_features = torch.stack([x['o_geo_features'] for x in od_batch]).to(device)
            d_geo_features = torch.stack([x['d_geo_features'] for x in od_batch]).to(device)
            od_features = torch.stack([x['od_features'] for x in od_batch]).to(device)
            hours = torch.stack([x['hour'] for x in od_batch]).to(device)
            
            o_station_emb = self.station_embed(o_station_ids)
            d_station_emb = self.station_embed(d_station_ids)
            hour_emb = self.hour_embed(hours)
            o_geo_emb = self.geo_encoder(o_geo_features)
            d_geo_emb = self.geo_encoder(d_geo_features)
            time_emb = self.time_encoder(hour_features)
            
            # OD特征融合
            od_combined = torch.cat([
                o_station_emb, d_station_emb, o_geo_emb, d_geo_emb, 
                od_features, hour_emb, time_emb
            ], dim=-1)
            
            od_encoded = self.od_encoder(od_combined)
            
            # 应用注意力机制
            od_attended, _ = self.od_attention(
                od_encoded.unsqueeze(0), 
                od_encoded.unsqueeze(0), 
                od_encoded.unsqueeze(0)
            )
            
            od_pred = self.od_predictor(od_attended.squeeze(0))
            predictions['od'] = torch.relu(od_pred.squeeze(-1))
        
        return predictions

# 改进的损失函数
class AdaptiveLoss(nn.Module):
    def __init__(self, alpha=0.5):
        super().__init__()
        self.alpha = alpha
        self.mse = nn.MSELoss()
        self.mae = nn.L1Loss()
        
    def forward(self, pred, target):
        # 对于稀疏数据，结合MSE和MAE
        mse_loss = self.mse(pred, target)
        mae_loss = self.mae(pred, target)
        
        # 自适应权重：对于较小的目标值给予更多关注
        weights = torch.exp(-target / (target.mean() + 1e-8))
        weighted_mse = (weights * (pred - target).pow(2)).mean()
        
        return self.alpha * mse_loss + (1 - self.alpha) * mae_loss + 0.1 * weighted_mse

# 训练函数
def train_model(model, train_loader, val_loader, epochs=5):
    optimizer = optim.AdamW(model.parameters(), lr=1e-3, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=epochs)
    criterion = AdaptiveLoss()
    
    best_mae = float('inf')
    train_losses, val_maes = [], []
    
    for epoch in range(epochs):
        model.train()
        total_loss = 0
        num_batches = 0
        
        for in_batch, out_batch, od_batch in tqdm(train_loader, desc=f"Epoch {epoch+1}/{epochs}"):
            optimizer.zero_grad()
            
            predictions = model(in_batch, out_batch, od_batch)
            
            loss = 0
            loss_count = 0
            
            if 'in' in predictions:
                targets = torch.stack([x['target'] for x in in_batch]).to(device)
                loss += criterion(predictions['in'], targets)
                loss_count += 1
                
            if 'out' in predictions:
                targets = torch.stack([x['target'] for x in out_batch]).to(device)
                loss += criterion(predictions['out'], targets)
                loss_count += 1
                
            if 'od' in predictions:
                targets = torch.stack([x['target'] for x in od_batch]).to(device)
                loss += criterion(predictions['od'], targets)
                loss_count += 1
            
            if loss_count > 0:
                loss = loss / loss_count
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                total_loss += loss.item()
                num_batches += 1
        
        scheduler.step()
        avg_loss = total_loss / max(num_batches, 1)
        train_losses.append(avg_loss)
        
        # 验证
        val_mae = evaluate_model(model, val_loader)
        val_maes.append(val_mae)
        
        print(f"Epoch {epoch+1}: Loss={avg_loss:.4f}, Val MAE={val_mae:.4f}")
        
        if val_mae < best_mae:
            best_mae = val_mae
            torch.save(model.state_dict(), 'best_model.pth')
            print(f"New best model saved with MAE: {best_mae:.4f}")
    
    return train_losses, val_maes

# 评估函数
def evaluate_model(model, loader):
    model.eval()
    all_preds, all_targets = [], []
    
    with torch.no_grad():
        for in_batch, out_batch, od_batch in loader:
            predictions = model(in_batch, out_batch, od_batch)
            
            if 'in' in predictions:
                targets = torch.stack([x['target'] for x in in_batch]).cpu().numpy()
                preds = predictions['in'].cpu().numpy()
                all_preds.extend(preds)
                all_targets.extend(targets)
                
            if 'out' in predictions:
                targets = torch.stack([x['target'] for x in out_batch]).cpu().numpy()
                preds = predictions['out'].cpu().numpy()
                all_preds.extend(preds)
                all_targets.extend(targets)
                
            if 'od' in predictions:
                targets = torch.stack([x['target'] for x in od_batch]).cpu().numpy()
                preds = predictions['od'].cpu().numpy()
                all_preds.extend(preds)
                all_targets.extend(targets)
    
    all_preds = np.maximum(all_preds, 0)  # 确保预测值非负
    return mean_absolute_error(all_targets, all_preds)

# 生成预测结果
def generate_predictions(model, test_in, test_out, test_od, features_df, feature_cols, station_encoder):
    model.eval()
    
    # 为测试数据创建数据集
    test_dataset = ImprovedTrafficDataset(test_in, test_out, test_od, features_df, feature_cols)
    test_loader = DataLoader(test_dataset, batch_size=128, shuffle=False, collate_fn=collate_fn)
    
    # 存储预测结果
    in_predictions = {}
    out_predictions = {}
    od_predictions = {}
    
    with torch.no_grad():
        for in_batch, out_batch, od_batch in test_loader:
            predictions = model(in_batch, out_batch, od_batch)
            
            if 'in' in predictions:
                preds = np.maximum(predictions['in'].cpu().numpy(), 0)
                for i, item in enumerate(in_batch):
                    station_id = item['station_id'].item()
                    hour = item['hour'].item()
                    key = (station_id, hour)
                    in_predictions[key] = preds[i]
            
            if 'out' in predictions:
                preds = np.maximum(predictions['out'].cpu().numpy(), 0)
                for i, item in enumerate(out_batch):
                    station_id = item['station_id'].item()
                    hour = item['hour'].item()
                    key = (station_id, hour)
                    out_predictions[key] = preds[i]
            
            if 'od' in predictions:
                preds = np.maximum(predictions['od'].cpu().numpy(), 0)
                for i, item in enumerate(od_batch):
                    o_station_id = item['o_station_id'].item()
                    d_station_id = item['d_station_id'].item()
                    hour = item['hour'].item()
                    key = (o_station_id, d_station_id, hour)
                    od_predictions[key] = preds[i]
    
    # 添加预测列到原始数据
    test_in_pred = test_in.copy()
    test_in_pred['prediction'] = test_in_pred.apply(
        lambda row: in_predictions.get((row['station_id'], row['hour']), 0), axis=1
    )
    
    test_out_pred = test_out.copy()
    test_out_pred['prediction'] = test_out_pred.apply(
        lambda row: out_predictions.get((row['station_id'], row['hour']), 0), axis=1
    )
    
    test_od_pred = test_od.copy()
    test_od_pred['prediction'] = test_od_pred.apply(
        lambda row: od_predictions.get((row['o_station_id'], row['d_station_id'], row['hour']), 0), axis=1
    )
    
    return test_in_pred, test_out_pred, test_od_pred

# SHAP分析
def shap_analysis(model, test_loader, num_samples=100):
    model.eval()
    
    # 收集样本用于SHAP分析
    sample_data = []
    sample_count = 0
    
    for in_batch, out_batch, od_batch in test_loader:
        if sample_count >= num_samples:
            break
            
        # 只分析OD数据的SHAP值
        for item in od_batch:
            if sample_count >= num_samples:
                break
            sample_data.append(item)
            sample_count += 1
    
    if not sample_data:
        print("No OD data available for SHAP analysis")
        return
    
    def model_wrapper(batch_data):
        od_batch = []
        for i in range(len(batch_data)):
            item = sample_data[i % len(sample_data)].copy()
            # 更新特征值
            features = ['od_features', 'hour_features', 'o_geo_features', 'd_geo_features']
            feature_idx = 0
            for feat in features:
                feat_size = item[feat].shape[0]
                item[feat] = torch.tensor(
                    batch_data[i, feature_idx:feature_idx+feat_size], 
                    dtype=torch.float
                )
                feature_idx += feat_size
            od_batch.append(item)
        
        with torch.no_grad():
            predictions = model([], [], od_batch)
            return predictions['od'].cpu().numpy() if 'od' in predictions else np.array([])
    
    # 准备SHAP分析的输入特征
    feature_matrix = []
    feature_names = []
    
    for item in sample_data:
        features = np.concatenate([
            item['od_features'].numpy(),
            item['hour_features'].numpy(),
            item['o_geo_features'].numpy(),
            item['d_geo_features'].numpy()
        ])
        feature_matrix.append(features)
    
    feature_matrix = np.array(feature_matrix)
    
    # 特征名称
    feature_names = (['surface_distance', 'translate', 'time', 'wait_time'] + 
                    ['hour_sin', 'hour_cos'] + 
                    [f'o_geo_{i}' for i in range(len(sample_data[0]['o_geo_features']))] +
                    [f'd_geo_{i}' for i in range(len(sample_data[0]['d_geo_features']))])
    
    # SHAP分析
    try:
        explainer = shap.KernelExplainer(model_wrapper, feature_matrix[:10])
        shap_values = explainer.shap_values(feature_matrix[:20])
        
        # 生成SHAP图表
        plt.figure(figsize=(12, 8))
        shap.summary_plot(shap_values, feature_matrix[:20], feature_names=feature_names, show=False)
        plt.tight_layout()
        plt.savefig('shap_summary.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 特征重要性
        feature_importance = np.abs(shap_values).mean(0)
        importance_df = pd.DataFrame({
            'feature': feature_names,
            'importance': feature_importance
        }).sort_values('importance', ascending=False)
        
        print("Top 10 most important features:")
        print(importance_df.head(10))
        
        importance_df.to_csv('feature_importance.csv', index=False)
        
    except Exception as e:
        print(f"SHAP analysis failed: {e}")

# 主执行流程
def main():
    # 文件路径
    in_shp = "C:\\Users\\<USER>\\Desktop\\接驳\\in_500.shp"
    out_shp = "C:\\Users\\<USER>\\Desktop\\接驳\\out_500.shp"
    od_csv = "C:\\Users\\<USER>\\Desktop\\接驳\\updated_北京市_subway_od_2024_modified3.csv"
    features_csv = "C:\\Users\\<USER>\\Desktop\\接驳\\station_features_result.csv"
    
    # 加载和预处理数据
    print("Loading data...")
    in_gdf, out_gdf, od_df, features_df = load_data(in_shp, out_shp, od_csv, features_csv)
    
    print("Preprocessing data...")
    in_gdf, out_gdf, od_df, features_df, station_encoder, feature_cols, scaler_od, scaler_features = preprocess_data(
        in_gdf, out_gdf, od_df, features_df
    )
    
    print(f"Data shapes: in={len(in_gdf)}, out={len(out_gdf)}, od={len(od_df)}")
    print(f"Number of stations: {len(station_encoder.classes_)}")
    print(f"Number of geo features: {len(feature_cols)}")
    
    # 数据划分
    train_ratio, val_ratio, test_ratio = 0.7, 0.15, 0.15
    
    # 进站数据划分
    train_in = in_gdf.sample(frac=train_ratio, random_state=42)
    remaining_in = in_gdf.drop(train_in.index)
    val_in = remaining_in.sample(frac=val_ratio/(val_ratio+test_ratio), random_state=42)
    test_in = remaining_in.drop(val_in.index)
    
    # 出站数据划分
    train_out = out_gdf.sample(frac=train_ratio, random_state=42)
    remaining_out = out_gdf.drop(train_out.index)
    val_out = remaining_out.sample(frac=val_ratio/(val_ratio+test_ratio), random_state=42)
    test_out = remaining_out.drop(val_out.index)
    
    # OD数据划分
    train_od = od_df.sample(frac=train_ratio, random_state=42)
    remaining_od = od_df.drop(train_od.index)
    val_od = remaining_od.sample(frac=val_ratio/(val_ratio+test_ratio), random_state=42)
    test_od = remaining_od.drop(val_od.index)
    
    print(f"Train sizes: in={len(train_in)}, out={len(train_out)}, od={len(train_od)}")
    print(f"Val sizes: in={len(val_in)}, out={len(val_out)}, od={len(val_od)}")
    print(f"Test sizes: in={len(test_in)}, out={len(test_out)}, od={len(test_od)}")
    
    # 创建数据集
    train_dataset = ImprovedTrafficDataset(train_in, train_out, train_od, features_df, feature_cols)
    val_dataset = ImprovedTrafficDataset(val_in, val_out, val_od, features_df, feature_cols)
    test_dataset = ImprovedTrafficDataset(test_in, test_out, test_od, features_df, feature_cols)
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=128, shuffle=True, collate_fn=collate_fn)
    val_loader = DataLoader(val_dataset, batch_size=128, shuffle=False, collate_fn=collate_fn)
    test_loader = DataLoader(test_dataset, batch_size=128, shuffle=False, collate_fn=collate_fn)
    
    # 初始化模型
    model = ImprovedTrafficModel(
        num_stations=len(station_encoder.classes_),
        geo_feature_dim=len(feature_cols)
    ).to(device)
    
    print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # 训练模型
    print("Training model...")
    train_losses, val_maes = train_model(model, train_loader, val_loader, epochs=5)
    
    # 加载最佳模型
    model.load_state_dict(torch.load('best_model.pth'))
    
    # 测试集评估
    test_mae = evaluate_model(model, test_loader)
    print(f"Final Test MAE: {test_mae:.4f}")
    
    # 分阶段评估
    print("\n=== 分阶段评估 ===")
    
    # 进站预测评估
    in_test_dataset = ImprovedTrafficDataset(pd.DataFrame(), pd.DataFrame(), pd.DataFrame(), features_df, feature_cols)
    in_test_dataset.in_gdf = test_in
    in_test_dataset.data_list = [('in', idx) for idx in test_in.index]
    in_test_loader = DataLoader(in_test_dataset, batch_size=128, shuffle=False, collate_fn=collate_fn)
    in_mae = evaluate_model(model, in_test_loader)
    print(f"进站预测 MAE: {in_mae:.4f}")
    
    # 出站预测评估
    out_test_dataset = ImprovedTrafficDataset(pd.DataFrame(), pd.DataFrame(), pd.DataFrame(), features_df, feature_cols)
    out_test_dataset.out_gdf = test_out
    out_test_dataset.data_list = [('out', idx) for idx in test_out.index]
    out_test_loader = DataLoader(out_test_dataset, batch_size=128, shuffle=False, collate_fn=collate_fn)
    out_mae = evaluate_model(model, out_test_loader)
    print(f"出站预测 MAE: {out_mae:.4f}")
    
    # OD预测评估
    od_test_dataset = ImprovedTrafficDataset(pd.DataFrame(), pd.DataFrame(), pd.DataFrame(), features_df, feature_cols)
    od_test_dataset.od_df = test_od
    od_test_dataset.data_list = [('od', idx) for idx in test_od.index]
    od_test_loader = DataLoader(od_test_dataset, batch_size=128, shuffle=False, collate_fn=collate_fn)
    od_mae = evaluate_model(model, od_test_loader)
    print(f"OD预测 MAE: {od_mae:.4f}")
    
    # 生成预测结果并保存
    print("\n=== 生成预测结果 ===")
    test_in_pred, test_out_pred, test_od_pred = generate_predictions(
        model, test_in, test_out, test_od, features_df, feature_cols, station_encoder
    )
    
    # 计算各阶段的详细指标
    def calculate_metrics(y_true, y_pred):
        y_pred = np.maximum(y_pred, 0)  # 确保预测值非负
        mae = mean_absolute_error(y_true, y_pred)
        mse = mean_squared_error(y_true, y_pred)
        rmse = np.sqrt(mse)
        mape = np.mean(np.abs((y_true - y_pred) / (y_true + 1e-8))) * 100
        return {'MAE': mae, 'MSE': mse, 'RMSE': rmse, 'MAPE': mape}
    
    print("进站预测指标:")
    in_metrics = calculate_metrics(test_in_pred['count'], test_in_pred['prediction'])
    for metric, value in in_metrics.items():
        print(f"  {metric}: {value:.4f}")
    
    print("出站预测指标:")
    out_metrics = calculate_metrics(test_out_pred['count'], test_out_pred['prediction'])
    for metric, value in out_metrics.items():
        print(f"  {metric}: {value:.4f}")
    
    print("OD预测指标:")
    od_metrics = calculate_metrics(test_od_pred['trip'], test_od_pred['prediction'])
    for metric, value in od_metrics.items():
        print(f"  {metric}: {value:.4f}")
    
    # 保存预测结果
    print("\n=== 保存预测结果 ===")
    
    # 保存shp文件（包含预测列）
    test_in_pred.to_file('in_500_predictions.shp')
    test_out_pred.to_file('out_500_predictions.shp')
    print("已保存进出站预测结果的shp文件")
    
    # 保存OD预测结果的CSV文件
    test_od_pred.to_csv('od_predictions.csv', index=False)
    print("已保存OD预测结果的CSV文件")
    
    # 保存预测vs真实值比较
    comparison_df = pd.DataFrame({
        'Type': (['In'] * len(test_in_pred) + 
                ['Out'] * len(test_out_pred) + 
                ['OD'] * len(test_od_pred)),
        'True_Value': (test_in_pred['count'].tolist() + 
                      test_out_pred['count'].tolist() + 
                      test_od_pred['trip'].tolist()),
        'Predicted_Value': (test_in_pred['prediction'].tolist() + 
                           test_out_pred['prediction'].tolist() + 
                           test_od_pred['prediction'].tolist())
    })
    comparison_df.to_csv('prediction_comparison.csv', index=False)
    print("已保存预测值vs真实值比较文件")
    
    # 保存训练历史
    history_df = pd.DataFrame({
        'epoch': range(1, len(train_losses) + 1),
        'train_loss': train_losses,
        'val_mae': val_maes
    })
    history_df.to_csv('training_history.csv', index=False)
    
    # 绘制训练曲线
    plt.figure(figsize=(12, 4))
    
    plt.subplot(1, 2, 1)
    plt.plot(train_losses)
    plt.title('Training Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    
    plt.subplot(1, 2, 2)
    plt.plot(val_maes)
    plt.title('Validation MAE')
    plt.xlabel('Epoch')
    plt.ylabel('MAE')
    
    plt.tight_layout()
    plt.savefig('training_curves.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("已保存训练曲线图")
    
    # 绘制预测vs真实值散点图
    plt.figure(figsize=(15, 5))
    
    plt.subplot(1, 3, 1)
    plt.scatter(test_in_pred['count'], test_in_pred['prediction'], alpha=0.5)
    plt.plot([0, test_in_pred['count'].max()], [0, test_in_pred['count'].max()], 'r--')
    plt.xlabel('True Values')
    plt.ylabel('Predicted Values')
    plt.title(f'In-Station (MAE: {in_metrics["MAE"]:.2f})')
    
    plt.subplot(1, 3, 2)
    plt.scatter(test_out_pred['count'], test_out_pred['prediction'], alpha=0.5)
    plt.plot([0, test_out_pred['count'].max()], [0, test_out_pred['count'].max()], 'r--')
    plt.xlabel('True Values')
    plt.ylabel('Predicted Values')
    plt.title(f'Out-Station (MAE: {out_metrics["MAE"]:.2f})')
    
    plt.subplot(1, 3, 3)
    plt.scatter(test_od_pred['trip'], test_od_pred['prediction'], alpha=0.5)
    plt.plot([0, test_od_pred['trip'].max()], [0, test_od_pred['trip'].max()], 'r--')
    plt.xlabel('True Values')
    plt.ylabel('Predicted Values')
    plt.title(f'OD Flow (MAE: {od_metrics["MAE"]:.2f})')
    
    plt.tight_layout()
    plt.savefig('prediction_scatter.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("已保存预测散点图")
    
    # 预测值分布分析
    print("\n=== 预测值分布分析 ===")
    
    def analyze_predictions(true_vals, pred_vals, name):
        print(f"\n{name}:")
        print(f"  真实值统计: 最小={true_vals.min():.2f}, 最大={true_vals.max():.2f}, "
              f"均值={true_vals.mean():.2f}, 标准差={true_vals.std():.2f}")
        print(f"  预测值统计: 最小={pred_vals.min():.2f}, 最大={pred_vals.max():.2f}, "
              f"均值={pred_vals.mean():.2f}, 标准差={pred_vals.std():.2f}")
        
        # 检查预测值的差异化程度
        unique_preds = len(np.unique(np.round(pred_vals, 2)))
        total_preds = len(pred_vals)
        print(f"  预测值唯一性: {unique_preds}/{total_preds} ({unique_preds/total_preds*100:.1f}%)")
        
        # 分析零预测的比例
        zero_preds = np.sum(pred_vals < 0.01)
        print(f"  接近零的预测: {zero_preds}/{total_preds} ({zero_preds/total_preds*100:.1f}%)")
    
    analyze_predictions(test_in_pred['count'].values, test_in_pred['prediction'].values, "进站预测")
    analyze_predictions(test_out_pred['count'].values, test_out_pred['prediction'].values, "出站预测")
    analyze_predictions(test_od_pred['trip'].values, test_od_pred['prediction'].values, "OD预测")
    
    # SHAP分析
    print("\n=== 进行SHAP分析 ===")
    shap_analysis(model, test_loader, num_samples=50)
    
    print("\n=== 所有任务完成 ===")
    print("输出文件:")
    print("1. in_500_predictions.shp - 进站预测结果")
    print("2. out_500_predictions.shp - 出站预测结果")
    print("3. od_predictions.csv - OD预测结果")
    print("4. prediction_comparison.csv - 预测值vs真实值比较")
    print("5. training_history.csv - 训练历史")
    print("6. training_curves.png - 训练曲线图")
    print("7. prediction_scatter.png - 预测散点图")
    print("8. shap_summary.png - SHAP特征重要性图")
    print("9. feature_importance.csv - 特征重要性排序")

if __name__ == "__main__":
    main()