"""
集成内存监控的主程序
"""
import torch
import numpy as np
from config import Config
from data_loader import DataLoader
from data_preprocessor_improved import ImprovedDataPreprocessor
from trainer_improved import ImprovedTrainer
from utils import ModelUtils
from memory_monitor import MemoryMonitor, quick_memory_check, estimate_tgcn_memory

# 禁用torch.compile以避免Triton依赖
import torch._dynamo
torch._dynamo.config.suppress_errors = True

def main():
    """带内存监控的主函数"""
    # 初始化内存监控
    monitor = MemoryMonitor()
    
    print("=" * 60)
    print("T-GCN 地铁客流预测系统 (带内存监控)")
    print("=" * 60)
    
    # 初始检查
    quick_memory_check()
    
    # 开始监控
    monitor.start_monitoring()
    
    try:
        # 初始化配置
        config = Config()
        ModelUtils.set_random_seed(config.RANDOM_SEED)
        
        # 第一步：加载数据
        print("\n🔄 第一步：加载原始数据...")
        data_loader = DataLoader(config)
        in_data, out_data, od_data, station_features = data_loader.load_data()
        
        # 检查数据加载后的内存
        current = monitor.get_current_memory()
        print(f"   数据加载后内存: {current['process_mb']:.1f} MB")
        
        # 第二步：预处理前的内存估算
        print("\n🔄 第二步：预处理前内存估算...")
        num_stations = len(set(in_data['station'].unique()) & 
                          set(out_data['station'].unique()) &
                          set(station_features['站名'].unique()))
        
        # 估算内存需求
        estimated_memory = estimate_tgcn_memory(
            num_stations=num_stations,
            time_steps=config.TIME_STEPS,
            hidden_dim=config.HIDDEN_DIM,
            feature_dim=len(station_features.columns) - 1 + 2  # 减去站名列，加上进出站流量
        )
        
        # 检查是否需要调整配置
        current = monitor.get_current_memory()
        if estimated_memory > current['available_mb']:
            print(f"\n⚠️  预估内存需求 {estimated_memory:.1f} MB 超过可用内存 {current['available_mb']:.1f} MB")
            
            # 自动调整配置
            if num_stations > 30:
                print("🔧 自动减少站点数量...")
                max_stations = min(30, int(num_stations * current['available_mb'] / estimated_memory))
                selected_stations = np.random.choice(
                    list(set(in_data['station'].unique())), 
                    max_stations, 
                    replace=False
                )
                in_data = in_data[in_data['station'].isin(selected_stations)]
                out_data = out_data[out_data['station'].isin(selected_stations)]
                od_data = od_data[
                    (od_data['o_rawname'].isin(selected_stations)) &
                    (od_data['d_rawname'].isin(selected_stations))
                ]
                print(f"   站点数量调整为: {max_stations}")
            
            # 调整模型配置
            if config.HIDDEN_DIM > 32:
                config.HIDDEN_DIM = 16
                print(f"   隐藏层维度调整为: {config.HIDDEN_DIM}")
            
            if config.TIME_STEPS > 12:
                config.TIME_STEPS = 6
                print(f"   时间步数调整为: {config.TIME_STEPS}")
        
        # 数据预处理
        print("\n🔄 数据预处理...")
        preprocessor = ImprovedDataPreprocessor(config)
        (X, in_flows, out_flows, od_flows_tensor, edge_index, edge_weights,
         train_indices, test_indices, common_stations, train_stations, test_stations,
         station_to_idx, in_data_filtered, out_data_filtered, od_data_filtered) = preprocessor.preprocess_data(
            in_data, out_data, od_data, station_features
        )
        
        # 预处理后内存检查
        current = monitor.get_current_memory()
        print(f"   预处理后内存: {current['process_mb']:.1f} MB")
        
        # 第三步：模型训练前最后检查
        print("\n🔄 第三步：训练前最终内存检查...")
        
        # 移动数据到设备（与配置保持一致）
        device = torch.device(config.DEVICE if torch.cuda.is_available() else "cpu")
        print(f"   使用设备: {device}")
        
        X = X.to(device)
        in_flows = in_flows.to(device)
        out_flows = out_flows.to(device)
        od_flows_tensor = od_flows_tensor.to(device)
        edge_index = edge_index.to(device)
        edge_weights = edge_weights.to(device)
        
        # 检查数据内存使用
        data_memory = monitor.estimate_data_memory(X, in_flows, out_flows, od_flows_tensor, edge_index, edge_weights)
        print(f"   数据内存使用: {data_memory['total_data_mb']:.1f} MB")
        
        # 检查是否还有足够内存
        recommendations = monitor.get_memory_recommendations(None, X, in_flows, out_flows, od_flows_tensor)
        
        if recommendations['recommendations']:
            print("🔧 内存优化建议:")
            for rec in recommendations['recommendations']:
                print(f"   {rec}")
            
            # 询问是否继续
            response = input("\n是否继续训练？ (y/n): ")
            if response.lower() != 'y':
                print("训练已取消")
                return
        
        # 开始训练
        print("\n🔄 开始模型训练...")
        # 确保trainer使用相同的设备
        config.DEVICE = str(device)
        trainer = ImprovedTrainer(config)
        
        try:
            model = trainer.train_multitask_model(
                X, in_flows, out_flows, od_flows_tensor, edge_index, edge_weights,
                train_indices, test_indices, common_stations
            )
            print("✅ 模型训练完成")
            
        except RuntimeError as e:
            if "out of memory" in str(e) or "bad allocation" in str(e):
                current = monitor.get_current_memory()
                print(f"\n❌ 内存不足错误!")
                print(f"   当前内存使用: {current['process_mb']:.1f} MB")
                print(f"   可用内存: {current['available_mb']:.1f} MB")
                
                # 给出具体的解决方案
                print(f"\n🔧 解决方案:")
                print(f"   1. 减少站点数量到 {len(common_stations)//2}")
                print(f"   2. 减少hidden_dim到 {config.HIDDEN_DIM//2}")
                print(f"   3. 减少time_steps到 {config.TIME_STEPS//2}")
                print(f"   4. 增加系统内存或使用更大的机器")
                
                raise e
            else:
                raise e
        
    except Exception as e:
        print(f"\n❌ 程序执行出错: {str(e)}")
        import traceback
        traceback.print_exc()
        raise
    
    finally:
        # 停止监控并生成报告
        print("\n🔄 生成内存使用报告...")
        final_report = monitor.stop_monitoring()
        
        # 保存内存使用日志
        if monitor.memory_log:
            import pandas as pd
            memory_df = pd.DataFrame(monitor.memory_log)
            memory_df.to_csv('memory_usage_log.csv', index=False)
            print("📁 内存使用日志已保存: memory_usage_log.csv")

if __name__ == "__main__":
    main() 