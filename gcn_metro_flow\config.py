"""
配置文件 - 纯GCN+时间特征地铁流量预测系统
支持多城市数据和可配置参数
"""
import os
import json
from typing import Dict, Any, Optional

class MetroFlowConfig:
    """地铁流量预测配置类"""

    def __init__(self, config_file: Optional[str] = None, city: str = "beijing"):
        self.city = city
        self.config_file = config_file

        # 默认配置
        self._default_config = self._get_default_config()

        # 加载配置
        if config_file and os.path.exists(config_file):
            self._load_from_file(config_file)
        else:
            self._config = self._default_config.copy()

        # 设置城市特定配置
        self._setup_city_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            # 数据路径配置
            "data": {
                "base_dir": r"C:\Users\<USER>\Desktop\接驳",
                "file_patterns": {
                    "in_flow": "in_*_with_coords.shp",
                    "out_flow": "out_*_with_coords.shp",
                    "od_flow": "*od*.csv",
                    "station_connect": "*connect*.csv",
                    "subway_flow": "*flow*.csv",
                    "grid_features": "*data.csv"
                },
                "field_mapping": {
                    "station_name": ["station", "station_name", "站点", "站名"],
                    "hour": ["hour", "时间", "小时"],
                    "flow_count": ["count", "flow", "流量", "人数"],
                    "longitude": ["longitude", "lon", "经度", "lng"],
                    "latitude": ["latitude", "lat", "纬度"],
                    "origin": ["o_rawname", "origin", "起点", "from_station"],
                    "destination": ["d_rawname", "destination", "终点", "to_station"],
                    "trip_count": ["trip", "flow", "流量", "人数"]
                }
            },

            # 模型参数配置
            "model": {
                # GCN参数
                "gcn_hidden_dim": 128,
                "gcn_layers": 3,
                "gcn_dropout": 0.3,

                # 时间特征参数
                "time_embed_dim": 32,
                "hour_embed_dim": 16,

                # 地理特征参数
                "geo_feature_dim": 51,
                "station_feature_dim": 64,

                # 训练参数
                "learning_rate": 0.001,
                "batch_size": 64,
                "epochs": 200,
                "early_stopping_patience": 20,
                "weight_decay": 1e-5,

                # 验证策略参数
                "train_ratio": 0.8,
                "val_ratio": 0.2,
                "time_split_hour": 18,

                # 内存优化
                "use_float16": True,
                "max_batch_size": 32,
                "gradient_accumulation_steps": 2
            },

            # 数据处理参数
            "data_processing": {
                "station_radius_km": 3.0,
                "min_flow_threshold": 1,
                "max_stations": 500,
                "hours_per_day": 24,
                "feature_normalize": True,
                "remove_outliers": True,
                "outlier_threshold": 3.0,
                "sequence_length": 12,  # 时序预测窗口长度
                "prediction_horizon": 6  # 预测未来6小时
            },

            # 设备配置
            "device": {
                "use_cuda": True,
                "cuda_device": 0,
                "num_workers": 4,
                "pin_memory": True
            },

            # 日志配置
            "logging": {
                "log_level": "INFO",
                "log_file": "gcn_metro_flow.log",
                "save_model_every": 10,
                "print_every": 5
            }
        }

    def _load_from_file(self, config_file: str):
        """从文件加载配置"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                file_config = json.load(f)

            # 深度合并配置
            self._config = self._deep_merge(self._default_config, file_config)
            print(f"配置已从 {config_file} 加载")

        except Exception as e:
            print(f"加载配置文件失败: {e}")
            self._config = self._default_config.copy()

    def _deep_merge(self, base: Dict, update: Dict) -> Dict:
        """深度合并字典"""
        result = base.copy()

        for key, value in update.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value

        return result

    def _setup_city_config(self):
        """设置城市特定配置"""
        city_configs = {
            "beijing": {
                "data": {
                    "base_dir": r"C:\Users\<USER>\Desktop\接驳",
                    "specific_files": {
                        "in_flow": "in_500_with_coords.shp",
                        "out_flow": "out_500_with_coords.shp",
                        "od_flow": "updated北京市_subway_od_2024_modified3.csv",
                        "station_connect": "station_connect_2023.csv",
                        "subway_flow": "subway_flow_24h_results.csv",
                        "grid_features": "leti_data.csv"
                    }
                },
                "data_processing": {
                    "max_stations": 358,
                    "geo_feature_dim": 51
                }
            },
            "shanghai": {
                "data": {
                    "base_dir": "./data/shanghai",
                    "specific_files": {
                        "in_flow": "shanghai_in_flow.shp",
                        "out_flow": "shanghai_out_flow.shp",
                        "od_flow": "shanghai_od_data.csv",
                        "station_connect": "shanghai_connections.csv",
                        "subway_flow": "shanghai_flow.csv",
                        "grid_features": "shanghai_features.csv"
                    }
                }
            },
            "guangzhou": {
                "data": {
                    "base_dir": "./data/guangzhou",
                    "specific_files": {
                        "in_flow": "gz_in_flow.shp",
                        "out_flow": "gz_out_flow.shp",
                        "od_flow": "gz_od_data.csv",
                        "station_connect": "gz_connections.csv",
                        "subway_flow": "gz_flow.csv",
                        "grid_features": "gz_features.csv"
                    }
                }
            }
        }

        if self.city in city_configs:
            city_config = city_configs[self.city]
            self._config = self._deep_merge(self._config, city_config)

    def get(self, key_path: str, default=None):
        """获取配置值，支持点分隔的路径"""
        keys = key_path.split('.')
        value = self._config

        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default

        return value

    def set(self, key_path: str, value):
        """设置配置值"""
        keys = key_path.split('.')
        config = self._config

        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]

        config[keys[-1]] = value

    def get_input_files(self) -> Dict[str, str]:
        """获取输入文件路径"""
        base_dir = self.get('data.base_dir')
        specific_files = self.get('data.specific_files', {})

        files = {}
        for file_type, filename in specific_files.items():
            files[file_type] = os.path.join(base_dir, filename)

        return files

    def get_output_files(self) -> Dict[str, str]:
        """获取输出文件路径"""
        base_dir = self.get('data.base_dir')

        return {
            'in_predictions': os.path.join(base_dir, f'{self.city}_in_predictions.shp'),
            'out_predictions': os.path.join(base_dir, f'{self.city}_out_predictions.shp'),
            'od_predictions': os.path.join(base_dir, f'{self.city}_od_predictions.csv'),
            'grid_predictions': os.path.join(base_dir, f'{self.city}_grid_predictions.csv'),
            'comparison': os.path.join(base_dir, f'{self.city}_prediction_comparison.csv'),
            'metrics': os.path.join(base_dir, f'{self.city}_metrics.json'),
            'model': os.path.join(base_dir, f'{self.city}_best_model.pth')
        }

    def save_config(self, filepath: str):
        """保存当前配置到文件"""
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=2, ensure_ascii=False)
            print(f"配置已保存到 {filepath}")
        except Exception as e:
            print(f"保存配置失败: {e}")

    @property
    def config(self) -> Dict[str, Any]:
        """获取完整配置"""
        return self._config.copy()

# 全局配置实例
config = MetroFlowConfig()

# 向后兼容的配置变量
MODEL_CONFIG = config.get('model', {})
DATA_CONFIG = config.get('data_processing', {})
DEVICE_CONFIG = config.get('device', {})
LOG_CONFIG = config.get('logging', {})
INPUT_FILES = config.get_input_files()
OUTPUT_FILES = config.get_output_files()
