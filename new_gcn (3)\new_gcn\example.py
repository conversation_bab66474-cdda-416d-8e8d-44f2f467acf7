#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
GCN地铁流量预测系统使用示例
展示主要功能的使用方法
"""

import os
import numpy as np
import pandas as pd
import torch

from config import Config
from data_loader import DataLoader
from graph_builder import GraphBuilder
from gcn_model import create_model, ModelUtils
from trainer import GCNTrainer
from predictor import GCNPredictor

def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 1. 初始化配置
    config = Config()
    print(f"设备: {config.DEVICE}")
    print(f"隐藏维度: {config.HIDDEN_DIM}")
    
    # 2. 加载数据
    data_loader = DataLoader()
    try:
        data_dict = data_loader.load_all_data()
        print("✓ 数据加载成功")
    except Exception as e:
        print(f"✗ 数据加载失败: {e}")
        print("请确保数据文件路径正确")
        return
    
    # 3. 构建图
    graph_builder = GraphBuilder()
    edge_index, edge_attr, stations = graph_builder.build_graph(
        data_dict['station_coords'], 
        data_dict['od_data']
    )
    
    # 4. 创建模型
    model = create_model(config, 'complex')
    ModelUtils.print_model_info(model)
    
    print("✓ 基本组件初始化完成")

def example_custom_graph():
    """自定义图构建示例"""
    print("\n=== 自定义图构建示例 ===")
    
    # 模拟站点坐标
    station_coords = {
        'station_A': (116.3974, 39.9093),  # 北京天安门
        'station_B': (116.4074, 39.9093),
        'station_C': (116.3974, 39.9193),
        'station_D': (116.4074, 39.9193)
    }
    
    graph_builder = GraphBuilder()
    
    # 构建不同类型的图
    print("构建距离图...")
    dist_edge_index, dist_edge_attr, stations = graph_builder.create_distance_graph(
        station_coords, distance_threshold=1000
    )
    
    print("构建KNN图...")
    knn_edge_index, knn_edge_attr, _ = graph_builder.create_knn_graph(
        station_coords, k=2
    )
    
    # 分析图属性
    properties = graph_builder.analyze_graph_properties(dist_edge_index, stations)
    print(f"图密度: {properties['density']:.4f}")

def example_model_training():
    """模型训练示例"""
    print("\n=== 模型训练示例 ===")
    
    config = Config()
    config.EPOCHS = 5  # 快速演示，减少训练轮数
    
    # 模拟数据
    num_stations = 10
    num_hours = 24
    
    # 模拟流量数据
    flow_data = np.random.rand(num_hours, num_stations).astype(np.float32) * 100
    time_features = np.random.rand(num_hours, num_stations, 6).astype(np.float32)
    spatial_features = np.random.rand(num_stations, 51).astype(np.float32)
    
    # 模拟图结构
    edge_index = torch.randint(0, num_stations, (2, 20))
    edge_attr = torch.rand(20)
    
    # 创建训练器
    trainer = GCNTrainer(config)
    
    # 准备数据
    train_data = trainer.prepare_data_tensors(
        flow_data, time_features, spatial_features, 
        edge_index, edge_attr, [f'station_{i}' for i in range(num_stations)]
    )
    
    # 简单分割
    train_size = int(len(flow_data) * 0.8)
    train_tensors = tuple(tensor[:train_size] for tensor in train_data)
    val_tensors = tuple(tensor[train_size:] for tensor in train_data)
    
    # 创建和训练模型
    model = create_model(config, 'complex')
    trained_model = trainer.train_model(model, train_tensors, val_tensors, 'in_flow')
    
    print("✓ 模型训练完成")

def example_prediction():
    """预测功能示例"""
    print("\n=== 预测功能示例 ===")
    
    config = Config()
    predictor = GCNPredictor(config)
    
    # 模拟坐标数据
    station_coords = {
        f'station_{i}': (116.0 + i*0.01, 39.0 + i*0.01) 
        for i in range(20)
    }
    
    grid_coords = {
        f'grid_{i}': (116.0 + i*0.005, 39.0 + i*0.005) 
        for i in range(50)
    }
    
    # 栅格到站点流量预测
    print("预测栅格到站点流量...")
    grid_to_station = predictor.predict_grid_to_station_flow(
        grid_coords, station_coords, threshold=2000
    )
    
    print(f"有效连接的栅格数: {len(grid_to_station)}")
    
    # 站点到栅格流量预测
    print("预测站点到栅格流量...")
    station_to_grid = predictor.predict_station_to_grid_flow(
        station_coords, grid_coords, threshold=2000
    )
    
    print(f"有效连接的站点数: {len(station_to_grid)}")

def example_new_line_impact():
    """新线路影响分析示例"""
    print("\n=== 新线路影响分析示例 ===")
    
    # 模拟现有网络数据
    current_data = {
        'station_coords': {
            f'existing_station_{i}': (116.0 + i*0.01, 39.0 + i*0.01) 
            for i in range(15)
        },
        'od_data': pd.DataFrame({
            'o_rawname': ['existing_station_0', 'existing_station_1'] * 10,
            'd_rawname': ['existing_station_2', 'existing_station_3'] * 10,
            'trip': np.random.rand(20) * 100
        })
    }
    
    # 定义新线路
    new_stations = ['new_station_A', 'new_station_B', 'new_station_C']
    new_connections = [
        ('new_station_A', 'new_station_B', 1.0),
        ('new_station_B', 'new_station_C', 1.0),
        ('existing_station_5', 'new_station_A', 1.0),  # 连接到现有网络
    ]
    
    predictor = GCNPredictor(Config())
    
    try:
        # 预测新线路影响
        impact = predictor.predict_new_line_impact(
            current_data, new_stations, new_connections
        )
        print("✓ 新线路影响分析完成")
    except Exception as e:
        print(f"新线路影响分析出错: {e}")
        print("注意：此功能需要预训练的模型")

def example_data_analysis():
    """数据分析示例"""
    print("\n=== 数据分析示例 ===")
    
    # 模拟数据统计
    data_stats = {
        'stations': 355,
        'total_records': 535016 + 415697,
        'time_periods': 24,
        'features': 51
    }
    
    print("数据统计:")
    for key, value in data_stats.items():
        print(f"  {key}: {value:,}")
    
    # 模拟特征重要性分析
    feature_importance = np.random.rand(10)
    feature_names = [
        'nighttime_lights', 'total_building_area', 'subway_distance',
        'bus_distance', 'commercial_poi', 'residential_poi',
        'education_poi', 'medical_poi', 'entertainment_poi', 'transport_poi'
    ]
    
    print("\n重要特征 Top 5:")
    sorted_idx = np.argsort(feature_importance)[::-1]
    for i in range(5):
        idx = sorted_idx[i]
        print(f"  {feature_names[idx]}: {feature_importance[idx]:.3f}")

def example_config_customization():
    """配置自定义示例"""
    print("\n=== 配置自定义示例 ===")
    
    # 创建自定义配置
    config = Config()
    
    # 修改模型参数
    config.HIDDEN_DIM = 256
    config.NUM_LAYERS = 4
    config.DROPOUT = 0.3
    config.LEARNING_RATE = 0.0005
    
    # 修改训练参数
    config.EPOCHS = 50
    config.PATIENCE = 10
    config.BATCH_SIZE = 64
    
    # 修改数据参数
    config.USE_FLOAT16 = True
    config.SPATIAL_THRESHOLD = 0.005
    
    print("自定义配置:")
    print(f"  隐藏维度: {config.HIDDEN_DIM}")
    print(f"  网络层数: {config.NUM_LAYERS}")
    print(f"  学习率: {config.LEARNING_RATE}")
    print(f"  批次大小: {config.BATCH_SIZE}")
    print(f"  空间阈值: {config.SPATIAL_THRESHOLD}")

def main():
    """运行所有示例"""
    print("GCN地铁流量预测系统 - 使用示例")
    print("=" * 50)
    
    # 基本使用
    example_basic_usage()
    
    # 自定义图构建
    example_custom_graph()
    
    # 模型训练（仅演示，实际需要真实数据）
    try:
        example_model_training()
    except Exception as e:
        print(f"模型训练示例跳过: {e}")
    
    # 预测功能
    example_prediction()
    
    # 新线路影响分析
    example_new_line_impact()
    
    # 数据分析
    example_data_analysis()
    
    # 配置自定义
    example_config_customization()
    
    print("\n" + "=" * 50)
    print("所有示例运行完成!")
    print("\n使用提示:")
    print("1. 确保数据文件路径正确")
    print("2. 根据硬件配置调整批次大小")
    print("3. 使用GPU可显著加速训练")
    print("4. 预测前需要先训练模型")

if __name__ == "__main__":
    main() 