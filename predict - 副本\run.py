import os
import argparse
import subprocess
import sys

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='地铁流量预测系统')
    parser.add_argument('action', type=str, choices=['train', 'predict', 'both'],
                        help='执行操作: train(训练), predict(预测), both(训练和预测)')
    parser.add_argument('--data_dir', type=str, default='C:/Users/<USER>/Desktop/接驳',
                        help='数据目录路径')
    parser.add_argument('--output_dir', type=str, default='./output',
                        help='输出目录路径')
    parser.add_argument('--results_dir', type=str, default='./results',
                        help='结果目录路径')
    parser.add_argument('--hidden_dim', type=int, default=128,
                        help='隐藏层维度')
    parser.add_argument('--batch_size', type=int, default=64,
                        help='批处理大小')
    parser.add_argument('--epochs', type=int, default=100,
                        help='训练轮数')
    parser.add_argument('--dropout', type=float, default=0.2,
                        help='Dropout比例')
    parser.add_argument('--use_fp16', action='store_true',
                        help='使用半精度浮点数')
    parser.add_argument('--model_prefix', type=str, default='best',
                        help='模型文件前缀，可选值: best或final，仅用于预测')
    return parser.parse_args()

def run_train(args):
    """运行训练过程"""
    print("开始训练...")
    
    cmd = [
        sys.executable, 'src/train.py',
        '--data_dir', args.data_dir,
        '--output_dir', args.output_dir,
        '--hidden_dim', str(args.hidden_dim),
        '--batch_size', str(args.batch_size),
        '--epochs', str(args.epochs),
        '--dropout', str(args.dropout)
    ]
    
    if args.use_fp16:
        cmd.append('--use_fp16')
    
    try:
        subprocess.run(cmd, check=True)
        print("训练完成！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"训练过程中发生错误: {e}")
        return False

def run_predict(args):
    """运行预测过程"""
    print("开始预测...")
    
    cmd = [
        sys.executable, 'src/predict.py',
        '--data_dir', args.data_dir,
        '--model_dir', args.output_dir,
        '--output_dir', args.results_dir,
        '--hidden_dim', str(args.hidden_dim),
        '--model_prefix', args.model_prefix
    ]
    
    if args.use_fp16:
        cmd.append('--use_fp16')
    
    try:
        subprocess.run(cmd, check=True)
        print("预测完成！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"预测过程中发生错误: {e}")
        return False

def main():
    """主函数"""
    args = parse_args()
    
    # 创建目录
    if not os.path.exists(args.output_dir):
        os.makedirs(args.output_dir)
    
    if not os.path.exists(args.results_dir):
        os.makedirs(args.results_dir)
    
    if args.action == 'train' or args.action == 'both':
        success = run_train(args)
        if not success and args.action == 'both':
            print("训练失败，跳过预测步骤。")
            return
    
    if args.action == 'predict' or args.action == 'both':
        run_predict(args)

if __name__ == "__main__":
    main() 