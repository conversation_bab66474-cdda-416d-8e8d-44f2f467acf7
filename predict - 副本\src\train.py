import os
import argparse
import torch
import numpy as np
import pandas as pd
from tqdm import tqdm
import matplotlib.pyplot as plt
from datetime import datetime

from data_loader import MetroDataLoader
from models import MetroFlowModel
from utils import (
    create_directory, timer, evaluate_predictions, plot_prediction_comparison,
    save_results_to_csv, prepare_prediction_comparison, log_message
)

# 配置GPU设备
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='地铁流量预测训练')
    parser.add_argument('--data_dir', type=str, default='C:/Users/<USER>/Desktop/接驳',
                        help='数据目录路径')
    parser.add_argument('--model_dir', type=str, default='./output',
                        help='模型保存目录路径')
    parser.add_argument('--hidden_dim', type=int, default=128,
                        help='隐藏层维度')
    parser.add_argument('--epochs', type=int, default=100,
                        help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=64,
                        help='批量大小')
    parser.add_argument('--lr', type=float, default=0.001,
                        help='学习率')
    parser.add_argument('--use_fp16', action='store_true',
                        help='使用半精度浮点数')
    parser.add_argument('--early_stopping', type=int, default=20,
                        help='早停轮数')
    parser.add_argument('--model_prefix', type=str, default='best',
                        help='模型文件前缀')
    return parser.parse_args()

@timer
def train_in_flow_model(model, data_loader, args, log_file=None):
    """
    训练进站流量预测模型
    
    参数:
    model: 模型
    data_loader: 数据加载器
    args: 命令行参数
    log_file: 日志文件路径
    
    返回:
    训练历史记录
    """
    log_message("开始训练进站流量预测模型...", log_file)
    
    # 获取训练和验证数据
    train_data = data_loader.get_grid_station_graph()
    
    # 获取训练和验证标签
    train_indices = data_loader.in_data[data_loader.in_data['dataset'] == 'train'].index
    val_indices = data_loader.in_data[data_loader.in_data['dataset'] == 'val'].index
    
    train_targets = torch.tensor(
        data_loader.in_data.loc[train_indices, 'count'].values, 
        dtype=torch.float32
    ).to(device).unsqueeze(1)
    
    val_targets = torch.tensor(
        data_loader.in_data.loc[val_indices, 'count'].values, 
        dtype=torch.float32
    ).to(device).unsqueeze(1)
    
    # 训练历史记录
    history = {
        'train_loss': [],
        'train_mae': [],
        'val_loss': [],
        'val_mae': []
    }
    
    # 早停设置
    best_val_loss = float('inf')
    best_epoch = 0
    patience = args.early_stopping
    
    # 训练循环
    for epoch in range(args.epochs):
        # 训练
        model.in_model.train()
        train_loss, train_mae = model.train_in_model(train_data, train_targets)
        
        # 验证
        model.in_model.eval()
        with torch.no_grad():
            val_preds = model.predict_in_flow(train_data)
            val_loss = torch.nn.functional.mse_loss(val_preds[val_indices], val_targets).item()
            val_mae = torch.mean(torch.abs(val_preds[val_indices] - val_targets)).item()
        
        # 记录历史
        history['train_loss'].append(train_loss)
        history['train_mae'].append(train_mae)
        history['val_loss'].append(val_loss)
        history['val_mae'].append(val_mae)
        
        # 打印进度
        log_message(
            f"Epoch {epoch+1}/{args.epochs} - "
            f"Train Loss: {train_loss:.4f}, Train MAE: {train_mae:.4f}, "
            f"Val Loss: {val_loss:.4f}, Val MAE: {val_mae:.4f}",
            log_file
        )
        
        # 早停检查
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            best_epoch = epoch
            # 保存最佳模型
            model.save_models(os.path.join(args.model_dir, args.model_prefix))
        elif epoch - best_epoch > patience:
            log_message(f"早停！在 {patience} 轮内验证损失没有改善。", log_file)
            break
    
    # 加载最佳模型
    model_path_prefix = os.path.join(args.model_dir, args.model_prefix)
    model.load_models(model_path_prefix)
    
    return history

@timer
def train_out_flow_model(model, data_loader, args, log_file=None):
    """
    训练出站流量预测模型
    
    参数:
    model: 模型
    data_loader: 数据加载器
    args: 命令行参数
    log_file: 日志文件路径
    
    返回:
    训练历史记录
    """
    log_message("开始训练出站流量预测模型...", log_file)
    
    # 获取训练和验证数据
    train_data = data_loader.get_grid_station_graph()
    
    # 获取训练和验证标签
    train_indices = data_loader.out_data[data_loader.out_data['dataset'] == 'train'].index
    val_indices = data_loader.out_data[data_loader.out_data['dataset'] == 'val'].index
    
    train_targets = torch.tensor(
        data_loader.out_data.loc[train_indices, 'count'].values, 
        dtype=torch.float32
    ).to(device).unsqueeze(1)
    
    val_targets = torch.tensor(
        data_loader.out_data.loc[val_indices, 'count'].values, 
        dtype=torch.float32
    ).to(device).unsqueeze(1)
    
    # 训练历史记录
    history = {
        'train_loss': [],
        'train_mae': [],
        'val_loss': [],
        'val_mae': []
    }
    
    # 早停设置
    best_val_loss = float('inf')
    best_epoch = 0
    patience = args.early_stopping
    
    # 训练循环
    for epoch in range(args.epochs):
        # 训练
        model.out_model.train()
        train_loss, train_mae = model.train_out_model(train_data, train_targets)
        
        # 验证
        model.out_model.eval()
        with torch.no_grad():
            val_preds = model.predict_out_flow(train_data)
            val_loss = torch.nn.functional.mse_loss(val_preds[val_indices], val_targets).item()
            val_mae = torch.mean(torch.abs(val_preds[val_indices] - val_targets)).item()
        
        # 记录历史
        history['train_loss'].append(train_loss)
        history['train_mae'].append(train_mae)
        history['val_loss'].append(val_loss)
        history['val_mae'].append(val_mae)
        
        # 打印进度
        log_message(
            f"Epoch {epoch+1}/{args.epochs} - "
            f"Train Loss: {train_loss:.4f}, Train MAE: {train_mae:.4f}, "
            f"Val Loss: {val_loss:.4f}, Val MAE: {val_mae:.4f}",
            log_file
        )
        
        # 早停检查
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            best_epoch = epoch
            # 保存最佳模型
            model.save_models(os.path.join(args.model_dir, args.model_prefix))
        elif epoch - best_epoch > patience:
            log_message(f"早停！在 {patience} 轮内验证损失没有改善。", log_file)
            break
    
    # 加载最佳模型
    model_path_prefix = os.path.join(args.model_dir, args.model_prefix)
    model.load_models(model_path_prefix)
    
    return history

@timer
def train_od_flow_model(model, data_loader, args, log_file=None):
    """
    训练OD流量预测模型
    
    参数:
    model: 模型
    data_loader: 数据加载器
    args: 命令行参数
    log_file: 日志文件路径
    
    返回:
    训练历史记录
    """
    log_message("开始训练OD流量预测模型...", log_file)
    
    # 获取训练和验证数据
    train_data = data_loader.get_station_station_graph()
    
    # 获取训练和验证标签
    train_indices = data_loader.od_data[data_loader.od_data['dataset'] == 'train'].index
    val_indices = data_loader.od_data[data_loader.od_data['dataset'] == 'val'].index
    
    train_targets = torch.tensor(
        data_loader.od_data.loc[train_indices, 'trip'].values, 
        dtype=torch.float32
    ).to(device).unsqueeze(1)
    
    val_targets = torch.tensor(
        data_loader.od_data.loc[val_indices, 'trip'].values, 
        dtype=torch.float32
    ).to(device).unsqueeze(1)
    
    # 训练历史记录
    history = {
        'train_loss': [],
        'train_mae': [],
        'val_loss': [],
        'val_mae': []
    }
    
    # 早停设置
    best_val_loss = float('inf')
    best_epoch = 0
    patience = args.early_stopping
    
    # 训练循环
    for epoch in range(args.epochs):
        # 训练
        model.od_model.train()
        train_loss, train_mae = model.train_od_model(train_data, train_targets)
        
        # 验证
        model.od_model.eval()
        with torch.no_grad():
            val_preds = model.predict_od_flow(train_data)
            val_loss = torch.nn.functional.mse_loss(val_preds[val_indices], val_targets).item()
            val_mae = torch.mean(torch.abs(val_preds[val_indices] - val_targets)).item()
        
        # 记录历史
        history['train_loss'].append(train_loss)
        history['train_mae'].append(train_mae)
        history['val_loss'].append(val_loss)
        history['val_mae'].append(val_mae)
        
        # 打印进度
        log_message(
            f"Epoch {epoch+1}/{args.epochs} - "
            f"Train Loss: {train_loss:.4f}, Train MAE: {train_mae:.4f}, "
            f"Val Loss: {val_loss:.4f}, Val MAE: {val_mae:.4f}",
            log_file
        )
        
        # 早停检查
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            best_epoch = epoch
            # 保存最佳模型
            model.save_models(os.path.join(args.model_dir, args.model_prefix))
        elif epoch - best_epoch > patience:
            log_message(f"早停！在 {patience} 轮内验证损失没有改善。", log_file)
            break
    
    # 加载最佳模型
    model_path_prefix = os.path.join(args.model_dir, args.model_prefix)
    model.load_models(model_path_prefix)
    
    return history

def plot_training_history(history, model_type, save_path):
    """
    绘制训练历史记录图
    
    参数:
    history: 训练历史记录
    model_type: 模型类型
    save_path: 保存路径
    """
    plt.figure(figsize=(12, 10))
    
    # 损失曲线
    plt.subplot(2, 1, 1)
    plt.plot(history['train_loss'], label='训练损失')
    plt.plot(history['val_loss'], label='验证损失')
    plt.title(f'{model_type}模型训练损失')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True)
    
    # MAE曲线
    plt.subplot(2, 1, 2)
    plt.plot(history['train_mae'], label='训练MAE')
    plt.plot(history['val_mae'], label='验证MAE')
    plt.title(f'{model_type}模型训练MAE')
    plt.xlabel('Epoch')
    plt.ylabel('MAE')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig(save_path)
    plt.close()

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 创建输出目录
    create_directory(args.model_dir)
    
    # 日志文件
    log_file = os.path.join(args.model_dir, 'training.log')
    
    # 记录基本信息
    log_message(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", log_file)
    log_message(f"设备: {device}", log_file)
    log_message(f"数据目录: {args.data_dir}", log_file)
    log_message(f"模型目录: {args.model_dir}", log_file)
    log_message(f"隐藏层维度: {args.hidden_dim}", log_file)
    log_message(f"训练轮数: {args.epochs}", log_file)
    log_message(f"批量大小: {args.batch_size}", log_file)
    log_message(f"学习率: {args.lr}", log_file)
    log_message(f"使用FP16: {args.use_fp16}", log_file)
    log_message(f"早停轮数: {args.early_stopping}", log_file)
    
    # 加载数据
    data_loader = MetroDataLoader(args.data_dir, use_fp16=args.use_fp16)
    data_loader.load_all_data()
    data_loader.preprocess_data()
    
    # 获取模型维度
    in_node_features = data_loader.grid_station_graph.x.shape[1]
    out_node_features = data_loader.grid_station_graph.x.shape[1]
    od_node_features = data_loader.station_station_graph.x.shape[1]
    od_edge_features = data_loader.station_station_graph.edge_attr.shape[1]
    
    # 创建模型
    model = MetroFlowModel(
        in_node_features, out_node_features, od_node_features, od_edge_features,
        hidden_dim=args.hidden_dim, use_fp16=args.use_fp16
    )
    
    # 训练进站流量模型
    in_history = train_in_flow_model(model, data_loader, args, log_file)
    plot_training_history(in_history, '进站流量', os.path.join(args.model_dir, 'in_training_history.png'))
    
    # 训练出站流量模型
    out_history = train_out_flow_model(model, data_loader, args, log_file)
    plot_training_history(out_history, '出站流量', os.path.join(args.model_dir, 'out_training_history.png'))
    
    # 训练OD流量模型
    od_history = train_od_flow_model(model, data_loader, args, log_file)
    plot_training_history(od_history, 'OD流量', os.path.join(args.model_dir, 'od_training_history.png'))
    
    # 保存最终模型
    model.save_models(os.path.join(args.model_dir, 'final'))
    
    log_message(f"训练完成！最终模型已保存至 {args.model_dir}", log_file)
    log_message(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", log_file)

if __name__ == "__main__":
    main() 