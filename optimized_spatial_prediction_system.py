"""
优化的空间感知地铁流量预测系统
目标：将R²从0.0968提升到0.3+，使用高级GNN和集成方法
"""
import os
import sys
import time
import numpy as np
import pandas as pd
import geopandas as gpd
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch_geometric.nn import GCNConv, GATConv, SAGEConv, global_mean_pool, global_max_pool
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
import warnings
warnings.filterwarnings('ignore')

class AdvancedGraphSAGE(nn.Module):
    """高级GraphSAGE网络"""
    
    def __init__(self, input_dim, hidden_dim=128, num_layers=4, dropout=0.3):
        super(AdvancedGraphSAGE, self).__init__()
        
        self.num_layers = num_layers
        self.dropout = dropout
        
        # 输入投影
        self.input_projection = nn.Linear(input_dim, hidden_dim)
        
        # GraphSAGE层
        self.sage_layers = nn.ModuleList()
        self.sage_layers.append(SAGEConv(hidden_dim, hidden_dim))
        for _ in range(num_layers - 1):
            self.sage_layers.append(SAGEConv(hidden_dim, hidden_dim))
        
        # 批归一化
        self.batch_norms = nn.ModuleList([
            nn.BatchNorm1d(hidden_dim) for _ in range(num_layers)
        ])
        
        # 残差连接
        self.residual_layers = nn.ModuleList([
            nn.Linear(hidden_dim, hidden_dim) for _ in range(num_layers)
        ])
        
    def forward(self, x, edge_index):
        x = self.input_projection(x)
        
        # GraphSAGE层处理
        for i, (sage_layer, bn, residual) in enumerate(zip(
            self.sage_layers, self.batch_norms, self.residual_layers
        )):
            # 残差连接
            residual_x = residual(x)
            
            # GraphSAGE处理
            x = sage_layer(x, edge_index)
            
            # 批归一化
            if x.size(0) > 1:
                x = bn(x)
            
            # 残差连接和激活
            x = x + residual_x
            x = F.relu(x)
            x = F.dropout(x, p=self.dropout, training=self.training)
        
        return x

class MultiHeadGraphAttention(nn.Module):
    """多头图注意力网络"""
    
    def __init__(self, input_dim, hidden_dim=128, num_heads=8, num_layers=3, dropout=0.3):
        super(MultiHeadGraphAttention, self).__init__()
        
        self.num_layers = num_layers
        self.num_heads = num_heads
        
        # 输入投影
        self.input_projection = nn.Linear(input_dim, hidden_dim)
        
        # GAT层
        self.gat_layers = nn.ModuleList()
        self.gat_layers.append(GATConv(hidden_dim, hidden_dim, heads=num_heads, dropout=dropout))
        for _ in range(num_layers - 1):
            self.gat_layers.append(GATConv(hidden_dim * num_heads, hidden_dim, heads=num_heads, dropout=dropout))

        # 最终输出投影层（将多头输出投影到固定维度）
        self.output_projection = nn.Linear(hidden_dim * num_heads, hidden_dim)

        # 层归一化
        self.layer_norms = nn.ModuleList([
            nn.LayerNorm(hidden_dim * num_heads) for _ in range(num_layers)
        ])
        
    def forward(self, x, edge_index):
        x = self.input_projection(x)

        for i, (gat_layer, ln) in enumerate(zip(self.gat_layers, self.layer_norms)):
            # GAT处理
            residual = x
            x = gat_layer(x, edge_index)

            # 层归一化
            if i > 0:  # 第一层维度不匹配，跳过残差连接
                x = ln(x + residual)
            else:
                x = ln(x)

        # 投影到固定维度
        x = self.output_projection(x)

        return x

class EnsembleMetroPredictor(nn.Module):
    """集成地铁流量预测器"""
    
    def __init__(self, input_dim, spatial_dim=3, temporal_dim=6, hidden_dim=128, model_type='station'):
        super(EnsembleMetroPredictor, self).__init__()
        
        self.model_type = model_type
        self.hidden_dim = hidden_dim
        
        if model_type == 'od':
            # OD模型架构（内存优化版本）
            # 减少隐藏层维度以降低内存使用
            od_hidden_dim = hidden_dim // 2  # 64维而不是128维

            self.origin_graphsage = AdvancedGraphSAGE(input_dim, od_hidden_dim, num_layers=2)  # 减少层数
            self.dest_graphsage = AdvancedGraphSAGE(input_dim, od_hidden_dim, num_layers=2)

            # OD对特征处理（简化）
            self.od_pair_encoder = nn.Sequential(
                nn.Linear(4, 16),  # 减少维度
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(16, 8)
            )

            # 时间特征处理（简化）
            self.temporal_encoder = nn.Sequential(
                nn.Linear(temporal_dim, 16),  # 减少维度
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(16, 8)
            )

            # 简化的特征融合（不使用注意力机制以节省内存）
            fusion_dim = od_hidden_dim * 2 + 8 + 8  # 64*2 + 8 + 8 = 144

            # 简单的融合层替代注意力机制
            self.od_fusion_layer = nn.Sequential(
                nn.Linear(fusion_dim, od_hidden_dim),
                nn.ReLU(),
                nn.BatchNorm1d(od_hidden_dim),
                nn.Dropout(0.3)
            )

            # 最终预测层（简化）
            self.final_predictor = nn.Sequential(
                nn.Linear(od_hidden_dim, od_hidden_dim // 2),
                nn.ReLU(),
                nn.Dropout(0.3),
                nn.Linear(od_hidden_dim // 2, 1)
            )
            
        else:
            # 进出站模型架构
            # 主要特征处理
            self.main_graphsage = AdvancedGraphSAGE(input_dim, hidden_dim, num_layers=4)
            self.main_gat = MultiHeadGraphAttention(input_dim, hidden_dim, num_heads=8, num_layers=3)
            
            # 空间特征处理
            self.spatial_encoder = nn.Sequential(
                nn.Linear(spatial_dim, 32),
                nn.ReLU(),
                nn.BatchNorm1d(32),
                nn.Dropout(0.2),
                nn.Linear(32, 16)
            )
            
            # 时间特征处理
            self.temporal_encoder = nn.Sequential(
                nn.Linear(temporal_dim, 32),
                nn.ReLU(),
                nn.BatchNorm1d(32),
                nn.Dropout(0.2),
                nn.Linear(32, 16)
            )
            
            # 特征融合层
            fusion_dim = hidden_dim * 2 + 16 + 16  # GraphSAGE + GAT + spatial + temporal

            # 确保融合维度能被注意力头数整除
            attention_dim = ((fusion_dim // 8) * 8) if fusion_dim % 8 != 0 else fusion_dim

            # 维度调整层
            self.fusion_projection = nn.Linear(fusion_dim, attention_dim)

            # 自注意力机制
            self.self_attention = nn.MultiheadAttention(
                embed_dim=attention_dim,
                num_heads=8,
                dropout=0.3,
                batch_first=True
            )
            
            # 深度预测网络
            self.predictor = nn.Sequential(
                nn.Linear(attention_dim, hidden_dim * 2),
                nn.ReLU(),
                nn.BatchNorm1d(hidden_dim * 2),
                nn.Dropout(0.4),
                nn.Linear(hidden_dim * 2, hidden_dim),
                nn.ReLU(),
                nn.BatchNorm1d(hidden_dim),
                nn.Dropout(0.3),
                nn.Linear(hidden_dim, hidden_dim // 2),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(hidden_dim // 2, 1)
            )
    
    def forward(self, *args):
        if self.model_type == 'od':
            origin_features, dest_features, od_pair_features, temporal_features, edge_index = args

            # 处理起点和终点特征
            origin_embed = self.origin_graphsage(origin_features, edge_index)
            dest_embed = self.dest_graphsage(dest_features, edge_index)

            # 处理OD对特征和时间特征
            od_embed = self.od_pair_encoder(od_pair_features)
            temp_embed = self.temporal_encoder(temporal_features)

            # 特征融合（简化版本，不使用注意力机制）
            combined_features = torch.cat([origin_embed, dest_embed, od_embed, temp_embed], dim=-1)

            # 简单融合处理
            fused_features = self.od_fusion_layer(combined_features)

            # 最终预测
            output = self.final_predictor(fused_features)
            
        else:
            main_features, spatial_features, temporal_features, edge_index = args
            
            # 多个GNN处理主要特征
            graphsage_embed = self.main_graphsage(main_features, edge_index)
            gat_embed = self.main_gat(main_features, edge_index)
            
            # 处理辅助特征
            spatial_embed = self.spatial_encoder(spatial_features)
            temporal_embed = self.temporal_encoder(temporal_features)
            
            # 特征融合
            combined_features = torch.cat([graphsage_embed, gat_embed, spatial_embed, temporal_embed], dim=-1)

            # 维度调整
            combined_features = self.fusion_projection(combined_features)

            # 自注意力处理
            combined_features = combined_features.unsqueeze(1)  # [batch, 1, features]
            attended_features, _ = self.self_attention(
                combined_features, combined_features, combined_features
            )
            attended_features = attended_features.squeeze(1)  # [batch, features]
            
            # 深度预测
            output = self.predictor(attended_features)
        
        return torch.relu(output)  # 确保非负

class OptimizedSpatialPredictionSystem:
    """优化的空间预测系统"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
        # 数据存储
        self.in_data = None
        self.out_data = None
        self.od_data = None
        self.grid_data = None
        self.station_coords = {}
        
        # 深度学习模型
        self.in_model = None
        self.out_model = None
        self.od_model = None
        
        # 传统机器学习模型（用于集成）
        self.in_rf_model = None
        self.out_rf_model = None
        self.od_rf_model = None
        
        self.in_gb_model = None
        self.out_gb_model = None
        self.od_gb_model = None
        
        # 标准化器
        self.feature_scaler = RobustScaler()  # 使用更鲁棒的标准化器
        self.spatial_scaler = StandardScaler()
        self.temporal_scaler = StandardScaler()
        self.od_scaler = RobustScaler()
        
        # 站点信息
        self.stations = []
        self.station_to_idx = {}
        self.edge_index = None

    def load_and_process_data(self):
        """加载和处理数据"""
        print("="*60)
        print("加载完整数据集")
        print("="*60)

        try:
            # 加载基础数据
            self.in_data = gpd.read_file('in_500_with_coords.shp')
            self.out_data = gpd.read_file('out_500_with_coords.shp')
            self.od_data = pd.read_csv('updated北京市_subway_od_2024_modified3.csv')
            self.grid_data = pd.read_csv('leti_data.csv')

            # 清理站点名称
            self.in_data['station_clean'] = self.in_data['station'].apply(lambda x: str(x).split('_')[0])
            self.out_data['station_clean'] = self.out_data['station'].apply(lambda x: str(x).split('_')[0])
            self.od_data['o_station_clean'] = self.od_data['o_rawname'].apply(lambda x: str(x).split('_')[0])
            self.od_data['d_station_clean'] = self.od_data['d_rawname'].apply(lambda x: str(x).split('_')[0])

            # 提取坐标
            self.in_data['longitude'] = self.in_data.geometry.centroid.x
            self.in_data['latitude'] = self.in_data.geometry.centroid.y
            self.out_data['longitude'] = self.out_data.geometry.centroid.x
            self.out_data['latitude'] = self.out_data.geometry.centroid.y

            print(f"进站数据: {self.in_data.shape}")
            print(f"出站数据: {self.out_data.shape}")
            print(f"OD数据: {self.od_data.shape}")
            print(f"栅格数据: {self.grid_data.shape}")

            # 获取站点列表和坐标
            self._extract_station_coordinates()

            # 构建图结构
            self._build_metro_graph()

            # 处理栅格特征
            feature_cols = [col for col in self.grid_data.columns if col not in ['站名', 'id', 'geometry']]
            self.grid_features = self.grid_data[feature_cols].values
            self.grid_features = np.nan_to_num(self.grid_features, nan=0.0)

            print(f"栅格特征维度: {self.grid_features.shape}")
            print(f"图边数量: {self.edge_index.shape[1]}")

            return True

        except Exception as e:
            print(f"数据加载失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _extract_station_coordinates(self):
        """提取站点坐标"""
        all_stations = set()
        all_stations.update(self.in_data['station_clean'].unique())
        all_stations.update(self.out_data['station_clean'].unique())
        all_stations.update(self.od_data['o_station_clean'].unique())
        all_stations.update(self.od_data['d_station_clean'].unique())

        self.stations = sorted(list(all_stations))
        self.station_to_idx = {station: idx for idx, station in enumerate(self.stations)}

        # 提取每个站点的代表坐标
        for station in self.stations:
            in_coords = self.in_data[self.in_data['station_clean'] == station][['longitude', 'latitude']]
            out_coords = self.out_data[self.out_data['station_clean'] == station][['longitude', 'latitude']]

            all_coords = pd.concat([in_coords, out_coords])
            if len(all_coords) > 0:
                self.station_coords[station] = {
                    'longitude': all_coords['longitude'].mean(),
                    'latitude': all_coords['latitude'].mean()
                }
            else:
                # 如果没有进出站数据，使用默认坐标
                self.station_coords[station] = {
                    'longitude': 116.4,  # 北京市中心
                    'latitude': 39.9
                }

        print(f"提取了 {len(self.stations)} 个站点的坐标")

    def _build_metro_graph(self):
        """构建地铁网络图"""
        print("构建地铁网络图...")

        # 基于地理距离构建图
        station_coords_list = []
        for station in self.stations:
            coord = self.station_coords[station]
            station_coords_list.append([coord['longitude'], coord['latitude']])

        station_coords_array = np.array(station_coords_list)

        # 计算距离矩阵
        distances = np.sqrt(((station_coords_array[:, np.newaxis] - station_coords_array[np.newaxis, :]) ** 2).sum(axis=2))

        # 构建边：连接距离最近的k个邻居
        k_neighbors = min(8, len(self.stations) - 1)  # 每个站点连接最近的8个邻居
        edges = []

        for i in range(len(self.stations)):
            # 找到最近的k个邻居
            neighbor_indices = np.argsort(distances[i])[1:k_neighbors+1]  # 排除自己
            for j in neighbor_indices:
                edges.append([i, j])
                edges.append([j, i])  # 无向图

        # 去重
        edges = list(set(tuple(edge) for edge in edges))
        edges = [list(edge) for edge in edges]

        self.edge_index = torch.tensor(edges).T.long()
        print(f"构建了包含 {len(edges)} 条边的地铁网络图")

    def _calculate_spatial_features(self, grid_lon, grid_lat, station_lon, station_lat):
        """计算增强的空间特征"""
        # 基础距离
        distance = np.sqrt((grid_lon - station_lon)**2 + (grid_lat - station_lat)**2)

        # 方向角度
        direction = np.arctan2(station_lat - grid_lat, station_lon - grid_lon)

        # 增强的可达性评分（多层次）
        accessibility = 1.0 / (1.0 + distance * 100)

        return np.array([distance, direction, accessibility])

    def _calculate_time_features(self, hour):
        """计算增强的时间特征"""
        return np.array([
            hour / 23.0,
            np.sin(2 * np.pi * hour / 24),
            np.cos(2 * np.pi * hour / 24),
            1.0 if 6 <= hour <= 10 else 0.0,
            1.0 if 17 <= hour <= 20 else 0.0,
            1.0 if hour in [0,1,2,3,4,5,21,22,23] else 0.0
        ])

    def prepare_station_training_data(self, data, flow_type):
        """准备进出站训练数据"""
        features = []
        spatial_features = []
        time_features = []
        targets = []

        avg_grid_features = np.mean(self.grid_features, axis=0)

        for idx, row in data.iterrows():
            station = row['station_clean']
            hour = row['hour']
            count = row['count']
            grid_lon = row['longitude']
            grid_lat = row['latitude']

            if station in self.station_coords:
                station_coord = self.station_coords[station]
                station_lon = station_coord['longitude']
                station_lat = station_coord['latitude']

                # 基础特征
                station_idx = self.station_to_idx[station]
                base_features = np.concatenate([
                    avg_grid_features,
                    [station_lon, station_lat, station_idx / len(self.stations)]
                ])

                # 空间特征
                spatial_feature = self._calculate_spatial_features(
                    grid_lon, grid_lat, station_lon, station_lat
                )

                # 时间特征
                time_feature = self._calculate_time_features(hour)

                features.append(base_features)
                spatial_features.append(spatial_feature)
                time_features.append(time_feature)
                targets.append(count)

        return (np.array(features), np.array(spatial_features),
                np.array(time_features), np.array(targets))

    def prepare_od_training_data(self, sample_ratio=0.1):
        """准备OD训练数据（使用采样减少内存使用）"""
        print(f"对OD数据进行采样，采样比例: {sample_ratio}")

        # 对OD数据进行采样以减少内存使用
        sampled_od_data = self.od_data.sample(frac=sample_ratio, random_state=42)
        print(f"采样后OD数据量: {len(sampled_od_data):,} 条记录")

        origin_features = []
        dest_features = []
        od_pair_features = []
        time_features = []
        targets = []

        avg_grid_features = np.mean(self.grid_features, axis=0)

        for idx, row in sampled_od_data.iterrows():
            o_station = row['o_station_clean']
            d_station = row['d_station_clean']
            hour = row['hour']
            trip = row['trip']

            # 检查站点是否存在
            if o_station in self.station_coords and d_station in self.station_coords:
                # 起点特征
                o_coord = self.station_coords[o_station]
                o_idx = self.station_to_idx[o_station]
                origin_feature = np.concatenate([
                    avg_grid_features,
                    [o_coord['longitude'], o_coord['latitude'], o_idx / len(self.stations)]
                ])

                # 终点特征
                d_coord = self.station_coords[d_station]
                d_idx = self.station_to_idx[d_station]
                dest_feature = np.concatenate([
                    avg_grid_features,
                    [d_coord['longitude'], d_coord['latitude'], d_idx / len(self.stations)]
                ])

                # OD对特征
                od_pair_feature = np.array([
                    row.get('surface_distance', 0),
                    row.get('translate', 0),
                    row.get('time', 0),
                    row.get('wait_time', 0)
                ])

                # 时间特征
                time_feature = self._calculate_time_features(hour)

                origin_features.append(origin_feature)
                dest_features.append(dest_feature)
                od_pair_features.append(od_pair_feature)
                time_features.append(time_feature)
                targets.append(trip)

        return (np.array(origin_features), np.array(dest_features),
                np.array(od_pair_features), np.array(time_features), np.array(targets))

    def train_ensemble_models(self):
        """训练集成模型"""
        print("="*60)
        print("训练优化的集成模型")
        print("="*60)

        # 准备进站数据
        print("准备进站训练数据...")
        in_features, in_spatial, in_time, in_targets = self.prepare_station_training_data(self.in_data, 'in')

        # 准备出站数据
        print("准备出站训练数据...")
        out_features, out_spatial, out_time, out_targets = self.prepare_station_training_data(self.out_data, 'out')

        # 准备OD数据
        print("准备OD训练数据...")
        od_origin, od_dest, od_pair, od_time, od_targets = self.prepare_od_training_data()

        # 标准化特征
        in_features = self.feature_scaler.fit_transform(in_features)
        in_spatial = self.spatial_scaler.fit_transform(in_spatial)
        in_time = self.temporal_scaler.fit_transform(in_time)

        # 训练进站模型
        print("训练进站集成模型...")
        self._train_ensemble_station_model(in_features, in_spatial, in_time, in_targets, 'in')

        # 使用相同的标准化器处理出站数据
        out_features = self.feature_scaler.transform(out_features)
        out_spatial = self.spatial_scaler.transform(out_spatial)
        out_time = self.temporal_scaler.transform(out_time)

        # 训练出站模型
        print("训练出站集成模型...")
        self._train_ensemble_station_model(out_features, out_spatial, out_time, out_targets, 'out')

        # 标准化OD特征
        od_origin = self.feature_scaler.transform(od_origin)
        od_dest = self.feature_scaler.transform(od_dest)
        od_pair = self.od_scaler.fit_transform(od_pair)
        od_time = self.temporal_scaler.transform(od_time)

        # 训练OD模型（使用更小的采样比例）
        print("训练OD集成模型...")
        od_origin_small, od_dest_small, od_pair_small, od_time_small, od_targets_small = self.prepare_od_training_data(sample_ratio=0.05)  # 进一步减少采样

        # 标准化OD特征
        od_origin_small = self.feature_scaler.transform(od_origin_small)
        od_dest_small = self.feature_scaler.transform(od_dest_small)
        od_pair_small = self.od_scaler.fit_transform(od_pair_small)
        od_time_small = self.temporal_scaler.transform(od_time_small)

        self._train_ensemble_od_model(od_origin_small, od_dest_small, od_pair_small, od_time_small, od_targets_small)

        print("所有集成模型训练完成！")

    def _train_ensemble_station_model(self, features, spatial_features, time_features, targets, model_type):
        """训练进出站集成模型"""
        # 1. 训练深度学习模型
        print(f"  训练{model_type}站深度学习模型...")
        input_dim = features.shape[1]

        if model_type == 'in':
            self.in_model = EnsembleMetroPredictor(
                input_dim=input_dim,
                spatial_dim=3,
                temporal_dim=6,
                hidden_dim=128,
                model_type='station'
            ).to(self.device)
            model = self.in_model
        else:
            self.out_model = EnsembleMetroPredictor(
                input_dim=input_dim,
                spatial_dim=3,
                temporal_dim=6,
                hidden_dim=128,
                model_type='station'
            ).to(self.device)
            model = self.out_model

        # 转换为张量
        features_tensor = torch.tensor(features, dtype=torch.float32).to(self.device)
        spatial_tensor = torch.tensor(spatial_features, dtype=torch.float32).to(self.device)
        time_tensor = torch.tensor(time_features, dtype=torch.float32).to(self.device)
        targets_tensor = torch.tensor(targets, dtype=torch.float32).to(self.device)
        edge_index = self.edge_index.to(self.device)

        # 训练设置
        optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=1e-4)
        criterion = nn.MSELoss()
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=15, factor=0.5)

        # 训练循环
        model.train()
        best_loss = float('inf')
        patience = 0

        for epoch in range(150):  # 增加训练轮数
            optimizer.zero_grad()

            predictions = model(features_tensor, spatial_tensor, time_tensor, edge_index).squeeze()
            loss = criterion(predictions, targets_tensor)

            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
            optimizer.step()

            scheduler.step(loss.item())

            if loss.item() < best_loss:
                best_loss = loss.item()
                patience = 0
            else:
                patience += 1

            if epoch % 30 == 0:
                print(f"    {model_type} DL Epoch {epoch}: Loss = {loss.item():.6f}")

            if patience >= 30:
                print(f"    {model_type} DL 早停于第 {epoch} 轮")
                break

        # 2. 训练传统机器学习模型
        print(f"  训练{model_type}站传统ML模型...")

        # 合并所有特征用于传统ML
        combined_features = np.concatenate([features, spatial_features, time_features], axis=1)

        # 随机森林
        if model_type == 'in':
            self.in_rf_model = RandomForestRegressor(
                n_estimators=200,
                max_depth=15,
                random_state=42,
                n_jobs=-1
            )
            self.in_rf_model.fit(combined_features, targets)

            # 梯度提升
            self.in_gb_model = GradientBoostingRegressor(
                n_estimators=200,
                max_depth=8,
                learning_rate=0.1,
                random_state=42
            )
            self.in_gb_model.fit(combined_features, targets)
        else:
            self.out_rf_model = RandomForestRegressor(
                n_estimators=200,
                max_depth=15,
                random_state=42,
                n_jobs=-1
            )
            self.out_rf_model.fit(combined_features, targets)

            # 梯度提升
            self.out_gb_model = GradientBoostingRegressor(
                n_estimators=200,
                max_depth=8,
                learning_rate=0.1,
                random_state=42
            )
            self.out_gb_model.fit(combined_features, targets)

    def _train_ensemble_od_model(self, origin_features, dest_features, od_pair_features, time_features, targets):
        """训练OD集成模型（内存优化版本）"""
        # 1. 训练深度学习模型（使用批处理）
        print("  训练OD深度学习模型（批处理模式）...")
        input_dim = origin_features.shape[1]

        # 使用更小的隐藏维度
        self.od_model = EnsembleMetroPredictor(
            input_dim=input_dim,
            spatial_dim=4,
            temporal_dim=6,
            hidden_dim=64,  # 减少隐藏维度
            model_type='od'
        ).to(self.device)

        # 训练设置
        optimizer = optim.AdamW(self.od_model.parameters(), lr=0.001, weight_decay=1e-4)
        criterion = nn.MSELoss()
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)

        # 批处理设置
        batch_size = 256  # 更小的批量大小
        n_samples = len(origin_features)
        n_batches = (n_samples + batch_size - 1) // batch_size

        print(f"    总样本数: {n_samples:,}, 批大小: {batch_size}, 批数: {n_batches}")

        # 训练循环
        self.od_model.train()
        best_loss = float('inf')
        patience = 0

        for epoch in range(100):  # 减少训练轮数
            epoch_loss = 0.0

            # 随机打乱数据
            indices = np.random.permutation(n_samples)

            for batch_idx in range(n_batches):
                start_idx = batch_idx * batch_size
                end_idx = min((batch_idx + 1) * batch_size, n_samples)
                batch_indices = indices[start_idx:end_idx]

                # 获取批数据
                batch_origin = torch.tensor(origin_features[batch_indices], dtype=torch.float32).to(self.device)
                batch_dest = torch.tensor(dest_features[batch_indices], dtype=torch.float32).to(self.device)
                batch_od_pair = torch.tensor(od_pair_features[batch_indices], dtype=torch.float32).to(self.device)
                batch_time = torch.tensor(time_features[batch_indices], dtype=torch.float32).to(self.device)
                batch_targets = torch.tensor(targets[batch_indices], dtype=torch.float32).to(self.device)
                edge_index = self.edge_index.to(self.device)

                # 前向传播
                optimizer.zero_grad()
                predictions = self.od_model(
                    batch_origin, batch_dest, batch_od_pair, batch_time, edge_index
                ).squeeze()

                loss = criterion(predictions, batch_targets)

                # 反向传播
                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.od_model.parameters(), 1.0)
                optimizer.step()

                epoch_loss += loss.item()

                # 清理内存
                del batch_origin, batch_dest, batch_od_pair, batch_time, batch_targets, predictions
                torch.cuda.empty_cache() if torch.cuda.is_available() else None

            avg_loss = epoch_loss / n_batches
            scheduler.step(avg_loss)

            if avg_loss < best_loss:
                best_loss = avg_loss
                patience = 0
            else:
                patience += 1

            if epoch % 20 == 0:
                print(f"    OD DL Epoch {epoch}: Loss = {avg_loss:.6f}")

            if patience >= 20:
                print(f"    OD DL 早停于第 {epoch} 轮")
                break

        # 2. 训练传统机器学习模型
        print("  训练OD传统ML模型...")

        # 合并所有特征用于传统ML
        combined_features = np.concatenate([
            origin_features, dest_features, od_pair_features, time_features
        ], axis=1)

        # 随机森林（减少参数）
        self.od_rf_model = RandomForestRegressor(
            n_estimators=100,  # 减少树的数量
            max_depth=10,      # 减少深度
            random_state=42,
            n_jobs=-1
        )
        self.od_rf_model.fit(combined_features, targets)

        # 梯度提升（减少参数）
        self.od_gb_model = GradientBoostingRegressor(
            n_estimators=100,  # 减少树的数量
            max_depth=6,       # 减少深度
            learning_rate=0.1,
            random_state=42
        )
        self.od_gb_model.fit(combined_features, targets)

    def generate_optimized_predictions(self):
        """生成优化的预测结果"""
        print("="*60)
        print("生成优化的预测结果")
        print("="*60)

        # 时间分割策略
        test_hours = list(range(18, 24))

        # 生成进站预测
        print("生成进站预测...")
        in_test_data = self.in_data[self.in_data['hour'].isin(test_hours)].copy()

        if len(in_test_data) > 0:
            in_test_features, in_test_spatial, in_test_time, _ = self.prepare_station_training_data(in_test_data, 'in')

            # 标准化
            in_test_features = self.feature_scaler.transform(in_test_features)
            in_test_spatial = self.spatial_scaler.transform(in_test_spatial)
            in_test_time = self.temporal_scaler.transform(in_test_time)

            # 集成预测
            in_predictions = self._ensemble_predict_station(
                in_test_features, in_test_spatial, in_test_time, 'in'
            )

            in_test_data.loc[:, 'prediction'] = np.maximum(in_predictions, 0)

            # 保存结果
            self._save_station_predictions(in_test_data, 'in')

        # 生成出站预测
        print("生成出站预测...")
        out_test_data = self.out_data[self.out_data['hour'].isin(test_hours)].copy()

        if len(out_test_data) > 0:
            out_test_features, out_test_spatial, out_test_time, _ = self.prepare_station_training_data(out_test_data, 'out')

            # 标准化
            out_test_features = self.feature_scaler.transform(out_test_features)
            out_test_spatial = self.spatial_scaler.transform(out_test_spatial)
            out_test_time = self.temporal_scaler.transform(out_test_time)

            # 集成预测
            out_predictions = self._ensemble_predict_station(
                out_test_features, out_test_spatial, out_test_time, 'out'
            )

            out_test_data.loc[:, 'prediction'] = np.maximum(out_predictions, 0)

            # 保存结果
            self._save_station_predictions(out_test_data, 'out')

        # 生成OD预测
        print("生成OD预测...")
        od_test_data = self.od_data[self.od_data['hour'].isin(test_hours)].copy()

        if len(od_test_data) > 0:
            od_predictions = self._ensemble_predict_od(od_test_data)

            od_test_data.loc[:, 'prediction'] = np.maximum(od_predictions, 0)

            # 保存OD预测结果
            od_test_data.to_csv('optimized_od_predictions.csv', index=False)
            print(f"✓ 优化OD预测结果已保存: {len(od_test_data)} 条记录")

        # 生成完整对比分析
        self._generate_optimized_comparison(in_test_data, out_test_data, od_test_data)

        return in_test_data, out_test_data, od_test_data

    def _ensemble_predict_station(self, features, spatial_features, time_features, model_type):
        """集成预测进出站流量"""
        # 深度学习预测
        model = self.in_model if model_type == 'in' else self.out_model
        model.eval()

        with torch.no_grad():
            features_tensor = torch.tensor(features, dtype=torch.float32).to(self.device)
            spatial_tensor = torch.tensor(spatial_features, dtype=torch.float32).to(self.device)
            time_tensor = torch.tensor(time_features, dtype=torch.float32).to(self.device)
            edge_index = self.edge_index.to(self.device)

            dl_predictions = model(features_tensor, spatial_tensor, time_tensor, edge_index).squeeze()
            dl_predictions = dl_predictions.cpu().numpy()

        # 传统机器学习预测
        combined_features = np.concatenate([features, spatial_features, time_features], axis=1)

        if model_type == 'in':
            rf_predictions = self.in_rf_model.predict(combined_features)
            gb_predictions = self.in_gb_model.predict(combined_features)
        else:
            rf_predictions = self.out_rf_model.predict(combined_features)
            gb_predictions = self.out_gb_model.predict(combined_features)

        # 集成预测（加权平均）
        ensemble_predictions = (
            0.5 * dl_predictions +
            0.3 * rf_predictions +
            0.2 * gb_predictions
        )

        return ensemble_predictions

    def _ensemble_predict_od(self, od_test_data):
        """集成预测OD流量（批处理版本）"""
        print("  使用批处理进行OD预测...")

        # 准备特征
        origin_features = []
        dest_features = []
        od_pair_features = []
        time_features = []

        avg_grid_features = np.mean(self.grid_features, axis=0)

        for idx, row in od_test_data.iterrows():
            o_station = row['o_station_clean']
            d_station = row['d_station_clean']
            hour = row['hour']

            if o_station in self.station_coords and d_station in self.station_coords:
                # 起点特征
                o_coord = self.station_coords[o_station]
                o_idx = self.station_to_idx[o_station]
                origin_feature = np.concatenate([
                    avg_grid_features,
                    [o_coord['longitude'], o_coord['latitude'], o_idx / len(self.stations)]
                ])

                # 终点特征
                d_coord = self.station_coords[d_station]
                d_idx = self.station_to_idx[d_station]
                dest_feature = np.concatenate([
                    avg_grid_features,
                    [d_coord['longitude'], d_coord['latitude'], d_idx / len(self.stations)]
                ])

                # OD对特征
                od_pair_feature = np.array([
                    row.get('surface_distance', 0),
                    row.get('translate', 0),
                    row.get('time', 0),
                    row.get('wait_time', 0)
                ])

                # 时间特征
                time_feature = self._calculate_time_features(hour)

                origin_features.append(origin_feature)
                dest_features.append(dest_feature)
                od_pair_features.append(od_pair_feature)
                time_features.append(time_feature)
            else:
                # 如果站点不存在，添加零向量
                zero_feature = np.zeros(len(avg_grid_features) + 3)
                origin_features.append(zero_feature)
                dest_features.append(zero_feature)
                od_pair_features.append(np.zeros(4))
                time_features.append(self._calculate_time_features(hour))

        # 转换为数组并标准化
        origin_features = np.array(origin_features)
        dest_features = np.array(dest_features)
        od_pair_features = np.array(od_pair_features)
        time_features = np.array(time_features)

        origin_features = self.feature_scaler.transform(origin_features)
        dest_features = self.feature_scaler.transform(dest_features)
        od_pair_features = self.od_scaler.transform(od_pair_features)
        time_features = self.temporal_scaler.transform(time_features)

        # 深度学习预测（批处理）
        self.od_model.eval()
        dl_predictions = []

        batch_size = 256  # 预测时的批大小
        n_samples = len(origin_features)
        n_batches = (n_samples + batch_size - 1) // batch_size

        with torch.no_grad():
            for batch_idx in range(n_batches):
                start_idx = batch_idx * batch_size
                end_idx = min((batch_idx + 1) * batch_size, n_samples)

                # 获取批数据
                batch_origin = torch.tensor(origin_features[start_idx:end_idx], dtype=torch.float32).to(self.device)
                batch_dest = torch.tensor(dest_features[start_idx:end_idx], dtype=torch.float32).to(self.device)
                batch_od_pair = torch.tensor(od_pair_features[start_idx:end_idx], dtype=torch.float32).to(self.device)
                batch_time = torch.tensor(time_features[start_idx:end_idx], dtype=torch.float32).to(self.device)
                edge_index = self.edge_index.to(self.device)

                # 预测
                batch_predictions = self.od_model(
                    batch_origin, batch_dest, batch_od_pair, batch_time, edge_index
                ).squeeze()

                dl_predictions.extend(batch_predictions.cpu().numpy())

                # 清理内存
                del batch_origin, batch_dest, batch_od_pair, batch_time, batch_predictions
                torch.cuda.empty_cache() if torch.cuda.is_available() else None

        dl_predictions = np.array(dl_predictions)

        # 传统机器学习预测
        combined_features = np.concatenate([
            origin_features, dest_features, od_pair_features, time_features
        ], axis=1)

        rf_predictions = self.od_rf_model.predict(combined_features)
        gb_predictions = self.od_gb_model.predict(combined_features)

        # 集成预测（加权平均）
        ensemble_predictions = (
            0.5 * dl_predictions +
            0.3 * rf_predictions +
            0.2 * gb_predictions
        )

        return ensemble_predictions

    def _save_station_predictions(self, data, flow_type):
        """保存进出站预测结果"""
        filename = f'optimized_{flow_type}_500_predictions_with_coords'

        try:
            import glob
            for f in glob.glob(f'{filename}.*'):
                try:
                    os.remove(f)
                except:
                    pass

            data.to_file(f'{filename}.shp')
            print(f"✓ 优化{flow_type}站预测结果已保存: {len(data)} 条记录")
        except Exception as e:
            print(f"保存{flow_type}站shapefile失败: {e}")
            data_csv = data.drop(columns=['geometry'])
            data_csv.to_csv(f'{filename}.csv', index=False)
            print(f"✓ 优化{flow_type}站预测结果已保存为CSV: {len(data)} 条记录")

    def _generate_optimized_comparison(self, in_test_data, out_test_data, od_test_data):
        """生成优化的预测对比分析"""
        print("生成优化预测对比汇总...")

        comparison_data = []

        # 进站对比
        if in_test_data is not None and len(in_test_data) > 0:
            for _, row in in_test_data.iterrows():
                comparison_data.append({
                    'Type': 'In',
                    'True_Value': float(row['count']),
                    'Predicted_Value': float(row['prediction']),
                    'Station': str(row['station_clean']),
                    'Hour': int(row['hour']),
                    'Error': float(abs(row['count'] - row['prediction']))
                })

        # 出站对比
        if out_test_data is not None and len(out_test_data) > 0:
            for _, row in out_test_data.iterrows():
                comparison_data.append({
                    'Type': 'Out',
                    'True_Value': float(row['count']),
                    'Predicted_Value': float(row['prediction']),
                    'Station': str(row['station_clean']),
                    'Hour': int(row['hour']),
                    'Error': float(abs(row['count'] - row['prediction']))
                })

        # OD对比
        if od_test_data is not None and len(od_test_data) > 0:
            for _, row in od_test_data.iterrows():
                comparison_data.append({
                    'Type': 'OD',
                    'True_Value': float(row['trip']),
                    'Predicted_Value': float(row['prediction']),
                    'Station': f"{row['o_station_clean']}->{row['d_station_clean']}",
                    'Hour': int(row['hour']),
                    'Error': float(abs(row['trip'] - row['prediction']))
                })

        # 保存完整对比结果
        if comparison_data:
            comparison_df = pd.DataFrame(comparison_data)
            comparison_df.to_csv('optimized_prediction_comparison.csv', index=False)
            print(f"✓ 优化预测对比汇总已保存: {len(comparison_data)} 条记录")

            # 生成性能分析
            self._analyze_optimized_performance(comparison_df)

        return comparison_data

    def _analyze_optimized_performance(self, comparison_df):
        """分析优化的预测性能"""
        print("\n" + "="*60)
        print("优化预测性能分析")
        print("="*60)

        performance_results = {}

        # 按类型分析
        for pred_type in ['In', 'Out', 'OD']:
            type_data = comparison_df[comparison_df['Type'] == pred_type]

            if len(type_data) > 0:
                true_values = type_data['True_Value'].values
                pred_values = type_data['Predicted_Value'].values

                mae = mean_absolute_error(true_values, pred_values)
                rmse = np.sqrt(mean_squared_error(true_values, pred_values))
                r2 = r2_score(true_values, pred_values)

                performance_results[pred_type] = {
                    'samples': len(type_data),
                    'mae': mae,
                    'rmse': rmse,
                    'r2': r2,
                    'mean_true': np.mean(true_values),
                    'mean_pred': np.mean(pred_values)
                }

                print(f"{pred_type} 流量预测:")
                print(f"  样本数: {len(type_data):,}")
                print(f"  MAE:  {mae:.4f}")
                print(f"  RMSE: {rmse:.4f}")
                print(f"  R²:   {r2:.4f}")
                print(f"  真实值均值: {np.mean(true_values):.2f}")
                print(f"  预测值均值: {np.mean(pred_values):.2f}")
                print()

        # 整体性能
        all_true = comparison_df['True_Value'].values
        all_pred = comparison_df['Predicted_Value'].values

        overall_mae = mean_absolute_error(all_true, all_pred)
        overall_rmse = np.sqrt(mean_squared_error(all_true, all_pred))
        overall_r2 = r2_score(all_true, all_pred)

        performance_results['Overall'] = {
            'samples': len(comparison_df),
            'mae': overall_mae,
            'rmse': overall_rmse,
            'r2': overall_r2,
            'mean_true': np.mean(all_true),
            'mean_pred': np.mean(all_pred)
        }

        print("整体预测性能:")
        print(f"  总样本数: {len(comparison_df):,}")
        print(f"  MAE:  {overall_mae:.4f}")
        print(f"  RMSE: {overall_rmse:.4f}")
        print(f"  R²:   {overall_r2:.4f}")
        print(f"  真实值均值: {np.mean(all_true):.2f}")
        print(f"  预测值均值: {np.mean(all_pred):.2f}")

        # 保存性能结果
        import json
        with open('optimized_performance_analysis.json', 'w') as f:
            json.dump(performance_results, f, indent=2)

        print("✓ 优化性能分析已保存到 optimized_performance_analysis.json")

        # 与原始系统对比
        self._compare_with_original_system(performance_results)

    def _compare_with_original_system(self, optimized_results):
        """与原始系统对比"""
        print("\n" + "="*60)
        print("与原始系统性能对比")
        print("="*60)

        # 原始系统性能（从之前的结果）
        original_overall_r2 = 0.0968
        original_overall_mae = 2.7816

        # 优化系统性能
        optimized_overall_r2 = optimized_results['Overall']['r2']
        optimized_overall_mae = optimized_results['Overall']['mae']

        # 计算改进
        r2_improvement = optimized_overall_r2 - original_overall_r2
        mae_improvement = (original_overall_mae - optimized_overall_mae) / original_overall_mae * 100

        print(f"R² 改进:")
        print(f"  原始系统: {original_overall_r2:.4f}")
        print(f"  优化系统: {optimized_overall_r2:.4f}")
        print(f"  绝对改进: {r2_improvement:+.4f}")
        print(f"  相对改进: {(r2_improvement / abs(original_overall_r2) * 100):+.2f}%")
        print()

        print(f"MAE 改进:")
        print(f"  原始系统: {original_overall_mae:.4f}")
        print(f"  优化系统: {optimized_overall_mae:.4f}")
        print(f"  相对改进: {mae_improvement:+.2f}%")
        print()

        # 判断是否达到目标
        if optimized_overall_r2 >= 0.3:
            print("🎉 成功达到R² ≥ 0.3的目标！")
        else:
            print(f"⚠️ 未完全达到R² ≥ 0.3的目标，当前为 {optimized_overall_r2:.4f}")

        if mae_improvement > 0:
            print("🎉 MAE性能得到改善！")
        else:
            print("⚠️ MAE性能有所下降")

def main():
    """主函数"""
    print("="*80)
    print("优化的空间感知地铁流量预测系统")
    print("目标：将R²从0.0968提升到0.3+")
    print("="*80)

    start_time = time.time()

    try:
        # 初始化系统
        system = OptimizedSpatialPredictionSystem()

        # 加载数据
        if not system.load_and_process_data():
            return False

        # 训练模型
        system.train_ensemble_models()

        # 生成预测
        in_results, out_results, od_results = system.generate_optimized_predictions()

        print(f"\n运行时间: {time.time() - start_time:.2f} 秒")
        print("="*60)
        print("优化的空间感知预测系统运行完成！")
        print("="*60)

        return True

    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
