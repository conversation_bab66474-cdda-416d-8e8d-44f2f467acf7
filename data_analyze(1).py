import pandas as pd
import networkx as nx
from collections import defaultdict
from tqdm import tqdm

# 读取数据
def load_data():
    print("正在读取数据...")
    # 读取OD数据
    od_data = pd.read_csv('updated北京市_subway_od_2024_modified3.csv')
    # 读取站点连接数据
    station_data = pd.read_csv('station_connect_2023.csv')
    return od_data, station_data

def create_graph(station_data, od_data):
    print("正在构建地铁网络图...")
    G = nx.Graph()
    
    # 用于存储站点之间的距离
    distance_dict = {}
    
    # 从od_data中提取距离信息
    print("处理站点距离信息...")
    for _, row in tqdm(od_data.iterrows(), total=len(od_data), desc="处理OD数据"):
        key = (row['o_rawname'], row['d_rawname'])
        distance_dict[key] = row['surface_distance']
        distance_dict[(row['d_rawname'], row['o_rawname'])] = row['surface_distance']

    # 处理站点连接数据
    print("处理站点连接关系...")
    for _, row in tqdm(station_data.iterrows(), total=len(station_data), desc="构建网络"):
        station1 = row['station1']
        station2 = row['station2']
        
        # 提取站名和线路号
        station1_name = station1.split('_')[0]
        station2_name = station2.split('_')[0]
        line1 = station1.split('_')[1]
        line2 = station2.split('_')[1]
        
        # 获取距离，如果没有则使用默认值2000
        distance = distance_dict.get((station1_name, station2_name), 2000)
        
        # 计算时间（考虑不同线路的速度）
        if '地铁19号线' in [line1, line2]:
            time = (distance / 1050) + 1  # 19号线速度
        else:
            time = (distance / 700) + 1   # 其他线路速度
            
        # 添加边
        G.add_edge(station1, station2, weight=time)
        
        # 添加换乘边（如果站名相同但线路不同）
        for other_station in G.nodes():
            other_name = other_station.split('_')[0]
            other_line = other_station.split('_')[1]
            if (station1_name == other_name and line1 != other_line):
                G.add_edge(station1, other_station, weight=4)  # 换乘时间4分钟
            if (station2_name == other_name and line2 != other_line):
                G.add_edge(station2, other_station, weight=4)

    return G

def calculate_flow(G, od_data, hour):
    # 筛选特定小时的数据
    hour_data = od_data[od_data['hour'] == hour]
    
    # 用于存储边的流量
    edge_flow = defaultdict(float)
    
    # 对每个OD对计算最短路径
    for _, row in tqdm(hour_data.iterrows(), total=len(hour_data), desc=f"计算第{hour}小时流量"):
        if row['o_rawname'] == row['d_rawname']:
            continue
            
        # 找到起终点对应的所有站点（考虑不同线路）
        origin_stations = [n for n in G.nodes() if row['o_rawname'] in n]
        dest_stations = [n for n in G.nodes() if row['d_rawname'] in n]
        
        min_path = None
        min_length = float('inf')
        
        # 找到最短路径
        for o_station in origin_stations:
            for d_station in dest_stations:
                try:
                    path = nx.shortest_path(G, o_station, d_station, weight='weight')
                    length = nx.shortest_path_length(G, o_station, d_station, weight='weight')
                    if length < min_length:
                        min_length = length
                        min_path = path
                except nx.NetworkXNoPath:
                    continue
        
        if min_path:
            # 将流量分配到路径上的每条边
            for i in range(len(min_path)-1):
                edge = tuple(sorted([min_path[i], min_path[i+1]]))
                edge_flow[edge] += row['trip']
    
    return edge_flow

def save_results(edge_flows, output_file):
    print("正在保存结果...")
    results = []
    for hour in tqdm(range(24), desc="整理结果数据"):
        for edge, flow in edge_flows[hour].items():
            station1, station2 = edge
            station1_name = station1.split('_')[0]
            station2_name = station2.split('_')[0]
            results.append({
                'hour': hour,
                'o_rawname': station1_name,
                'd_rawname': station2_name,
                'trip': flow
            })
    
    # 创建DataFrame并保存
    results_df = pd.DataFrame(results)
    results_df.to_csv(output_file, index=False)
    print(f"结果已保存到: {output_file}")

def main():
    print("开始处理地铁流量数据...")
    # 读取数据
    od_data, station_data = load_data()
    
    # 创建图
    G = create_graph(station_data, od_data)
    
    # 存储每小时的边流量
    edge_flows = {}
    
    # 对每个小时计算流量
    print("开始计算24小时流量...")
    for hour in range(24):
        edge_flows[hour] = calculate_flow(G, od_data, hour)
    
    # 保存结果
    save_results(edge_flows, 'subway_flow_results.csv')
    print("程序执行完成！")

if __name__ == "__main__":
    main()