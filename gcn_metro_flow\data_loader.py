"""
数据加载和预处理模块
"""
import pandas as pd
import geopandas as gpd
import numpy as np
import torch
from torch_geometric.data import Data
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.neighbors import BallTree
import warnings
warnings.filterwarnings('ignore')

from config import INPUT_FILES, DATA_CONFIG, MODEL_CONFIG

class MetroDataLoader:
    """地铁流量数据加载器"""
    
    def __init__(self):
        self.station_encoder = LabelEncoder()
        self.feature_scaler = StandardScaler()
        self.flow_scaler = StandardScaler()
        self.stations = None
        self.station_features = None
        self.grid_features = None
        
    def load_all_data(self):
        """加载所有数据"""
        print("Loading all data files...")
        
        # 加载进出站流量数据
        self.in_flow_data = self._load_flow_data(INPUT_FILES['in_flow'], 'in')
        self.out_flow_data = self._load_flow_data(INPUT_FILES['out_flow'], 'out')
        
        # 加载OD流量数据
        self.od_flow_data = self._load_od_data()
        
        # 加载站点连接数据
        self.station_connections = self._load_station_connections()
        
        # 加载相邻站间流量数据
        self.subway_flow_data = self._load_subway_flow()
        
        # 加载栅格特征数据
        self.grid_features = self._load_grid_features()
        
        # 构建站点特征
        self._build_station_features()
        
        print("Data loading completed!")
        return self
    
    def _load_flow_data(self, file_path, flow_type):
        """加载进出站流量数据"""
        print(f"Loading {flow_type} flow data from {file_path}")
        
        try:
            gdf = gpd.read_file(file_path)
            print(f"{flow_type} flow data shape: {gdf.shape}")
            
            # 清理站点名称（移除下划线后的内容）
            gdf['station_clean'] = gdf['station'].apply(self._clean_station_name)
            
            # 过滤异常值
            if DATA_CONFIG['remove_outliers']:
                gdf = self._remove_outliers(gdf, 'count')
            
            return gdf
            
        except Exception as e:
            print(f"Error loading {flow_type} flow data: {e}")
            return None
    
    def _load_od_data(self):
        """加载OD流量数据"""
        print(f"Loading OD data from {INPUT_FILES['od_flow']}")
        
        try:
            df = pd.read_csv(INPUT_FILES['od_flow'])
            print(f"OD data shape: {df.shape}")
            
            # 清理站点名称
            df['o_station_clean'] = df['o_rawname'].apply(self._clean_station_name)
            df['d_station_clean'] = df['d_rawname'].apply(self._clean_station_name)
            
            # 过滤异常值
            if DATA_CONFIG['remove_outliers']:
                df = self._remove_outliers(df, 'trip')
            
            return df
            
        except Exception as e:
            print(f"Error loading OD data: {e}")
            return None
    
    def _load_station_connections(self):
        """加载站点连接数据"""
        print(f"Loading station connections from {INPUT_FILES['station_connect']}")
        
        try:
            df = pd.read_csv(INPUT_FILES['station_connect'])
            print(f"Station connections shape: {df.shape}")
            
            # 清理站点名称
            df['station_1_clean'] = df['station_1'].apply(self._clean_station_name)
            df['station_2_clean'] = df['station_2'].apply(self._clean_station_name)
            
            return df
            
        except Exception as e:
            print(f"Error loading station connections: {e}")
            return None
    
    def _load_subway_flow(self):
        """加载相邻站间流量数据"""
        print(f"Loading subway flow from {INPUT_FILES['subway_flow']}")
        
        try:
            df = pd.read_csv(INPUT_FILES['subway_flow'])
            print(f"Subway flow shape: {df.shape}")
            
            # 清理站点名称
            df['o_station_clean'] = df['o_rawname'].apply(self._clean_station_name)
            df['d_station_clean'] = df['d_rawname'].apply(self._clean_station_name)
            
            return df
            
        except Exception as e:
            print(f"Error loading subway flow: {e}")
            return None
    
    def _load_grid_features(self):
        """加载栅格特征数据"""
        print(f"Loading grid features from {INPUT_FILES['grid_features']}")
        
        try:
            df = pd.read_csv(INPUT_FILES['grid_features'])
            print(f"Grid features shape: {df.shape}")
            
            # 提取特征列（除了id和geometry）
            feature_cols = [col for col in df.columns if col not in ['id', 'geometry']]
            features = df[feature_cols].values
            
            # 标准化特征
            if DATA_CONFIG['feature_normalize']:
                features = self.feature_scaler.fit_transform(features)
            
            return {
                'features': features,
                'feature_names': feature_cols,
                'grid_ids': df['id'].values,
                'geometries': df['geometry'] if 'geometry' in df.columns else None
            }
            
        except Exception as e:
            print(f"Error loading grid features: {e}")
            return None
    
    def _clean_station_name(self, station_name):
        """清理站点名称，移除下划线后的内容"""
        if pd.isna(station_name):
            return station_name
        return str(station_name).split('_')[0]
    
    def _remove_outliers(self, df, column):
        """移除异常值"""
        if column not in df.columns:
            return df
        
        Q1 = df[column].quantile(0.25)
        Q3 = df[column].quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - DATA_CONFIG['outlier_threshold'] * IQR
        upper_bound = Q3 + DATA_CONFIG['outlier_threshold'] * IQR
        
        before_count = len(df)
        df = df[(df[column] >= lower_bound) & (df[column] <= upper_bound)]
        after_count = len(df)
        
        print(f"Removed {before_count - after_count} outliers from {column}")
        return df
    
    def _build_station_features(self):
        """构建站点特征"""
        print("Building station features...")
        
        # 获取所有唯一站点
        stations_set = set()
        
        if self.in_flow_data is not None:
            stations_set.update(self.in_flow_data['station_clean'].unique())
        if self.out_flow_data is not None:
            stations_set.update(self.out_flow_data['station_clean'].unique())
        if self.od_flow_data is not None:
            stations_set.update(self.od_flow_data['o_station_clean'].unique())
            stations_set.update(self.od_flow_data['d_station_clean'].unique())
        
        self.stations = sorted(list(stations_set))
        print(f"Found {len(self.stations)} unique stations")
        
        # 编码站点
        self.station_encoder.fit(self.stations)
        
        # 构建站点特征矩阵
        self._aggregate_grid_features_to_stations()
    
    def _aggregate_grid_features_to_stations(self):
        """将栅格特征聚合到站点"""
        print("Aggregating grid features to stations...")

        if self.grid_features is None:
            print("Grid features not available, using default features")
            # 使用默认特征
            num_features = MODEL_CONFIG['geo_feature_dim']
            self.station_features = np.random.normal(0, 0.1, (len(self.stations), num_features))
            return

        if self.in_flow_data is None:
            print("Station data not available, using default features")
            num_features = self.grid_features['features'].shape[1]
            self.station_features = np.random.normal(0, 0.1, (len(self.stations), num_features))
            return

        # 获取站点坐标
        station_coords = {}
        for _, row in self.in_flow_data.iterrows():
            station = row['station_clean']
            if station not in station_coords and 'longitude' in row and 'latitude' in row:
                station_coords[station] = (row['longitude'], row['latitude'])

        # 为每个站点聚合周围栅格的特征
        station_features = []

        for station in self.stations:
            if station in station_coords:
                lon, lat = station_coords[station]
                # 这里简化处理，实际应该根据地理距离聚合
                # 暂时使用平均特征作为站点特征
                avg_features = np.mean(self.grid_features['features'], axis=0)
                station_features.append(avg_features)
            else:
                # 如果没有坐标信息，使用平均特征
                avg_features = np.mean(self.grid_features['features'], axis=0)
                station_features.append(avg_features)

        self.station_features = np.array(station_features)
        print(f"Station features shape: {self.station_features.shape}")
