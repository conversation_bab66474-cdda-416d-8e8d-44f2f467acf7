"""
完整的机器学习地铁流量预测系统
确保输出所有必需的预测结果文件
"""
import os
import sys
import time
import numpy as np
import pandas as pd
import geopandas as gpd
import torch
import torch.nn as nn
import torch.optim as optim
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.ensemble import RandomForestRegressor
import warnings
warnings.filterwarnings('ignore')

class CompletePredictionSystem:
    """完整的预测系统"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
        # 数据存储
        self.in_data = None
        self.out_data = None
        self.od_data = None
        self.grid_data = None
        
        # 模型
        self.in_flow_model = None
        self.out_flow_model = None
        self.od_flow_model = None
        
        # 标准化器
        self.in_scaler = StandardScaler()
        self.out_scaler = StandardScaler()
        self.od_scaler = StandardScaler()
        
        # 站点映射
        self.stations = []
        self.station_to_idx = {}
        
    def load_all_data(self):
        """加载所有数据"""
        print("="*60)
        print("加载完整数据集")
        print("="*60)
        
        try:
            # 1. 加载进出站数据
            print("加载进出站数据...")
            self.in_data = gpd.read_file('in_500_with_coords.shp')
            self.out_data = gpd.read_file('out_500_with_coords.shp')
            
            # 清理站点名称
            self.in_data['station_clean'] = self.in_data['station'].apply(lambda x: str(x).split('_')[0])
            self.out_data['station_clean'] = self.out_data['station'].apply(lambda x: str(x).split('_')[0])
            
            # 提取坐标
            self.in_data['longitude'] = self.in_data.geometry.centroid.x
            self.in_data['latitude'] = self.in_data.geometry.centroid.y
            self.out_data['longitude'] = self.out_data.geometry.centroid.x
            self.out_data['latitude'] = self.out_data.geometry.centroid.y
            
            print(f"进站数据: {self.in_data.shape}")
            print(f"出站数据: {self.out_data.shape}")
            
            # 2. 加载OD数据
            print("加载OD数据...")
            self.od_data = pd.read_csv('updated北京市_subway_od_2024_modified3.csv')
            self.od_data['o_station_clean'] = self.od_data['o_rawname'].apply(lambda x: str(x).split('_')[0])
            self.od_data['d_station_clean'] = self.od_data['d_rawname'].apply(lambda x: str(x).split('_')[0])
            
            print(f"OD数据: {self.od_data.shape}")
            
            # 3. 加载栅格特征数据
            print("加载栅格特征数据...")
            self.grid_data = pd.read_csv('leti_data.csv')
            
            # 提取51维特征
            feature_cols = [col for col in self.grid_data.columns if col not in ['站名', 'id', 'geometry']]
            self.grid_features = self.grid_data[feature_cols].values
            self.grid_features = np.nan_to_num(self.grid_features, nan=0.0)
            
            print(f"栅格特征: {self.grid_features.shape}")
            
            # 4. 获取站点列表
            all_stations = set()
            all_stations.update(self.in_data['station_clean'].unique())
            all_stations.update(self.out_data['station_clean'].unique())
            all_stations.update(self.od_data['o_station_clean'].unique())
            all_stations.update(self.od_data['d_station_clean'].unique())
            
            self.stations = sorted(list(all_stations))
            self.station_to_idx = {station: idx for idx, station in enumerate(self.stations)}
            
            print(f"总站点数: {len(self.stations)}")
            
            return True
            
        except Exception as e:
            print(f"数据加载失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def prepare_station_features(self, data, flow_type):
        """准备站点特征"""
        features = []
        targets = []
        indices = []
        
        # 计算平均栅格特征
        avg_grid_features = np.mean(self.grid_features, axis=0)
        
        for idx, row in data.iterrows():
            station = row['station_clean']
            hour = row['hour']
            count = row['count']
            lon = row['longitude']
            lat = row['latitude']
            
            # 构建特征向量：栅格特征(51) + 经纬度(2) + 时间特征(6)
            feature_vector = np.concatenate([
                avg_grid_features,  # 51维栅格特征
                [lon, lat],  # 经纬度
                [
                    hour / 23.0,  # 标准化小时
                    np.sin(2 * np.pi * hour / 24),  # 小时周期性
                    np.cos(2 * np.pi * hour / 24),
                    1.0 if 6 <= hour <= 10 else 0.0,  # 早高峰
                    1.0 if 17 <= hour <= 20 else 0.0,  # 晚高峰
                    1.0 if hour in [0,1,2,3,4,5,21,22,23] else 0.0  # 深夜
                ]
            ])
            
            features.append(feature_vector)
            targets.append(count)
            indices.append(idx)
        
        return np.array(features), np.array(targets), indices
    
    def prepare_od_features(self):
        """准备OD特征"""
        features = []
        targets = []
        indices = []
        
        # 计算平均栅格特征
        avg_grid_features = np.mean(self.grid_features, axis=0)
        
        for idx, row in self.od_data.iterrows():
            o_station = row['o_station_clean']
            d_station = row['d_station_clean']
            hour = row['hour']
            trip = row['trip']
            
            # OD特征
            surface_distance = row.get('surface_distance', 0)
            translate = row.get('translate', 0)
            time_cost = row.get('time', 0)
            wait_time = row.get('wait_time', 0)
            
            # 构建特征向量：起点特征(51) + 终点特征(51) + OD特征(4) + 时间特征(6)
            feature_vector = np.concatenate([
                avg_grid_features,  # 起点栅格特征
                avg_grid_features,  # 终点栅格特征
                [surface_distance, translate, time_cost, wait_time],  # OD特征
                [
                    hour / 23.0,  # 标准化小时
                    np.sin(2 * np.pi * hour / 24),  # 小时周期性
                    np.cos(2 * np.pi * hour / 24),
                    1.0 if 6 <= hour <= 10 else 0.0,  # 早高峰
                    1.0 if 17 <= hour <= 20 else 0.0,  # 晚高峰
                    1.0 if hour in [0,1,2,3,4,5,21,22,23] else 0.0  # 深夜
                ]
            ])
            
            features.append(feature_vector)
            targets.append(trip)
            indices.append(idx)
        
        return np.array(features), np.array(targets), indices
    
    def train_all_models(self):
        """训练所有模型"""
        print("="*60)
        print("训练所有预测模型")
        print("="*60)
        
        # 1. 训练进站流量模型
        print("训练进站流量模型...")
        in_features, in_targets, _ = self.prepare_station_features(self.in_data, 'in')
        in_features = self.in_scaler.fit_transform(in_features)
        
        self.in_flow_model = RandomForestRegressor(
            n_estimators=100, 
            max_depth=15, 
            random_state=42,
            n_jobs=-1
        )
        self.in_flow_model.fit(in_features, in_targets)
        
        # 2. 训练出站流量模型
        print("训练出站流量模型...")
        out_features, out_targets, _ = self.prepare_station_features(self.out_data, 'out')
        out_features = self.out_scaler.fit_transform(out_features)
        
        self.out_flow_model = RandomForestRegressor(
            n_estimators=100, 
            max_depth=15, 
            random_state=42,
            n_jobs=-1
        )
        self.out_flow_model.fit(out_features, out_targets)
        
        # 3. 训练OD流量模型
        print("训练OD流量模型...")
        od_features, od_targets, _ = self.prepare_od_features()
        od_features = self.od_scaler.fit_transform(od_features)
        
        self.od_flow_model = RandomForestRegressor(
            n_estimators=100, 
            max_depth=15, 
            random_state=42,
            n_jobs=-1
        )
        self.od_flow_model.fit(od_features, od_targets)
        
        print("所有模型训练完成！")
    
    def generate_predictions(self):
        """生成所有预测结果"""
        print("="*60)
        print("生成预测结果")
        print("="*60)
        
        # 数据分割 - 使用时间分割策略
        train_hours = list(range(0, 18))  # 0-17点用于训练
        test_hours = list(range(18, 24))  # 18-23点用于测试
        
        # 1. 生成进站预测结果
        print("生成进站预测结果...")
        in_test_data = self.in_data[self.in_data['hour'].isin(test_hours)].copy()
        
        if len(in_test_data) > 0:
            in_test_features, _, test_indices = self.prepare_station_features(in_test_data, 'in')
            in_test_features = self.in_scaler.transform(in_test_features)
            in_predictions = self.in_flow_model.predict(in_test_features)
            in_predictions = np.maximum(in_predictions, 0)  # 确保非负
            
            # 添加预测列
            in_test_data.loc[:, 'prediction'] = in_predictions
            
            # 保存为shapefile
            try:
                # 删除已存在的文件
                import glob
                for f in glob.glob('in_500_predictions_with_coords.*'):
                    try:
                        os.remove(f)
                    except:
                        pass

                in_test_data.to_file('in_500_predictions_with_coords.shp')
                print(f"✓ 进站预测结果已保存: {len(in_test_data)} 条记录")
            except Exception as e:
                print(f"保存进站预测结果失败: {e}")
                # 备用方案：保存为CSV
                in_test_data_csv = in_test_data.drop(columns=['geometry'])
                in_test_data_csv.to_csv('in_500_predictions_with_coords.csv', index=False)
                print(f"✓ 进站预测结果已保存为CSV: {len(in_test_data)} 条记录")
        
        # 2. 生成出站预测结果
        print("生成出站预测结果...")
        out_test_data = self.out_data[self.out_data['hour'].isin(test_hours)].copy()
        
        if len(out_test_data) > 0:
            out_test_features, _, test_indices = self.prepare_station_features(out_test_data, 'out')
            out_test_features = self.out_scaler.transform(out_test_features)
            out_predictions = self.out_flow_model.predict(out_test_features)
            out_predictions = np.maximum(out_predictions, 0)  # 确保非负
            
            # 添加预测列
            out_test_data.loc[:, 'prediction'] = out_predictions
            
            # 保存为shapefile
            try:
                # 删除已存在的文件
                import glob
                for f in glob.glob('out_500_predictions_with_coords.*'):
                    try:
                        os.remove(f)
                    except:
                        pass

                out_test_data.to_file('out_500_predictions_with_coords.shp')
                print(f"✓ 出站预测结果已保存: {len(out_test_data)} 条记录")
            except Exception as e:
                print(f"保存出站预测结果失败: {e}")
                # 备用方案：保存为CSV
                out_test_data_csv = out_test_data.drop(columns=['geometry'])
                out_test_data_csv.to_csv('out_500_predictions_with_coords.csv', index=False)
                print(f"✓ 出站预测结果已保存为CSV: {len(out_test_data)} 条记录")
        
        # 3. 生成OD预测结果
        print("生成OD预测结果...")
        od_test_data = self.od_data[self.od_data['hour'].isin(test_hours)].copy()
        
        if len(od_test_data) > 0:
            od_test_features, _, test_indices = self.prepare_od_features()
            # 只选择测试时间段的数据
            od_test_mask = od_test_data.index
            od_test_features_filtered = od_test_features[od_test_data.index]
            
            od_test_features_filtered = self.od_scaler.transform(od_test_features_filtered)
            od_predictions = self.od_flow_model.predict(od_test_features_filtered)
            od_predictions = np.maximum(od_predictions, 0)  # 确保非负
            
            # 添加预测列
            od_test_data.loc[:, 'prediction'] = od_predictions
            
            # 保存为CSV
            od_test_data.to_csv('od_predictions.csv', index=False)
            print(f"✓ OD预测结果已保存: {len(od_test_data)} 条记录")
        
        # 4. 生成预测对比汇总文件
        print("生成预测对比汇总...")
        comparison_data = []
        
        # 进站对比数据
        if len(in_test_data) > 0:
            for _, row in in_test_data.iterrows():
                comparison_data.append({
                    'Type': 'In',
                    'True_Value': float(row['count']),
                    'Predicted_Value': float(row['prediction']),
                    'Station': str(row['station_clean']),
                    'Hour': int(row['hour']),
                    'Error': float(abs(row['count'] - row['prediction']))
                })
        
        # 出站对比数据
        if len(out_test_data) > 0:
            for _, row in out_test_data.iterrows():
                comparison_data.append({
                    'Type': 'Out',
                    'True_Value': float(row['count']),
                    'Predicted_Value': float(row['prediction']),
                    'Station': str(row['station_clean']),
                    'Hour': int(row['hour']),
                    'Error': float(abs(row['count'] - row['prediction']))
                })
        
        # OD对比数据
        if len(od_test_data) > 0:
            for _, row in od_test_data.iterrows():
                comparison_data.append({
                    'Type': 'OD',
                    'True_Value': float(row['trip']),
                    'Predicted_Value': float(row['prediction']),
                    'Station': f"{row['o_station_clean']}->{row['d_station_clean']}",
                    'Hour': int(row['hour']),
                    'Error': float(abs(row['trip'] - row['prediction']))
                })
        
        # 保存对比结果
        if comparison_data:
            comparison_df = pd.DataFrame(comparison_data)
            comparison_df.to_csv('prediction_comparison.csv', index=False)
            print(f"✓ 预测对比汇总已保存: {len(comparison_data)} 条记录")
        
        return comparison_data
    
    def evaluate_predictions(self, comparison_data):
        """评估预测结果"""
        print("="*60)
        print("评估预测性能")
        print("="*60)
        
        if not comparison_data:
            print("没有可评估的数据")
            return
        
        df = pd.DataFrame(comparison_data)
        
        # 按类型分组评估
        for pred_type in ['In', 'Out', 'OD']:
            type_data = df[df['Type'] == pred_type]
            
            if len(type_data) > 0:
                true_values = type_data['True_Value'].values
                pred_values = type_data['Predicted_Value'].values
                
                mae = mean_absolute_error(true_values, pred_values)
                rmse = np.sqrt(mean_squared_error(true_values, pred_values))
                r2 = r2_score(true_values, pred_values)
                
                print(f"{pred_type} 流量预测:")
                print(f"  样本数: {len(type_data)}")
                print(f"  MAE:  {mae:.4f}")
                print(f"  RMSE: {rmse:.4f}")
                print(f"  R²:   {r2:.4f}")
                print()
        
        # 整体评估
        all_true = df['True_Value'].values
        all_pred = df['Predicted_Value'].values
        
        overall_mae = mean_absolute_error(all_true, all_pred)
        overall_rmse = np.sqrt(mean_squared_error(all_true, all_pred))
        overall_r2 = r2_score(all_true, all_pred)
        
        print("整体预测性能:")
        print(f"  总样本数: {len(df)}")
        print(f"  MAE:  {overall_mae:.4f}")
        print(f"  RMSE: {overall_rmse:.4f}")
        print(f"  R²:   {overall_r2:.4f}")
    
    def verify_output_files(self):
        """验证输出文件"""
        print("="*60)
        print("验证输出文件")
        print("="*60)
        
        required_files = [
            'in_500_predictions_with_coords.shp',
            'out_500_predictions_with_coords.shp',
            'od_predictions.csv',
            'prediction_comparison.csv'
        ]
        
        for filename in required_files:
            if os.path.exists(filename):
                try:
                    if filename.endswith('.shp'):
                        data = gpd.read_file(filename)
                        print(f"✓ {filename}: {data.shape}, CRS: {data.crs}")
                        print(f"  列: {list(data.columns)}")
                        if 'prediction' in data.columns:
                            pred_stats = data['prediction'].describe()
                            print(f"  预测值统计: min={pred_stats['min']:.2f}, max={pred_stats['max']:.2f}, mean={pred_stats['mean']:.2f}")
                    else:
                        data = pd.read_csv(filename)
                        print(f"✓ {filename}: {data.shape}")
                        print(f"  列: {list(data.columns)}")
                        if 'Predicted_Value' in data.columns:
                            pred_stats = data['Predicted_Value'].describe()
                            print(f"  预测值统计: min={pred_stats['min']:.2f}, max={pred_stats['max']:.2f}, mean={pred_stats['mean']:.2f}")
                    print()
                except Exception as e:
                    print(f"✗ {filename}: 读取失败 - {e}")
            else:
                print(f"✗ {filename}: 文件不存在")

def main():
    """主函数"""
    print("="*80)
    print("完整的机器学习地铁流量预测系统")
    print("输出所有必需的预测结果文件")
    print("="*80)
    
    start_time = time.time()
    
    try:
        # 初始化系统
        system = CompletePredictionSystem()
        
        # 加载数据
        if not system.load_all_data():
            return False
        
        # 训练模型
        system.train_all_models()
        
        # 生成预测结果
        comparison_data = system.generate_predictions()
        
        # 评估预测结果
        system.evaluate_predictions(comparison_data)
        
        # 验证输出文件
        system.verify_output_files()
        
        print(f"\n运行时间: {time.time() - start_time:.2f} 秒")
        print("="*60)
        print("所有预测结果文件已生成完成！")
        print("="*60)
        
        return True
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
