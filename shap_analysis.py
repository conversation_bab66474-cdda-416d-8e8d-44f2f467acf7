import os
import torch
import pandas as pd
import numpy as np
import shap
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import pickle
from torch.utils.data import Dataset
from tqdm import tqdm

# Set random seed and device
torch.manual_seed(42)
np.random.seed(42)
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"Using device: {device}")

# Set font for Chinese characters
plt.rcParams['font.sans-serif'] = ['SimHei']  # Use SimHei for Chinese support
plt.rcParams['axes.unicode_minus'] = False  # Ensure minus signs display correctly

# Data loading
def load_preprocessed_data(cache_dir="cache"):
    cache_file = os.path.join(cache_dir, "preprocessed_data_full.pkl")
    if not os.path.exists(cache_file):
        raise FileNotFoundError(f"Cached data not found at {cache_file}")
    with open(cache_file, 'rb') as f:
        data = pickle.load(f)
    return (data['in_gdf'], data['out_gdf'], data['od_df'], data['features_df'],
            data['station_encoder'], data['feature_cols'])

# Dataset class
class TrafficDataset(Dataset):
    def __init__(self, in_gdf, out_gdf, od_df, features_df, feature_cols):
        self.in_gdf = in_gdf
        self.out_gdf = out_gdf
        self.od_df = od_df
        self.features_df = features_df
        self.feature_cols = feature_cols
        
    def __len__(self):
        return len(self.in_gdf) + len(self.out_gdf) + len(self.od_df)
    
    def __getitem__(self, idx):
        if idx < len(self.in_gdf):
            row = self.in_gdf.iloc[idx]
            station_id = row['station_id']
            geo_features = self.features_df[self.features_df['station_id'] == station_id][self.feature_cols].values
            geo_features = geo_features[0] if len(geo_features) > 0 else np.zeros(len(self.feature_cols))
            return {
                'type': 'in',
                'station_id': torch.tensor(station_id, dtype=torch.long),
                'hour': torch.tensor([row['hour_sin'], row['hour_cos']], dtype=torch.float),
                'geo_features': torch.tensor(geo_features, dtype=torch.float),
                'count': torch.tensor(row['count'], dtype=torch.float),
                'index': idx
            }
        elif idx < len(self.in_gdf) + len(self.out_gdf):
            row = self.out_gdf.iloc[idx - len(self.in_gdf)]
            station_id = row['station_id']
            geo_features = self.features_df[self.features_df['station_id'] == station_id][self.feature_cols].values
            geo_features = geo_features[0] if len(geo_features) > 0 else np.zeros(len(self.feature_cols))
            return {
                'type': 'out',
                'station_id': torch.tensor(station_id, dtype=torch.long),
                'hour': torch.tensor([row['hour_sin'], row['hour_cos']], dtype=torch.float),
                'geo_features': torch.tensor(geo_features, dtype=torch.float),
                'count': torch.tensor(row['count'], dtype=torch.float),
                'index': idx - len(self.in_gdf)
            }
        else:
            row = self.od_df.iloc[idx - len(self.in_gdf) - len(self.out_gdf)]
            o_station_id = row['o_station_id']
            d_station_id = row['d_station_id']
            o_geo = self.features_df[self.features_df['station_id'] == o_station_id][self.feature_cols].values
            d_geo = self.features_df[self.features_df['station_id'] == d_station_id][self.feature_cols].values
            o_geo = o_geo[0] if len(o_geo) > 0 else np.zeros(len(self.feature_cols))
            d_geo = d_geo[0] if len(d_geo) > 0 else np.zeros(len(self.feature_cols))
            return {
                'type': 'od',
                'o_station_id': torch.tensor(o_station_id, dtype=torch.long),
                'd_station_id': torch.tensor(d_station_id, dtype=torch.long),
                'hour': torch.tensor([row['hour_sin'], row['hour_cos']], dtype=torch.float),
                'o_geo_features': torch.tensor(o_geo, dtype=torch.float),
                'd_geo_features': torch.tensor(d_geo, dtype=torch.float),
                'od_features': torch.tensor([
                    row['surface_distance'], row['translate'], row['time'], row['wait_time']
                ], dtype=torch.float),
                'trip': torch.tensor(row['trip'], dtype=torch.float),
                'index': idx - len(self.in_gdf) - len(self.out_gdf)
            }

def collate_fn(batch):
    in_batch = [x for x in batch if x['type'] == 'in']
    out_batch = [x for x in batch if x['type'] == 'out']
    od_batch = [x for x in batch if x['type'] == 'od']
    return in_batch, out_batch, od_batch

# TrafficModel
class TrafficModel(torch.nn.Module):
    def __init__(self, num_stations, geo_feature_dim, station_dim=64, time_dim=2, hidden_dim=128):
        super().__init__()
        self.num_stations = num_stations
        self.station_dim = station_dim
        self.hidden_dim = hidden_dim
        self.time_dim = time_dim
        
        self.station_embed = torch.nn.Embedding(num_stations, station_dim)
        self.geo_embed = torch.nn.Linear(geo_feature_dim, hidden_dim)
        self.time_embed = torch.nn.Linear(time_dim, time_dim)
        
        self.mlp_grid = torch.nn.Sequential(
            torch.nn.Linear(station_dim + hidden_dim + time_dim, hidden_dim),
            torch.nn.ReLU(),
            torch.nn.Dropout(0.2),
            torch.nn.Linear(hidden_dim, hidden_dim),
            torch.nn.ReLU(),
            torch.nn.Dropout(0.2)
        )
        self.fc_grid = torch.nn.Linear(hidden_dim, 1)
        
        self.dnn = torch.nn.Sequential(
            torch.nn.Linear(station_dim*2 + hidden_dim*2 + 4 + time_dim, 256),
            torch.nn.ReLU(),
            torch.nn.Dropout(0.2),
            torch.nn.Linear(256, 128),
            torch.nn.ReLU(),
            torch.nn.Dropout(0.2),
            torch.nn.Linear(128, hidden_dim)
        )
        self.attention = torch.nn.MultiheadAttention(embed_dim=hidden_dim, num_heads=8)
        self.fc_od = torch.nn.Linear(hidden_dim, 1)
        
    def forward(self, in_batch, out_batch, od_batch):
        count_pred_in, count_pred_out, trip_pred = [], [], []
        
        if in_batch:
            station_ids = torch.tensor([x['station_id'] for x in in_batch], dtype=torch.long, device=device)
            hours = torch.stack([x['hour'] for x in in_batch]).to(device)
            geo_features = torch.stack([x['geo_features'] for x in in_batch]).to(device)
            
            station_emb = self.station_embed(station_ids)
            geo_emb = self.geo_embed(geo_features)
            time_emb = self.time_embed(hours)
            combined = torch.cat([station_emb, geo_emb, time_emb], dim=-1)
            
            mlp_out = self.mlp_grid(combined)
            count_pred_in = torch.relu(self.fc_grid(mlp_out)).squeeze(-1)
            count_pred_in = torch.nan_to_num(count_pred_in, nan=0.0)
        
        if out_batch:
            station_ids = torch.tensor([x['station_id'] for x in out_batch], dtype=torch.long, device=device)
            hours = torch.stack([x['hour'] for x in out_batch]).to(device)
            geo_features = torch.stack([x['geo_features'] for x in out_batch]).to(device)
            
            station_emb = self.station_embed(station_ids)
            geo_emb = self.geo_embed(geo_features)
            time_emb = self.time_embed(hours)
            combined = torch.cat([station_emb, geo_emb, time_emb], dim=-1)
            
            mlp_out = self.mlp_grid(combined)
            count_pred_out = torch.relu(self.fc_grid(mlp_out)).squeeze(-1)
            count_pred_out = torch.nan_to_num(count_pred_out, nan=0.0)
        
        if od_batch:
            o_station_ids = torch.tensor([x['o_station_id'] for x in od_batch], dtype=torch.long, device=device)
            d_station_ids = torch.tensor([x['d_station_id'] for x in od_batch], dtype=torch.long, device=device)
            hours = torch.stack([x['hour'] for x in od_batch]).to(device)
            o_geo = torch.stack([x['o_geo_features'] for x in od_batch]).to(device)
            d_geo = torch.stack([x['d_geo_features'] for x in od_batch]).to(device)
            od_features = torch.stack([x['od_features'] for x in od_batch]).to(device)
            
            o_emb = self.station_embed(o_station_ids)
            d_emb = self.station_embed(d_station_ids)
            o_geo_emb = self.geo_embed(o_geo)
            d_geo_emb = self.geo_embed(d_geo)
            time_emb = self.time_embed(hours)
            
            combined = torch.cat([o_emb, d_emb, o_geo_emb, d_geo_emb, od_features, time_emb], dim=-1)
            dnn_out = self.dnn(combined)
            attn_out, _ = self.attention(dnn_out.unsqueeze(0), dnn_out.unsqueeze(0), dnn_out.unsqueeze(0))
            trip_pred = torch.relu(self.fc_od(attn_out.squeeze(0))).squeeze(-1)
            trip_pred = torch.nan_to_num(trip_pred, nan=0.0)
            trip_pred = torch.round(trip_pred)
        
        return count_pred_in, count_pred_out, trip_pred

# SHAP model wrapper
def model_predict(inputs, model, batch_type, feature_cols):
    model.eval()
    with torch.no_grad():
        # Convert NumPy inputs to PyTorch tensors
        inputs = torch.from_numpy(inputs).to(device)
        
        if batch_type == 'in':
            in_batch = [{
                'station_id': inputs[i, 0].to(torch.long),
                'hour': inputs[i, 1:3].to(torch.float),
                'geo_features': inputs[i, 3:].to(torch.float)
            } for i in range(inputs.shape[0])]
            out_batch, od_batch = [], []
        elif batch_type == 'out':
            out_batch = [{
                'station_id': inputs[i, 0].to(torch.long),
                'hour': inputs[i, 1:3].to(torch.float),
                'geo_features': inputs[i, 3:].to(torch.float)
            } for i in range(inputs.shape[0])]
            in_batch, od_batch = [], []
        else:  # od
            od_batch = [{
                'o_station_id': inputs[i, 0].to(torch.long),
                'd_station_id': inputs[i, 1].to(torch.long),
                'hour': inputs[i, 2:4].to(torch.float),
                'o_geo_features': inputs[i, 4:4+len(feature_cols)].to(torch.float),
                'd_geo_features': inputs[i, 4+len(feature_cols):4+2*len(feature_cols)].to(torch.float),
                'od_features': inputs[i, 4+2*len(feature_cols):].to(torch.float)
            } for i in range(inputs.shape[0])]
            in_batch, out_batch = [], []
        
        count_pred_in, count_pred_out, trip_pred = model(in_batch, out_batch, od_batch)
        if batch_type == 'in':
            return count_pred_in.cpu().numpy()
        elif batch_type == 'out':
            return count_pred_out.cpu().numpy()
        else:
            return trip_pred.cpu().numpy()

# Diagnostic function to check model predictions
def check_model_predictions(model, test_dataset, num_samples=10):
    model.eval()
    with torch.no_grad():
        # Sample a few data points
        in_samples = [test_dataset[i] for i in np.random.choice(len(test_dataset.in_gdf), size=num_samples, replace=False)]
        out_samples = [test_dataset[len(test_dataset.in_gdf) + i] for i in np.random.choice(len(test_dataset.out_gdf), size=num_samples, replace=False)]
        od_samples = [test_dataset[len(test_dataset.in_gdf) + len(test_dataset.out_gdf) + i] for i in np.random.choice(len(test_dataset.od_df), size=num_samples, replace=False)]
        
        # Get predictions
        in_preds, _, _ = model(in_samples, [], [])
        _, out_preds, _ = model([], out_samples, [])
        _, _, od_preds = model([], [], od_samples)
        
        print("Sample Predictions:")
        print(f"Inflow Predictions: {in_preds.cpu().numpy()}")
        print(f"Outflow Predictions: {out_preds.cpu().numpy()}")
        print(f"OD Trip Predictions: {od_preds.cpu().numpy()}")
        
        # Check if predictions are non-zero and varied
        if torch.all(in_preds == 0) and torch.all(out_preds == 0) and torch.all(od_preds == 0):
            print("Warning: All predictions are zero. Model may not be trained correctly.")
        elif torch.std(in_preds) < 1e-6 and torch.std(out_preds) < 1e-6 and torch.std(od_preds) < 1e-6:
            print("Warning: Predictions have very low variance. Model may be outputting constants.")

# SHAP analysis with KernelExplainer
def perform_shap_analysis(model, test_dataset, feature_cols):
    # Sample data for SHAP
    in_indices = np.random.choice(len(test_dataset.in_gdf), size=100, replace=False)
    out_indices = np.random.choice(len(test_dataset.out_gdf), size=100, replace=False)
    od_indices = np.random.choice(len(test_dataset.od_df), size=100, replace=False)
    
    # Prepare background data
    background_in = [test_dataset[i] for i in np.random.choice(in_indices, size=50, replace=False)]
    background_out = [test_dataset[len(test_dataset.in_gdf) + i] for i in np.random.choice(out_indices, size=50, replace=False)]
    background_od = [test_dataset[len(test_dataset.in_gdf) + len(test_dataset.out_gdf) + i] for i in np.random.choice(od_indices, size=50, replace=False)]
    
    # Convert to NumPy arrays for KernelExplainer
    in_features = np.stack([torch.cat([x['station_id'].float().unsqueeze(0), x['hour'], x['geo_features']]).numpy() for x in background_in])
    out_features = np.stack([torch.cat([x['station_id'].float().unsqueeze(0), x['hour'], x['geo_features']]).numpy() for x in background_out])
    od_features = np.stack([torch.cat([
        x['o_station_id'].float().unsqueeze(0),
        x['d_station_id'].float().unsqueeze(0),
        x['hour'],
        x['o_geo_features'],
        x['d_geo_features'],
        x['od_features']
    ]).numpy() for x in background_od])
    
    # Feature names
    in_feature_names = ['station_id', 'hour_sin', 'hour_cos'] + feature_cols
    od_feature_names = ['o_station_id', 'd_station_id', 'hour_sin', 'hour_cos'] + \
                       [f'o_{col}' for col in feature_cols] + [f'd_{col}' for col in feature_cols] + \
                       ['surface_distance', 'translate', 'time', 'wait_time']
    
    # SHAP KernelExplainer with more samples
    explainer_in = shap.KernelExplainer(lambda x: model_predict(x, model, 'in', feature_cols), in_features[:50])
    explainer_out = shap.KernelExplainer(lambda x: model_predict(x, model, 'out', feature_cols), out_features[:50])
    explainer_od = shap.KernelExplainer(lambda x: model_predict(x, model, 'od', feature_cols), od_features[:50])
    
    # Compute SHAP values with increased samples for better accuracy
    shap_in = explainer_in.shap_values(in_features[:50], nsamples=200)
    shap_out = explainer_out.shap_values(out_features[:50], nsamples=200)
    shap_od = explainer_od.shap_values(od_features[:50], nsamples=200)
    
    # Check if SHAP values are all zero
    if np.all(np.abs(shap_in) < 1e-6) and np.all(np.abs(shap_out) < 1e-6) and np.all(np.abs(shap_od) < 1e-6):
        print("Warning: All SHAP values are zero or near zero. Model predictions may be constant.")
    
    # Plot and save SHAP summary plots
    plt.figure(figsize=(10, 6))
    shap.summary_plot(shap_in, in_features[:50], feature_names=in_feature_names, show=False)
    plt.savefig('shap_in_summary_no_dgl.png', bbox_inches='tight')
    plt.close()
    
    plt.figure(figsize=(10, 6))
    shap.summary_plot(shap_out, out_features[:50], feature_names=in_feature_names, show=False)
    plt.savefig('shap_out_summary_no_dgl.png', bbox_inches='tight')
    plt.close()
    
    plt.figure(figsize=(10, 6))
    shap.summary_plot(shap_od, od_features[:50], feature_names=od_feature_names, show=False)
    plt.savefig('shap_od_summary_no_dgl.png', bbox_inches='tight')
    plt.close()
    
    # Save feature importance
    importance_in = pd.DataFrame({
        'feature': in_feature_names,
        'mean_abs_shap': np.mean(np.abs(shap_in), axis=0)
    }).sort_values('mean_abs_shap', ascending=False)
    importance_out = pd.DataFrame({
        'feature': in_feature_names,
        'mean_abs_shap': np.mean(np.abs(shap_out), axis=0)
    }).sort_values('mean_abs_shap', ascending=False)
    importance_od = pd.DataFrame({
        'feature': od_feature_names,
        'mean_abs_shap': np.mean(np.abs(shap_od), axis=0)
    }).sort_values('mean_abs_shap', ascending=False)
    
    importance_in.to_csv('shap_importance_in_no_dgl.csv', index=False)
    importance_out.to_csv('shap_importance_out_no_dgl.csv', index=False)
    importance_od.to_csv('shap_importance_od_no_dgl.csv', index=False)

# Main execution
if __name__ == "__main__":
    # Load data
    in_gdf, out_gdf, od_df, features_df, station_encoder, feature_cols = load_preprocessed_data()
    
    # Normalize data (same as in training)
    in_gdf, out_gdf, od_df, features_df, feature_scaler, target_scaler = normalize_data(
        in_gdf, out_gdf, od_df, features_df, feature_cols
    )
    
    # Split data for test set
    train_in = in_gdf.sample(frac=0.7, random_state=42)
    val_in = in_gdf.drop(train_in.index).sample(frac=0.5, random_state=42)
    test_in = in_gdf.drop(train_in.index).drop(val_in.index)
    
    train_out = out_gdf.sample(frac=0.7, random_state=42)
    val_out = out_gdf.drop(train_out.index).sample(frac=0.5, random_state=42)
    test_out = out_gdf.drop(train_out.index).drop(val_out.index)
    
    train_od = od_df.sample(frac=0.7, random_state=42)
    val_od = od_df.drop(train_od.index).sample(frac=0.5, random_state=42)
    test_od = od_df.drop(train_od.index).drop(val_od.index)
    
    # Create test dataset
    test_dataset = TrafficDataset(test_in, test_out, test_od, features_df, feature_cols)
    
    # Initialize model
    model = TrafficModel(
        num_stations=len(station_encoder.classes_),
        geo_feature_dim=len(feature_cols)
    ).to(device)
    
    # Load retrained model
    model.load_state_dict(torch.load('new_model_no_dgl_retrained.pth', weights_only=True))
    
    # Check model predictions before SHAP analysis
    check_model_predictions(model, test_dataset, num_samples=10)
    
    # Perform SHAP analysis
    perform_shap_analysis(model, test_dataset, feature_cols)

    print("SHAP analysis completed. Check shap_*_no_dgl.png and shap_importance_*_no_dgl.csv files.")