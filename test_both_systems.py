"""
测试V1和V2完整版本系统的基本功能和性能对比
验证CUDA支持、内存管理和预测管道
"""
import os
import sys
import time
import subprocess

def test_system(system_file, version_name):
    """测试单个系统"""
    print(f"\n{'='*60}")
    print(f"测试 {version_name} 系统")
    print(f"{'='*60}")
    
    start_time = time.time()
    
    try:
        # 运行测试
        result = subprocess.run([
            sys.executable, system_file, 'test'
        ], capture_output=True, text=True, timeout=300)
        
        elapsed_time = time.time() - start_time
        
        if result.returncode == 0:
            print(f"✅ {version_name} 测试成功")
            print(f"⏱️ 测试时间: {elapsed_time:.2f} 秒")
            
            # 提取关键信息
            output_lines = result.stdout.split('\n')
            for line in output_lines:
                if 'CUDA' in line or 'GPU' in line or 'CPU' in line:
                    print(f"🔧 {line.strip()}")
                elif '系统内存' in line or 'PyTorch版本' in line:
                    print(f"📊 {line.strip()}")
                elif '数据加载完成' in line:
                    print(f"📁 {line.strip()}")
                elif '特征准备测试通过' in line:
                    print(f"🎯 {line.strip()}")
            
            return True
        else:
            print(f"❌ {version_name} 测试失败")
            print(f"错误输出: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {version_name} 测试超时")
        return False
    except Exception as e:
        print(f"❌ {version_name} 测试异常: {e}")
        return False

def run_quick_performance_test():
    """运行快速性能测试"""
    print(f"\n{'='*60}")
    print("快速性能对比测试")
    print(f"{'='*60}")
    
    # 检查是否存在之前的性能分析文件
    v1_perf_file = 'v1_full_performance_analysis.json'
    v2_perf_file = 'v2_full_performance_analysis.json'
    
    if os.path.exists(v1_perf_file):
        print("✅ 发现V1性能分析文件")
        try:
            import json
            with open(v1_perf_file, 'r') as f:
                v1_perf = json.load(f)
            print(f"📊 V1整体R²: {v1_perf.get('Overall', {}).get('r2', 'N/A'):.4f}")
            print(f"📊 V1整体MAE: {v1_perf.get('Overall', {}).get('mae', 'N/A'):.4f}")
        except Exception as e:
            print(f"⚠️ 读取V1性能文件失败: {e}")
    else:
        print("⚠️ 未找到V1性能分析文件")
    
    if os.path.exists(v2_perf_file):
        print("✅ 发现V2性能分析文件")
        try:
            import json
            with open(v2_perf_file, 'r') as f:
                v2_perf = json.load(f)
            print(f"📊 V2整体R²: {v2_perf.get('Overall', {}).get('r2', 'N/A'):.4f}")
            print(f"📊 V2整体MAE: {v2_perf.get('Overall', {}).get('mae', 'N/A'):.4f}")
        except Exception as e:
            print(f"⚠️ 读取V2性能文件失败: {e}")
    else:
        print("⚠️ 未找到V2性能分析文件")

def check_system_requirements():
    """检查系统要求"""
    print(f"{'='*60}")
    print("系统要求检查")
    print(f"{'='*60}")
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"🐍 Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查必要的包
    required_packages = [
        'torch', 'numpy', 'pandas', 'geopandas', 
        'sklearn', 'psutil'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"❌ {package} 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️ 缺少以下包: {', '.join(missing_packages)}")
        return False
    
    # 检查数据文件
    required_files = [
        'in_500_with_coords.shp',
        'out_500_with_coords.shp', 
        'updated北京市_subway_od_2024_modified3.csv',
        'leti_data.csv'
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file} 存在")
        else:
            print(f"❌ {file} 不存在")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n⚠️ 缺少以下数据文件: {', '.join(missing_files)}")
        return False
    
    return True

def main():
    """主函数"""
    print("🚀 V1和V2完整版本系统测试")
    print("验证CUDA支持、内存管理和预测管道")
    print(f"{'='*60}")
    
    # 检查系统要求
    if not check_system_requirements():
        print("\n❌ 系统要求检查失败，请安装缺少的包和数据文件")
        return False
    
    print("\n✅ 系统要求检查通过")
    
    # 测试V1系统
    v1_success = test_system('optimized_spatial_prediction_system_v1_full.py', 'V1完整版本')
    
    # 测试V2系统
    v2_success = test_system('optimized_spatial_prediction_system_v2_full.py', 'V2完整版本')
    
    # 运行性能对比
    run_quick_performance_test()
    
    # 总结
    print(f"\n{'='*60}")
    print("测试总结")
    print(f"{'='*60}")
    
    if v1_success and v2_success:
        print("🎉 所有系统测试通过！")
        print("✅ V1完整版本: 基于EnhancedSpatialModel架构")
        print("✅ V2完整版本: 基于AdvancedSpatialModel + Transformer架构")
        print("🚀 两个系统都支持完整数据集预测和CUDA加速")
        print("📊 可以开始运行完整的训练和预测流程")
        return True
    else:
        print("❌ 部分系统测试失败")
        if not v1_success:
            print("❌ V1完整版本测试失败")
        if not v2_success:
            print("❌ V2完整版本测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
