"""
预测与评估模块
"""
import torch
import numpy as np
import pandas as pd
import geopandas as gpd
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import os
from collections import defaultdict

from config import OUTPUT_FILES, MODEL_CONFIG
from models import MetroFlowGCN

class MetroFlowPredictor:
    """地铁流量预测器"""
    
    def __init__(self, data_loader, graph_builder, model_path=None):
        self.data_loader = data_loader
        self.graph_builder = graph_builder
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 初始化模型
        grid_feature_dim = self.data_loader.grid_features['features'].shape[1] if self.data_loader.grid_features else 51
        self.model = MetroFlowGCN(grid_feature_dim).to(self.device)
        
        # 加载预训练模型
        if model_path and os.path.exists(model_path):
            self.model.load_state_dict(torch.load(model_path, map_location=self.device))
            print(f"Loaded model from {model_path}")
        
        self.model.eval()
        
        # 预测结果存储
        self.predictions = {
            'in_flow': [],
            'out_flow': [],
            'od_flow': []
        }
        
        self.targets = {
            'in_flow': [],
            'out_flow': [],
            'od_flow': []
        }
        
        self.hourly_results = defaultdict(dict)
    
    def predict_all_hours(self):
        """预测所有小时的流量"""
        print("Starting prediction for all hours...")
        
        with torch.no_grad():
            for hour in range(24):
                print(f"Predicting hour {hour}...")
                
                # 获取图数据
                graph_data = self.graph_builder.graphs_by_hour[hour].to(self.device)
                od_data = self.graph_builder.get_od_data_for_hour(hour)
                
                if od_data is not None:
                    od_data = {k: v.to(self.device) for k, v in od_data.items()}
                
                # 预测
                predictions = self.model(graph_data, od_data)
                
                # 存储结果
                self._store_hourly_results(hour, predictions, graph_data, od_data)
        
        print("Prediction completed for all hours!")
        return self.hourly_results
    
    def _store_hourly_results(self, hour, predictions, graph_data, od_data):
        """存储每小时的预测结果"""
        # 站点流量预测
        in_flow_pred = predictions['in_flow'].detach().cpu().numpy()
        out_flow_pred = predictions['out_flow'].detach().cpu().numpy()
        
        in_flow_true = graph_data.y[:, 0].detach().cpu().numpy()
        out_flow_true = graph_data.y[:, 1].detach().cpu().numpy()
        
        # 存储站点预测结果
        self.hourly_results[hour]['station_predictions'] = {
            'in_flow_pred': in_flow_pred,
            'out_flow_pred': out_flow_pred,
            'in_flow_true': in_flow_true,
            'out_flow_true': out_flow_true,
            'stations': [self.graph_builder.idx_to_station[i] for i in range(len(in_flow_pred))]
        }
        
        # OD流量预测
        if od_data is not None and 'od_flow' in predictions:
            od_flow_pred = predictions['od_flow'].detach().cpu().numpy()
            od_flow_true = od_data['od_flows'].detach().cpu().numpy()
            od_pairs = od_data['od_pairs'].detach().cpu().numpy()
            
            self.hourly_results[hour]['od_predictions'] = {
                'od_flow_pred': od_flow_pred,
                'od_flow_true': od_flow_true,
                'od_pairs': od_pairs
            }
        
        # 累积全局预测结果
        self.predictions['in_flow'].extend(in_flow_pred)
        self.predictions['out_flow'].extend(out_flow_pred)
        self.targets['in_flow'].extend(in_flow_true)
        self.targets['out_flow'].extend(out_flow_true)
        
        if od_data is not None and 'od_flow' in predictions:
            self.predictions['od_flow'].extend(od_flow_pred)
            self.targets['od_flow'].extend(od_flow_true)
    
    def compute_metrics(self):
        """计算评估指标"""
        print("Computing evaluation metrics...")
        
        metrics = {}
        
        # 站点流量指标
        for flow_type in ['in_flow', 'out_flow']:
            if len(self.predictions[flow_type]) > 0:
                pred = np.array(self.predictions[flow_type])
                true = np.array(self.targets[flow_type])
                
                metrics[f'{flow_type}_mae'] = mean_absolute_error(true, pred)
                metrics[f'{flow_type}_mse'] = mean_squared_error(true, pred)
                metrics[f'{flow_type}_rmse'] = np.sqrt(metrics[f'{flow_type}_mse'])
                metrics[f'{flow_type}_r2'] = r2_score(true, pred)
                
                print(f"{flow_type.upper()} Metrics:")
                print(f"  MAE: {metrics[f'{flow_type}_mae']:.4f}")
                print(f"  RMSE: {metrics[f'{flow_type}_rmse']:.4f}")
                print(f"  R²: {metrics[f'{flow_type}_r2']:.4f}")
        
        # OD流量指标
        if len(self.predictions['od_flow']) > 0:
            pred = np.array(self.predictions['od_flow'])
            true = np.array(self.targets['od_flow'])
            
            metrics['od_flow_mae'] = mean_absolute_error(true, pred)
            metrics['od_flow_mse'] = mean_squared_error(true, pred)
            metrics['od_flow_rmse'] = np.sqrt(metrics['od_flow_mse'])
            metrics['od_flow_r2'] = r2_score(true, pred)
            
            print(f"OD Flow Metrics:")
            print(f"  MAE: {metrics['od_flow_mae']:.4f}")
            print(f"  RMSE: {metrics['od_flow_rmse']:.4f}")
            print(f"  R²: {metrics['od_flow_r2']:.4f}")
        
        return metrics
    
    def compute_hourly_metrics(self):
        """计算每小时的评估指标"""
        print("Computing hourly metrics...")
        
        hourly_metrics = {}
        
        for hour in range(24):
            if hour not in self.hourly_results:
                continue
            
            hour_metrics = {}
            
            # 站点流量指标
            station_data = self.hourly_results[hour]['station_predictions']
            
            for flow_type in ['in_flow', 'out_flow']:
                pred = station_data[f'{flow_type}_pred']
                true = station_data[f'{flow_type}_true']
                
                if len(pred) > 0:
                    hour_metrics[f'{flow_type}_mae'] = mean_absolute_error(true, pred)
                    hour_metrics[f'{flow_type}_rmse'] = np.sqrt(mean_squared_error(true, pred))
            
            # OD流量指标
            if 'od_predictions' in self.hourly_results[hour]:
                od_data = self.hourly_results[hour]['od_predictions']
                pred = od_data['od_flow_pred']
                true = od_data['od_flow_true']
                
                if len(pred) > 0:
                    hour_metrics['od_flow_mae'] = mean_absolute_error(true, pred)
                    hour_metrics['od_flow_rmse'] = np.sqrt(mean_squared_error(true, pred))
            
            hourly_metrics[hour] = hour_metrics
            
            print(f"Hour {hour}: In MAE={hour_metrics.get('in_flow_mae', 0):.4f}, "
                  f"Out MAE={hour_metrics.get('out_flow_mae', 0):.4f}")
        
        return hourly_metrics
    
    def save_predictions(self):
        """保存预测结果"""
        print("Saving prediction results...")
        
        # 1. 保存进站预测结果
        self._save_station_flow_predictions('in')
        
        # 2. 保存出站预测结果
        self._save_station_flow_predictions('out')
        
        # 3. 保存OD预测结果
        self._save_od_predictions()
        
        # 4. 保存预测值与真实值比较
        self._save_comparison_results()
        
        print("All prediction results saved!")
    
    def _save_station_flow_predictions(self, flow_type):
        """保存站点流量预测结果"""
        # 读取原始shapefile
        if flow_type == 'in':
            original_file = self.data_loader.in_flow_data
            output_file = OUTPUT_FILES['in_predictions']
        else:
            original_file = self.data_loader.out_flow_data
            output_file = OUTPUT_FILES['out_predictions']
        
        if original_file is None:
            print(f"Warning: No {flow_type} flow data available")
            return
        
        # 创建预测结果DataFrame
        predictions_list = []
        
        for hour in range(24):
            if hour not in self.hourly_results:
                continue
            
            station_data = self.hourly_results[hour]['station_predictions']
            stations = station_data['stations']
            pred_values = station_data[f'{flow_type}_flow_pred']
            
            for i, station in enumerate(stations):
                predictions_list.append({
                    'station': station,
                    'hour': hour,
                    'prediction': pred_values[i]
                })
        
        pred_df = pd.DataFrame(predictions_list)
        
        # 合并原始数据和预测结果
        original_df = original_file.copy()
        original_df['station_clean'] = original_df['station'].apply(
            lambda x: str(x).split('_')[0] if pd.notna(x) else x
        )
        
        merged_df = original_df.merge(
            pred_df,
            left_on=['station_clean', 'hour'],
            right_on=['station', 'hour'],
            how='left'
        )
        
        # 保存为shapefile
        merged_gdf = gpd.GeoDataFrame(merged_df)
        merged_gdf.to_file(output_file)
        print(f"Saved {flow_type} flow predictions to {output_file}")
    
    def _save_od_predictions(self):
        """保存OD预测结果"""
        if self.data_loader.od_flow_data is None:
            print("Warning: No OD flow data available")
            return
        
        # 创建预测结果列表
        predictions_list = []
        
        for hour in range(24):
            if hour not in self.hourly_results or 'od_predictions' not in self.hourly_results[hour]:
                continue
            
            od_data = self.hourly_results[hour]['od_predictions']
            od_pairs = od_data['od_pairs']
            pred_values = od_data['od_flow_pred']
            
            for i, (o_idx, d_idx) in enumerate(od_pairs):
                o_station = self.graph_builder.idx_to_station[o_idx]
                d_station = self.graph_builder.idx_to_station[d_idx]
                
                predictions_list.append({
                    'hour': hour,
                    'o_rawname': o_station,
                    'd_rawname': d_station,
                    'prediction': pred_values[i]
                })
        
        pred_df = pd.DataFrame(predictions_list)
        
        # 合并原始OD数据和预测结果
        original_df = self.data_loader.od_flow_data.copy()
        
        merged_df = original_df.merge(
            pred_df,
            on=['hour', 'o_rawname', 'd_rawname'],
            how='left'
        )
        
        # 保存为CSV
        merged_df.to_csv(OUTPUT_FILES['od_predictions'], index=False)
        print(f"Saved OD predictions to {OUTPUT_FILES['od_predictions']}")
    
    def _save_comparison_results(self):
        """保存预测值与真实值比较"""
        comparison_data = []
        
        # 进站流量比较
        for pred, true in zip(self.predictions['in_flow'], self.targets['in_flow']):
            comparison_data.append({
                'Type': 'In',
                'True_Value': true,
                'Predicted_Value': pred
            })
        
        # 出站流量比较
        for pred, true in zip(self.predictions['out_flow'], self.targets['out_flow']):
            comparison_data.append({
                'Type': 'Out',
                'True_Value': true,
                'Predicted_Value': pred
            })
        
        # OD流量比较
        for pred, true in zip(self.predictions['od_flow'], self.targets['od_flow']):
            comparison_data.append({
                'Type': 'OD',
                'True_Value': true,
                'Predicted_Value': pred
            })
        
        # 保存为CSV
        comparison_df = pd.DataFrame(comparison_data)
        comparison_df.to_csv(OUTPUT_FILES['comparison'], index=False)
        print(f"Saved comparison results to {OUTPUT_FILES['comparison']}")
    
    def generate_summary_report(self, metrics, hourly_metrics):
        """生成总结报告"""
        print("\n" + "="*50)
        print("METRO FLOW PREDICTION SUMMARY REPORT")
        print("="*50)
        
        print("\n1. OVERALL METRICS:")
        print("-" * 30)
        for metric, value in metrics.items():
            print(f"{metric.upper()}: {value:.4f}")
        
        print("\n2. HOURLY PERFORMANCE:")
        print("-" * 30)
        
        # 计算每小时平均指标
        hourly_avg = defaultdict(list)
        for hour, hour_metrics in hourly_metrics.items():
            for metric, value in hour_metrics.items():
                hourly_avg[metric].append(value)
        
        for metric, values in hourly_avg.items():
            avg_value = np.mean(values)
            std_value = np.std(values)
            print(f"{metric.upper()}: {avg_value:.4f} ± {std_value:.4f}")
        
        print("\n3. PEAK HOUR ANALYSIS:")
        print("-" * 30)
        
        # 早高峰 (7-9点)
        morning_peak_mae = []
        for hour in range(7, 10):
            if hour in hourly_metrics:
                morning_peak_mae.append(hourly_metrics[hour].get('in_flow_mae', 0))
        
        if morning_peak_mae:
            print(f"Morning Peak (7-9h) In Flow MAE: {np.mean(morning_peak_mae):.4f}")
        
        # 晚高峰 (17-19点)
        evening_peak_mae = []
        for hour in range(17, 20):
            if hour in hourly_metrics:
                evening_peak_mae.append(hourly_metrics[hour].get('out_flow_mae', 0))
        
        if evening_peak_mae:
            print(f"Evening Peak (17-19h) Out Flow MAE: {np.mean(evening_peak_mae):.4f}")
        
        print("\n" + "="*50)
