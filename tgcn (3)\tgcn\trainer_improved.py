"""
改进的训练器 - 支持有向加权图和多任务学习（修复设备问题）
"""
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from model_improved import DirectedWeightedTGCN

class ImprovedTrainer:
    def __init__(self, config):
        self.config = config
        self.device = torch.device(config.DEVICE if torch.cuda.is_available() else "cpu")
    
    def train_multitask_model(self, X, in_flows, out_flows, od_flows, edge_index, edge_weights,
                             train_indices, test_indices, common_stations):
        """多任务训练"""
        print("开始多任务训练...")
        
        # 准备数据
        train_data = self._prepare_multitask_data(
            X, in_flows, out_flows, od_flows, edge_index, edge_weights, train_indices, test_indices
        )
        
        # 初始化模型并移动到设备
        model = DirectedWeightedTGCN(
            input_dim=X.shape[-1],
            hidden_dim=self.config.HIDDEN_DIM,
            num_nodes=len(train_indices),
            output_tasks=['in_flow', 'out_flow', 'od_flow']
        ).to(self.device)
        
        # 多任务损失函数
        criteria = {
            'in_flow': nn.MSELoss(),
            'out_flow': nn.MSELoss(),
            'od_flow': nn.MSELoss()
        }
        
        # 任务权重
        task_weights = {
            'in_flow': 0.3,
            'out_flow': 0.3,
            'od_flow': 0.4  # OD流量更重要
        }
        
        optimizer = optim.Adam(model.parameters(), lr=0.001)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
        
        # 训练循环
        num_epochs = 200
        best_loss = float('inf')
        
        for epoch in range(num_epochs):
            model.train()
            
            # 前向传播
            outputs = model(train_data['X_train'], train_data['edge_index_train'], train_data['edge_weights_train'])
            
            # 计算多任务损失
            total_loss = 0
            task_losses = {}
            
            if 'in_flow' in outputs:
                in_loss = criteria['in_flow'](outputs['in_flow'], train_data['in_train'])
                task_losses['in_flow'] = in_loss.item()
                total_loss += task_weights['in_flow'] * in_loss
            
            if 'out_flow' in outputs:
                out_loss = criteria['out_flow'](outputs['out_flow'], train_data['out_train'])
                task_losses['out_flow'] = out_loss.item()
                total_loss += task_weights['out_flow'] * out_loss
            
            if 'od_flow' in outputs:
                od_loss = criteria['od_flow'](outputs['od_flow'], train_data['od_train'])
                task_losses['od_flow'] = od_loss.item()
                total_loss += task_weights['od_flow'] * od_loss
            
            # 反向传播
            optimizer.zero_grad()
            total_loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            # 验证
            if epoch % 10 == 0:
                model.eval()
                with torch.no_grad():
                    val_outputs = model(train_data['X_test'], train_data['edge_index_test'], train_data['edge_weights_test'])
                    
                    val_loss = 0
                    if 'in_flow' in val_outputs:
                        val_loss += task_weights['in_flow'] * criteria['in_flow'](val_outputs['in_flow'], train_data['in_test'])
                    if 'out_flow' in val_outputs:
                        val_loss += task_weights['out_flow'] * criteria['out_flow'](val_outputs['out_flow'], train_data['out_test'])
                    if 'od_flow' in val_outputs:
                        val_loss += task_weights['od_flow'] * criteria['od_flow'](val_outputs['od_flow'], train_data['od_test'])
                
                scheduler.step(val_loss)
                
                print(f'Epoch {epoch+1}/{num_epochs}:')
                print(f'  Total Loss: {total_loss.item():.4f}, Val Loss: {val_loss.item():.4f}')
                for task, loss in task_losses.items():
                    print(f'  {task}: {loss:.4f}')
                
                if val_loss < best_loss:
                    best_loss = val_loss
                    torch.save(model.state_dict(), 'best_multitask_model.pth')
        
        print("多任务训练完成！")
        return model
    
    def _prepare_multitask_data(self, X, in_flows, out_flows, od_flows, edge_index, edge_weights, train_indices, test_indices):
        """准备多任务训练数据"""
        
        # 确保indices在正确的设备上
        train_indices = torch.tensor(train_indices, dtype=torch.long, device=self.device)
        test_indices = torch.tensor(test_indices, dtype=torch.long, device=self.device)
        
        # 重新映射边索引
        train_edge_index, train_edge_weights = self._remap_edges(edge_index, edge_weights, train_indices)
        test_edge_index, test_edge_weights = self._remap_edges(edge_index, edge_weights, test_indices)
        
        # 准备OD目标 - 只取训练边的OD流量
        od_train_targets = []
        od_test_targets = []
        
        # 使用最后一个时间步的OD流量
        last_time_od = od_flows[-1]  # [n_stations, n_stations]
        
        # 将indices转换回numpy以便索引
        train_indices_np = train_indices.cpu().numpy()
        test_indices_np = test_indices.cpu().numpy()
        
        for i in range(train_edge_index.shape[1]):
            src_idx = train_edge_index[0, i].item()
            dst_idx = train_edge_index[1, i].item()
            od_train_targets.append(last_time_od[train_indices_np[src_idx], train_indices_np[dst_idx]].item())
        
        for i in range(test_edge_index.shape[1]):
            src_idx = test_edge_index[0, i].item()
            dst_idx = test_edge_index[1, i].item()
            od_test_targets.append(last_time_od[test_indices_np[src_idx], test_indices_np[dst_idx]].item())
        
        # 确保所有数据都在正确的设备上
        return {
            'X_train': X[:, train_indices_np].unsqueeze(0),
            'X_test': X[:, test_indices_np].unsqueeze(0),
            'in_train': in_flows[-1, train_indices_np].unsqueeze(0),
            'in_test': in_flows[-1, test_indices_np].unsqueeze(0),
            'out_train': out_flows[-1, train_indices_np].unsqueeze(0),
            'out_test': out_flows[-1, test_indices_np].unsqueeze(0),
            'od_train': torch.tensor(od_train_targets, dtype=torch.float32, device=self.device).unsqueeze(0),
            'od_test': torch.tensor(od_test_targets, dtype=torch.float32, device=self.device).unsqueeze(0),
            'edge_index_train': train_edge_index,
            'edge_weights_train': train_edge_weights,
            'edge_index_test': test_edge_index,
            'edge_weights_test': test_edge_weights
        }
    
    def _remap_edges(self, edge_index, edge_weights, node_indices):
        """重新映射边索引（修复设备问题）"""
        # 确保所有张量都在同一设备上
        device = edge_index.device
        node_indices_tensor = node_indices.to(device) if isinstance(node_indices, torch.Tensor) else torch.tensor(node_indices, dtype=torch.long, device=device)
        
        # 创建原始索引到新索引的映射
        if isinstance(node_indices, torch.Tensor):
            node_indices_list = node_indices.cpu().numpy().tolist()
        else:
            node_indices_list = list(node_indices)
        
        old_to_new = {old_idx: new_idx for new_idx, old_idx in enumerate(node_indices_list)}
        
        # 筛选子图中的边
        mask = torch.isin(edge_index[0], node_indices_tensor) & torch.isin(edge_index[1], node_indices_tensor)
        
        sub_edge_index = edge_index[:, mask]
        sub_edge_weights = edge_weights[mask]
        
        # 重新映射索引
        remapped_edge_index = torch.zeros_like(sub_edge_index)
        for i in range(sub_edge_index.shape[1]):
            old_src = sub_edge_index[0, i].item()
            old_dst = sub_edge_index[1, i].item()
            remapped_edge_index[0, i] = old_to_new[old_src]
            remapped_edge_index[1, i] = old_to_new[old_dst]
        
        return remapped_edge_index, sub_edge_weights 