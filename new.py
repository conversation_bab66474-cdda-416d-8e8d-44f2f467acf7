"""
优化的空间感知地铁流量预测系统 V4
基于V1和V3的经验，融合最佳架构，专门解决R²<0.4的问题
目标：将整体R²从V3的0.27提升到0.4+，同时保持所有预测类型的稳定性
"""
import os
import sys
import time
import psutil
import gc
import numpy as np
import pandas as pd
import geopandas as gpd
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from sklearn.preprocessing import StandardScaler, RobustScaler, MinMaxScaler
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, ExtraTreesRegressor
from sklearn.linear_model import Ridge, ElasticNet
import warnings
from scipy.spatial.distance import cdist
from scipy.stats import norm

warnings.filterwarnings('ignore')


class PositionCodingModule(nn.Module):
    """
    位置编码模块 - 用于预测新增站点对原站点的分流作用

    该模块包含：
    1. 高维位置编码器
    2. 空间竞争建模
    3. 分流影响预测
    4. 影响范围分析
    """

    def __init__(self, position_dim=64, hidden_dim=128, num_stations=None):
        super(PositionCodingModule, self).__init__()

        self.position_dim = position_dim
        self.hidden_dim = hidden_dim
        self.num_stations = num_stations

        # 位置编码器 - 将2D坐标映射到高维空间
        self.position_encoder = nn.Sequential(
            nn.Linear(2, hidden_dim),
            nn.ReLU(),
            nn.BatchNorm1d(hidden_dim),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, position_dim),
            nn.ReLU(),
            nn.BatchNorm1d(position_dim),
            nn.Dropout(0.1),
            nn.Linear(position_dim, position_dim)
        )

        # 空间竞争建模器
        self.competition_encoder = nn.Sequential(
            nn.Linear(position_dim * 2 + 3, hidden_dim),  # 两个站点位置编码 + 距离特征
            nn.ReLU(),
            nn.BatchNorm1d(hidden_dim),
            nn.Dropout(0.15),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.BatchNorm1d(hidden_dim // 2),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim // 2, 32)
        )

        # 分流影响预测器
        self.diversion_predictor = nn.Sequential(
            nn.Linear(32 + position_dim + 6, hidden_dim),  # 竞争特征 + 新站点编码 + 时间特征
            nn.ReLU(),
            nn.BatchNorm1d(hidden_dim),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.BatchNorm1d(hidden_dim // 2),
            nn.Dropout(0.15),
            nn.Linear(hidden_dim // 2, 1),
            nn.Sigmoid()  # 输出分流比例 (0-1)
        )

        # 影响强度预测器
        self.impact_intensity_predictor = nn.Sequential(
            nn.Linear(32 + position_dim + 6, hidden_dim),
            nn.ReLU(),
            nn.BatchNorm1d(hidden_dim),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.BatchNorm1d(hidden_dim // 2),
            nn.Dropout(0.15),
            nn.Linear(hidden_dim // 2, 1),
            nn.ReLU()  # 输出影响强度
        )

    def forward(self, new_station_coords, existing_station_coords, distance_features, time_features):
        """
        前向传播

        Args:
            new_station_coords: 新站点坐标 [batch_size, 2]
            existing_station_coords: 现有站点坐标 [batch_size, 2]
            distance_features: 距离特征 [batch_size, 3] (欧氏距离, 曼哈顿距离, 影响衰减)
            time_features: 时间特征 [batch_size, 6]

        Returns:
            diversion_ratio: 分流比例 [batch_size, 1]
            impact_intensity: 影响强度 [batch_size, 1]
        """
        # 位置编码
        new_station_encoding = self.position_encoder(new_station_coords)
        existing_station_encoding = self.position_encoder(existing_station_coords)

        # 空间竞争特征
        competition_input = torch.cat([
            new_station_encoding,
            existing_station_encoding,
            distance_features
        ], dim=-1)
        competition_features = self.competition_encoder(competition_input)

        # 分流预测输入
        diversion_input = torch.cat([
            competition_features,
            new_station_encoding,
            time_features
        ], dim=-1)

        # 预测分流比例和影响强度
        diversion_ratio = self.diversion_predictor(diversion_input)
        impact_intensity = self.impact_intensity_predictor(diversion_input)

        return diversion_ratio, impact_intensity


class DiversionImpactPredictor:
    """
    分流影响预测器 - 预测新站点对现有站点网络的影响
    """

    def __init__(self, position_coding_module, station_coords, device='cpu'):
        self.position_module = position_coding_module
        self.station_coords = station_coords
        self.device = device

    def calculate_distance_features(self, new_coord, existing_coord):
        """计算距离特征"""
        # 欧氏距离
        euclidean_dist = np.sqrt(
            (new_coord[0] - existing_coord[0]) ** 2 +
            (new_coord[1] - existing_coord[1]) ** 2
        )

        # 曼哈顿距离
        manhattan_dist = abs(new_coord[0] - existing_coord[0]) + abs(new_coord[1] - existing_coord[1])

        # 影响衰减因子 (基于距离的高斯衰减)
        impact_decay = np.exp(-euclidean_dist * 10)  # 10是衰减参数

        return np.array([euclidean_dist, manhattan_dist, impact_decay])

    def predict_station_diversion(self, new_station_coord, new_station_name, time_features):
        """
        预测新站点对所有现有站点的分流影响

        Args:
            new_station_coord: 新站点坐标 [lon, lat]
            new_station_name: 新站点名称
            time_features: 时间特征 [6维]

        Returns:
            diversion_results: 包含每个现有站点的分流预测结果
        """
        results = {}

        self.position_module.eval()
        with torch.no_grad():
            for station_name, coord in self.station_coords.items():
                if station_name == new_station_name:
                    continue

                # 准备输入数据
                new_coord_tensor = torch.tensor([new_station_coord], dtype=torch.float32).to(self.device)
                existing_coord_tensor = torch.tensor([[coord['longitude'], coord['latitude']]],
                                                   dtype=torch.float32).to(self.device)

                # 计算距离特征
                distance_features = self.calculate_distance_features(
                    new_station_coord,
                    [coord['longitude'], coord['latitude']]
                )
                distance_tensor = torch.tensor([distance_features], dtype=torch.float32).to(self.device)

                # 时间特征
                time_tensor = torch.tensor([time_features], dtype=torch.float32).to(self.device)

                # 预测分流
                diversion_ratio, impact_intensity = self.position_module(
                    new_coord_tensor, existing_coord_tensor, distance_tensor, time_tensor
                )

                results[station_name] = {
                    'diversion_ratio': float(diversion_ratio.cpu().numpy()[0]),
                    'impact_intensity': float(impact_intensity.cpu().numpy()[0]),
                    'distance': distance_features[0],
                    'impact_decay': distance_features[2]
                }

        return results

    def analyze_network_impact(self, new_station_coord, new_station_name, time_features,
                             current_flows=None):
        """
        分析新站点对整个网络的影响

        Args:
            new_station_coord: 新站点坐标
            new_station_name: 新站点名称
            time_features: 时间特征
            current_flows: 当前各站点的流量数据 (可选)

        Returns:
            network_impact: 网络影响分析结果
        """
        # 获取单站点分流预测
        station_impacts = self.predict_station_diversion(
            new_station_coord, new_station_name, time_features
        )

        # 计算网络级别的影响指标
        total_diversion = sum([impact['diversion_ratio'] * impact['impact_intensity']
                              for impact in station_impacts.values()])

        # 影响范围分析
        high_impact_stations = [
            station for station, impact in station_impacts.items()
            if impact['diversion_ratio'] > 0.1 and impact['impact_intensity'] > 0.5
        ]

        medium_impact_stations = [
            station for station, impact in station_impacts.items()
            if 0.05 < impact['diversion_ratio'] <= 0.1 and impact['impact_intensity'] > 0.3
        ]

        # 计算影响半径
        impact_distances = [impact['distance'] for impact in station_impacts.values()
                          if impact['diversion_ratio'] > 0.05]
        avg_impact_radius = np.mean(impact_distances) if impact_distances else 0
        max_impact_radius = max(impact_distances) if impact_distances else 0

        network_impact = {
            'total_network_diversion': total_diversion,
            'high_impact_stations': high_impact_stations,
            'medium_impact_stations': medium_impact_stations,
            'num_affected_stations': len([s for s, i in station_impacts.items()
                                        if i['diversion_ratio'] > 0.01]),
            'average_impact_radius': avg_impact_radius,
            'maximum_impact_radius': max_impact_radius,
            'station_impacts': station_impacts
        }

        return network_impact


class HybridSpatialModel(nn.Module):
    """混合空间模型 - 融合V1稳定性和V3创新"""

    def __init__(self, input_dim, spatial_dim=3, temporal_dim=6, hidden_dim=128, model_type='station'):
        super(HybridSpatialModel, self).__init__()

        self.model_type = model_type
        self.hidden_dim = hidden_dim

        if model_type == 'od':
            # OD模型架构（基于V1但增强）
            self.origin_encoder = nn.Sequential(
                nn.Linear(input_dim, hidden_dim * 2),
                nn.ReLU(),
                nn.BatchNorm1d(hidden_dim * 2),
                nn.Dropout(0.15),
                nn.Linear(hidden_dim * 2, hidden_dim),
                nn.ReLU(),
                nn.BatchNorm1d(hidden_dim),
                nn.Dropout(0.15),
                nn.Linear(hidden_dim, hidden_dim // 2)
            )

            self.dest_encoder = nn.Sequential(
                nn.Linear(input_dim, hidden_dim * 2),
                nn.ReLU(),
                nn.BatchNorm1d(hidden_dim * 2),
                nn.Dropout(0.15),
                nn.Linear(hidden_dim * 2, hidden_dim),
                nn.ReLU(),
                nn.BatchNorm1d(hidden_dim),
                nn.Dropout(0.15),
                nn.Linear(hidden_dim, hidden_dim // 2)
            )

            # OD对特征处理（增强）
            self.od_pair_encoder = nn.Sequential(
                nn.Linear(4, 64),
                nn.ReLU(),
                nn.BatchNorm1d(64),
                nn.Dropout(0.1),
                nn.Linear(64, 32),
                nn.ReLU(),
                nn.Linear(32, 16)
            )

            # 时间特征处理（增强）
            self.temporal_encoder = nn.Sequential(
                nn.Linear(temporal_dim, 64),
                nn.ReLU(),
                nn.BatchNorm1d(64),
                nn.Dropout(0.1),
                nn.Linear(64, 32),
                nn.ReLU(),
                nn.Linear(32, 16)
            )

            # 距离特征处理
            self.distance_encoder = nn.Sequential(
                nn.Linear(1, 32),
                nn.ReLU(),
                nn.Linear(32, 16),
                nn.ReLU(),
                nn.Linear(16, 8)
            )

            # 深度交互层（V4增强）
            fusion_dim = (hidden_dim // 2) * 2 + 16 + 16 + 8  # 64+64+16+16+8=168
            self.interaction_layer = nn.Sequential(
                nn.Linear(fusion_dim, hidden_dim * 3),
                nn.ReLU(),
                nn.BatchNorm1d(hidden_dim * 3),
                nn.Dropout(0.3),
                nn.Linear(hidden_dim * 3, hidden_dim * 2),
                nn.ReLU(),
                nn.BatchNorm1d(hidden_dim * 2),
                nn.Dropout(0.25),
                nn.Linear(hidden_dim * 2, hidden_dim),
                nn.ReLU(),
                nn.BatchNorm1d(hidden_dim),
                nn.Dropout(0.2),
                nn.Linear(hidden_dim, hidden_dim // 2),
                nn.ReLU(),
                nn.Dropout(0.15),
                nn.Linear(hidden_dim // 2, 1)
            )

        else:
            # 进出站模型架构（基于V1增强）
            self.main_encoder = nn.Sequential(
                nn.Linear(input_dim, hidden_dim * 2),
                nn.ReLU(),
                nn.BatchNorm1d(hidden_dim * 2),
                nn.Dropout(0.15),
                nn.Linear(hidden_dim * 2, hidden_dim),
                nn.ReLU(),
                nn.BatchNorm1d(hidden_dim),
                nn.Dropout(0.15),
                nn.Linear(hidden_dim, hidden_dim // 2)
            )

            self.spatial_encoder = nn.Sequential(
                nn.Linear(spatial_dim, 64),
                nn.ReLU(),
                nn.BatchNorm1d(64),
                nn.Dropout(0.1),
                nn.Linear(64, 32),
                nn.ReLU(),
                nn.Linear(32, 16)
            )

            self.temporal_encoder = nn.Sequential(
                nn.Linear(temporal_dim, 64),
                nn.ReLU(),
                nn.BatchNorm1d(64),
                nn.Dropout(0.1),
                nn.Linear(64, 32),
                nn.ReLU(),
                nn.Linear(32, 16)
            )

            # 深度预测网络（V4增强）
            fusion_dim = (hidden_dim // 2) + 16 + 16  # 64+16+16=96
            self.predictor = nn.Sequential(
                nn.Linear(fusion_dim, hidden_dim * 3),
                nn.ReLU(),
                nn.BatchNorm1d(hidden_dim * 3),
                nn.Dropout(0.3),
                nn.Linear(hidden_dim * 3, hidden_dim * 2),
                nn.ReLU(),
                nn.BatchNorm1d(hidden_dim * 2),
                nn.Dropout(0.25),
                nn.Linear(hidden_dim * 2, hidden_dim),
                nn.ReLU(),
                nn.BatchNorm1d(hidden_dim),
                nn.Dropout(0.2),
                nn.Linear(hidden_dim, hidden_dim // 2),
                nn.ReLU(),
                nn.Dropout(0.15),
                nn.Linear(hidden_dim // 2, 1)
            )

    def forward(self, *args):
        if self.model_type == 'od':
            origin_features, dest_features, od_pair_features, temporal_features, distance_features = args

            origin_embed = self.origin_encoder(origin_features)
            dest_embed = self.dest_encoder(dest_features)
            od_embed = self.od_pair_encoder(od_pair_features)
            temp_embed = self.temporal_encoder(temporal_features)
            dist_embed = self.distance_encoder(distance_features)

            combined_features = torch.cat([origin_embed, dest_embed, od_embed, temp_embed, dist_embed], dim=-1)
            output = self.interaction_layer(combined_features)

        else:
            main_features, spatial_features, temporal_features = args

            main_embed = self.main_encoder(main_features)
            spatial_embed = self.spatial_encoder(spatial_features)
            temporal_embed = self.temporal_encoder(temporal_features)

            combined_features = torch.cat([main_embed, spatial_embed, temporal_embed], dim=-1)
            output = self.predictor(combined_features)

        return torch.relu(output)


class V4SpatialPredictionSystem:
    """V4空间预测系统 - 融合最佳实践，目标R²≥0.4"""

    def __init__(self):
        # CUDA设备检测和配置
        self.device = self._setup_device()
        self._print_system_info()

        # 数据存储
        self.in_data = None
        self.out_data = None
        self.od_data = None
        self.grid_data = None
        self.station_coords = {}

        # 模型
        self.in_model = None
        self.out_model = None
        self.od_model = None

        # Position Coding 模块
        self.position_coding_module = None
        self.diversion_predictor = None

        # 传统机器学习模型（增强）
        self.in_rf_model = None
        self.out_rf_model = None
        self.od_rf_model = None
        self.in_et_model = None  # 新增ExtraTrees
        self.out_et_model = None
        self.od_et_model = None
        self.in_ridge_model = None
        self.out_ridge_model = None
        self.od_ridge_model = None
        self.in_elastic_model = None  # 新增ElasticNet
        self.out_elastic_model = None
        self.od_elastic_model = None

        # 标准化器（优化）
        self.feature_scaler = RobustScaler()
        self.spatial_scaler = StandardScaler()
        self.temporal_scaler = StandardScaler()
        self.od_scaler = StandardScaler()  # 改用StandardScaler
        self.distance_scaler = StandardScaler()

        # 站点信息
        self.stations = []
        self.station_to_idx = {}

        # 性能监控
        self.start_time = time.time()

    def _setup_device(self):
        """设置计算设备"""
        if torch.cuda.is_available():
            device = torch.device('cuda')
            torch.cuda.empty_cache()
            print(f"CUDA可用，使用GPU: {torch.cuda.get_device_name(0)}")
            print(f"  GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024 ** 3:.1f} GB")
        else:
            device = torch.device('cpu')
            print("CUDA不可用，使用CPU")

        return device

    def _print_system_info(self):
        """打印系统信息"""
        print(f"系统信息:")
        print(f"  CPU核心数: {psutil.cpu_count()}")
        print(f"  系统内存: {psutil.virtual_memory().total / 1024 ** 3:.1f} GB")
        print(f"  可用内存: {psutil.virtual_memory().available / 1024 ** 3:.1f} GB")
        print(f"  PyTorch版本: {torch.__version__}")

    def _monitor_memory(self, stage=""):
        """监控内存使用"""
        if stage:
            print(f"\n[{stage}] 内存使用情况:")

        # 系统内存
        mem = psutil.virtual_memory()
        print(f"  系统内存: {mem.used / 1024 ** 3:.1f} GB / {mem.total / 1024 ** 3:.1f} GB ({mem.percent:.1f}%)")

        # GPU内存
        if self.device.type == 'cuda':
            gpu_mem = torch.cuda.memory_allocated() / 1024 ** 3
            gpu_cached = torch.cuda.memory_reserved() / 1024 ** 3
            print(f"  GPU内存: {gpu_mem:.1f} GB (已分配), {gpu_cached:.1f} GB (已缓存)")

        # 强制垃圾回收
        gc.collect()
        if self.device.type == 'cuda':
            torch.cuda.empty_cache()

    def load_and_process_data(self):
        """加载和处理数据"""
        print("=" * 80)
        print("V4版本 - 加载完整数据集（融合最佳实践）")
        print("=" * 80)

        try:
            self._monitor_memory("数据加载前")

            # 加载基础数据
            print("加载基础数据文件...")
            self.in_data = gpd.read_file('in_500_with_coords.shp')
            self.out_data = gpd.read_file('out_500_with_coords.shp')
            self.od_data = pd.read_csv('updated北京市_subway_od_2024_modified3.csv')
            self.grid_data = pd.read_csv('leti_data.csv')

            # 清理站点名称
            print("清理站点名称...")
            self.in_data['station_clean'] = self.in_data['station'].apply(lambda x: str(x).split('_')[0])
            self.out_data['station_clean'] = self.out_data['station'].apply(lambda x: str(x).split('_')[0])
            self.od_data['o_station_clean'] = self.od_data['o_rawname'].apply(lambda x: str(x).split('_')[0])
            self.od_data['d_station_clean'] = self.od_data['d_rawname'].apply(lambda x: str(x).split('_')[0])

            # 提取坐标
            print("提取坐标信息...")
            self.in_data['longitude'] = self.in_data.geometry.centroid.x
            self.in_data['latitude'] = self.in_data.geometry.centroid.y
            self.out_data['longitude'] = self.out_data.geometry.centroid.x
            self.out_data['latitude'] = self.out_data.geometry.centroid.y

            print(f"数据加载完成:")
            print(f"  进站数据: {self.in_data.shape}")
            print(f"  出站数据: {self.out_data.shape}")
            print(f"  OD数据: {self.od_data.shape}")
            print(f"  栅格数据: {self.grid_data.shape}")

            # 获取站点列表和坐标
            self._extract_station_coordinates()

            # 处理栅格特征
            print("处理栅格特征...")
            feature_cols = [col for col in self.grid_data.columns if col not in ['站名', 'id', 'geometry']]
            self.grid_features = self.grid_data[feature_cols].values
            self.grid_features = np.nan_to_num(self.grid_features, nan=0.0)

            print(f"栅格特征维度: {self.grid_features.shape}")
            print(f"提取了 {len(self.stations)} 个站点")

            self._monitor_memory("数据加载后")

            return True

        except Exception as e:
            print(f"数据加载失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _extract_station_coordinates(self):
        """提取站点坐标"""
        print("提取站点坐标...")
        all_stations = set()
        all_stations.update(self.in_data['station_clean'].unique())
        all_stations.update(self.out_data['station_clean'].unique())
        all_stations.update(self.od_data['o_station_clean'].unique())
        all_stations.update(self.od_data['d_station_clean'].unique())

        self.stations = sorted(list(all_stations))
        self.station_to_idx = {station: idx for idx, station in enumerate(self.stations)}

        # 提取每个站点的代表坐标
        for station in self.stations:
            in_coords = self.in_data[self.in_data['station_clean'] == station][['longitude', 'latitude']]
            out_coords = self.out_data[self.out_data['station_clean'] == station][['longitude', 'latitude']]

            all_coords = pd.concat([in_coords, out_coords])
            if len(all_coords) > 0:
                self.station_coords[station] = {
                    'longitude': all_coords['longitude'].mean(),
                    'latitude': all_coords['latitude'].mean()
                }
            else:
                self.station_coords[station] = {
                    'longitude': 116.4,  # 北京市中心
                    'latitude': 39.9
                }

        print(f"提取了 {len(self.stations)} 个站点的坐标")

    def _calculate_spatial_features(self, grid_lon, grid_lat, station_lon, station_lat):
        """计算空间特征（V4增强）"""
        distance = np.sqrt((grid_lon - station_lon) ** 2 + (grid_lat - station_lat) ** 2)
        direction = np.arctan2(station_lat - grid_lat, station_lon - grid_lon)
        accessibility = 1.0 / (1.0 + distance * 100)
        return np.array([distance, direction, accessibility])

    def _calculate_time_features(self, hour):
        """计算时间特征（V4增强）"""
        return np.array([
            hour / 23.0,
            np.sin(2 * np.pi * hour / 24),
            np.cos(2 * np.pi * hour / 24),
            1.0 if 6 <= hour <= 10 else 0.0,
            1.0 if 17 <= hour <= 20 else 0.0,
            1.0 if hour in [0, 1, 2, 3, 4, 5, 21, 22, 23] else 0.0
        ])

    def _calculate_distance_between_stations(self, station1, station2):
        """计算两个站点之间的距离"""
        if station1 in self.station_coords and station2 in self.station_coords:
            coord1 = self.station_coords[station1]
            coord2 = self.station_coords[station2]
            distance = np.sqrt(
                (coord1['longitude'] - coord2['longitude']) ** 2 +
                (coord1['latitude'] - coord2['latitude']) ** 2
            )
            return distance
        return 0.1  # 默认距离

    def prepare_station_training_data(self, data, flow_type):
        """准备进出站训练数据（V4优化）"""
        print(f"准备{flow_type}站训练数据...")
        features = []
        spatial_features = []
        time_features = []
        targets = []

        avg_grid_features = np.mean(self.grid_features, axis=0)

        for idx, row in data.iterrows():
            station = row['station_clean']
            hour = row['hour']
            count = row['count']
            grid_lon = row['longitude']
            grid_lat = row['latitude']

            if station in self.station_coords:
                station_coord = self.station_coords[station]
                station_lon = station_coord['longitude']
                station_lat = station_coord['latitude']

                # 基础特征（V4增强）
                station_idx = self.station_to_idx[station]
                base_features = np.concatenate([
                    avg_grid_features,
                    [station_lon, station_lat, station_idx / len(self.stations)]
                ])

                # 空间特征
                spatial_feature = self._calculate_spatial_features(
                    grid_lon, grid_lat, station_lon, station_lat
                )

                # 时间特征
                time_feature = self._calculate_time_features(hour)

                features.append(base_features)
                spatial_features.append(spatial_feature)
                time_features.append(time_feature)
                targets.append(count)

        print(f"{flow_type}站数据准备完成: {len(features)} 条记录")
        return (np.array(features), np.array(spatial_features),
                np.array(time_features), np.array(targets))

    def prepare_od_training_data_v4(self, sample_ratio=0.10):
        """准备OD训练数据（V4优化版本，增加采样）"""
        print(f"准备OD训练数据 V4版本 (采样比例: {sample_ratio})...")

        # 对OD数据进行采样
        sampled_od_data = self.od_data.sample(frac=sample_ratio, random_state=42)
        print(f"采样后OD数据量: {len(sampled_od_data):,} 条记录")

        origin_features = []
        dest_features = []
        od_pair_features = []
        time_features = []
        distance_features = []
        targets = []

        avg_grid_features = np.mean(self.grid_features, axis=0)

        for idx, row in sampled_od_data.iterrows():
            o_station = row['o_station_clean']
            d_station = row['d_station_clean']
            hour = row['hour']
            trip = row['trip']

            if o_station in self.station_coords and d_station in self.station_coords:
                # 起点特征
                o_coord = self.station_coords[o_station]
                o_idx = self.station_to_idx[o_station]
                origin_feature = np.concatenate([
                    avg_grid_features,
                    [o_coord['longitude'], o_coord['latitude'], o_idx / len(self.stations)]
                ])

                # 终点特征
                d_coord = self.station_coords[d_station]
                d_idx = self.station_to_idx[d_station]
                dest_feature = np.concatenate([
                    avg_grid_features,
                    [d_coord['longitude'], d_coord['latitude'], d_idx / len(self.stations)]
                ])

                # OD对特征（V4增强）
                surface_distance = row.get('surface_distance', 0)
                translate = row.get('translate', 0)
                travel_time = row.get('time', 0)
                wait_time = row.get('wait_time', 0)

                od_pair_feature = np.array([
                    surface_distance,
                    translate,
                    travel_time,
                    wait_time
                ])

                # 时间特征
                time_feature = self._calculate_time_features(hour)

                # 距离特征
                station_distance = self._calculate_distance_between_stations(o_station, d_station)
                distance_feature = np.array([station_distance])

                origin_features.append(origin_feature)
                dest_features.append(dest_feature)
                od_pair_features.append(od_pair_feature)
                time_features.append(time_feature)
                distance_features.append(distance_feature)
                targets.append(trip)

        print(f"OD数据准备完成: {len(origin_features)} 条记录")
        return (np.array(origin_features), np.array(dest_features),
                np.array(od_pair_features), np.array(time_features),
                np.array(distance_features), np.array(targets))

    def train_v4_models(self):
        """训练V4模型（融合最佳实践）"""
        print("=" * 80)
        print("训练V4混合模型（目标R²≥0.4）")
        print("=" * 80)

        self._monitor_memory("训练开始前")

        # 准备进站数据
        in_features, in_spatial, in_time, in_targets = self.prepare_station_training_data(self.in_data, 'in')

        # 准备出站数据
        out_features, out_spatial, out_time, out_targets = self.prepare_station_training_data(self.out_data, 'out')

        # 准备OD数据（V4优化版本）
        od_origin, od_dest, od_pair, od_time, od_distance, od_targets = self.prepare_od_training_data_v4()

        # 标准化特征（V4优化）
        print("标准化特征...")
        in_features = self.feature_scaler.fit_transform(in_features)
        in_spatial = self.spatial_scaler.fit_transform(in_spatial)
        in_time = self.temporal_scaler.fit_transform(in_time)

        # 训练进站模型
        print("\n训练进站模型...")
        self._train_v4_station_model(in_features, in_spatial, in_time, in_targets, 'in')

        # 使用相同的标准化器处理出站数据
        out_features = self.feature_scaler.transform(out_features)
        out_spatial = self.spatial_scaler.transform(out_spatial)
        out_time = self.temporal_scaler.transform(out_time)

        # 训练出站模型
        print("\n训练出站模型...")
        self._train_v4_station_model(out_features, out_spatial, out_time, out_targets, 'out')

        # 标准化OD特征（V4优化）
        od_origin = self.feature_scaler.transform(od_origin)
        od_dest = self.feature_scaler.transform(od_dest)
        od_pair = self.od_scaler.fit_transform(od_pair)
        od_time = self.temporal_scaler.transform(od_time)
        od_distance = self.distance_scaler.fit_transform(od_distance)

        # 训练OD模型（V4重点优化）
        print("\n训练OD模型（V4重点优化）...")
        self._train_v4_od_model(od_origin, od_dest, od_pair, od_time, od_distance, od_targets)

        # 训练Position Coding模块
        print("\n训练Position Coding模块（新增站点分流预测）...")
        self._train_position_coding_module()

        self._monitor_memory("训练完成后")
        print("所有V4模型训练完成！")

    def _train_v4_station_model(self, features, spatial_features, time_features, targets, model_type):
        """训练进出站V4模型（融合最佳实践）"""
        print(f"  训练{model_type}站模型...")
        input_dim = features.shape[1]

        if model_type == 'in':
            self.in_model = HybridSpatialModel(
                input_dim=input_dim,
                spatial_dim=3,
                temporal_dim=6,
                hidden_dim=128,
                model_type='station'
            ).to(self.device)
            model = self.in_model
        else:
            self.out_model = HybridSpatialModel(
                input_dim=input_dim,
                spatial_dim=3,
                temporal_dim=6,
                hidden_dim=128,
                model_type='station'
            ).to(self.device)
            model = self.out_model

        # 转换为张量
        features_tensor = torch.tensor(features, dtype=torch.float32).to(self.device)
        spatial_tensor = torch.tensor(spatial_features, dtype=torch.float32).to(self.device)
        time_tensor = torch.tensor(time_features, dtype=torch.float32).to(self.device)
        targets_tensor = torch.tensor(targets, dtype=torch.float32).to(self.device)

        # 训练设置（V4优化）
        optimizer = optim.AdamW(model.parameters(), lr=0.0008, weight_decay=1e-4)
        criterion = nn.HuberLoss(delta=1.0)
        scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=120, eta_min=1e-6)

        # 训练循环
        model.train()
        best_loss = float('inf')
        patience = 0

        for epoch in range(120):
            optimizer.zero_grad()

            predictions = model(features_tensor, spatial_tensor, time_tensor).squeeze()
            loss = criterion(predictions, targets_tensor)

            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), 0.5)
            optimizer.step()
            scheduler.step()

            if loss.item() < best_loss:
                best_loss = loss.item()
                patience = 0
            else:
                patience += 1

            if epoch % 25 == 0:
                print(
                    f"    {model_type} DL Epoch {epoch}: Loss = {loss.item():.6f}, LR = {scheduler.get_last_lr()[0]:.8f}")

            if patience >= 30:
                print(f"    {model_type} DL 早停于第 {epoch} 轮")
                break

        # 清理GPU内存
        del features_tensor, spatial_tensor, time_tensor, targets_tensor
        if self.device.type == 'cuda':
            torch.cuda.empty_cache()

        # 训练传统机器学习模型（V4增强）
        print(f"  训练{model_type}站传统ML模型...")
        combined_features = np.concatenate([features, spatial_features, time_features], axis=1)

        if model_type == 'in':
            # 随机森林
            self.in_rf_model = RandomForestRegressor(
                n_estimators=200, max_depth=15, min_samples_split=5, min_samples_leaf=2,
                random_state=42, n_jobs=-1
            )
            self.in_rf_model.fit(combined_features, targets)

            # ExtraTrees（新增）
            self.in_et_model = ExtraTreesRegressor(
                n_estimators=200, max_depth=15, min_samples_split=5, min_samples_leaf=2,
                random_state=42, n_jobs=-1
            )
            self.in_et_model.fit(combined_features, targets)

            # Ridge回归
            self.in_ridge_model = Ridge(alpha=1.0, random_state=42)
            self.in_ridge_model.fit(combined_features, targets)

            # ElasticNet（新增）
            self.in_elastic_model = ElasticNet(alpha=0.1, l1_ratio=0.5, random_state=42)
            self.in_elastic_model.fit(combined_features, targets)
        else:
            # 随机森林
            self.out_rf_model = RandomForestRegressor(
                n_estimators=200, max_depth=15, min_samples_split=5, min_samples_leaf=2,
                random_state=42, n_jobs=-1
            )
            self.out_rf_model.fit(combined_features, targets)

            # ExtraTrees（新增）
            self.out_et_model = ExtraTreesRegressor(
                n_estimators=200, max_depth=15, min_samples_split=5, min_samples_leaf=2,
                random_state=42, n_jobs=-1
            )
            self.out_et_model.fit(combined_features, targets)

            # Ridge回归
            self.out_ridge_model = Ridge(alpha=1.0, random_state=42)
            self.out_ridge_model.fit(combined_features, targets)

            # ElasticNet（新增）
            self.out_elastic_model = ElasticNet(alpha=0.1, l1_ratio=0.5, random_state=42)
            self.out_elastic_model.fit(combined_features, targets)

    def _train_v4_od_model(self, origin_features, dest_features, od_pair_features, time_features, distance_features,
                           targets):
        """训练OD V4模型（重点优化）"""
        print("  训练OD模型（V4重点优化）...")
        input_dim = origin_features.shape[1]

        self.od_model = HybridSpatialModel(
            input_dim=input_dim,
            hidden_dim=128,
            model_type='od'
        ).to(self.device)

        # 训练设置（V4优化）
        optimizer = optim.AdamW(self.od_model.parameters(), lr=0.0006, weight_decay=1e-5)
        criterion = nn.HuberLoss(delta=1.0)
        scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=150, eta_min=1e-7)

        # 批处理设置
        batch_size = 512
        n_samples = len(origin_features)
        n_batches = (n_samples + batch_size - 1) // batch_size

        print(f"    总样本数: {n_samples:,}, 批大小: {batch_size}, 批数: {n_batches}")

        # 训练循环
        self.od_model.train()
        best_loss = float('inf')
        patience = 0

        for epoch in range(150):
            epoch_loss = 0.0
            indices = np.random.permutation(n_samples)

            for batch_idx in range(n_batches):
                start_idx = batch_idx * batch_size
                end_idx = min((batch_idx + 1) * batch_size, n_samples)
                batch_indices = indices[start_idx:end_idx]

                # 获取批数据
                batch_origin = torch.tensor(origin_features[batch_indices], dtype=torch.float32).to(self.device)
                batch_dest = torch.tensor(dest_features[batch_indices], dtype=torch.float32).to(self.device)
                batch_od_pair = torch.tensor(od_pair_features[batch_indices], dtype=torch.float32).to(self.device)
                batch_time = torch.tensor(time_features[batch_indices], dtype=torch.float32).to(self.device)
                batch_distance = torch.tensor(distance_features[batch_indices], dtype=torch.float32).to(self.device)
                batch_targets = torch.tensor(targets[batch_indices], dtype=torch.float32).to(self.device)

                # 前向传播
                optimizer.zero_grad()
                predictions = self.od_model(
                    batch_origin, batch_dest, batch_od_pair, batch_time, batch_distance
                ).squeeze()

                loss = criterion(predictions, batch_targets)

                # 反向传播
                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.od_model.parameters(), 0.5)
                optimizer.step()

                epoch_loss += loss.item()

                # 清理内存
                del batch_origin, batch_dest, batch_od_pair, batch_time, batch_distance, batch_targets, predictions
                if self.device.type == 'cuda':
                    torch.cuda.empty_cache()

            avg_loss = epoch_loss / n_batches
            scheduler.step()

            if avg_loss < best_loss:
                best_loss = avg_loss
                patience = 0
            else:
                patience += 1

            if epoch % 30 == 0:
                print(f"    OD DL Epoch {epoch}: Loss = {avg_loss:.6f}, LR = {scheduler.get_last_lr()[0]:.8f}")

            if patience >= 40:
                print(f"    OD DL 早停于第 {epoch} 轮")
                break

        # 训练传统机器学习模型（V4增强）
        print("  训练OD传统ML模型...")
        combined_features = np.concatenate([
            origin_features, dest_features, od_pair_features, time_features, distance_features
        ], axis=1)

        # 随机森林
        self.od_rf_model = RandomForestRegressor(
            n_estimators=200, max_depth=12, min_samples_split=8, min_samples_leaf=4,
            random_state=42, n_jobs=-1
        )
        self.od_rf_model.fit(combined_features, targets)

        # ExtraTrees（新增）
        self.od_et_model = ExtraTreesRegressor(
            n_estimators=200, max_depth=12, min_samples_split=8, min_samples_leaf=4,
            random_state=42, n_jobs=-1
        )
        self.od_et_model.fit(combined_features, targets)

        # Ridge回归
        self.od_ridge_model = Ridge(alpha=10.0, random_state=42)
        self.od_ridge_model.fit(combined_features, targets)

        # ElasticNet（新增）
        self.od_elastic_model = ElasticNet(alpha=1.0, l1_ratio=0.5, random_state=42)
        self.od_elastic_model.fit(combined_features, targets)

    def _train_position_coding_module(self):
        """训练Position Coding模块"""
        print("  初始化Position Coding模块...")

        # 初始化Position Coding模块
        self.position_coding_module = PositionCodingModule(
            position_dim=64,
            hidden_dim=128,
            num_stations=len(self.stations)
        ).to(self.device)

        # 初始化分流影响预测器
        self.diversion_predictor = DiversionImpactPredictor(
            self.position_coding_module,
            self.station_coords,
            self.device
        )

        # 准备训练数据
        print("  准备Position Coding训练数据...")
        training_data = self._prepare_position_coding_training_data()

        if len(training_data) == 0:
            print("  警告: 没有足够的数据训练Position Coding模块")
            return

        # 训练设置
        optimizer = optim.AdamW(self.position_coding_module.parameters(), lr=0.001, weight_decay=1e-4)
        criterion_diversion = nn.MSELoss()
        criterion_intensity = nn.MSELoss()
        scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=100, eta_min=1e-6)

        # 转换训练数据为张量
        new_coords = torch.tensor([d['new_coord'] for d in training_data], dtype=torch.float32).to(self.device)
        existing_coords = torch.tensor([d['existing_coord'] for d in training_data], dtype=torch.float32).to(self.device)
        distance_features = torch.tensor([d['distance_features'] for d in training_data], dtype=torch.float32).to(self.device)
        time_features = torch.tensor([d['time_features'] for d in training_data], dtype=torch.float32).to(self.device)
        target_diversion = torch.tensor([d['target_diversion'] for d in training_data], dtype=torch.float32).to(self.device)
        target_intensity = torch.tensor([d['target_intensity'] for d in training_data], dtype=torch.float32).to(self.device)

        print(f"  训练数据: {len(training_data)} 条记录")

        # 训练循环
        self.position_coding_module.train()
        best_loss = float('inf')
        patience = 0

        for epoch in range(100):
            optimizer.zero_grad()

            # 前向传播
            pred_diversion, pred_intensity = self.position_coding_module(
                new_coords, existing_coords, distance_features, time_features
            )

            # 计算损失
            loss_diversion = criterion_diversion(pred_diversion.squeeze(), target_diversion)
            loss_intensity = criterion_intensity(pred_intensity.squeeze(), target_intensity)
            total_loss = loss_diversion + loss_intensity

            # 反向传播
            total_loss.backward()
            torch.nn.utils.clip_grad_norm_(self.position_coding_module.parameters(), 0.5)
            optimizer.step()
            scheduler.step()

            if total_loss.item() < best_loss:
                best_loss = total_loss.item()
                patience = 0
            else:
                patience += 1

            if epoch % 20 == 0:
                print(f"    Position Coding Epoch {epoch}: Loss = {total_loss.item():.6f} "
                      f"(Diversion: {loss_diversion.item():.6f}, Intensity: {loss_intensity.item():.6f})")

            if patience >= 25:
                print(f"    Position Coding 早停于第 {epoch} 轮")
                break

        # 清理GPU内存
        del new_coords, existing_coords, distance_features, time_features, target_diversion, target_intensity
        if self.device.type == 'cuda':
            torch.cuda.empty_cache()

        print("  Position Coding模块训练完成")

    def _prepare_position_coding_training_data(self):
        """准备Position Coding训练数据"""
        training_data = []

        # 使用OD数据来模拟站点间的竞争关系
        # 基本思路：高流量的OD对表示站点间竞争较弱，低流量表示竞争较强

        # 计算每个OD对的平均流量
        od_avg_flows = self.od_data.groupby(['o_station_clean', 'd_station_clean'])['trip'].mean().reset_index()

        # 为每个站点对生成训练样本
        station_list = list(self.station_coords.keys())

        for i, station1 in enumerate(station_list[:50]):  # 限制数量以加快训练
            for j, station2 in enumerate(station_list[:50]):
                if i >= j:  # 避免重复和自身比较
                    continue

                coord1 = self.station_coords[station1]
                coord2 = self.station_coords[station2]

                # 计算距离特征
                distance_features = self._calculate_distance_features_for_position_coding(
                    [coord1['longitude'], coord1['latitude']],
                    [coord2['longitude'], coord2['latitude']]
                )

                # 生成时间特征（使用高峰时段）
                time_features = self._calculate_time_features(8)  # 早高峰

                # 计算目标分流比例和影响强度
                # 基于距离的简单模型：距离越近，分流越大
                distance = distance_features[0]
                target_diversion = max(0.0, min(1.0, np.exp(-distance * 20)))  # 距离衰减
                target_intensity = max(0.0, min(10.0, 1.0 / (1.0 + distance * 50)))  # 影响强度

                training_data.append({
                    'new_coord': [coord1['longitude'], coord1['latitude']],
                    'existing_coord': [coord2['longitude'], coord2['latitude']],
                    'distance_features': distance_features,
                    'time_features': time_features,
                    'target_diversion': target_diversion,
                    'target_intensity': target_intensity
                })

        return training_data

    def _calculate_distance_features_for_position_coding(self, coord1, coord2):
        """为Position Coding计算距离特征"""
        # 欧氏距离
        euclidean_dist = np.sqrt((coord1[0] - coord2[0]) ** 2 + (coord1[1] - coord2[1]) ** 2)

        # 曼哈顿距离
        manhattan_dist = abs(coord1[0] - coord2[0]) + abs(coord1[1] - coord2[1])

        # 影响衰减因子
        impact_decay = np.exp(-euclidean_dist * 10)

        return np.array([euclidean_dist, manhattan_dist, impact_decay])

    def generate_v4_predictions(self):
        """生成V4预测结果（完整测试）"""
        print("=" * 80)
        print("生成V4预测结果（融合最佳实践，目标R²≥0.4）")
        print("=" * 80)

        self._monitor_memory("预测开始前")

        # 完整测试时间范围
        test_hours = list(range(18, 24))
        print(f"测试时间段: {test_hours}")

        # 生成进站预测（完整测试）
        print("\n生成进站预测（完整测试）...")
        in_test_data = self.in_data[self.in_data['hour'].isin(test_hours)].copy()
        print(f"进站测试数据: {len(in_test_data):,} 条记录")

        if len(in_test_data) > 0:
            in_test_features, in_test_spatial, in_test_time, _ = self.prepare_station_training_data(in_test_data, 'in')

            # 标准化
            in_test_features = self.feature_scaler.transform(in_test_features)
            in_test_spatial = self.spatial_scaler.transform(in_test_spatial)
            in_test_time = self.temporal_scaler.transform(in_test_time)

            # 集成预测
            in_predictions = self._v4_predict_station(
                in_test_features, in_test_spatial, in_test_time, 'in'
            )

            in_test_data.loc[:, 'prediction'] = np.maximum(in_predictions, 0)

            # 保存结果
            try:
                in_test_data.to_file('v4_in_500_predictions_with_coords.shp')
                print(f"V4进站预测结果已保存: {len(in_test_data):,} 条记录")
            except:
                in_test_data.drop(columns=['geometry']).to_csv('v4_in_500_predictions_with_coords.csv', index=False)
                print(f"V4进站预测结果已保存为CSV: {len(in_test_data):,} 条记录")

        # 生成出站预测（完整测试）
        print("\n生成出站预测（完整测试）...")
        out_test_data = self.out_data[self.out_data['hour'].isin(test_hours)].copy()
        print(f"出站测试数据: {len(out_test_data):,} 条记录")

        if len(out_test_data) > 0:
            out_test_features, out_test_spatial, out_test_time, _ = self.prepare_station_training_data(out_test_data,
                                                                                                       'out')

            # 标准化
            out_test_features = self.feature_scaler.transform(out_test_features)
            out_test_spatial = self.spatial_scaler.transform(out_test_spatial)
            out_test_time = self.temporal_scaler.transform(out_test_time)

            # 集成预测
            out_predictions = self._v4_predict_station(
                out_test_features, out_test_spatial, out_test_time, 'out'
            )

            out_test_data.loc[:, 'prediction'] = np.maximum(out_predictions, 0)

            # 保存结果
            try:
                out_test_data.to_file('v4_out_500_predictions_with_coords.shp')
                print(f"V4出站预测结果已保存: {len(out_test_data):,} 条记录")
            except:
                out_test_data.drop(columns=['geometry']).to_csv('v4_out_500_predictions_with_coords.csv', index=False)
                print(f"V4出站预测结果已保存为CSV: {len(out_test_data):,} 条记录")

        # 生成OD预测（完整测试）
        print("\n生成OD预测（V4完整测试）...")
        od_test_data = self.od_data[self.od_data['hour'].isin(test_hours)].copy()
        print(f"OD测试数据: {len(od_test_data):,} 条记录")

        if len(od_test_data) > 0:
            od_predictions = self._v4_predict_od(od_test_data)
            od_test_data.loc[:, 'prediction'] = np.maximum(od_predictions, 0)

            # 保存OD预测结果
            od_test_data.to_csv('v4_od_predictions.csv', index=False)
            print(f"V4 OD预测结果已保存: {len(od_test_data):,} 条记录")

        # 生成对比分析
        self._generate_v4_comparison(in_test_data, out_test_data, od_test_data)

        # 生成新增站点分流预测示例
        if self.position_coding_module is not None:
            print("\n生成新增站点分流预测示例...")
            self._generate_diversion_prediction_examples()

        self._monitor_memory("预测完成后")

        return in_test_data, out_test_data, od_test_data

    def _v4_predict_station(self, features, spatial_features, time_features, model_type):
        """V4预测进出站流量（增强集成）"""
        model = self.in_model if model_type == 'in' else self.out_model
        model.eval()

        with torch.no_grad():
            features_tensor = torch.tensor(features, dtype=torch.float32).to(self.device)
            spatial_tensor = torch.tensor(spatial_features, dtype=torch.float32).to(self.device)
            time_tensor = torch.tensor(time_features, dtype=torch.float32).to(self.device)

            dl_predictions = model(features_tensor, spatial_tensor, time_tensor).squeeze()
            dl_predictions = dl_predictions.cpu().numpy()

            # 清理GPU内存
            del features_tensor, spatial_tensor, time_tensor
            if self.device.type == 'cuda':
                torch.cuda.empty_cache()

        # 传统机器学习预测（V4增强）
        combined_features = np.concatenate([features, spatial_features, time_features], axis=1)

        if model_type == 'in':
            rf_predictions = self.in_rf_model.predict(combined_features)
            et_predictions = self.in_et_model.predict(combined_features)
            ridge_predictions = self.in_ridge_model.predict(combined_features)
            elastic_predictions = self.in_elastic_model.predict(combined_features)
        else:
            rf_predictions = self.out_rf_model.predict(combined_features)
            et_predictions = self.out_et_model.predict(combined_features)
            ridge_predictions = self.out_ridge_model.predict(combined_features)
            elastic_predictions = self.out_elastic_model.predict(combined_features)

        # 集成预测（V4权重优化）
        ensemble_predictions = (
                0.45 * dl_predictions +  # 深度学习
                0.25 * rf_predictions +  # 随机森林
                0.15 * et_predictions +  # ExtraTrees
                0.10 * ridge_predictions +  # Ridge
                0.05 * elastic_predictions  # ElasticNet
        )

        return ensemble_predictions

    def _v4_predict_od(self, od_test_data):
        """V4 OD预测（完整数据集批处理）"""
        print("  使用V4优化方法进行完整OD预测...")

        # 准备特征
        origin_features = []
        dest_features = []
        od_pair_features = []
        time_features = []
        distance_features = []

        avg_grid_features = np.mean(self.grid_features, axis=0)

        print("  准备OD特征...")
        for idx, row in od_test_data.iterrows():
            if idx % 100000 == 0:
                print(f"    处理进度: {idx:,} / {len(od_test_data):,}")

            o_station = row['o_station_clean']
            d_station = row['d_station_clean']
            hour = row['hour']

            if o_station in self.station_coords and d_station in self.station_coords:
                # 起点特征
                o_coord = self.station_coords[o_station]
                o_idx = self.station_to_idx[o_station]
                origin_feature = np.concatenate([
                    avg_grid_features,
                    [o_coord['longitude'], o_coord['latitude'], o_idx / len(self.stations)]
                ])

                # 终点特征
                d_coord = self.station_coords[d_station]
                d_idx = self.station_to_idx[d_station]
                dest_feature = np.concatenate([
                    avg_grid_features,
                    [d_coord['longitude'], d_coord['latitude'], d_idx / len(self.stations)]
                ])

                # OD对特征
                od_pair_feature = np.array([
                    row.get('surface_distance', 0),
                    row.get('translate', 0),
                    row.get('time', 0),
                    row.get('wait_time', 0)
                ])

                # 时间特征
                time_feature = self._calculate_time_features(hour)

                # 距离特征
                station_distance = self._calculate_distance_between_stations(o_station, d_station)
                distance_feature = np.array([station_distance])

                origin_features.append(origin_feature)
                dest_features.append(dest_feature)
                od_pair_features.append(od_pair_feature)
                time_features.append(time_feature)
                distance_features.append(distance_feature)
            else:
                # 如果站点不存在，添加零向量
                zero_feature = np.zeros(len(avg_grid_features) + 3)
                origin_features.append(zero_feature)
                dest_features.append(zero_feature)
                od_pair_features.append(np.zeros(4))
                time_features.append(self._calculate_time_features(hour))
                distance_features.append(np.array([0.1]))

        # 转换为数组并标准化
        print("  标准化特征...")
        origin_features = np.array(origin_features)
        dest_features = np.array(dest_features)
        od_pair_features = np.array(od_pair_features)
        time_features = np.array(time_features)
        distance_features = np.array(distance_features)

        origin_features = self.feature_scaler.transform(origin_features)
        dest_features = self.feature_scaler.transform(dest_features)
        od_pair_features = self.od_scaler.transform(od_pair_features)
        time_features = self.temporal_scaler.transform(time_features)
        distance_features = self.distance_scaler.transform(distance_features)

        # 深度学习预测（批处理）
        print("  深度学习预测...")
        self.od_model.eval()
        dl_predictions = []

        batch_size = 1024
        n_samples = len(origin_features)
        n_batches = (n_samples + batch_size - 1) // batch_size

        with torch.no_grad():
            for batch_idx in range(n_batches):
                if batch_idx % 100 == 0:
                    print(f"    预测进度: {batch_idx} / {n_batches}")

                start_idx = batch_idx * batch_size
                end_idx = min((batch_idx + 1) * batch_size, n_samples)

                # 获取批数据
                batch_origin = torch.tensor(origin_features[start_idx:end_idx], dtype=torch.float32).to(self.device)
                batch_dest = torch.tensor(dest_features[start_idx:end_idx], dtype=torch.float32).to(self.device)
                batch_od_pair = torch.tensor(od_pair_features[start_idx:end_idx], dtype=torch.float32).to(self.device)
                batch_time = torch.tensor(time_features[start_idx:end_idx], dtype=torch.float32).to(self.device)
                batch_distance = torch.tensor(distance_features[start_idx:end_idx], dtype=torch.float32).to(self.device)

                # 预测
                batch_predictions = self.od_model(
                    batch_origin, batch_dest, batch_od_pair, batch_time, batch_distance
                ).squeeze()

                dl_predictions.extend(batch_predictions.cpu().numpy())

                # 清理内存
                del batch_origin, batch_dest, batch_od_pair, batch_time, batch_distance, batch_predictions
                if self.device.type == 'cuda':
                    torch.cuda.empty_cache()

        dl_predictions = np.array(dl_predictions)

        # 传统机器学习预测（V4增强）
        print("  传统ML预测...")
        combined_features = np.concatenate([
            origin_features, dest_features, od_pair_features, time_features, distance_features
        ], axis=1)

        rf_predictions = self.od_rf_model.predict(combined_features)
        et_predictions = self.od_et_model.predict(combined_features)
        ridge_predictions = self.od_ridge_model.predict(combined_features)
        elastic_predictions = self.od_elastic_model.predict(combined_features)

        # 集成预测（V4权重优化）
        ensemble_predictions = (
                0.45 * dl_predictions +  # 深度学习
                0.25 * rf_predictions +  # 随机森林
                0.15 * et_predictions +  # ExtraTrees
                0.10 * ridge_predictions +  # Ridge
                0.05 * elastic_predictions  # ElasticNet
        )

        return ensemble_predictions

    def _generate_v4_comparison(self, in_test_data, out_test_data, od_test_data):
        """生成V4预测对比分析"""
        print("\n生成V4预测对比汇总...")

        comparison_data = []

        # 进站对比
        if in_test_data is not None and len(in_test_data) > 0:
            for _, row in in_test_data.iterrows():
                comparison_data.append({
                    'Type': 'In',
                    'True_Value': float(row['count']),
                    'Predicted_Value': float(row['prediction']),
                    'Station': str(row['station_clean']),
                    'Hour': int(row['hour']),
                    'Error': float(abs(row['count'] - row['prediction']))
                })

        # 出站对比
        if out_test_data is not None and len(out_test_data) > 0:
            for _, row in out_test_data.iterrows():
                comparison_data.append({
                    'Type': 'Out',
                    'True_Value': float(row['count']),
                    'Predicted_Value': float(row['prediction']),
                    'Station': str(row['station_clean']),
                    'Hour': int(row['hour']),
                    'Error': float(abs(row['count'] - row['prediction']))
                })

        # OD对比
        if od_test_data is not None and len(od_test_data) > 0:
            for _, row in od_test_data.iterrows():
                comparison_data.append({
                    'Type': 'OD',
                    'True_Value': float(row['trip']),
                    'Predicted_Value': float(row['prediction']),
                    'Station': f"{row['o_station_clean']}->{row['d_station_clean']}",
                    'Hour': int(row['hour']),
                    'Error': float(abs(row['trip'] - row['prediction']))
                })

        # 保存对比结果
        if comparison_data:
            comparison_df = pd.DataFrame(comparison_data)
            comparison_df.to_csv('v4_prediction_comparison.csv', index=False)
            print(f"V4预测对比汇总已保存: {len(comparison_data):,} 条记录")

            # 生成性能分析
            self._analyze_v4_performance(comparison_df)

        return comparison_data

    def _analyze_v4_performance(self, comparison_df):
        """分析V4预测性能"""
        print("\n" + "=" * 80)
        print("V4预测性能分析（融合最佳实践，目标R²≥0.4）")
        print("=" * 80)

        performance_results = {}

        # 按类型分析
        for pred_type in ['In', 'Out', 'OD']:
            type_data = comparison_df[comparison_df['Type'] == pred_type]

            if len(type_data) > 0:
                true_values = type_data['True_Value'].values
                pred_values = type_data['Predicted_Value'].values

                mae = mean_absolute_error(true_values, pred_values)
                rmse = np.sqrt(mean_squared_error(true_values, pred_values))
                r2 = r2_score(true_values, pred_values)

                performance_results[pred_type] = {
                    'samples': len(type_data),
                    'mae': mae,
                    'rmse': rmse,
                    'r2': r2,
                    'mean_true': np.mean(true_values),
                    'mean_pred': np.mean(pred_values)
                }

                print(f"{pred_type} 流量预测:")
                print(f"  样本数: {len(type_data):,}")
                print(f"  MAE:  {mae:.4f}")
                print(f"  RMSE: {rmse:.4f}")
                print(f"  R²:   {r2:.4f}")
                print(f"  真实值均值: {np.mean(true_values):.2f}")
                print(f"  预测值均值: {np.mean(pred_values):.2f}")
                print()

        # 整体性能
        all_true = comparison_df['True_Value'].values
        all_pred = comparison_df['Predicted_Value'].values

        overall_mae = mean_absolute_error(all_true, all_pred)
        overall_rmse = np.sqrt(mean_squared_error(all_true, all_pred))
        overall_r2 = r2_score(all_true, all_pred)

        performance_results['Overall'] = {
            'samples': len(comparison_df),
            'mae': overall_mae,
            'rmse': overall_rmse,
            'r2': overall_r2,
            'mean_true': np.mean(all_true),
            'mean_pred': np.mean(all_pred)
        }

        print("整体预测性能:")
        print(f"  总样本数: {len(comparison_df):,}")
        print(f"  MAE:  {overall_mae:.4f}")
        print(f"  RMSE: {overall_rmse:.4f}")
        print(f"  R²:   {overall_r2:.4f}")
        print(f"  真实值均值: {np.mean(all_true):.2f}")
        print(f"  预测值均值: {np.mean(all_pred):.2f}")

        # 目标达成检查
        print("\n" + "=" * 60)
        print("V4目标达成检查")
        print("=" * 60)
        print(f"目标: 整体R² ≥ 0.4")
        print(f"实际: 整体R² = {overall_r2:.4f}")

        if overall_r2 >= 0.4:
            print("🎉 V4成功达到R²≥0.4的目标！")
        else:
            print(f"⚠️ V4未达到目标，还需提升 {0.4 - overall_r2:.4f}")

        # 保存性能结果
        import json
        with open('v4_performance_analysis.json', 'w') as f:
            json.dump(performance_results, f, indent=2)

        print("V4性能分析已保存到 v4_performance_analysis.json")

    def _generate_diversion_prediction_examples(self):
        """生成新增站点分流预测示例"""
        print("  生成分流预测示例...")

        # 选择几个示例新站点位置
        example_new_stations = [
            {"name": "新站点A", "coord": [116.3, 39.9]},  # 北京市中心附近
            {"name": "新站点B", "coord": [116.5, 40.0]},  # 东北方向
            {"name": "新站点C", "coord": [116.2, 39.8]},  # 西南方向
        ]

        # 生成时间特征（早高峰）
        time_features = self._calculate_time_features(8)

        diversion_results = {}

        for new_station in example_new_stations:
            print(f"    分析新站点: {new_station['name']} 位置: {new_station['coord']}")

            # 预测对网络的影响
            network_impact = self.diversion_predictor.analyze_network_impact(
                new_station['coord'],
                new_station['name'],
                time_features
            )

            diversion_results[new_station['name']] = network_impact

            # 打印关键结果
            print(f"      总体网络分流: {network_impact['total_network_diversion']:.4f}")
            print(f"      高影响站点数: {len(network_impact['high_impact_stations'])}")
            print(f"      中等影响站点数: {len(network_impact['medium_impact_stations'])}")
            print(f"      平均影响半径: {network_impact['average_impact_radius']:.4f}")

            if network_impact['high_impact_stations']:
                print(f"      高影响站点: {', '.join(network_impact['high_impact_stations'][:5])}")

        # 保存分流预测结果
        self._save_diversion_results(diversion_results)

        print("  分流预测示例生成完成")

    def _save_diversion_results(self, diversion_results):
        """保存分流预测结果"""
        try:
            import json

            # 转换为可序列化的格式
            serializable_results = {}
            for station_name, impact in diversion_results.items():
                serializable_results[station_name] = {
                    'total_network_diversion': float(impact['total_network_diversion']),
                    'high_impact_stations': impact['high_impact_stations'],
                    'medium_impact_stations': impact['medium_impact_stations'],
                    'num_affected_stations': int(impact['num_affected_stations']),
                    'average_impact_radius': float(impact['average_impact_radius']),
                    'maximum_impact_radius': float(impact['maximum_impact_radius']),
                    'station_impacts': {
                        k: {
                            'diversion_ratio': float(v['diversion_ratio']),
                            'impact_intensity': float(v['impact_intensity']),
                            'distance': float(v['distance']),
                            'impact_decay': float(v['impact_decay'])
                        }
                        for k, v in impact['station_impacts'].items()
                    }
                }

            # 保存到文件
            with open('v4_diversion_predictions.json', 'w', encoding='utf-8') as f:
                json.dump(serializable_results, f, indent=2, ensure_ascii=False)

            print("    分流预测结果已保存到 v4_diversion_predictions.json")

            # 生成简化的CSV报告
            self._generate_diversion_csv_report(diversion_results)

        except Exception as e:
            print(f"    保存分流预测结果时出错: {e}")

    def _generate_diversion_csv_report(self, diversion_results):
        """生成分流预测的CSV报告"""
        try:
            report_data = []

            for new_station_name, impact in diversion_results.items():
                for existing_station, station_impact in impact['station_impacts'].items():
                    report_data.append({
                        'New_Station': new_station_name,
                        'Existing_Station': existing_station,
                        'Diversion_Ratio': station_impact['diversion_ratio'],
                        'Impact_Intensity': station_impact['impact_intensity'],
                        'Distance': station_impact['distance'],
                        'Impact_Decay': station_impact['impact_decay'],
                        'Impact_Level': self._classify_impact_level(
                            station_impact['diversion_ratio'],
                            station_impact['impact_intensity']
                        )
                    })

            # 保存CSV
            report_df = pd.DataFrame(report_data)
            report_df.to_csv('v4_diversion_impact_report.csv', index=False, encoding='utf-8')

            print("    分流影响报告已保存到 v4_diversion_impact_report.csv")

        except Exception as e:
            print(f"    生成CSV报告时出错: {e}")

    def _classify_impact_level(self, diversion_ratio, impact_intensity):
        """分类影响等级"""
        if diversion_ratio > 0.1 and impact_intensity > 0.5:
            return "High"
        elif diversion_ratio > 0.05 and impact_intensity > 0.3:
            return "Medium"
        elif diversion_ratio > 0.01:
            return "Low"
        else:
            return "Minimal"

    def predict_new_station_impact(self, new_station_coord, new_station_name, hour=8):
        """
        公共接口：预测新增站点的分流影响

        Args:
            new_station_coord: 新站点坐标 [longitude, latitude]
            new_station_name: 新站点名称
            hour: 预测时间（小时，0-23）

        Returns:
            dict: 包含分流预测结果的字典
        """
        if self.diversion_predictor is None:
            print("错误: Position Coding模块未训练，请先运行完整的训练流程")
            return None

        print(f"预测新站点 '{new_station_name}' 在坐标 {new_station_coord} 的分流影响...")

        # 生成时间特征
        time_features = self._calculate_time_features(hour)

        # 预测网络影响
        network_impact = self.diversion_predictor.analyze_network_impact(
            new_station_coord,
            new_station_name,
            time_features
        )

        # 生成详细报告
        print(f"\n新站点 '{new_station_name}' 分流影响分析:")
        print(f"  总体网络分流: {network_impact['total_network_diversion']:.4f}")
        print(f"  受影响站点数: {network_impact['num_affected_stations']}")
        print(f"  高影响站点数: {len(network_impact['high_impact_stations'])}")
        print(f"  中等影响站点数: {len(network_impact['medium_impact_stations'])}")
        print(f"  平均影响半径: {network_impact['average_impact_radius']:.4f}")
        print(f"  最大影响半径: {network_impact['maximum_impact_radius']:.4f}")

        if network_impact['high_impact_stations']:
            print(f"  高影响站点: {', '.join(network_impact['high_impact_stations'])}")

        return network_impact


def main():
    """主函数"""
    print("=" * 80)
    print("V4版本 - 融合最佳实践的空间感知地铁流量预测系统")
    print("目标：将整体R²从V3的0.27提升到0.4+")
    print("=" * 80)

    start_time = time.time()

    try:
        # 初始化系统
        system = V4SpatialPredictionSystem()

        # 加载数据
        if not system.load_and_process_data():
            return False

        # 训练模型
        system.train_v4_models()

        # 生成预测
        in_results, out_results, od_results = system.generate_v4_predictions()

        elapsed_time = time.time() - start_time
        print(f"\n总运行时间: {elapsed_time:.2f} 秒 ({elapsed_time / 60:.1f} 分钟)")
        print("=" * 80)
        print("V4融合预测系统运行完成！")
        print("=" * 80)

        return True

    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_system():
    """测试系统功能"""
    print("=" * 80)
    print("V4系统测试")
    print("=" * 80)

    try:
        # 初始化系统
        system = V4SpatialPredictionSystem()

        # 测试数据加载
        print("测试数据加载...")
        if not system.load_and_process_data():
            print("数据加载测试失败")
            return False

        print("数据加载测试通过")

        # 测试小批量预测
        print("\n测试小批量预测...")
        test_data = system.in_data.head(100)
        features, spatial, temporal, targets = system.prepare_station_training_data(test_data, 'in')

        print(f"特征准备测试通过: {features.shape}")

        print("V4系统测试全部通过")
        return True

    except Exception as e:
        print(f"系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_position_coding():
    """测试Position Coding功能"""
    print("=" * 80)
    print("Position Coding模块测试")
    print("=" * 80)

    try:
        # 初始化系统
        system = V4SpatialPredictionSystem()

        # 加载数据
        print("加载数据...")
        if not system.load_and_process_data():
            print("数据加载失败")
            return False

        # 训练模型（包括Position Coding）
        print("训练模型...")
        system.train_v4_models()

        # 测试新站点分流预测
        print("\n测试新站点分流预测...")

        # 测试案例1：北京市中心新站点
        test_coord1 = [116.4, 39.9]
        result1 = system.predict_new_station_impact(test_coord1, "测试站点1", hour=8)

        # 测试案例2：郊区新站点
        test_coord2 = [116.6, 40.1]
        result2 = system.predict_new_station_impact(test_coord2, "测试站点2", hour=18)

        if result1 and result2:
            print("\n✅ Position Coding模块测试通过")
            print(f"测试站点1影响的站点数: {result1['num_affected_stations']}")
            print(f"测试站点2影响的站点数: {result2['num_affected_stations']}")
            return True
        else:
            print("❌ Position Coding测试失败")
            return False

    except Exception as e:
        print(f"Position Coding测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        if sys.argv[1] == 'test':
            success = test_system()
        elif sys.argv[1] == 'test_position':
            success = test_position_coding()
        else:
            print("使用方法:")
            print("  python main.py              # 运行完整系统")
            print("  python main.py test         # 测试基本功能")
            print("  python main.py test_position # 测试Position Coding功能")
            success = False
    else:
        success = main()

    sys.exit(0 if success else 1)
