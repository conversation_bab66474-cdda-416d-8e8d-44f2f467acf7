import os
import numpy as np
import pandas as pd
import geopandas as gpd
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import r2_score, mean_squared_error
import warnings
warnings.filterwarnings('ignore')

# ------------------------------
# Configuration and Hyperparameters
# ------------------------------
DATA_DIR = r"C:\Users\<USER>\Desktop\接驳"
DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {DEVICE}")

# Data parameters
N_GRID = 70000    # approx number of grid nodes
N_STATION = 350   # approx number of station nodes
HIST_WINDOW = 6   # use past 6 hours for prediction
BATCH_SIZE = 256  # Reduced for memory efficiency
EPOCHS = 30       # Reduced for faster training
LR = 1e-3
HIDDEN_DIM = 32   # Reduced for memory efficiency
NUM_LAYERS = 2    # Reduced for memory efficiency

# Output files
OUT_IN = os.path.join(DATA_DIR, 'in_500_predictions_with_coords.shp')
OUT_OUT = os.path.join(DATA_DIR, 'out_500_predictions_with_coords.shp')
OUT_OD = os.path.join(DATA_DIR, 'od_predictions.csv')

# ------------------------------
# Utility functions
# ------------------------------
def load_shapefile(fname):
    return gpd.read_file(os.path.join(DATA_DIR, fname))

def build_station_graph(station_connect_csv):
    # Build adjacency for stations
    df = pd.read_csv(os.path.join(DATA_DIR, station_connect_csv))
    # clean names
    df['station_1'] = df['station_1'].str.replace(r'_.*$', '', regex=True)
    df['station_2'] = df['station_2'].str.replace(r'_.*$', '', regex=True)
    stations = pd.unique(df[['station_1','station_2']].values.ravel())
    idx = {s: i for i, s in enumerate(stations)}
    N = len(stations)
    A = np.zeros((N, N), dtype=np.float32)
    for _, row in df.iterrows():
        u, v = idx[row['station_1']], idx[row['station_2']]
        A[u, v] = A[v, u] = 1.0
    # Add self-loops
    np.fill_diagonal(A, 1.0)
    # normalize
    D = np.diag(1.0 / (A.sum(axis=1) + 1e-6))
    A_norm = D.dot(A)
    return torch.tensor(A_norm, device=DEVICE), stations, idx

def aggregate_station_features(grid_gdf, grid_feats_df, station_idx):
    """Aggregate grid features around each station within 1.5km radius"""
    station_feats = []

    for station_name in station_idx.keys():
        # Get grids associated with this station
        station_grids = grid_gdf[grid_gdf['station'] == station_name]

        if len(station_grids) > 0:
            # Get grid IDs for this station
            grid_ids = station_grids['grid_id'].values

            # Get features for these grids
            station_grid_feats = grid_feats_df[grid_feats_df['id'].isin(grid_ids)]

            if len(station_grid_feats) > 0:
                # Aggregate features (mean)
                numeric_cols = station_grid_feats.select_dtypes(include=[np.number]).columns
                numeric_cols = [col for col in numeric_cols if col != 'id']

                agg_feats = station_grid_feats[numeric_cols].mean()
                agg_feats['station'] = station_name
                station_feats.append(agg_feats)
            else:
                # Create zero features if no grid data
                numeric_cols = grid_feats_df.select_dtypes(include=[np.number]).columns
                numeric_cols = [col for col in numeric_cols if col != 'id']

                zero_feats = pd.Series(0.0, index=numeric_cols)
                zero_feats['station'] = station_name
                station_feats.append(zero_feats)
        else:
            # Create zero features if no grid data
            numeric_cols = grid_feats_df.select_dtypes(include=[np.number]).columns
            numeric_cols = [col for col in numeric_cols if col != 'id']

            zero_feats = pd.Series(0.0, index=numeric_cols)
            zero_feats['station'] = station_name
            station_feats.append(zero_feats)

    return pd.DataFrame(station_feats)

# ------------------------------
# Dataset definitions
# ------------------------------
class MetroDataset(Dataset):
    def __init__(self, in_data, out_data, od_df, grid_feats, station_feats, station_idx):
        """
        in_data: DataFrame with columns ['hour', 'grid_id', 'station', 'count']
        out_data: DataFrame with columns ['hour', 'grid_id', 'station', 'count']
        od_df: DataFrame with OD flow data
        """
        self.in_data = in_data.copy()
        self.out_data = out_data.copy()
        self.od_df = od_df.copy()
        self.grid_feats = grid_feats.copy()
        self.station_feats = station_feats.copy()
        self.station_idx = station_idx

        # Create simplified dataset for training
        # Use aggregated hourly flows per station
        self.station_hourly_in = self.in_data.groupby(['hour', 'station'])['count'].sum().reset_index()
        self.station_hourly_out = self.out_data.groupby(['hour', 'station'])['count'].sum().reset_index()

        # Create training samples from station flows
        self.samples = []
        stations = list(station_idx.keys())
        hours = sorted(self.station_hourly_in['hour'].unique())

        for station in stations:
            for hour in hours[6:]:  # Start from hour 6 to have history
                # Get historical data
                hist_hours = list(range(hour-6, hour))

                # Get in/out flows for this station
                in_flows = []
                out_flows = []

                for h in hist_hours:
                    in_flow = self.station_hourly_in[
                        (self.station_hourly_in['hour'] == h) &
                        (self.station_hourly_in['station'] == station)
                    ]['count'].sum()
                    out_flow = self.station_hourly_out[
                        (self.station_hourly_out['hour'] == h) &
                        (self.station_hourly_out['station'] == station)
                    ]['count'].sum()

                    in_flows.append(in_flow)
                    out_flows.append(out_flow)

                # Current hour flows as targets
                target_in = self.station_hourly_in[
                    (self.station_hourly_in['hour'] == hour) &
                    (self.station_hourly_in['station'] == station)
                ]['count'].sum()

                target_out = self.station_hourly_out[
                    (self.station_hourly_out['hour'] == hour) &
                    (self.station_hourly_out['station'] == station)
                ]['count'].sum()

                self.samples.append({
                    'station': station,
                    'hour': hour,
                    'in_flows': in_flows,
                    'out_flows': out_flows,
                    'target_in': target_in,
                    'target_out': target_out
                })

        # Prepare scalers
        self.scaler = StandardScaler()
        numeric_cols = self.station_feats.select_dtypes(include=[np.number]).columns
        self.scaler.fit(self.station_feats[numeric_cols])

    def __len__(self):
        return len(self.samples)

    def __getitem__(self, idx):
        sample = self.samples[idx]
        station = sample['station']

        # Get station features
        station_feats = self.station_feats[self.station_feats['station'] == station]
        if len(station_feats) > 0:
            numeric_cols = station_feats.select_dtypes(include=[np.number]).columns
            feats = station_feats[numeric_cols].values.flatten()
            feats = self.scaler.transform(feats.reshape(1, -1)).flatten()
        else:
            feats = np.zeros(50)  # Default feature size

        # Combine historical flows and features
        in_flows = np.array(sample['in_flows'], dtype=np.float32)
        out_flows = np.array(sample['out_flows'], dtype=np.float32)

        # Create input features
        X = np.concatenate([in_flows, out_flows, feats])

        # Targets
        y_in = np.array([sample['target_in']], dtype=np.float32)
        y_out = np.array([sample['target_out']], dtype=np.float32)

        return (torch.tensor(X, dtype=torch.float32),
                torch.tensor(y_in, dtype=torch.float32),
                torch.tensor(y_out, dtype=torch.float32),
                self.station_idx[station])

# ------------------------------
# Simplified Neural Network Model
# ------------------------------
class MetroFlowPredictor(nn.Module):
    def __init__(self, input_dim, hidden_dim=64):
        super().__init__()
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim

        # Shared feature extractor
        self.feature_extractor = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2)
        )

        # Separate heads for in and out flow prediction
        self.in_predictor = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, 1)
        )

        self.out_predictor = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, 1)
        )

    def forward(self, x):
        # Extract features
        features = self.feature_extractor(x)

        # Predict in and out flows
        in_pred = self.in_predictor(features)
        out_pred = self.out_predictor(features)

        return in_pred.squeeze(-1), out_pred.squeeze(-1)

# ------------------------------
# Training and Inference
# ------------------------------
def train():
    print("Loading data...")

    # Load data files
    try:
        # Load CSV data (grid-station flows)
        in_data = pd.read_csv(os.path.join(DATA_DIR, 'in_500.csv'))
        out_data = pd.read_csv(os.path.join(DATA_DIR, 'out_500.csv'))
        grid_feats = pd.read_csv(os.path.join(DATA_DIR, 'leti_data.csv'))

        print(f"Loaded in_data: {len(in_data)} records")
        print(f"Loaded out_data: {len(out_data)} records")
        print(f"Loaded grid_feats: {len(grid_feats)} records")

        # Build station graph
        A_station, stations, station_idx = build_station_graph('station_connect_2023.csv')
        print(f"Built station graph with {len(stations)} stations")

        # Aggregate station features
        # Create a mapping from station names to grid features
        station_grid_map = {}
        for station in stations:
            # Get grids associated with this station
            station_grids = in_data[in_data['station'] == station]['grid_id'].unique()
            station_grid_map[station] = station_grids

        # Aggregate features for each station
        station_feats = aggregate_station_features_simple(grid_feats, station_grid_map, stations)
        print(f"Aggregated station features: {station_feats.shape}")

        # Create dummy OD data for now (since the OD file might not be accessible)
        od_df = pd.DataFrame()

        # Prepare dataset and loader
        dataset = MetroDataset(in_data, out_data, od_df, grid_feats, station_feats, station_idx)
        loader = DataLoader(dataset, batch_size=BATCH_SIZE, shuffle=True)
        print(f"Created dataset with {len(dataset)} samples")

        # Determine input dimension
        sample_x, _, _, _ = dataset[0]
        input_dim = sample_x.shape[0]
        print(f"Input dimension: {input_dim}")

        # Model
        model = MetroFlowPredictor(input_dim=input_dim, hidden_dim=HIDDEN_DIM)
        model.to(DEVICE)
        optimizer = optim.Adam(model.parameters(), lr=LR)
        criterion = nn.MSELoss()

        print("Starting training...")

        # Training loop
        best_loss = float('inf')
        for epoch in range(EPOCHS):
            model.train()
            total_loss = 0
            num_batches = 0

            for batch_data in loader:
                X, y_in, y_out, station_ids = batch_data
                X = X.to(DEVICE)
                y_in = y_in.to(DEVICE)
                y_out = y_out.to(DEVICE)

                optimizer.zero_grad()

                # Forward pass
                pred_in, pred_out = model(X)

                # Calculate loss
                loss_in = criterion(pred_in, y_in.squeeze())
                loss_out = criterion(pred_out, y_out.squeeze())
                loss = loss_in + loss_out

                loss.backward()
                optimizer.step()

                total_loss += loss.item()
                num_batches += 1

            avg_loss = total_loss / num_batches
            print(f"Epoch {epoch+1}/{EPOCHS}, Loss: {avg_loss:.4f}")

            if avg_loss < best_loss:
                best_loss = avg_loss
                torch.save(model.state_dict(), os.path.join(DATA_DIR, 'best_metro_model.pth'))

        print("Training completed!")

        # Generate predictions
        print("Generating predictions...")
        generate_predictions(model, dataset, in_data, out_data, station_idx)

    except Exception as e:
        print(f"Error during training: {e}")
        import traceback
        traceback.print_exc()

def aggregate_station_features_simple(grid_feats, station_grid_map, stations):
    """Simple aggregation of station features"""
    station_feats_list = []

    # Get numeric columns
    numeric_cols = grid_feats.select_dtypes(include=[np.number]).columns
    numeric_cols = [col for col in numeric_cols if col != 'id']

    for station in stations:
        if station in station_grid_map and len(station_grid_map[station]) > 0:
            # Get features for grids associated with this station
            station_grid_feats = grid_feats[grid_feats['id'].isin(station_grid_map[station])]

            if len(station_grid_feats) > 0:
                # Aggregate features (mean)
                agg_feats = station_grid_feats[numeric_cols].mean()
            else:
                # Create zero features
                agg_feats = pd.Series(0.0, index=numeric_cols)
        else:
            # Create zero features
            agg_feats = pd.Series(0.0, index=numeric_cols)

        agg_feats['station'] = station
        station_feats_list.append(agg_feats)

    return pd.DataFrame(station_feats_list)

def generate_predictions(model, dataset, in_data, out_data, station_idx):
    """Generate predictions and save to files"""
    try:
        model.eval()

        # Generate predictions for all samples
        all_predictions_in = []
        all_predictions_out = []
        all_stations = []
        all_hours = []

        with torch.no_grad():
            for i in range(len(dataset)):
                X, y_in_true, y_out_true, station_id = dataset[i]
                X = X.unsqueeze(0).to(DEVICE)  # Add batch dimension

                pred_in, pred_out = model(X)

                # Get station name from station_id
                station_name = None
                for name, sid in station_idx.items():
                    if sid == station_id:
                        station_name = name
                        break

                if station_name:
                    sample = dataset.samples[i]
                    all_predictions_in.append(pred_in.cpu().item())
                    all_predictions_out.append(pred_out.cpu().item())
                    all_stations.append(station_name)
                    all_hours.append(sample['hour'])

        # Create prediction DataFrames
        pred_df_in = pd.DataFrame({
            'station': all_stations,
            'hour': all_hours,
            'prediction': all_predictions_in
        })

        pred_df_out = pd.DataFrame({
            'station': all_stations,
            'hour': all_hours,
            'prediction': all_predictions_out
        })

        # Save predictions
        pred_df_in.to_csv(os.path.join(DATA_DIR, 'in_flow_predictions.csv'), index=False)
        pred_df_out.to_csv(os.path.join(DATA_DIR, 'out_flow_predictions.csv'), index=False)

        print(f"Saved predictions: {len(pred_df_in)} in-flow, {len(pred_df_out)} out-flow")

        # Calculate R² scores
        if len(all_predictions_in) > 0:
            # Get true values for comparison
            true_in = []
            true_out = []

            for i in range(len(dataset)):
                _, y_in_true, y_out_true, _ = dataset[i]
                true_in.append(y_in_true.item())
                true_out.append(y_out_true.item())

            r2_in = r2_score(true_in, all_predictions_in)
            r2_out = r2_score(true_out, all_predictions_out)

            print(f"R² Score - In Flow: {r2_in:.4f}")
            print(f"R² Score - Out Flow: {r2_out:.4f}")

            # Save performance metrics
            metrics = {
                'r2_in': r2_in,
                'r2_out': r2_out,
                'mse_in': mean_squared_error(true_in, all_predictions_in),
                'mse_out': mean_squared_error(true_out, all_predictions_out)
            }

            import json
            with open(os.path.join(DATA_DIR, 'model_metrics.json'), 'w') as f:
                json.dump(metrics, f, indent=2)

        # Try to create shapefile outputs if possible
        try:
            create_shapefile_outputs(pred_df_in, pred_df_out, in_data, out_data)
        except Exception as e:
            print(f"Could not create shapefile outputs: {e}")

    except Exception as e:
        print(f"Error generating predictions: {e}")
        import traceback
        traceback.print_exc()

def create_shapefile_outputs(pred_df_in, pred_df_out, in_data, out_data):
    """Create shapefile outputs with predictions"""
    try:
        # Load existing shapefiles if they exist
        try:
            in_gdf = load_shapefile('in_500_with_coords.shp')
            out_gdf = load_shapefile('out_500_with_coords.shp')

            # Merge predictions with spatial data
            # This is a simplified approach - in practice you'd need proper spatial joining
            in_gdf['prediction'] = 0.0  # Default value
            out_gdf['prediction'] = 0.0  # Default value

            # Save updated shapefiles
            in_gdf.to_file(OUT_IN)
            out_gdf.to_file(OUT_OUT)

            print("Created shapefile outputs with predictions")

        except Exception as e:
            print(f"Could not load/process shapefiles: {e}")

    except Exception as e:
        print(f"Error creating shapefile outputs: {e}")

if __name__ == '__main__':
    print("Starting Metro Flow Prediction System...")
    train()

