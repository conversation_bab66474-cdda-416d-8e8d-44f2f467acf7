import os
import numpy as np
import pandas as pd
import geopandas as gpd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler

# ------------------------------
# Configuration and Hyperparameters
# ------------------------------
DATA_DIR = r"C:\Users\<USER>\Desktop\接驳"
DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

# Data parameters
N_GRID = 70000    # approx number of grid nodes
N_STATION = 350   # approx number of station nodes
HIST_WINDOW = 6   # use past 6 hours for prediction
BATCH_SIZE = 1024
EPOCHS = 50
LR = 1e-3
HIDDEN_DIM = 64   # hidden dimension for STGCN layers
NUM_LAYERS = 3    # number of STGCN layers

# Output files
OUT_IN = os.path.join(DATA_DIR, 'in_500_predictions_with_coords.shp')
OUT_OUT = os.path.join(DATA_DIR, 'out_500_predictions_with_coords.shp')
OUT_OD = os.path.join(DATA_DIR, 'od_predictions.csv')

# ------------------------------
# Utility functions
# ------------------------------
def load_shapefile(fname):
    return gpd.read_file(os.path.join(DATA_DIR, fname))

def build_station_graph(station_connect_csv):
    # Build adjacency for stations
    df = pd.read_csv(os.path.join(DATA_DIR, station_connect_csv))
    # clean names
    df['station_1'] = df['station_1'].str.replace(r'_.*$', '', regex=True)
    df['station_2'] = df['station_2'].str.replace(r'_.*$', '', regex=True)
    stations = pd.unique(df[['station_1','station_2']].values.ravel())
    idx = {s: i for i, s in enumerate(stations)}
    N = len(stations)
    A = np.zeros((N, N), dtype=np.float32)
    for _, row in df.iterrows():
        u, v = idx[row['station_1']], idx[row['station_2']]
        A[u, v] = A[v, u] = 1.0
    # normalize
    D = np.diag(1.0 / (A.sum(axis=1) + 1e-6))
    A_norm = D.dot(A)
    return torch.tensor(A_norm, device=DEVICE), stations, idx

# ------------------------------
# Dataset definitions
# ------------------------------
class MetroDataset(Dataset):
    def __init__(self, in_gdf, out_gdf, od_df, grid_feats, station_feats,
                 station_idx, hist_window):
        # merge and scale features
        self.hist = hist_window
        # prepare grid-station flows
        self.grid_in = in_gdf.copy()
        self.grid_out = out_gdf.copy()
        # OD flows
        self.od = od_df.copy()
        # merge geo features
        self.grid_feats = grid_feats.copy()
        self.station_feats = station_feats.copy()
        self.station_idx = station_idx
        # scaler
        self.scaler = StandardScaler()
        # fit scaler on grid + station features
        all_feats = pd.concat([self.grid_feats.drop('station', axis=1),
                                self.station_feats.drop('station', axis=1)])
        self.scaler.fit(all_feats)

    def __len__(self):
        return len(self.od)

    def __getitem__(self, i):
        row = self.od.iloc[i]
        h = int(row['hour'])
        # time window
        hist_hours = list(range(h-self.hist, h))
        # grid->station series
        # Here simplified: select last hist flows for OD origin and dest stations
        o_i = int(self.station_idx[row['o_rawname']])
        d_i = int(self.station_idx[row['d_rawname']])
        # load historical OD flows
        od_seq_o = self.od[(self.od['o_id']==row['o_id']) & (self.od['hour'].isin(hist_hours))]['trip'].values
        od_seq_d = self.od[(self.od['d_id']==row['d_id']) & (self.od['hour'].isin(hist_hours))]['trip'].values
        if len(od_seq_o) < self.hist:
            od_seq_o = np.pad(od_seq_o, (self.hist-len(od_seq_o),0))
        if len(od_seq_d) < self.hist:
            od_seq_d = np.pad(od_seq_d, (self.hist-len(od_seq_d),0))
        
        # features
        grid_in_feats = self.grid_feats[self.grid_feats['station']==row['o_rawname']].drop('station',axis=1).values
        grid_out_feats = self.grid_feats[self.grid_feats['station']==row['d_rawname']].drop('station',axis=1).values
        station_o_feats = self.station_feats[self.station_feats['station']==row['o_rawname']].drop('station',axis=1).values
        station_d_feats = self.station_feats[self.station_feats['station']==row['d_rawname']].drop('station',axis=1).values
        od_feats = row[['surface_distance','translate','time','wait_time']].values.astype(np.float32)
        
        # scale
        feats = self.scaler.transform(
            np.vstack([grid_in_feats, grid_out_feats, station_o_feats, station_d_feats]))
        # combine time series and static features
        X = np.concatenate([od_seq_o, od_seq_d, feats.flatten(), od_feats])
        y = row['trip'].astype(np.float32)
        return torch.tensor(X, device=DEVICE), torch.tensor(y, device=DEVICE)

# ------------------------------
# STGCN Model
# ------------------------------
class STGCNBlock(nn.Module):
    def __init__(self, in_dim, out_dim, A):
        super().__init__()
        self.A = A
        self.conv_spatial = nn.Linear(in_dim, out_dim)
        self.conv_time = nn.Conv1d(out_dim, out_dim, kernel_size=3, padding=1)
        self.relu = nn.ReLU()
    def forward(self, X):
        # X: (batch, nodes, features)
        batch, N, F = X.size()
        # spatial
        Xs = torch.matmul(self.A, X)         # (batch, nodes, features)
        Xs = self.conv_spatial(Xs)           # (batch, nodes, out_dim)
        Xs = self.relu(Xs)
        # temporal (treat nodes as channels for simplicity)
        Xt = Xs.permute(0,2,1)               # (batch, out_dim, nodes)
        Xt = self.conv_time(Xt)              # (batch, out_dim, nodes)
        Xt = Xt.permute(0,2,1)               # (batch, nodes, out_dim)
        return self.relu(Xt)

class MetroSTGCN(nn.Module):
    def __init__(self, grid_feat_dim, station_feat_dim, A_grid_station, A_station):
        super().__init__()
        # shared blocks
        self.grid_blocks = nn.ModuleList([
            STGCNBlock(grid_feat_dim if i==0 else HIDDEN_DIM,
                       HIDDEN_DIM, A_grid_station)
            for i in range(NUM_LAYERS)
        ])
        self.station_blocks = nn.ModuleList([
            STGCNBlock(station_feat_dim if i==0 else HIDDEN_DIM,
                       HIDDEN_DIM, A_station)
            for i in range(NUM_LAYERS)
        ])
        # decoders
        self.decoder_in = nn.Sequential(
            nn.Linear(HIDDEN_DIM*2, HIDDEN_DIM),
            nn.ReLU(),
            nn.Linear(HIDDEN_DIM, 1)
        )
        self.decoder_out = nn.Sequential(
            nn.Linear(HIDDEN_DIM*2, HIDDEN_DIM),
            nn.ReLU(),
            nn.Linear(HIDDEN_DIM, 1)
        )
        self.decoder_od = nn.Sequential(
            nn.Linear(HIDDEN_DIM*2 + 4, HIDDEN_DIM),
            nn.ReLU(),
            nn.Linear(HIDDEN_DIM, 1)
        )

    def forward(self, X_grid, X_station, od_feats, edge_index_grid_station, od_index):
        # X_grid: (batch, G, F_g)
        # X_station: (batch, S, F_s)
        h_g, h_s = X_grid, X_station
        for g_block, s_block in zip(self.grid_blocks, self.station_blocks):
            h_g = g_block(h_g)
            h_s = s_block(h_s)
        # decode grid->station for in/out
        # edge_index_grid_station: list of (g,s) pairs
        preds_in, preds_out = [], []
        for (g,s) in edge_index_grid_station:
            hcat = torch.cat([h_g[:,g,:], h_s[:,s,:]], dim=-1)
            preds_in.append(self.decoder_in(hcat))
            preds_out.append(self.decoder_out(hcat))
        preds_in = torch.stack(preds_in, dim=1).squeeze(-1)
        preds_out = torch.stack(preds_out, dim=1).squeeze(-1)
        # decode OD
        # od_index: list of (o,s) for each sample
        preds_od = []
        for idx, (o,s) in enumerate(od_index):
            hcat = torch.cat([h_s[idx, o, :], h_s[idx, s, :], od_feats[idx].to(DEVICE)], dim=-1)
            preds_od.append(self.decoder_od(hcat))
        preds_od = torch.stack(preds_od).squeeze(-1)
        return preds_in, preds_out, preds_od

# ------------------------------
# Training and Inference
# ------------------------------
def train():
    # Load data
    in_gdf = load_shapefile('in_500_with_coords.shp')
    out_gdf = load_shapefile('out_500_withcoords.shp')
    od_df = pd.read_csv(os.path.join(DATA_DIR, 'updated北京市_subway_od_2024_modified3.csv'))
    grid_feats = pd.read_csv(os.path.join(DATA_DIR, 'leti_data.csv'))
    station_feats = ...  # aggregate from grid_feats
    A_station, stations, station_idx = build_station_graph('station_connect.csv')
    # build bipartite edges grid->station
    edge_index_gs = list(zip(in_gdf['grid_id'], in_gdf['station_id']))

    # Prepare dataset and loader
    dataset = MetroDataset(in_gdf, out_gdf, od_df, grid_feats, station_feats,
                           station_idx, HIST_WINDOW)
    loader = DataLoader(dataset, batch_size=BATCH_SIZE, shuffle=True)

    # Model
    model = MetroSTGCN(grid_feat_dim=grid_feats.shape[1]-1,
                       station_feat_dim=station_feats.shape[1]-1,
                       A_grid_station=torch.zeros((N_GRID,N_STATION)).to(DEVICE),
                       A_station=A_station)
    model.to(DEVICE)
    optimizer = optim.Adam(model.parameters(), lr=LR)
    criterion = nn.MSELoss()

    # Training loop
    for epoch in range(EPOCHS):
        model.train()
        total_loss = 0
        for X, y in loader:
            optimizer.zero_grad()
            # split features
            # ... omitted detailed split
            preds_in, preds_out, preds_od = model(...)
            loss = criterion(preds_od, y)
            loss.backward()
            optimizer.step()
            total_loss += loss.item()
        print(f"Epoch {epoch+1}/{EPOCHS}, Loss: {total_loss/len(loader):.4f}")

    # After training: inference on full graph to generate files
    # ... implement prediction loop, save shapefiles and CSV

if __name__ == '__main__':
    train()

