"""
改进的空间感知地铁流量预测系统
修复训练问题，实现真正的空间差异化预测
"""
import os
import sys
import time
import numpy as np
import pandas as pd
import geopandas as gpd
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.ensemble import RandomForestRegressor
import warnings
warnings.filterwarnings('ignore')

class ImprovedSpatialModel(nn.Module):
    """改进的空间感知模型"""
    
    def __init__(self, input_dim, hidden_dim=64):
        super(ImprovedSpatialModel, self).__init__()
        
        # 特征编码器
        self.feature_encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.BatchNorm1d(hidden_dim),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU()
        )
        
        # 空间距离编码器
        self.spatial_encoder = nn.Sequential(
            nn.Linear(3, 16),  # 距离、方向、可达性
            nn.ReLU(),
            nn.Linear(16, 8)
        )
        
        # 时间编码器
        self.time_encoder = nn.Sequential(
            nn.Linear(6, 16),  # 时间特征
            nn.ReLU(),
            nn.Linear(16, 8)
        )
        
        # 融合层
        fusion_dim = hidden_dim // 2 + 8 + 8
        self.fusion_layer = nn.Sequential(
            nn.Linear(fusion_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim, 32),
            nn.ReLU(),
            nn.Linear(32, 1)
        )
        
    def forward(self, features, spatial_features, time_features):
        # 编码各类特征
        feat_encoded = self.feature_encoder(features)
        spatial_encoded = self.spatial_encoder(spatial_features)
        time_encoded = self.time_encoder(time_features)
        
        # 特征融合
        combined = torch.cat([feat_encoded, spatial_encoded, time_encoded], dim=-1)
        
        # 最终预测
        output = self.fusion_layer(combined)
        
        return torch.relu(output)  # 确保非负

class ImprovedSpatialPredictionSystem:
    """改进的空间预测系统"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
        # 数据存储
        self.in_data = None
        self.out_data = None
        self.grid_data = None
        self.station_coords = {}
        
        # 模型
        self.in_model = None
        self.out_model = None
        
        # 标准化器
        self.feature_scaler = StandardScaler()
        self.spatial_scaler = StandardScaler()
        self.time_scaler = StandardScaler()
        
        # 站点信息
        self.stations = []
        self.station_to_idx = {}
        
    def load_and_process_data(self):
        """加载和处理数据"""
        print("="*60)
        print("加载数据并计算空间关系")
        print("="*60)
        
        try:
            # 加载基础数据
            self.in_data = gpd.read_file('in_500_with_coords.shp')
            self.out_data = gpd.read_file('out_500_with_coords.shp')
            self.grid_data = pd.read_csv('leti_data.csv')
            
            # 清理站点名称
            self.in_data['station_clean'] = self.in_data['station'].apply(lambda x: str(x).split('_')[0])
            self.out_data['station_clean'] = self.out_data['station'].apply(lambda x: str(x).split('_')[0])
            
            # 提取坐标
            self.in_data['longitude'] = self.in_data.geometry.centroid.x
            self.in_data['latitude'] = self.in_data.geometry.centroid.y
            self.out_data['longitude'] = self.out_data.geometry.centroid.x
            self.out_data['latitude'] = self.out_data.geometry.centroid.y
            
            print(f"进站数据: {self.in_data.shape}")
            print(f"出站数据: {self.out_data.shape}")
            print(f"栅格数据: {self.grid_data.shape}")
            
            # 获取站点列表和坐标
            self._extract_station_coordinates()
            
            # 处理栅格特征
            feature_cols = [col for col in self.grid_data.columns if col not in ['站名', 'id', 'geometry']]
            self.grid_features = self.grid_data[feature_cols].values
            self.grid_features = np.nan_to_num(self.grid_features, nan=0.0)
            
            print(f"栅格特征维度: {self.grid_features.shape}")
            
            return True
            
        except Exception as e:
            print(f"数据加载失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _extract_station_coordinates(self):
        """提取站点坐标"""
        all_stations = set()
        all_stations.update(self.in_data['station_clean'].unique())
        all_stations.update(self.out_data['station_clean'].unique())
        
        self.stations = sorted(list(all_stations))
        self.station_to_idx = {station: idx for idx, station in enumerate(self.stations)}
        
        # 提取每个站点的代表坐标
        for station in self.stations:
            in_coords = self.in_data[self.in_data['station_clean'] == station][['longitude', 'latitude']]
            out_coords = self.out_data[self.out_data['station_clean'] == station][['longitude', 'latitude']]
            
            all_coords = pd.concat([in_coords, out_coords])
            if len(all_coords) > 0:
                self.station_coords[station] = {
                    'longitude': all_coords['longitude'].mean(),
                    'latitude': all_coords['latitude'].mean()
                }
        
        print(f"提取了 {len(self.stations)} 个站点的坐标")
    
    def _calculate_spatial_features(self, grid_lon, grid_lat, station_lon, station_lat):
        """计算空间特征"""
        # 计算距离
        distance = np.sqrt((grid_lon - station_lon)**2 + (grid_lat - station_lat)**2)
        
        # 计算方向
        direction = np.arctan2(station_lat - grid_lat, station_lon - grid_lon)
        
        # 可达性评分
        accessibility = 1.0 / (1.0 + distance * 100)  # 放大距离影响
        
        return np.array([distance, direction, accessibility])
    
    def _calculate_time_features(self, hour):
        """计算时间特征"""
        return np.array([
            hour / 23.0,
            np.sin(2 * np.pi * hour / 24),
            np.cos(2 * np.pi * hour / 24),
            1.0 if 6 <= hour <= 10 else 0.0,  # 早高峰
            1.0 if 17 <= hour <= 20 else 0.0,  # 晚高峰
            1.0 if hour in [0,1,2,3,4,5,21,22,23] else 0.0  # 深夜
        ])
    
    def prepare_training_data(self, data, flow_type):
        """准备训练数据"""
        features = []
        spatial_features = []
        time_features = []
        targets = []
        
        # 计算平均栅格特征
        avg_grid_features = np.mean(self.grid_features, axis=0)
        
        for idx, row in data.iterrows():
            station = row['station_clean']
            hour = row['hour']
            count = row['count']
            grid_lon = row['longitude']
            grid_lat = row['latitude']
            
            if station in self.station_coords:
                station_coord = self.station_coords[station]
                station_lon = station_coord['longitude']
                station_lat = station_coord['latitude']
                
                # 基础特征：栅格特征 + 站点特征
                station_idx = self.station_to_idx[station]
                base_features = np.concatenate([
                    avg_grid_features,
                    [station_lon, station_lat, station_idx / len(self.stations)]
                ])
                
                # 空间特征
                spatial_feature = self._calculate_spatial_features(
                    grid_lon, grid_lat, station_lon, station_lat
                )
                
                # 时间特征
                time_feature = self._calculate_time_features(hour)
                
                features.append(base_features)
                spatial_features.append(spatial_feature)
                time_features.append(time_feature)
                targets.append(count)
        
        return (np.array(features), np.array(spatial_features), 
                np.array(time_features), np.array(targets))
    
    def train_improved_models(self):
        """训练改进的模型"""
        print("="*60)
        print("训练改进的空间感知模型")
        print("="*60)
        
        # 准备进站数据
        print("准备进站训练数据...")
        in_features, in_spatial, in_time, in_targets = self.prepare_training_data(self.in_data, 'in')
        
        # 准备出站数据
        print("准备出站训练数据...")
        out_features, out_spatial, out_time, out_targets = self.prepare_training_data(self.out_data, 'out')
        
        # 标准化特征
        in_features = self.feature_scaler.fit_transform(in_features)
        in_spatial = self.spatial_scaler.fit_transform(in_spatial)
        in_time = self.time_scaler.fit_transform(in_time)
        
        # 训练进站模型
        print("训练进站模型...")
        self.in_model = self._train_single_model(in_features, in_spatial, in_time, in_targets, 'in')
        
        # 使用相同的标准化器处理出站数据
        out_features = self.feature_scaler.transform(out_features)
        out_spatial = self.spatial_scaler.transform(out_spatial)
        out_time = self.time_scaler.transform(out_time)
        
        # 训练出站模型
        print("训练出站模型...")
        self.out_model = self._train_single_model(out_features, out_spatial, out_time, out_targets, 'out')
        
        print("模型训练完成！")
    
    def _train_single_model(self, features, spatial_features, time_features, targets, model_type):
        """训练单个模型"""
        # 初始化模型
        input_dim = features.shape[1]
        model = ImprovedSpatialModel(input_dim=input_dim, hidden_dim=64).to(self.device)
        
        # 转换为张量
        features_tensor = torch.tensor(features, dtype=torch.float32).to(self.device)
        spatial_tensor = torch.tensor(spatial_features, dtype=torch.float32).to(self.device)
        time_tensor = torch.tensor(time_features, dtype=torch.float32).to(self.device)
        targets_tensor = torch.tensor(targets, dtype=torch.float32).to(self.device)
        
        # 训练设置
        optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=1e-4)
        criterion = nn.MSELoss()
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
        
        # 训练循环
        model.train()
        best_loss = float('inf')
        patience = 0
        
        for epoch in range(100):
            optimizer.zero_grad()
            
            # 前向传播
            predictions = model(features_tensor, spatial_tensor, time_tensor).squeeze()
            
            # 计算损失
            loss = criterion(predictions, targets_tensor)
            
            # 反向传播
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
            optimizer.step()
            
            # 学习率调度
            scheduler.step(loss.item())
            
            # 早停检查
            if loss.item() < best_loss:
                best_loss = loss.item()
                patience = 0
            else:
                patience += 1
            
            if epoch % 20 == 0:
                print(f"  {model_type} Epoch {epoch}: Loss = {loss.item():.6f}")
            
            if patience >= 20:
                print(f"  {model_type} 早停于第 {epoch} 轮")
                break
        
        return model
    
    def generate_improved_predictions(self):
        """生成改进的预测结果"""
        print("="*60)
        print("生成改进的空间感知预测结果")
        print("="*60)
        
        # 时间分割策略
        test_hours = list(range(18, 24))
        
        # 生成进站预测
        print("生成进站预测...")
        in_test_data = self.in_data[self.in_data['hour'].isin(test_hours)].copy()
        
        if len(in_test_data) > 0:
            in_test_features, in_test_spatial, in_test_time, _ = self.prepare_training_data(in_test_data, 'in')
            
            # 标准化
            in_test_features = self.feature_scaler.transform(in_test_features)
            in_test_spatial = self.spatial_scaler.transform(in_test_spatial)
            in_test_time = self.time_scaler.transform(in_test_time)
            
            # 预测
            in_predictions = self._predict_with_model(
                self.in_model, in_test_features, in_test_spatial, in_test_time
            )
            
            in_test_data.loc[:, 'prediction'] = np.maximum(in_predictions, 0)
            
            # 保存结果
            try:
                import glob
                for f in glob.glob('improved_in_500_predictions_with_coords.*'):
                    try:
                        os.remove(f)
                    except:
                        pass
                
                in_test_data.to_file('improved_in_500_predictions_with_coords.shp')
                print(f"✓ 改进进站预测结果已保存: {len(in_test_data)} 条记录")
            except Exception as e:
                print(f"保存shapefile失败: {e}")
                in_test_data_csv = in_test_data.drop(columns=['geometry'])
                in_test_data_csv.to_csv('improved_in_500_predictions_with_coords.csv', index=False)
                print(f"✓ 改进进站预测结果已保存为CSV: {len(in_test_data)} 条记录")
        
        # 生成出站预测
        print("生成出站预测...")
        out_test_data = self.out_data[self.out_data['hour'].isin(test_hours)].copy()
        
        if len(out_test_data) > 0:
            out_test_features, out_test_spatial, out_test_time, _ = self.prepare_training_data(out_test_data, 'out')
            
            # 标准化
            out_test_features = self.feature_scaler.transform(out_test_features)
            out_test_spatial = self.spatial_scaler.transform(out_test_spatial)
            out_test_time = self.time_scaler.transform(out_test_time)
            
            # 预测
            out_predictions = self._predict_with_model(
                self.out_model, out_test_features, out_test_spatial, out_test_time
            )
            
            out_test_data.loc[:, 'prediction'] = np.maximum(out_predictions, 0)
            
            # 保存结果
            try:
                import glob
                for f in glob.glob('improved_out_500_predictions_with_coords.*'):
                    try:
                        os.remove(f)
                    except:
                        pass
                
                out_test_data.to_file('improved_out_500_predictions_with_coords.shp')
                print(f"✓ 改进出站预测结果已保存: {len(out_test_data)} 条记录")
            except Exception as e:
                print(f"保存shapefile失败: {e}")
                out_test_data_csv = out_test_data.drop(columns=['geometry'])
                out_test_data_csv.to_csv('improved_out_500_predictions_with_coords.csv', index=False)
                print(f"✓ 改进出站预测结果已保存为CSV: {len(out_test_data)} 条记录")
        
        # 生成对比分析
        self._generate_improved_comparison(in_test_data, out_test_data)
        
        return in_test_data, out_test_data
    
    def _predict_with_model(self, model, features, spatial_features, time_features):
        """使用模型进行预测"""
        model.eval()
        
        with torch.no_grad():
            features_tensor = torch.tensor(features, dtype=torch.float32).to(self.device)
            spatial_tensor = torch.tensor(spatial_features, dtype=torch.float32).to(self.device)
            time_tensor = torch.tensor(time_features, dtype=torch.float32).to(self.device)
            
            predictions = model(features_tensor, spatial_tensor, time_tensor).squeeze()
            
        return predictions.cpu().numpy()
    
    def _generate_improved_comparison(self, in_test_data, out_test_data):
        """生成改进的对比分析"""
        comparison_data = []
        
        # 进站对比
        if len(in_test_data) > 0:
            for _, row in in_test_data.iterrows():
                comparison_data.append({
                    'Type': 'In',
                    'True_Value': float(row['count']),
                    'Predicted_Value': float(row['prediction']),
                    'Station': str(row['station_clean']),
                    'Hour': int(row['hour']),
                    'Error': float(abs(row['count'] - row['prediction'])),
                    'Longitude': float(row['longitude']),
                    'Latitude': float(row['latitude'])
                })
        
        # 出站对比
        if len(out_test_data) > 0:
            for _, row in out_test_data.iterrows():
                comparison_data.append({
                    'Type': 'Out',
                    'True_Value': float(row['count']),
                    'Predicted_Value': float(row['prediction']),
                    'Station': str(row['station_clean']),
                    'Hour': int(row['hour']),
                    'Error': float(abs(row['count'] - row['prediction'])),
                    'Longitude': float(row['longitude']),
                    'Latitude': float(row['latitude'])
                })
        
        # 保存对比结果
        if comparison_data:
            comparison_df = pd.DataFrame(comparison_data)
            comparison_df.to_csv('improved_prediction_comparison.csv', index=False)
            print(f"✓ 改进预测对比汇总已保存: {len(comparison_data)} 条记录")
        
        return comparison_data

def main():
    """主函数"""
    print("="*80)
    print("改进的空间感知地铁流量预测系统")
    print("修复训练问题，实现真正的空间差异化预测")
    print("="*80)
    
    start_time = time.time()
    
    try:
        # 初始化系统
        system = ImprovedSpatialPredictionSystem()
        
        # 加载数据
        if not system.load_and_process_data():
            return False
        
        # 训练模型
        system.train_improved_models()
        
        # 生成预测
        in_results, out_results = system.generate_improved_predictions()
        
        print(f"\n运行时间: {time.time() - start_time:.2f} 秒")
        print("="*60)
        print("改进的空间感知预测系统运行完成！")
        print("="*60)
        
        return True
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
