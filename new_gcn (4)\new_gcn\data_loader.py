import pandas as pd
import geopandas as gpd
import numpy as np
from shapely.geometry import Point
from sklearn.preprocessing import StandardScaler, LabelEncoder
import torch
from config import Config

class DataLoader:
    def __init__(self):
        self.config = Config()
        self.station_encoder = LabelEncoder()
        self.scaler = StandardScaler()
        self.station_coords = {}
        self.station_features = {}
        self.grid_coords = {}
        
    def load_shapefile_data(self, shapefile_path, data_type='in', sample_ratio=None):
        """加载shapefile数据并提取地理信息"""
        print(f"正在加载{data_type}站数据...")
        gdf = gpd.read_file(shapefile_path)
        
        # 采样数据以减少内存使用
        if sample_ratio is None:
            sample_ratio = self.config.SAMPLE_RATIO
        
        if sample_ratio < 1.0:
            n_samples = int(len(gdf) * sample_ratio)
            gdf = gdf.sample(n=n_samples, random_state=42).reset_index(drop=True)
            print(f"采样后数据大小: {len(gdf)} 行")
        
        # 提取栅格中心坐标作为栅格经纬度
        if 'geometry' in gdf.columns:
            gdf['grid_lon'] = gdf.geometry.centroid.x
            gdf['grid_lat'] = gdf.geometry.centroid.y
            
            # 提取所有栅格的地理特征（经纬度）
            grid_features = []
            for idx, row in gdf.iterrows():
                geom = row.geometry
                if geom is not None:
                    # 提取几何特征
                    centroid = geom.centroid
                    area = geom.area
                    bounds = geom.bounds  # (minx, miny, maxx, maxy)
                    
                    features = [
                        centroid.x,  # 经度
                        centroid.y,  # 纬度
                        area,        # 面积
                        bounds[2] - bounds[0],  # 宽度
                        bounds[3] - bounds[1],  # 高度
                    ]
                    grid_features.append(features)
                else:
                    grid_features.append([0.0] * 5)
            
            # 添加栅格地理特征到数据框
            grid_features = np.array(grid_features)
            for i, feature_name in enumerate(['grid_centroid_x', 'grid_centroid_y', 'grid_area', 'grid_width', 'grid_height']):
                gdf[feature_name] = grid_features[:, i]
        
        # 确保数据类型正确
        if self.config.USE_FLOAT16:
            numeric_cols = gdf.select_dtypes(include=[np.number]).columns
            for col in numeric_cols:
                if col not in ['hour']:  # 保持hour为整数
                    gdf[col] = gdf[col].astype(np.float16)
        
        print(f"{data_type}站数据形状: {gdf.shape}")
        return gdf
    
    def load_od_data(self, sample_ratio=None):
        """加载OD数据"""
        print("正在加载OD数据...")
        od_data = pd.read_csv(self.config.OD_PATH)
        
        # 采样数据
        if sample_ratio is None:
            sample_ratio = self.config.SAMPLE_RATIO
            
        if sample_ratio < 1.0:
            n_samples = int(len(od_data) * sample_ratio)
            od_data = od_data.sample(n=n_samples, random_state=42).reset_index(drop=True)
            print(f"OD数据采样后大小: {len(od_data)} 行")
        
        # 确保OD特征列存在
        missing_features = []
        for feature in self.config.OD_FEATURES:
            if feature not in od_data.columns:
                missing_features.append(feature)
        
        if missing_features:
            print(f"警告: OD数据缺少特征列: {missing_features}")
            # 为缺少的特征创建默认值
            for feature in missing_features:
                od_data[feature] = 0.0
        
        if self.config.USE_FLOAT16:
            numeric_cols = od_data.select_dtypes(include=[np.number]).columns
            for col in numeric_cols:
                if col not in ['hour']:
                    od_data[col] = od_data[col].astype(np.float16)
        
        print(f"OD数据形状: {od_data.shape}")
        return od_data
    
    def extract_od_features(self, od_data):
        """提取OD数据的特征矩阵"""
        print("正在提取OD特征...")
        od_pairs = []
        od_features_list = []
        
        # 按OD对分组并提取特征
        for (o_station, d_station), group in od_data.groupby(['o_rawname', 'd_rawname']):
            od_pairs.append((o_station, d_station))
            
            # 计算该OD对的平均特征
            features = []
            for feature_name in self.config.OD_FEATURES:
                if feature_name in group.columns:
                    avg_value = group[feature_name].mean()
                    features.append(avg_value)
                else:
                    features.append(0.0)
            
            od_features_list.append(features)
        
        od_features_matrix = np.array(od_features_list)
        
        if self.config.USE_FLOAT16:
            od_features_matrix = od_features_matrix.astype(np.float16)
        
        print(f"提取了 {len(od_pairs)} 个OD对的特征，特征维度: {od_features_matrix.shape[1]}")
        return od_pairs, od_features_matrix
    
    def load_station_features(self):
        """加载站点特征数据"""
        print("正在加载站点特征数据...")
        features_df = pd.read_csv(self.config.STATION_FEATURES_PATH)
        
        # 除站名外的所有列都是特征列
        feature_cols = [col for col in features_df.columns if col != '站名']
        
        if self.config.USE_FLOAT16:
            for col in feature_cols:
                features_df[col] = features_df[col].astype(np.float16)
        
        print(f"站点特征数据形状: {features_df.shape}")
        return features_df
    
    def extract_station_coordinates(self, in_data, out_data):
        """提取地铁站点坐标"""
        print("正在提取站点坐标...")
        coords = {}
        
        # 从进站数据提取坐标
        if 'longitude' in in_data.columns and 'latitude' in in_data.columns:
            for _, row in in_data.drop_duplicates('station').iterrows():
                try:
                    lon = float(row['longitude'])
                    lat = float(row['latitude'])
                    
                    if not (np.isnan(lon) or np.isnan(lat)):
                        coords[row['station']] = (lon, lat)
                except (ValueError, TypeError):
                    print(f"警告: 站点 {row['station']} 的坐标无效，跳过")
                    continue
        
        # 从出站数据补充坐标
        if 'longitude' in out_data.columns and 'latitude' in out_data.columns:
            for _, row in out_data.drop_duplicates('station').iterrows():
                if row['station'] not in coords:
                    try:
                        lon = float(row['longitude'])
                        lat = float(row['latitude'])
                        
                        if not (np.isnan(lon) or np.isnan(lat)):
                            coords[row['station']] = (lon, lat)
                    except (ValueError, TypeError):
                        print(f"警告: 站点 {row['station']} 的坐标无效，跳过")
                        continue
        
        print(f"提取到 {len(coords)} 个有效站点坐标")
        return coords
    
    def create_time_features(self, hour):
        """创建时间特征"""
        # 创建周期性时间特征
        hour_sin = np.sin(2 * np.pi * hour / 24)
        hour_cos = np.cos(2 * np.pi * hour / 24)
        
        # 创建时段特征
        is_morning_peak = ((hour >= 7) & (hour <= 9)).astype(int)
        is_evening_peak = ((hour >= 17) & (hour <= 19)).astype(int)
        is_night = ((hour >= 22) | (hour <= 6)).astype(int)
        is_weekend = np.zeros_like(hour, dtype=int)
        
        return np.column_stack([hour_sin, hour_cos, is_morning_peak, 
                               is_evening_peak, is_night, is_weekend])
    
    def create_time_sequences(self, flow_matrix, time_features, sequence_length=None):
        """创建时间序列数据（修复维度问题）"""
        if sequence_length is None:
            sequence_length = self.config.SEQUENCE_LENGTH
        
        print(f"创建时间序列，序列长度: {sequence_length}")
        print(f"输入flow_matrix形状: {flow_matrix.shape}")
        
        n_timesteps, n_stations = flow_matrix.shape
        
        # 创建滑动窗口序列
        sequences = []
        time_seq = []
        targets = []
        
        for i in range(sequence_length, n_timesteps):
            # 输入序列 (sequence_length, n_stations)
            seq = flow_matrix[i-sequence_length:i, :]
            time_feat = time_features[i-sequence_length:i, :, :]  # (seq_len, n_stations, time_feat_dim)
            target = flow_matrix[i, :]  # (n_stations,)
            
            sequences.append(seq)
            time_seq.append(time_feat)
            targets.append(target)
        
        sequences = np.array(sequences)  # (n_samples, seq_len, n_stations)
        time_seq = np.array(time_seq)    # (n_samples, seq_len, n_stations, time_feat_dim)
        targets = np.array(targets)      # (n_samples, n_stations)
        
        print(f"生成的序列形状:")
        print(f"  sequences: {sequences.shape}")
        print(f"  time_seq: {time_seq.shape}")  
        print(f"  targets: {targets.shape}")
        
        return sequences, time_seq, targets
    
    def process_flow_data(self, flow_data, flow_type='in'):
        """处理流量数据（修复版本）"""
        print(f"正在处理{flow_type}站流量数据...")
        
        # 创建站点-时间矩阵
        stations = sorted(flow_data['station'].unique())
        hours = sorted(flow_data['hour'].unique())
        
        print(f"站点数量: {len(stations)}, 时间点数量: {len(hours)}")
        
        # 编码站点
        if not hasattr(self.station_encoder, 'classes_'):
            self.station_encoder.fit(stations)
        
        flow_matrix = np.zeros((len(hours), len(stations)))
        time_feature_matrix = np.zeros((len(hours), len(stations), self.config.TIME_FEATURES))
        
        for i, hour in enumerate(hours):
            current_hour_features = self.create_time_features(np.array([hour]))[0]
            hour_data = flow_data[flow_data['hour'] == hour]
            
            for _, row in hour_data.iterrows():
                try:
                    station_idx = stations.index(row['station'])
                    flow_matrix[i, station_idx] = row['count']
                    time_feature_matrix[i, station_idx] = current_hour_features
                except ValueError:
                    continue
        
        # 创建时间序列
        sequences, time_sequences, targets = self.create_time_sequences(
            flow_matrix, time_feature_matrix
        )
        
        return sequences, time_sequences, targets, stations
    
    def balance_flow_data(self, in_sequences, out_sequences):
        """平衡进出站流量数据"""
        print("正在平衡进出站流量...")
        
        total_in = np.sum(in_sequences)
        total_out = np.sum(out_sequences)
        
        if total_out > 0:
            balance_factor = (total_in + total_out) / (2 * total_out)
            balanced_out_sequences = out_sequences * balance_factor
        else:
            balanced_out_sequences = out_sequences
        
        print(f"平衡前: 进站总量={total_in:.2f}, 出站总量={total_out:.2f}")
        print(f"平衡后: 进站总量={total_in:.2f}, 出站总量={np.sum(balanced_out_sequences):.2f}")
        
        return in_sequences, balanced_out_sequences
    
    def create_grid_station_connections(self, grid_coords, station_coords, threshold=None):
        """创建栅格-站点连接，基于距离阈值"""
        if threshold is None:
            threshold = self.config.SPATIAL_THRESHOLD
        
        print(f"正在创建栅格-站点连接 (阈值: {threshold})...")
        connections = {}
        
        for grid_id, (grid_lon, grid_lat) in grid_coords.items():
            connections[grid_id] = []
            for station, (station_lon, station_lat) in station_coords.items():
                # 计算距离 (简化的欧几里得距离)
                distance = np.sqrt((grid_lon - station_lon)**2 + (grid_lat - station_lat)**2)
                if distance < threshold:
                    connections[grid_id].append((station, distance))
        
        # 移除没有连接的栅格
        connections = {k: v for k, v in connections.items() if v}
        
        print(f"创建了 {len(connections)} 个栅格-站点连接")
        return connections
    
    def load_all_data(self):
        """加载所有数据"""
        print("开始加载所有数据...")
        print(f"使用采样比例: {self.config.SAMPLE_RATIO}")
        
        # 清理GPU缓存
        self.config.clear_gpu_cache()
        
        # 加载基础数据
        in_data = self.load_shapefile_data(self.config.IN_STATION_PATH, 'in')
        out_data = self.load_shapefile_data(self.config.OUT_STATION_PATH, 'out')
        od_data = self.load_od_data()
        station_features = self.load_station_features()
        
        # 提取坐标
        station_coords = self.extract_station_coordinates(in_data, out_data)
        
        # 处理流量数据（返回时间序列格式）
        in_sequences, in_time_seq, in_targets, in_stations = self.process_flow_data(in_data, 'in')
        out_sequences, out_time_seq, out_targets, out_stations = self.process_flow_data(out_data, 'out')
        
        # 获取共同站点
        common_stations = list(set(in_stations) & set(out_stations) & set(station_coords.keys()))
        print(f"共同站点数量: {len(common_stations)}")
        
        # 平衡流量数据
        in_sequences_balanced, out_sequences_balanced = self.balance_flow_data(in_sequences, out_sequences)
        
        # 提取OD特征
        od_pairs, od_features = self.extract_od_features(od_data)
        
        # 准备返回数据
        data_dict = {
            'in_data': in_data,
            'out_data': out_data,
            'od_data': od_data,
            'station_features': station_features,
            'station_coords': station_coords,
            'in_sequences': in_sequences_balanced,  # 修改：返回序列数据
            'out_sequences': out_sequences_balanced,
            'in_time_sequences': in_time_seq,
            'out_time_sequences': out_time_seq,
            'in_targets': in_targets,
            'out_targets': out_targets,
            'common_stations': common_stations,
            'od_pairs': od_pairs,
            'od_features': od_features
        }
        
        print("数据加载完成!")
        print(f"内存使用情况: {self.config.get_memory_info()}")
        return data_dict 