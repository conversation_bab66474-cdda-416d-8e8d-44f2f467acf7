import pandas as pd
import networkx as nx
from collections import defaultdict
from tqdm import tqdm

# 读取数据
def load_data():
    print("正在读取数据...")
    try:
        # 读取OD数据
        od_data = pd.read_csv('updated北京市_subway_od_2024_modified3.csv')
        print(f"成功读取OD数据，共 {len(od_data)} 条记录")
        
        # 读取站点连接数据
        station_data = pd.read_csv('station_connect_2023.csv')
        print(f"成功读取站点连接数据，共 {len(station_data)} 条记录")
        
        return od_data, station_data
    except Exception as e:
        print(f"读取数据时出错: {str(e)}")
        raise

def process_station_distances(station_data, od_data):
    print("处理站点距离信息...")
    
    # 创建一个字典存储相邻站点之间的距离
    distance_dict = {}
    
    # 从station_data中获取相邻站点对
    adjacent_stations = set()
    for _, row in station_data.iterrows():
        station1 = row['station_1']
        station2 = row['station_2']
        station1_name = station1.split('_')[0]
        station2_name = station2.split('_')[0]
        adjacent_stations.add((station1_name, station2_name))
        adjacent_stations.add((station2_name, station1_name))
    
    print(f"找到 {len(adjacent_stations)//2} 对相邻站点")
    
    # 从od_data中只提取相邻站点的距离
    print("从OD数据中提取相邻站点距离...")
    valid_distances = defaultdict(list)
    for _, row in tqdm(od_data.iterrows(), total=len(od_data), desc="处理站点距离"):
        o_name = row['o_rawname']
        d_name = row['d_rawname']
        if (o_name, d_name) in adjacent_stations:
            valid_distances[(o_name, d_name)].append(row['surface_distance'])
    
    # 对每对相邻站点的距离取平均值
    print("计算站点间平均距离...")
    for station_pair, distances in valid_distances.items():
        avg_distance = sum(distances) / len(distances)
        distance_dict[station_pair] = avg_distance
        # 确保双向距离相同
        reverse_pair = (station_pair[1], station_pair[0])
        distance_dict[reverse_pair] = avg_distance
    
    # 检查是否有相邻站点缺失距离信息
    print("检查距离信息完整性...")
    missing_pairs = []
    for station_pair in adjacent_stations:
        if station_pair not in distance_dict:
            missing_pairs.append(station_pair)
            # 使用默认距离2000米
            distance_dict[station_pair] = 2000
            distance_dict[(station_pair[1], station_pair[0])] = 2000
    
    if missing_pairs:
        print(f"发现{len(missing_pairs)}对相邻站点缺失距离信息，已使用默认值2000米")
    
    return distance_dict

def create_graph(station_data, od_data):
    print("正在构建地铁网络图...")
    G = nx.Graph()
    
    # 获取处理后的站点距离信息
    distance_dict = process_station_distances(station_data, od_data)
    
    # 处理站点连接数据
    edge_count = 0
    transfer_count = 0
    
    print("处理站点连接关系...")
    for _, row in tqdm(station_data.iterrows(), total=len(station_data), desc="构建网络"):
        station1 = row['station_1']
        station2 = row['station_2']
        
        # 提取站名和线路号
        station1_name = station1.split('_')[0]
        station2_name = station2.split('_')[0]
        line1 = station1.split('_')[1]
        line2 = station2.split('_')[1]
        
        # 获取距离
        distance = distance_dict.get((station1_name, station2_name))
        
        # 计算时间（考虑不同线路的速度）
        if '地铁19号线' in [line1, line2]:
            time = (distance / 1050) + 1  # 19号线速度
        else:
            time = (distance / 700) + 1   # 其他线路速度
            
        # 添加边
        G.add_edge(station1, station2, weight=time)
        edge_count += 1
        
        # 添加换乘边（如果站名相同但线路不同）
        for other_station in G.nodes():
            other_name = other_station.split('_')[0]
            other_line = other_station.split('_')[1]
            if (station1_name == other_name and line1 != other_line):
                G.add_edge(station1, other_station, weight=4)  # 换乘时间4分钟
                transfer_count += 1
            if (station2_name == other_name and line2 != other_line):
                G.add_edge(station2, other_station, weight=4)
                transfer_count += 1

    print(f"\n网络构建完成:")
    print(f"节点数量: {G.number_of_nodes()}")
    print(f"边数量: {edge_count}")
    print(f"换乘连接数量: {transfer_count}")
    
    return G

def calculate_flow_8am(G, od_data):
    """只计算早上8点的流量"""
    print("\n计算早上8点的流量...")
    
    # 筛选8点的数据
    hour_data = od_data[od_data['hour'] == 8]
    print(f"8点时段共有 {len(hour_data)} 条OD记录")
    
    # 用于存储边的流量
    edge_flow = defaultdict(float)
    
    # 用于统计
    successful_paths = 0
    failed_paths = 0
    total_flow = 0
    
    # 对每个OD对计算最短路径
    for _, row in tqdm(hour_data.iterrows(), total=len(hour_data), desc="处理8点OD对"):
        if row['o_rawname'] == row['d_rawname']:
            continue
            
        # 找到起终点对应的所有站点（考虑不同线路）
        origin_stations = [n for n in G.nodes() if row['o_rawname'] in n]
        dest_stations = [n for n in G.nodes() if row['d_rawname'] in n]
        
        min_path = None
        min_length = float('inf')
        
        # 找到最短路径
        for o_station in origin_stations:
            for d_station in dest_stations:
                try:
                    path = nx.shortest_path(G, o_station, d_station, weight='weight')
                    length = nx.shortest_path_length(G, o_station, d_station, weight='weight')
                    if length < min_length:
                        min_length = length
                        min_path = path
                except nx.NetworkXNoPath:
                    continue
        
        if min_path:
            # 将流量分配到路径上的每条边
            for i in range(len(min_path)-1):
                edge = tuple(sorted([min_path[i], min_path[i+1]]))
                edge_flow[edge] += row['trip']
            successful_paths += 1
            total_flow += row['trip']
        else:
            failed_paths += 1
            print(f"警告: 未找到从 {row['o_rawname']} 到 {row['d_rawname']} 的路径")
    
    print("\n路径计算统计:")
    print(f"成功计算路径数: {successful_paths}")
    print(f"未找到路径数: {failed_paths}")
    print(f"总流量: {total_flow:.2f}")
    
    return edge_flow

def save_results(edge_flow, output_file):
    print("\n正在保存结果...")
    results = []
    
    # 保存8点的数据
    for edge, flow in edge_flow.items():
        station1, station2 = edge
        station1_name = station1.split('_')[0]
        station2_name = station2.split('_')[0]
        
        results.append({
            'hour': 8,
            'o_rawname': station1_name,
            'd_rawname': station2_name,
            'trip': flow
        })
    
    # 创建DataFrame并保存
    results_df = pd.DataFrame(results)
    
    # 输出一些基本统计信息
    print("\n流量统计信息:")
    print(f"总边数: {len(results_df)}")
    print(f"总流量: {results_df['trip'].sum():.2f}")
    print(f"平均边流量: {results_df['trip'].mean():.2f}")
    print(f"最大单边流量: {results_df['trip'].max():.2f}")
    
    # 显示流量最大的前10条边
    print("\n流量最大的10条边:")
    top_10_edges = results_df.nlargest(10, 'trip')
    print(top_10_edges)
    
    # 保存结果
    results_df.to_csv(output_file, index=False)
    print(f"\n结果已保存到: {output_file}")

def main():
    print("开始处理地铁流量数据...")
    
    try:
        # 读取数据
        od_data, station_data = load_data()
        
        # 创建图
        G = create_graph(station_data, od_data)
        
        # 只计算8点的流量
        edge_flow = calculate_flow_8am(G, od_data)
        
        # 保存结果
        save_results(edge_flow, 'subway_flow_8am_results.csv')
        
        print("程序执行完成！")
        
    except Exception as e:
        print(f"程序执行出错: {str(e)}")
        raise

if __name__ == "__main__":
    main()