#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
GCN系统快速测试脚本
测试系统的基本功能和配置
"""

import sys
import torch
import numpy as np
from config import Config

def test_environment():
    """测试环境配置"""
    print("="*50)
    print("🔧 环境测试")
    print("="*50)
    
    config = Config()
    
    # 测试设备
    print(f"设备: {config.DEVICE}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        print(f"GPU数量: {torch.cuda.device_count()}")
        print(f"GPU名称: {torch.cuda.get_device_name(0)}")
        print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    
    # 测试内存设置
    print(f"使用Float16: {config.USE_FLOAT16}")
    print(f"批次大小: {config.BATCH_SIZE}")
    print(f"采样比例: {config.SAMPLE_RATIO}")
    
    return config

def test_imports():
    """测试所有必要的导入"""
    print("\n📦 导入测试")
    print("-"*30)
    
    try:
        from data_loader import DataLoader
        print("✓ DataLoader")
    except Exception as e:
        print(f"✗ DataLoader: {e}")
        return False
    
    try:
        from graph_builder import GraphBuilder
        print("✓ GraphBuilder")
    except Exception as e:
        print(f"✗ GraphBuilder: {e}")
        return False
    
    try:
        from gcn_model import create_model, ModelUtils
        print("✓ GCN模型")
    except Exception as e:
        print(f"✗ GCN模型: {e}")
        return False
    
    try:
        from trainer import GCNTrainer
        print("✓ GCNTrainer")
    except Exception as e:
        print(f"✗ GCNTrainer: {e}")
        return False
    
    try:
        from predictor import GCNPredictor
        print("✓ GCNPredictor")
    except Exception as e:
        print(f"✗ GCNPredictor: {e}")
        return False
    
    return True

def test_model_creation():
    """测试模型创建"""
    print("\n🤖 模型创建测试")
    print("-"*30)
    
    try:
        config = Config()
        from gcn_model import create_model, ModelUtils
        
        model = create_model(config, 'complex')
        ModelUtils.print_model_info(model)
        print("✓ 模型创建成功")
        
        # 测试模型基本功能
        batch_size = 2
        seq_len = config.SEQUENCE_LENGTH
        num_nodes = 10
        
        # 创建测试数据
        x = torch.randn(batch_size, seq_len, num_nodes)
        edge_index = torch.randint(0, num_nodes, (2, 20))
        edge_attr = torch.randn(20)
        time_features = torch.randn(batch_size, seq_len, num_nodes, config.TIME_FEATURES)
        spatial_features = torch.randn(num_nodes, config.SPATIAL_FEATURES)
        
        if config.USE_FLOAT16:
            x = x.half()
            edge_attr = edge_attr.half()
            time_features = time_features.half()
            spatial_features = spatial_features.half()
        
        # 测试前向传播
        with torch.no_grad():
            output, attention = model(x, edge_index, edge_attr, time_features, spatial_features)
            print(f"✓ 前向传播成功，输出形状: {output.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ 模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_loader():
    """测试数据加载器（使用小量数据）"""
    print("\n📊 数据加载测试")
    print("-"*30)
    
    try:
        from data_loader import DataLoader
        config = Config()
        
        # 临时设置更小的采样比例进行测试
        original_ratio = config.SAMPLE_RATIO
        config.SAMPLE_RATIO = 0.01  # 仅使用1%的数据进行测试
        
        loader = DataLoader()
        loader.config = config
        
        # 测试基本功能
        print("测试时间特征创建...")
        hours = np.array([0, 6, 12, 18])
        time_features = loader.create_time_features(hours)
        print(f"✓ 时间特征形状: {time_features.shape}")
        
        # 恢复原设置
        config.SAMPLE_RATIO = original_ratio
        print("✓ 数据加载器基本功能正常")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据加载器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 GCN地铁流量预测系统 - 快速测试")
    
    # 测试环境
    config = test_environment()
    
    # 测试导入
    if not test_imports():
        print("\n❌ 导入测试失败")
        return False
    
    # 测试模型
    if not test_model_creation():
        print("\n❌ 模型测试失败")
        return False
    
    # 测试数据加载器
    if not test_data_loader():
        print("\n❌ 数据加载器测试失败")
        return False
    
    print("\n" + "="*50)
    print("✅ 所有测试通过！")
    print("🎯 系统准备就绪，可以运行完整流程")
    print("="*50)
    
    print("\n📖 运行建议:")
    print("1. 快速测试: python main.py --mode train --epochs 2 --sample_ratio 0.05")
    print("2. 完整训练: python main.py --mode train")
    print("3. 仅预测: python main.py --mode eval")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 