"""
高级空间感知地铁流量预测系统
解决栅格-地铁站流量分配差异化建模问题
"""
import os
import sys
import time
import numpy as np
import pandas as pd
import geopandas as gpd
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch_geometric.nn import GCNConv, GATConv, global_mean_pool
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from scipy.spatial.distance import cdist
import warnings
warnings.filterwarnings('ignore')

class SpatialAttentionLayer(nn.Module):
    """空间注意力层"""
    
    def __init__(self, input_dim, hidden_dim):
        super(SpatialAttentionLayer, self).__init__()
        self.query = nn.Linear(input_dim, hidden_dim)
        self.key = nn.Linear(input_dim, hidden_dim)
        self.value = nn.Linear(input_dim, hidden_dim)
        self.scale = np.sqrt(hidden_dim)
        
    def forward(self, x, spatial_weights):
        """
        x: [batch_size, seq_len, input_dim]
        spatial_weights: [batch_size, seq_len, seq_len] 空间权重矩阵
        """
        Q = self.query(x)
        K = self.key(x)
        V = self.value(x)
        
        # 计算注意力分数
        attention_scores = torch.matmul(Q, K.transpose(-2, -1)) / self.scale
        
        # 融入空间权重
        attention_scores = attention_scores + spatial_weights
        
        # 应用softmax
        attention_weights = F.softmax(attention_scores, dim=-1)
        
        # 计算输出
        output = torch.matmul(attention_weights, V)
        
        return output, attention_weights

class GridStationInteractionModel(nn.Module):
    """栅格-地铁站交互模型"""
    
    def __init__(self, grid_feature_dim, station_feature_dim, hidden_dim=128):
        super(GridStationInteractionModel, self).__init__()
        
        # 栅格特征编码器
        self.grid_encoder = nn.Sequential(
            nn.Linear(grid_feature_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, hidden_dim // 2)
        )
        
        # 地铁站特征编码器
        self.station_encoder = nn.Sequential(
            nn.Linear(station_feature_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, hidden_dim // 2)
        )
        
        # 空间距离编码器
        self.distance_encoder = nn.Sequential(
            nn.Linear(4, 32),  # 距离、方向、可达性等特征
            nn.ReLU(),
            nn.Linear(32, 16)
        )
        
        # 时间编码器
        self.time_encoder = nn.Sequential(
            nn.Linear(8, 32),  # 时间特征
            nn.ReLU(),
            nn.Linear(32, 16)
        )
        
        # 交互层
        interaction_dim = hidden_dim // 2 + hidden_dim // 2 + 16 + 16
        self.interaction_layer = nn.Sequential(
            nn.Linear(interaction_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, 1),
            nn.Sigmoid()  # 输出0-1之间的分配概率
        )
        
    def forward(self, grid_features, station_features, spatial_features, time_features):
        """
        grid_features: [batch_size, grid_feature_dim]
        station_features: [batch_size, station_feature_dim]
        spatial_features: [batch_size, 4] (距离、方向等)
        time_features: [batch_size, 8] (时间特征)
        """
        # 编码各类特征
        grid_encoded = self.grid_encoder(grid_features)
        station_encoded = self.station_encoder(station_features)
        spatial_encoded = self.distance_encoder(spatial_features)
        time_encoded = self.time_encoder(time_features)
        
        # 特征融合
        combined_features = torch.cat([
            grid_encoded, station_encoded, spatial_encoded, time_encoded
        ], dim=-1)
        
        # 交互建模
        allocation_prob = self.interaction_layer(combined_features)
        
        return allocation_prob

class AdvancedSpatialGNN(nn.Module):
    """高级空间图神经网络"""
    
    def __init__(self, node_feature_dim, edge_feature_dim, hidden_dim=128):
        super(AdvancedSpatialGNN, self).__init__()
        
        # 节点特征处理
        self.node_norm = nn.LayerNorm(node_feature_dim)
        self.node_projection = nn.Linear(node_feature_dim, hidden_dim)
        
        # 边特征处理
        self.edge_projection = nn.Linear(edge_feature_dim, hidden_dim)
        
        # 图注意力层
        self.gat_layers = nn.ModuleList([
            GATConv(hidden_dim, hidden_dim, heads=4, dropout=0.2, edge_dim=hidden_dim)
            for _ in range(3)
        ])
        
        # 空间注意力
        self.spatial_attention = SpatialAttentionLayer(hidden_dim, hidden_dim)
        
        # 输出层
        self.output_layer = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim // 2, 2),  # 进站、出站
            nn.ReLU()
        )
        
    def forward(self, x, edge_index, edge_attr, spatial_weights):
        """
        x: [num_nodes, node_feature_dim] 节点特征
        edge_index: [2, num_edges] 边索引
        edge_attr: [num_edges, edge_feature_dim] 边特征
        spatial_weights: [num_nodes, num_nodes] 空间权重矩阵
        """
        # 节点特征标准化和投影
        x = self.node_norm(x)
        x = self.node_projection(x)
        
        # 边特征投影
        edge_attr = self.edge_projection(edge_attr)
        
        # 图注意力层
        for gat_layer in self.gat_layers:
            x_residual = x
            x = gat_layer(x, edge_index, edge_attr)
            x = x + x_residual  # 残差连接
            x = F.relu(x)
        
        # 空间注意力
        x_expanded = x.unsqueeze(0)  # [1, num_nodes, hidden_dim]
        spatial_weights_expanded = spatial_weights.unsqueeze(0)  # [1, num_nodes, num_nodes]
        
        x_attended, attention_weights = self.spatial_attention(x_expanded, spatial_weights_expanded)
        x = x_attended.squeeze(0)  # [num_nodes, hidden_dim]
        
        # 输出预测
        output = self.output_layer(x)
        
        return output, attention_weights

class AdvancedSpatialPredictionSystem:
    """高级空间感知预测系统"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
        # 数据存储
        self.in_data = None
        self.out_data = None
        self.od_data = None
        self.grid_data = None
        self.station_coords = {}
        
        # 模型
        self.grid_station_model = None
        self.spatial_gnn = None
        
        # 标准化器
        self.grid_scaler = StandardScaler()
        self.station_scaler = StandardScaler()
        self.spatial_scaler = MinMaxScaler()
        
        # 站点信息
        self.stations = []
        self.station_to_idx = {}
        
    def load_and_process_data(self):
        """加载和处理数据"""
        print("="*60)
        print("加载数据并计算空间关系")
        print("="*60)
        
        try:
            # 加载基础数据
            self.in_data = gpd.read_file('in_500_with_coords.shp')
            self.out_data = gpd.read_file('out_500_with_coords.shp')
            self.od_data = pd.read_csv('updated北京市_subway_od_2024_modified3.csv')
            self.grid_data = pd.read_csv('leti_data.csv')
            
            # 清理站点名称
            self.in_data['station_clean'] = self.in_data['station'].apply(lambda x: str(x).split('_')[0])
            self.out_data['station_clean'] = self.out_data['station'].apply(lambda x: str(x).split('_')[0])
            
            # 提取坐标
            self.in_data['longitude'] = self.in_data.geometry.centroid.x
            self.in_data['latitude'] = self.in_data.geometry.centroid.y
            self.out_data['longitude'] = self.out_data.geometry.centroid.x
            self.out_data['latitude'] = self.out_data.geometry.centroid.y
            
            print(f"进站数据: {self.in_data.shape}")
            print(f"出站数据: {self.out_data.shape}")
            print(f"OD数据: {self.od_data.shape}")
            print(f"栅格数据: {self.grid_data.shape}")
            
            # 获取站点列表和坐标
            self._extract_station_coordinates()
            
            # 计算空间关系
            self._compute_spatial_relationships()
            
            return True
            
        except Exception as e:
            print(f"数据加载失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _extract_station_coordinates(self):
        """提取站点坐标"""
        all_stations = set()
        all_stations.update(self.in_data['station_clean'].unique())
        all_stations.update(self.out_data['station_clean'].unique())
        
        self.stations = sorted(list(all_stations))
        self.station_to_idx = {station: idx for idx, station in enumerate(self.stations)}
        
        # 提取每个站点的代表坐标
        for station in self.stations:
            in_coords = self.in_data[self.in_data['station_clean'] == station][['longitude', 'latitude']]
            out_coords = self.out_data[self.out_data['station_clean'] == station][['longitude', 'latitude']]
            
            all_coords = pd.concat([in_coords, out_coords])
            if len(all_coords) > 0:
                self.station_coords[station] = {
                    'longitude': all_coords['longitude'].mean(),
                    'latitude': all_coords['latitude'].mean()
                }
        
        print(f"提取了 {len(self.stations)} 个站点的坐标")
    
    def _compute_spatial_relationships(self):
        """计算空间关系"""
        print("计算栅格-地铁站空间关系...")
        
        # 获取栅格中心坐标（假设栅格数据包含坐标信息）
        # 这里简化处理，实际应根据栅格数据的具体格式调整
        grid_coords = []
        station_coords_list = []
        
        # 构建站点坐标矩阵
        for station in self.stations:
            if station in self.station_coords:
                coord = self.station_coords[station]
                station_coords_list.append([coord['longitude'], coord['latitude']])
            else:
                station_coords_list.append([0, 0])  # 默认坐标
        
        self.station_coords_matrix = np.array(station_coords_list)
        
        # 计算栅格特征
        feature_cols = [col for col in self.grid_data.columns if col not in ['站名', 'id', 'geometry']]
        self.grid_features = self.grid_data[feature_cols].values
        self.grid_features = np.nan_to_num(self.grid_features, nan=0.0)
        
        print(f"栅格特征维度: {self.grid_features.shape}")
        print(f"站点坐标矩阵: {self.station_coords_matrix.shape}")
    
    def _calculate_spatial_features(self, grid_lon, grid_lat, station_lon, station_lat, hour):
        """计算空间特征"""
        # 计算距离（使用欧几里得距离，实际可用更精确的地理距离）
        distance = np.sqrt((grid_lon - station_lon)**2 + (grid_lat - station_lat)**2)
        
        # 计算方向（角度）
        direction = np.arctan2(station_lat - grid_lat, station_lon - grid_lon)
        
        # 可达性评分（距离的倒数，加上时间因子）
        accessibility = 1.0 / (1.0 + distance)
        
        # 时间段影响因子
        time_factor = 1.0
        if 6 <= hour <= 10 or 17 <= hour <= 20:  # 高峰期
            time_factor = 1.2
        elif hour in [0, 1, 2, 3, 4, 5, 22, 23]:  # 深夜
            time_factor = 0.8
        
        return np.array([distance, direction, accessibility, time_factor])
    
    def _calculate_time_features(self, hour):
        """计算时间特征"""
        return np.array([
            hour / 23.0,  # 标准化小时
            np.sin(2 * np.pi * hour / 24),  # 小时周期性
            np.cos(2 * np.pi * hour / 24),
            1.0 if 6 <= hour <= 10 else 0.0,  # 早高峰
            1.0 if 17 <= hour <= 20 else 0.0,  # 晚高峰
            1.0 if 10 <= hour <= 16 else 0.0,  # 平峰
            1.0 if hour in [0,1,2,3,4,5,21,22,23] else 0.0,  # 深夜
            1.0 if hour >= 6 and hour <= 22 else 0.0  # 运营时间
        ])
    
    def prepare_advanced_features(self, data, flow_type):
        """准备高级特征"""
        features = []
        targets = []
        spatial_features = []
        time_features = []
        
        # 计算平均栅格特征
        avg_grid_features = np.mean(self.grid_features, axis=0)
        
        for idx, row in data.iterrows():
            station = row['station_clean']
            hour = row['hour']
            count = row['count']
            grid_lon = row['longitude']
            grid_lat = row['latitude']
            
            if station in self.station_coords:
                station_coord = self.station_coords[station]
                station_lon = station_coord['longitude']
                station_lat = station_coord['latitude']
                
                # 栅格特征
                grid_feature = avg_grid_features
                
                # 地铁站特征（简化为坐标+站点编码）
                station_idx = self.station_to_idx[station]
                station_feature = np.array([
                    station_lon, station_lat, 
                    station_idx / len(self.stations),  # 标准化站点ID
                    len([s for s in self.stations if abs(self.station_coords.get(s, {}).get('longitude', 0) - station_lon) < 0.01])  # 附近站点数
                ])
                
                # 空间特征
                spatial_feature = self._calculate_spatial_features(
                    grid_lon, grid_lat, station_lon, station_lat, hour
                )
                
                # 时间特征
                time_feature = self._calculate_time_features(hour)
                
                # 合并特征
                combined_feature = np.concatenate([grid_feature, station_feature])
                
                features.append(combined_feature)
                targets.append(count)
                spatial_features.append(spatial_feature)
                time_features.append(time_feature)
        
        return (np.array(features), np.array(targets), 
                np.array(spatial_features), np.array(time_features))
    
    def build_spatial_weight_matrix(self, coords):
        """构建空间权重矩阵"""
        # 计算距离矩阵
        distances = cdist(coords, coords, metric='euclidean')
        
        # 转换为权重（距离越近权重越大）
        weights = 1.0 / (1.0 + distances)
        
        # 对角线设为0（自己与自己的权重）
        np.fill_diagonal(weights, 0)
        
        return weights
    
    def train_advanced_models(self):
        """训练高级模型"""
        print("="*60)
        print("训练高级空间感知模型")
        print("="*60)
        
        # 准备训练数据
        print("准备进站数据...")
        in_features, in_targets, in_spatial, in_time = self.prepare_advanced_features(self.in_data, 'in')
        
        print("准备出站数据...")
        out_features, out_targets, out_spatial, out_time = self.prepare_advanced_features(self.out_data, 'out')
        
        # 标准化特征
        in_features = self.grid_scaler.fit_transform(in_features)
        out_features = self.station_scaler.fit_transform(out_features)
        
        # 训练栅格-地铁站交互模型
        print("训练栅格-地铁站交互模型...")
        self._train_grid_station_model(in_features, in_targets, in_spatial, in_time)
        
        print("高级模型训练完成！")
    
    def _train_grid_station_model(self, features, targets, spatial_features, time_features):
        """训练栅格-地铁站交互模型"""
        # 分割特征
        grid_dim = self.grid_features.shape[1]
        grid_features = features[:, :grid_dim]
        station_features = features[:, grid_dim:]
        
        # 初始化模型
        self.grid_station_model = GridStationInteractionModel(
            grid_feature_dim=grid_dim,
            station_feature_dim=station_features.shape[1],
            hidden_dim=128
        ).to(self.device)
        
        # 转换为张量
        grid_tensor = torch.tensor(grid_features, dtype=torch.float32).to(self.device)
        station_tensor = torch.tensor(station_features, dtype=torch.float32).to(self.device)
        spatial_tensor = torch.tensor(spatial_features, dtype=torch.float32).to(self.device)
        time_tensor = torch.tensor(time_features, dtype=torch.float32).to(self.device)
        target_tensor = torch.tensor(targets, dtype=torch.float32).to(self.device)
        
        # 训练设置
        optimizer = optim.AdamW(self.grid_station_model.parameters(), lr=0.001, weight_decay=1e-4)
        criterion = nn.MSELoss()
        
        # 训练循环
        self.grid_station_model.train()
        for epoch in range(100):
            optimizer.zero_grad()
            
            # 前向传播
            allocation_probs = self.grid_station_model(
                grid_tensor, station_tensor, spatial_tensor, time_tensor
            ).squeeze()
            
            # 计算预测流量（分配概率 * 总流量的估计）
            predicted_flows = allocation_probs * target_tensor.mean()
            
            # 计算损失
            loss = criterion(predicted_flows, target_tensor)
            
            # 反向传播
            loss.backward()
            torch.nn.utils.clip_grad_norm_(self.grid_station_model.parameters(), 1.0)
            optimizer.step()
            
            if epoch % 20 == 0:
                print(f"Epoch {epoch}: Loss = {loss.item():.6f}")
    
    def generate_advanced_predictions(self):
        """生成高级预测结果"""
        print("="*60)
        print("生成高级空间感知预测结果")
        print("="*60)
        
        # 时间分割策略
        train_hours = list(range(0, 18))
        test_hours = list(range(18, 24))
        
        # 生成进站预测
        print("生成进站预测...")
        in_test_data = self.in_data[self.in_data['hour'].isin(test_hours)].copy()
        
        if len(in_test_data) > 0:
            in_test_features, _, in_test_spatial, in_test_time = self.prepare_advanced_features(in_test_data, 'in')
            in_predictions = self._predict_with_spatial_awareness(
                in_test_features, in_test_spatial, in_test_time
            )
            
            in_test_data.loc[:, 'prediction'] = np.maximum(in_predictions, 0)
            
            # 保存结果
            try:
                import glob
                for f in glob.glob('advanced_in_500_predictions_with_coords.*'):
                    try:
                        os.remove(f)
                    except:
                        pass
                
                in_test_data.to_file('advanced_in_500_predictions_with_coords.shp')
                print(f"✓ 高级进站预测结果已保存: {len(in_test_data)} 条记录")
            except Exception as e:
                print(f"保存shapefile失败: {e}")
                in_test_data_csv = in_test_data.drop(columns=['geometry'])
                in_test_data_csv.to_csv('advanced_in_500_predictions_with_coords.csv', index=False)
                print(f"✓ 高级进站预测结果已保存为CSV: {len(in_test_data)} 条记录")
        
        # 生成出站预测
        print("生成出站预测...")
        out_test_data = self.out_data[self.out_data['hour'].isin(test_hours)].copy()
        
        if len(out_test_data) > 0:
            out_test_features, _, out_test_spatial, out_test_time = self.prepare_advanced_features(out_test_data, 'out')
            out_predictions = self._predict_with_spatial_awareness(
                out_test_features, out_test_spatial, out_test_time
            )
            
            out_test_data.loc[:, 'prediction'] = np.maximum(out_predictions, 0)
            
            # 保存结果
            try:
                import glob
                for f in glob.glob('advanced_out_500_predictions_with_coords.*'):
                    try:
                        os.remove(f)
                    except:
                        pass
                
                out_test_data.to_file('advanced_out_500_predictions_with_coords.shp')
                print(f"✓ 高级出站预测结果已保存: {len(out_test_data)} 条记录")
            except Exception as e:
                print(f"保存shapefile失败: {e}")
                out_test_data_csv = out_test_data.drop(columns=['geometry'])
                out_test_data_csv.to_csv('advanced_out_500_predictions_with_coords.csv', index=False)
                print(f"✓ 高级出站预测结果已保存为CSV: {len(out_test_data)} 条记录")
        
        # 生成对比分析
        self._generate_comparison_analysis(in_test_data, out_test_data)
        
        return in_test_data, out_test_data
    
    def _predict_with_spatial_awareness(self, features, spatial_features, time_features):
        """使用空间感知进行预测"""
        self.grid_station_model.eval()
        
        with torch.no_grad():
            # 分割特征
            grid_dim = self.grid_features.shape[1]
            grid_features = features[:, :grid_dim]
            station_features = features[:, grid_dim:]
            
            # 转换为张量
            grid_tensor = torch.tensor(grid_features, dtype=torch.float32).to(self.device)
            station_tensor = torch.tensor(station_features, dtype=torch.float32).to(self.device)
            spatial_tensor = torch.tensor(spatial_features, dtype=torch.float32).to(self.device)
            time_tensor = torch.tensor(time_features, dtype=torch.float32).to(self.device)
            
            # 预测分配概率
            allocation_probs = self.grid_station_model(
                grid_tensor, station_tensor, spatial_tensor, time_tensor
            ).squeeze()
            
            # 转换为流量预测（这里使用简化的转换方法）
            base_flow = 10.0  # 基础流量
            predicted_flows = allocation_probs.cpu().numpy() * base_flow
            
        return predicted_flows
    
    def _generate_comparison_analysis(self, in_test_data, out_test_data):
        """生成对比分析"""
        comparison_data = []
        
        # 进站对比
        if len(in_test_data) > 0:
            for _, row in in_test_data.iterrows():
                comparison_data.append({
                    'Type': 'In',
                    'True_Value': float(row['count']),
                    'Predicted_Value': float(row['prediction']),
                    'Station': str(row['station_clean']),
                    'Hour': int(row['hour']),
                    'Error': float(abs(row['count'] - row['prediction'])),
                    'Longitude': float(row['longitude']),
                    'Latitude': float(row['latitude'])
                })
        
        # 出站对比
        if len(out_test_data) > 0:
            for _, row in out_test_data.iterrows():
                comparison_data.append({
                    'Type': 'Out',
                    'True_Value': float(row['count']),
                    'Predicted_Value': float(row['prediction']),
                    'Station': str(row['station_clean']),
                    'Hour': int(row['hour']),
                    'Error': float(abs(row['count'] - row['prediction'])),
                    'Longitude': float(row['longitude']),
                    'Latitude': float(row['latitude'])
                })
        
        # 保存对比结果
        if comparison_data:
            comparison_df = pd.DataFrame(comparison_data)
            comparison_df.to_csv('advanced_prediction_comparison.csv', index=False)
            print(f"✓ 高级预测对比汇总已保存: {len(comparison_data)} 条记录")
        
        return comparison_data

def main():
    """主函数"""
    print("="*80)
    print("高级空间感知地铁流量预测系统")
    print("解决栅格-地铁站流量分配差异化建模")
    print("="*80)
    
    start_time = time.time()
    
    try:
        # 初始化系统
        system = AdvancedSpatialPredictionSystem()
        
        # 加载数据
        if not system.load_and_process_data():
            return False
        
        # 训练模型
        system.train_advanced_models()
        
        # 生成预测
        in_results, out_results = system.generate_advanced_predictions()
        
        print(f"\n运行时间: {time.time() - start_time:.2f} 秒")
        print("="*60)
        print("高级空间感知预测系统运行完成！")
        print("="*60)
        
        return True
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
