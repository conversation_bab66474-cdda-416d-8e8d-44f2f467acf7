"""
修复版内存监控工具
解决设备不匹配问题
"""
import torch
import psutil
import tracemalloc
import numpy as np
import gc
import sys
from typing import Dict, Tuple
import threading
import time

class MemoryMonitor:
    def __init__(self):
        self.process = psutil.Process()
        self.initial_memory = None
        self.peak_memory = 0
        self.monitoring = False
        self.memory_log = []
        
    def start_monitoring(self):
        """开始内存监控"""
        try:
            tracemalloc.start()
            self.initial_memory = self.get_current_memory()
            self.peak_memory = self.initial_memory['total_mb']
            self.monitoring = True
            print(f"🔍 开始内存监控...")
            print(f"   初始内存使用: {self.initial_memory['total_mb']:.1f} MB")
            
            # 启动后台监控线程
            self.monitor_thread = threading.Thread(target=self._background_monitor)
            self.monitor_thread.daemon = True
            self.monitor_thread.start()
        except Exception as e:
            print(f"⚠️ 内存监控启动失败: {e}")
    
    def _background_monitor(self):
        """后台持续监控内存"""
        while self.monitoring:
            try:
                current = self.get_current_memory()
                self.peak_memory = max(self.peak_memory, current['total_mb'])
                self.memory_log.append({
                    'timestamp': time.time(),
                    'memory_mb': current['total_mb'],
                    'available_mb': current['available_mb']
                })
            except Exception:
                pass  # 忽略监控过程中的错误
            time.sleep(1)
    
    def get_current_memory(self) -> Dict:
        """获取当前内存使用情况"""
        try:
            # 系统内存
            system_memory = psutil.virtual_memory()
            
            # 进程内存
            process_memory = self.process.memory_info()
            
            # GPU内存（如果可用）
            gpu_memory = self.get_gpu_memory()
            
            return {
                'total_mb': system_memory.used / 1024 / 1024,
                'available_mb': system_memory.available / 1024 / 1024,
                'process_mb': process_memory.rss / 1024 / 1024,
                'gpu_allocated_mb': gpu_memory['allocated_mb'],
                'gpu_total_mb': gpu_memory['total_mb'],
                'memory_percent': system_memory.percent
            }
        except Exception as e:
            print(f"⚠️ 获取内存信息失败: {e}")
            return {
                'total_mb': 0, 'available_mb': 0, 'process_mb': 0,
                'gpu_allocated_mb': 0, 'gpu_total_mb': 0, 'memory_percent': 0
            }
    
    def get_gpu_memory(self) -> Dict:
        """获取GPU内存使用情况"""
        if not torch.cuda.is_available():
            return {'allocated_mb': 0, 'total_mb': 0}
        
        try:
            allocated = torch.cuda.memory_allocated() / 1024 / 1024
            total = torch.cuda.get_device_properties(0).total_memory / 1024 / 1024
            return {'allocated_mb': allocated, 'total_mb': total}
        except Exception:
            return {'allocated_mb': 0, 'total_mb': 0}
    
    def estimate_model_memory(self, model) -> Dict:
        """估算模型内存需求（修复版）"""
        if model is None:
            return {'parameters_mb': 0, 'gradients_mb': 0, 'total_mb': 0}
        
        try:
            # 安全计算参数内存
            param_size = 0
            for p in model.parameters():
                try:
                    param_size += p.numel() * p.element_size()
                except Exception:
                    # 如果参数在不同设备上，单独计算
                    param_size += p.numel() * 4  # 假设float32
            
            param_mb = param_size / 1024 / 1024
            grad_mb = param_mb  # 梯度内存
            optimizer_mb = param_mb * 2  # 优化器状态
            
            total_mb = param_mb + grad_mb + optimizer_mb
            
            return {
                'parameters_mb': param_mb,
                'gradients_mb': grad_mb,
                'optimizer_mb': optimizer_mb,
                'total_mb': total_mb
            }
        except Exception as e:
            print(f"⚠️ 模型内存估算失败: {e}")
            return {'parameters_mb': 0, 'gradients_mb': 0, 'optimizer_mb': 0, 'total_mb': 0}
    
    def estimate_data_memory(self, *tensors) -> Dict:
        """估算数据内存需求（修复版）"""
        total_size = 0
        tensor_info = []
        
        for i, tensor in enumerate(tensors):
            if tensor is not None:
                try:
                    # 安全获取张量信息，避免设备不匹配问题
                    size = tensor.numel() * tensor.element_size()
                    size_mb = size / 1024 / 1024
                    total_size += size
                    
                    # 安全获取张量设备信息
                    try:
                        device_info = str(tensor.device)
                    except:
                        device_info = "unknown"
                    
                    tensor_info.append({
                        f'tensor_{i}_shape': tuple(tensor.shape),
                        f'tensor_{i}_mb': size_mb,
                        f'tensor_{i}_dtype': str(tensor.dtype),
                        f'tensor_{i}_device': device_info
                    })
                except Exception as e:
                    print(f"⚠️ 张量 {i} 信息获取失败: {e}")
                    # 使用默认值
                    tensor_info.append({
                        f'tensor_{i}_shape': "unknown",
                        f'tensor_{i}_mb': 0,
                        f'tensor_{i}_dtype': "unknown",
                        f'tensor_{i}_device': "unknown"
                    })
        
        return {
            'total_data_mb': total_size / 1024 / 1024,
            'tensor_details': tensor_info
        }
    
    def check_memory_availability(self, required_mb: float) -> Dict:
        """检查是否有足够内存"""
        try:
            current = self.get_current_memory()
            
            return {
                'required_mb': required_mb,
                'available_mb': current['available_mb'],
                'sufficient': current['available_mb'] > required_mb,
                'shortage_mb': max(0, required_mb - current['available_mb']),
                'usage_percent': (required_mb / max(current['available_mb'] + required_mb, 1)) * 100
            }
        except Exception as e:
            print(f"⚠️ 内存可用性检查失败: {e}")
            return {
                'required_mb': required_mb, 'available_mb': 0, 'sufficient': False,
                'shortage_mb': required_mb, 'usage_percent': 100
            }
    
    def get_memory_recommendations(self, model=None, *data_tensors) -> Dict:
        """获取内存优化建议（修复版）"""
        try:
            current = self.get_current_memory()
            
            # 安全估算需求
            model_memory = self.estimate_model_memory(model)
            data_memory = self.estimate_data_memory(*data_tensors)
            
            total_estimated = model_memory['total_mb'] + data_memory['total_data_mb']
            
            recommendations = []
            
            # 内存不足的建议
            if current['available_mb'] < total_estimated:
                shortage = total_estimated - current['available_mb']
                recommendations.append(f"⚠️  内存不足 {shortage:.1f} MB")
                
                # 具体建议
                if model_memory['total_mb'] > 100:
                    recommendations.append("🔧 减少模型复杂度：降低hidden_dim")
                
                if data_memory['total_data_mb'] > 500:
                    recommendations.append("📊 减少数据量：限制station数量或time_steps")
                
                recommendations.append("💻 考虑使用CPU而非GPU")
                recommendations.append("🔄 使用梯度累积减少batch size")
            
            return {
                'current_available_mb': current['available_mb'],
                'estimated_required_mb': total_estimated,
                'shortage_mb': max(0, total_estimated - current['available_mb']),
                'recommendations': recommendations
            }
        except Exception as e:
            print(f"⚠️ 内存建议生成失败: {e}")
            return {
                'current_available_mb': 0, 'estimated_required_mb': 0,
                'shortage_mb': 0, 'recommendations': ["⚠️ 内存分析失败"]
            }
    
    def stop_monitoring(self):
        """停止监控并生成报告"""
        if not self.monitoring:
            return {}
        
        self.monitoring = False
        
        try:
            # 获取tracemalloc统计
            if tracemalloc.is_tracing():
                current, peak = tracemalloc.get_traced_memory()
                tracemalloc.stop()
                tracemalloc_peak_mb = peak / 1024 / 1024
            else:
                tracemalloc_peak_mb = 0
            
            # 生成报告
            final_memory = self.get_current_memory()
            memory_increase = final_memory['total_mb'] - self.initial_memory['total_mb']
            
            report = {
                'initial_memory_mb': self.initial_memory['total_mb'],
                'final_memory_mb': final_memory['total_mb'],
                'peak_memory_mb': self.peak_memory,
                'memory_increase_mb': memory_increase,
                'tracemalloc_peak_mb': tracemalloc_peak_mb,
                'gpu_peak_mb': final_memory['gpu_allocated_mb']
            }
            
            print("\n📊 内存使用报告:")
            print("=" * 50)
            print(f"初始内存:     {report['initial_memory_mb']:.1f} MB")
            print(f"峰值内存:     {report['peak_memory_mb']:.1f} MB")
            print(f"最终内存:     {report['final_memory_mb']:.1f} MB")
            print(f"内存增长:     {report['memory_increase_mb']:.1f} MB")
            if tracemalloc_peak_mb > 0:
                print(f"Python峰值:   {report['tracemalloc_peak_mb']:.1f} MB")
            if report['gpu_peak_mb'] > 0:
                print(f"GPU内存:      {report['gpu_peak_mb']:.1f} MB")
            print("=" * 50)
            
            return report
        except Exception as e:
            print(f"⚠️ 内存报告生成失败: {e}")
            return {}

# 使用示例和快速检查函数
def quick_memory_check():
    """快速内存检查"""
    try:
        monitor = MemoryMonitor()
        current = monitor.get_current_memory()
        
        print("🔍 当前内存状态:")
        print(f"   系统内存使用: {current['total_mb']:.1f} MB ({current['memory_percent']:.1f}%)")
        print(f"   可用内存:     {current['available_mb']:.1f} MB")
        print(f"   进程内存:     {current['process_mb']:.1f} MB")
        
        if current['gpu_total_mb'] > 0:
            gpu_percent = (current['gpu_allocated_mb'] / current['gpu_total_mb']) * 100
            print(f"   GPU内存:      {current['gpu_allocated_mb']:.1f}/{current['gpu_total_mb']:.1f} MB ({gpu_percent:.1f}%)")
        
        # 内存警告
        if current['available_mb'] < 1000:
            print("⚠️  警告: 可用内存不足1GB")
        elif current['available_mb'] < 2000:
            print("⚠️  注意: 可用内存少于2GB")
        else:
            print("✅ 内存充足")
    except Exception as e:
        print(f"⚠️ 内存检查失败: {e}")

def estimate_tgcn_memory(num_stations, time_steps, hidden_dim, feature_dim):
    """估算T-GCN模型的内存需求（安全版本）"""
    try:
        print(f"\n🧮 估算T-GCN内存需求:")
        print(f"   站点数: {num_stations}")
        print(f"   时间步: {time_steps}")
        print(f"   隐藏维度: {hidden_dim}")
        print(f"   特征维度: {feature_dim}")
        
        # 估算模型参数
        gcn_params = feature_dim * hidden_dim + hidden_dim * hidden_dim
        gru_params = hidden_dim * hidden_dim * 3
        output_params = hidden_dim * 1
        
        total_params = gcn_params + gru_params + output_params
        param_mb = total_params * 4 / 1024 / 1024  # float32
        
        # 估算数据内存
        input_mb = num_stations * time_steps * feature_dim * 4 / 1024 / 1024
        hidden_mb = num_stations * time_steps * hidden_dim * 4 / 1024 / 1024
        grad_mb = param_mb
        
        total_mb = param_mb + input_mb + hidden_mb + grad_mb
        
        print(f"\n📊 内存估算结果:")
        print(f"   模型参数:     {param_mb:.1f} MB")
        print(f"   输入数据:     {input_mb:.1f} MB")
        print(f"   隐藏状态:     {hidden_mb:.1f} MB")
        print(f"   梯度内存:     {grad_mb:.1f} MB")
        print(f"   总计约:       {total_mb:.1f} MB")
        
        # 建议
        monitor = MemoryMonitor()
        current = monitor.get_current_memory()
        
        if total_mb > current['available_mb']:
            shortage = total_mb - current['available_mb']
            print(f"\n⚠️  内存不足约 {shortage:.1f} MB")
            
            print(f"\n🔧 优化建议:")
            if num_stations > 20:
                print(f"   - 减少站点数到 {int(num_stations * 0.5)} 可节约约 {input_mb * 0.5:.1f} MB")
            if hidden_dim > 16:
                print(f"   - 减少hidden_dim到 {hidden_dim//2} 可节约约 {hidden_mb * 0.5:.1f} MB")
            if time_steps > 6:
                print(f"   - 减少time_steps到 {time_steps//2} 可节约约 {input_mb * 0.5:.1f} MB")
        else:
            print(f"\n✅ 内存充足，剩余约 {current['available_mb'] - total_mb:.1f} MB")
        
        return total_mb
    except Exception as e:
        print(f"⚠️ 内存估算失败: {e}")
        return 0

if __name__ == "__main__":
    # 快速检查当前内存状态
    quick_memory_check()
    
    # 估算典型T-GCN配置的内存需求
    print("\n" + "="*60)
    estimate_tgcn_memory(num_stations=100, time_steps=24, hidden_dim=64, feature_dim=10)
    
    print("\n" + "="*60)
    estimate_tgcn_memory(num_stations=50, time_steps=12, hidden_dim=32, feature_dim=10)