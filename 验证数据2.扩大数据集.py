# -*- coding: utf-8 -*-
"""
Created on Wed Apr  9 16:21:27 2025

@author: wxj01
"""

import pandas as pd
import numpy as np
from shapely.geometry import Polygon
import geopandas as gpd
import math

df1 = pd.read_csv('jiebo_in_modified.csv')
df2 = pd.read_csv('jiebo_out_modified.csv')
df = pd.read_csv('updated_北京市_subway_od_2024_modified.csv')

# 获取df1中station列的所有唯一值
stations1 = list(df1['station'].unique())
stations2 = list(df2['station'].unique())
stationso = list(df['o_rawname'].unique())
stationsd = list(df['d_rawname'].unique())


df=df_filtered
# 创建结果列表
results = []

# 对每个station进行处理
for station in stations:
    # 计算df中o_rawname为该station的trip总和
    o_trip_sum = df[df['o_rawname'] == station]['trip'].sum()
    
    # 计算df中d_rawname为该station的trip总和
    d_trip_sum = df[df['d_rawname'] == station]['trip'].sum()
    
    # 计算df1中该station的记录数
    df1_count = len(df1[df1['station'] == station])
    
    # 计算df2中该station的记录数
    df2_count = len(df2[df2['station'] == station])
    
    # 将结果添加到列表
    results.append({
        '站名': station,
        'o_trip总和': o_trip_sum,
        'd_trip总和': d_trip_sum,
        'df1记录数': df1_count,
        'df2记录数': df2_count
    })

# 创建结果DataFrame
result_df = pd.DataFrame(results)

# 调整列顺序（可选）
result_df = result_df[['站名', 'o_trip总和', 'd_trip总和', 'df1记录数', 'df2记录数']]