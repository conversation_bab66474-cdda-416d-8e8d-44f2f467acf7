# -*- coding: utf-8 -*-
"""
Created on Wed May 28 13:32:13 2025

@author: wxj01
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
import pandas as pd
import geopandas as gpd
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.metrics import mean_absolute_error, mean_squared_error
import matplotlib.pyplot as plt
from torch_geometric.nn import GCNConv
from torch_geometric.data import Data
import warnings
warnings.filterwarnings('ignore')

# 设置设备
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"使用设备: {device}")

class ComplexTGCN(nn.Module):
    """
    复杂的T-GCN模型，包含多层GCN和LSTM，以及注意力机制
    """
    def __init__(self, node_features, hidden_dim=128, num_gnn_layers=3, 
                 num_lstm_layers=2, dropout=0.3, attention=True):
        super(ComplexTGCN, self).__init__()
        
        self.node_features = node_features
        self.hidden_dim = hidden_dim
        self.num_gnn_layers = num_gnn_layers
        self.num_lstm_layers = num_lstm_layers
        self.attention = attention
        
        # 多层GCN
        self.gnn_layers = nn.ModuleList()
        self.gnn_layers.append(GCNConv(node_features, hidden_dim))
        for i in range(num_gnn_layers - 1):
            self.gnn_layers.append(GCNConv(hidden_dim, hidden_dim))
        
        # 批归一化层
        self.batch_norms = nn.ModuleList()
        for i in range(num_gnn_layers):
            self.batch_norms.append(nn.BatchNorm1d(hidden_dim))
        
        # LSTM层
        self.lstm = nn.LSTM(hidden_dim, hidden_dim, 
                           num_layers=num_lstm_layers, 
                           batch_first=True, 
                           dropout=dropout if num_lstm_layers > 1 else 0,
                           bidirectional=True)
        
        # 注意力机制
        if self.attention:
            self.attention_weights = nn.Linear(hidden_dim * 2, 1)
        
        # 输出层
        self.output_layer = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, 1)
        )
        
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x, edge_index, batch_size, seq_len):
        # x shape: [batch_size * seq_len, num_nodes, features]
        # 重塑输入
        batch_seq, num_nodes, features = x.shape
        
        outputs = []
        
        # 对每个时间步进行GCN处理
        for t in range(seq_len):
            # 获取当前时间步的数据
            x_t = x[t * batch_size:(t + 1) * batch_size]  # [batch_size, num_nodes, features]
            x_t = x_t.view(-1, features)  # [batch_size * num_nodes, features]
            
            # 多层GCN处理
            h = x_t
            for i, (gnn_layer, bn) in enumerate(zip(self.gnn_layers, self.batch_norms)):
                h = gnn_layer(h, edge_index)
                h = bn(h)
                h = F.relu(h)
                h = self.dropout(h)
            
            # 重塑为 [batch_size, num_nodes, hidden_dim]
            h = h.view(batch_size, num_nodes, -1)
            outputs.append(h)
        
        # 堆叠时间序列: [batch_size, seq_len, num_nodes, hidden_dim]
        temporal_features = torch.stack(outputs, dim=1)
        
        # 对每个节点进行LSTM处理
        final_outputs = []
        for node_idx in range(num_nodes):
            # 获取该节点的时间序列: [batch_size, seq_len, hidden_dim]
            node_temporal = temporal_features[:, :, node_idx, :]
            
            # LSTM处理
            lstm_out, _ = self.lstm(node_temporal)  # [batch_size, seq_len, hidden_dim*2]
            
            if self.attention:
                # 注意力机制
                attention_scores = self.attention_weights(lstm_out)  # [batch_size, seq_len, 1]
                attention_weights = F.softmax(attention_scores, dim=1)
                attended_output = torch.sum(lstm_out * attention_weights, dim=1)  # [batch_size, hidden_dim*2]
            else:
                attended_output = lstm_out[:, -1, :]  # 使用最后一个时间步
            
            final_outputs.append(attended_output)
        
        # 堆叠所有节点的输出: [batch_size, num_nodes, hidden_dim*2]
        final_features = torch.stack(final_outputs, dim=1)
        
        # 输出层处理
        output = self.output_layer(final_features)  # [batch_size, num_nodes, 1]
        
        return output.squeeze(-1)  # [batch_size, num_nodes]

class DataProcessor:
    """数据处理类"""
    def __init__(self, base_path):
        self.base_path = base_path
        self.scaler = StandardScaler()
        self.flow_scaler = MinMaxScaler()
        
    def load_data(self):
        """加载所有数据"""
        print("正在加载数据...")
        
        # 加载地铁站数据
        self.in_data = gpd.read_file(f"{self.base_path}\\in_500_with_coords.shp")
        self.out_data = gpd.read_file(f"{self.base_path}\\out_500_with_coords.shp")
        
        # 加载OD数据
        self.od_data = pd.read_csv(f"{self.base_path}\\updated_北京市_subway_od_2024_modified3.csv")
        
        # 加载站点特征数据
        self.station_features = pd.read_csv(f"{self.base_path}\\station_features_result.csv")
        
        print(f"进站数据形状: {self.in_data.shape}")
        print(f"出站数据形状: {self.out_data.shape}")
        print(f"OD数据形状: {self.od_data.shape}")
        print(f"站点特征数据形状: {self.station_features.shape}")
        
    def load_grid_data(self, grid_shp_path):
        """加载栅格数据并计算栅格中心"""
        print("正在加载栅格数据...")
        grid_data = gpd.read_file(grid_shp_path)
        
        # 计算栅格中心点
        grid_data['center_lon'] = grid_data.geometry.centroid.x
        grid_data['center_lat'] = grid_data.geometry.centroid.y
        
        return grid_data
        
    def calculate_grid_station_distance(self, grid_data):
        """计算栅格中心到地铁站的最短距离"""
        print("正在计算栅格-地铁站距离...")
        
        # 获取唯一站点及其坐标
        stations = self.in_data[['station', 'longitude', 'latitude']].drop_duplicates()
        
        # 为每个栅格计算到最近地铁站的距离
        grid_distances = []
        
        for idx, grid_row in grid_data.iterrows():
            grid_lon, grid_lat = grid_row['center_lon'], grid_row['center_lat']
            
            # 计算到所有地铁站的距离
            distances = []
            for _, station_row in stations.iterrows():
                station_lon, station_lat = station_row['longitude'], station_row['latitude']
                # 使用欧几里得距离（可以改为球面距离）
                dist = np.sqrt((grid_lon - station_lon)**2 + (grid_lat - station_lat)**2)
                distances.append(dist)
            
            # 取最短距离
            min_distance = min(distances)
            grid_distances.append(min_distance)
            
            if idx % 1000 == 0:
                print(f"处理栅格 {idx}/{len(grid_data)}")
        
        grid_data['min_station_distance'] = grid_distances
        return grid_data
        
    def prepare_features(self, grid_data=None):
        """准备特征矩阵"""
        print("正在准备特征...")
        
        # 获取共同站点
        in_stations = set(self.in_data['station'].unique())
        out_stations = set(self.out_data['station'].unique())
        feature_stations = set(self.station_features['站名'].unique())
        
        common_stations = in_stations & out_stations & feature_stations
        common_stations = sorted(list(common_stations))
        
        print(f"共同站点数量: {len(common_stations)}")
        
        # 过滤数据
        self.in_data = self.in_data[self.in_data['station'].isin(common_stations)]
        self.out_data = self.out_data[self.out_data['station'].isin(common_stations)]
        self.station_features = self.station_features[self.station_features['站名'].isin(common_stations)]
        
        # 创建站点到索引的映射
        self.station_to_idx = {station: idx for idx, station in enumerate(common_stations)}
        self.idx_to_station = {idx: station for station, idx in self.station_to_idx.items()}
        
        # 准备站点特征
        feature_cols = [col for col in self.station_features.columns if col != '站名']
        features_matrix = self.station_features[feature_cols].values
        features_matrix = self.scaler.fit_transform(features_matrix)
        
        # 如果有栅格数据，添加距离特征
        if grid_data is not None:
            # 为每个站点计算平均栅格距离特征
            station_grid_features = []
            for station in common_stations:
                station_coords = self.in_data[self.in_data['station'] == station][['longitude', 'latitude']].iloc[0]
                # 这里可以添加更复杂的栅格特征计算逻辑
                # 暂时使用随机值作为示例
                grid_feature = np.random.random(5)  # 5个栅格相关特征
                station_grid_features.append(grid_feature)
            
            station_grid_features = np.array(station_grid_features)
            features_matrix = np.concatenate([features_matrix, station_grid_features], axis=1)
        
        # 准备时间序列数据
        time_series_data = []
        
        for hour in range(24):
            hour_features = []
            
            for station in common_stations:
                # 基础特征
                station_idx = self.station_to_idx[station]
                base_features = features_matrix[station_idx]
                
                # 流量特征
                in_flow = self.in_data[(self.in_data['station'] == station) & 
                                      (self.in_data['hour'] == hour)]['count'].sum()
                out_flow = self.out_data[(self.out_data['station'] == station) & 
                                       (self.out_data['hour'] == hour)]['count'].sum()
                
                # 组合特征
                combined_features = np.concatenate([base_features, [in_flow, out_flow]])
                hour_features.append(combined_features)
            
            time_series_data.append(hour_features)
        
        self.features_matrix = np.array(time_series_data)  # [24, num_stations, features]
        self.num_stations = len(common_stations)
        self.num_features = self.features_matrix.shape[2]
        
        print(f"特征矩阵形状: {self.features_matrix.shape}")
        
        return common_stations
    
    def create_adjacency_matrix(self, stations):
        """基于OD数据创建邻接矩阵"""
        print("正在创建邻接矩阵...")
        
        num_stations = len(stations)
        adj_matrix = np.zeros((num_stations, num_stations))
        
        # 基于OD流量创建边
        for _, row in self.od_data.iterrows():
            if row['o_rawname'] in self.station_to_idx and row['d_rawname'] in self.station_to_idx:
                i = self.station_to_idx[row['o_rawname']]
                j = self.station_to_idx[row['d_rawname']]
                adj_matrix[i, j] += row['trip']
        
        # 归一化
        row_sums = adj_matrix.sum(axis=1)
        row_sums[row_sums == 0] = 1  # 避免除零
        adj_matrix = adj_matrix / row_sums[:, np.newaxis]
        
        # 转换为PyTorch Geometric格式
        edge_indices = []
        edge_weights = []
        
        for i in range(num_stations):
            for j in range(num_stations):
                if adj_matrix[i, j] > 0.01:  # 阈值过滤
                    edge_indices.append([i, j])
                    edge_weights.append(adj_matrix[i, j])
        
        edge_index = torch.tensor(edge_indices, dtype=torch.long).T
        edge_weight = torch.tensor(edge_weights, dtype=torch.float)
        
        print(f"图边数量: {edge_index.shape[1]}")
        
        return edge_index, edge_weight

def create_sequences(data, seq_length=6, pred_length=1):
    """创建时间序列序列"""
    X, y = [], []
    
    for i in range(seq_length, len(data) - pred_length + 1):
        X.append(data[i-seq_length:i])
        y.append(data[i:i+pred_length])
    
    return np.array(X), np.array(y)

def train_model(model, train_loader, val_loader, edge_index, num_epochs=10, lr=0.001):
    """训练模型"""
    optimizer = optim.Adam(model.parameters(), lr=lr, weight_decay=1e-5)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
    criterion = nn.MSELoss()
    
    train_losses = []
    val_losses = []
    train_maes = []
    val_maes = []
    
    edge_index = edge_index.to(device)
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0
        train_mae = 0
        num_batches = 0
        
        for batch_X, batch_y in train_loader:
            batch_X = batch_X.to(device)
            batch_y = batch_y.to(device)
            
            batch_size, seq_len = batch_X.shape[0], batch_X.shape[1]
            
            # 重塑输入数据
            X_reshaped = batch_X.view(-1, batch_X.shape[2], batch_X.shape[3])
            
            optimizer.zero_grad()
            
            # 前向传播
            predictions = model(X_reshaped, edge_index, batch_size, seq_len)
            
            # 计算损失
            # 获取流量部分（最后2列是in_flow和out_flow）
            target_flow = batch_y[:, :, -2:]  # 取最后两列流量数据
            target_flow = target_flow.mean(dim=-1)  # 或者选择其中一列，如 target_flow[:, :, 0]
            loss = criterion(predictions, target_flow)
            
            # 反向传播
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            train_loss += loss.item()
            target_flow = batch_y.squeeze(-1)
            train_mae += mean_absolute_error(batch_y.squeeze(-1).cpu().numpy().flatten(), 
                               predictions.detach().cpu().numpy().flatten())
            num_batches += 1
        
        # 验证阶段
        model.eval()
        val_loss = 0
        val_mae = 0
        val_batches = 0
        
        with torch.no_grad():
            for batch_X, batch_y in val_loader:
                batch_X = batch_X.to(device)
                batch_y = batch_y.to(device)
                
                batch_size, seq_len = batch_X.shape[0], batch_X.shape[1]
                X_reshaped = batch_X.view(-1, batch_X.shape[2], batch_X.shape[3])
                
                predictions = model(X_reshaped, edge_index, batch_size, seq_len)
                loss = criterion(predictions, batch_y.squeeze(-1))
                
                val_loss += loss.item()
                target_flow = batch_y.squeeze(-1)
                val_mae += mean_absolute_error(batch_y.squeeze(-1).cpu().numpy().flatten(), 
                             predictions.cpu().numpy().flatten())
                val_batches += 1
        
        # 记录指标
        avg_train_loss = train_loss / num_batches
        avg_val_loss = val_loss / val_batches
        avg_train_mae = train_mae / num_batches
        avg_val_mae = val_mae / val_batches
        
        train_losses.append(avg_train_loss)
        val_losses.append(avg_val_loss)
        train_maes.append(avg_train_mae)
        val_maes.append(avg_val_mae)
        
        scheduler.step(avg_val_loss)
        
        if epoch % 10 == 0:
            print(f'Epoch {epoch:3d}: Train Loss={avg_train_loss:.4f}, Val Loss={avg_val_loss:.4f}, '
                  f'Train MAE={avg_train_mae:.4f}, Val MAE={avg_val_mae:.4f}')
    
    return train_losses, val_losses, train_maes, val_maes

def main():
    """主函数"""
    # 数据路径
    base_path = "C:\\Users\\<USER>\\Desktop\\接驳"
    
    # 初始化数据处理器
    processor = DataProcessor(base_path)
    
    # 加载数据
    processor.load_data()
    
    # 如果有栅格数据，可以加载（这里注释掉，因为没有提供栅格文件路径）
    # grid_data = processor.load_grid_data("path_to_grid.shp")
    # grid_data = processor.calculate_grid_station_distance(grid_data)
    
    # 准备特征
    stations = processor.prepare_features()
    edge_index, edge_weight = processor.create_adjacency_matrix(stations)
    # 分离特征和目标（流量）
    features = processor.features_matrix[:, :, :-2]  # 除了最后2列流量的所有特征
    targets = processor.features_matrix[:, :, -2:]   # 最后2列是进站和出站流量
    targets = targets.mean(axis=-1)  # 或者选择其中一列作为目标
    
    # 创建时间序列数据
    seq_length = 6
    X, y = create_sequences(features, seq_length, 1)
    target_X, target_y = create_sequences(targets, seq_length, 1)
    
    # 使用target_y作为真实标签
    y = target_y
    
    print(f"序列数据形状: X={X.shape}, y={y.shape}")
    
    # 分割数据集
    train_size = int(0.7 * len(X))
    val_size = int(0.15 * len(X))
    
    X_train, y_train = X[:train_size], y[:train_size]
    X_val, y_val = X[train_size:train_size+val_size], y[train_size:train_size+val_size]
    X_test, y_test = X[train_size+val_size:], y[train_size+val_size:]
    
    # 转换为PyTorch张量
    X_train = torch.FloatTensor(X_train)
    y_train = torch.FloatTensor(y_train)
    X_val = torch.FloatTensor(X_val)
    y_val = torch.FloatTensor(y_val)
    X_test = torch.FloatTensor(X_test)
    y_test = torch.FloatTensor(y_test)
    
    # 创建数据加载器
    from torch.utils.data import TensorDataset, DataLoader
    
    batch_size = 8  # 适应显存限制
    train_dataset = TensorDataset(X_train, y_train)
    val_dataset = TensorDataset(X_val, y_val)
    test_dataset = TensorDataset(X_test, y_test)
    
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    
    # 创建模型
    model = ComplexTGCN(
        node_features=processor.num_features-2,  # 减去2个流量特征
        hidden_dim=64,
        num_gnn_layers=2,
        num_lstm_layers=2,
        dropout=0.3,
        attention=True
    ).to(device)
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters())}")
    
    # 训练模型
    print("开始训练...")
    train_losses, val_losses, train_maes, val_maes = train_model(
        model, train_loader, val_loader, edge_index, num_epochs=100, lr=0.001
    )
    
    # 测试模型
    model.eval()
    test_predictions = []
    test_actuals = []
    
    with torch.no_grad():
        for batch_X, batch_y in test_loader:
            batch_X = batch_X.to(device)
            batch_y = batch_y.to(device)
            
            batch_size, seq_len = batch_X.shape[0], batch_X.shape[1]
            X_reshaped = batch_X.view(-1, batch_X.shape[2], batch_X.shape[3])
            
            predictions = model(X_reshaped, edge_index.to(device), batch_size, seq_len)
            
            test_predictions.extend(predictions.cpu().numpy().flatten())
            test_actuals.extend(batch_y.cpu().numpy().flatten())
    
    # 计算测试指标
    test_mae = mean_absolute_error(test_actuals, test_predictions)
    test_rmse = np.sqrt(mean_squared_error(test_actuals, test_predictions))
    
    print(f"\n测试结果:")
    print(f"测试 MAE: {test_mae:.4f}")
    print(f"测试 RMSE: {test_rmse:.4f}")
    
    # 绘制训练过程
    plt.figure(figsize=(15, 5))
    
    plt.subplot(1, 3, 1)
    plt.plot(train_losses, label='训练损失')
    plt.plot(val_losses, label='验证损失')
    plt.title('损失变化')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    
    plt.subplot(1, 3, 2)
    plt.plot(train_maes, label='训练MAE')
    plt.plot(val_maes, label='验证MAE')
    plt.title('MAE变化')
    plt.xlabel('Epoch')
    plt.ylabel('MAE')
    plt.legend()
    
    plt.subplot(1, 3, 3)
    plt.scatter(test_actuals[:1000], test_predictions[:1000], alpha=0.5)
    plt.plot([min(test_actuals), max(test_actuals)], [min(test_actuals), max(test_actuals)], 'r--')
    plt.title('预测 vs 实际')
    plt.xlabel('实际值')
    plt.ylabel('预测值')
    
    plt.tight_layout()
    plt.savefig(f"{base_path}\\training_results.png", dpi=300, bbox_inches='tight')
    plt.show()
    
    # 保存预测结果
    results_df = pd.DataFrame({
        'actual': test_actuals,
        'predicted': test_predictions
    })
    results_df.to_csv(f"{base_path}\\tgcn_predictions.csv", index=False)
    
    # 保存训练历史
    history_df = pd.DataFrame({
        'epoch': range(len(train_losses)),
        'train_loss': train_losses,
        'val_loss': val_losses,
        'train_mae': train_maes,
        'val_mae': val_maes
    })
    history_df.to_csv(f"{base_path}\\training_history.csv", index=False)
    
    print("训练完成，结果已保存！")

if __name__ == "__main__":
    main()