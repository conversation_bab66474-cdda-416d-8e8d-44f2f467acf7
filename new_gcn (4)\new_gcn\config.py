import os
import torch

class Config:
    # 数据路径配置
    BASE_DIR = r"C:\Users\<USER>\Desktop\接驳"
    IN_STATION_PATH = os.path.join(BASE_DIR, "in_500_with_coords.shp")
    OUT_STATION_PATH = os.path.join(BASE_DIR, "out_500_with_coords.shp")
    OD_PATH = os.path.join(BASE_DIR, "updated北京市_subway_od_2024_modified3.csv")
    STATION_FEATURES_PATH = os.path.join(BASE_DIR, "station_features_result.csv")
    
    # 输出路径配置
    OUT_IN_PREDICTIONS = os.path.join(BASE_DIR, "in_500_predictions_with_coords.shp")
    OUT_OUT_PREDICTIONS = os.path.join(BASE_DIR, "out_500_predictions_with_coords.shp")
    OUT_OD_PREDICTIONS = os.path.join(BASE_DIR, "od_predictions.csv")
    OUT_COMPARISON = os.path.join(BASE_DIR, "prediction_comparison.csv")
    
    # 设备和内存配置 - 适配用户电脑
    DEVICE = "cuda" if torch.cuda.is_available() else "cpu"
    USE_FLOAT16 = True  # 使用float16减少50%内存使用
    MAX_BATCH_SIZE = 16  # 降低批次大小避免显存溢出
    GRADIENT_CHECKPOINTING = True  # 启用梯度检查点减少内存
    
    # 模型配置 - 降低复杂度适配显存
    HIDDEN_DIM = 64  # 降低隐藏层维度
    NUM_LAYERS = 2   # 降低层数
    DROPOUT = 0.3
    LEARNING_RATE = 0.001
    EPOCHS = 10      # 降低训练轮数用于测试
    PATIENCE = 3
    BATCH_SIZE = 8   # 进一步降低批次大小
    
    # 数据处理配置
    TIME_FEATURES = 6    # 时间特征维度
    SPATIAL_FEATURES = 51  # 空间特征维度（52-1站名）
    SEQUENCE_LENGTH = 6    # 时间序列长度
    SAMPLE_RATIO = 0.1     # 仅使用10%数据进行快速测试
    
    # 图构建配置
    DISTANCE_THRESHOLD = 2000  # 米
    K_NEAREST = 3             # 降低K值减少边数
    
    # 验证配置
    TRAIN_RATIO = 0.7
    VAL_RATIO = 0.2
    TEST_RATIO = 0.1
    
    # OD流量预测特征配置
    OD_FEATURES = ['surface_distance', 'translate', 'time', 'wait_time']
    OD_FEATURE_DIM = 4
    
    @staticmethod
    def get_memory_info():
        """获取GPU内存信息"""
        if torch.cuda.is_available():
            current = torch.cuda.memory_allocated() / 1024**2  # MB
            max_memory = torch.cuda.max_memory_allocated() / 1024**2  # MB
            total = torch.cuda.get_device_properties(0).total_memory / 1024**2  # MB
            return f"GPU内存: {current:.1f}MB / {total:.1f}MB (峰值: {max_memory:.1f}MB)"
        return "使用CPU"
    
    @staticmethod
    def clear_gpu_cache():
        """清理GPU缓存"""
        if torch.cuda.is_available():
            torch.cuda.empty_cache() 