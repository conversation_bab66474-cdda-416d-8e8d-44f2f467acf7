import os
import torch
import torch.nn as nn
import pandas as pd
import geopandas as gpd
import numpy as np
import shap
import matplotlib.pyplot as plt
import pickle
from tqdm import tqdm
from torch.cuda.amp import autocast

# Set random seed and device
torch.manual_seed(42)
np.random.seed(42)
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"Using device: {device}")

# Data loading
def load_preprocessed_data(cache_dir="cache"):
    cache_file = os.path.join(cache_dir, "preprocessed_data_full.pkl")
    if not os.path.exists(cache_file):
        raise FileNotFoundError(f"Cached data not found at {cache_file}")
    with open(cache_file, 'rb') as f:
        data = pickle.load(f)
    return (data['in_gdf'], data['out_gdf'], data['od_df'], data['features_df'],
            data['station_encoder'], data['feature_cols'])

# Dataset class
class TrafficDataset(torch.utils.data.Dataset):
    def __init__(self, in_gdf, out_gdf, od_df, features_df, feature_cols):
        self.in_gdf = in_gdf
        self.out_gdf = out_gdf
        self.od_df = od_df
        self.features_df = features_df
        self.feature_cols = feature_cols
        
    def __len__(self):
        return len(self.in_gdf) + len(self.out_gdf) + len(self.od_df)
    
    def __getitem__(self, idx):
        if idx < len(self.in_gdf):
            row = self.in_gdf.iloc[idx]
            station_id = row['station_id']
            geo_features = self.features_df[self.features_df['station_id'] == station_id][self.feature_cols].values
            geo_features = geo_features[0] if len(geo_features) > 0 else np.zeros(len(self.feature_cols))
            return {
                'type': 'in',
                'station_id': torch.tensor(station_id, dtype=torch.long),
                'hour': torch.tensor([row['hour_sin'], row['hour_cos']], dtype=torch.float),
                'geo_features': torch.tensor(geo_features, dtype=torch.float),
                'count': torch.tensor(row['count'], dtype=torch.float),
                'index': idx
            }
        elif idx < len(self.in_gdf) + len(self.out_gdf):
            row = self.out_gdf.iloc[idx - len(self.in_gdf)]
            station_id = row['station_id']
            geo_features = self.features_df[self.features_df['station_id'] == station_id][self.feature_cols].values
            geo_features = geo_features[0] if len(geo_features) > 0 else np.zeros(len(self.feature_cols))
            return {
                'type': 'out',
                'station_id': torch.tensor(station_id, dtype=torch.long),
                'hour': torch.tensor([row['hour_sin'], row['hour_cos']], dtype=torch.float),
                'geo_features': torch.tensor(geo_features, dtype=torch.float),
                'count': torch.tensor(row['count'], dtype=torch.float),
                'index': idx - len(self.in_gdf)
            }
        else:
            row = self.od_df.iloc[idx - len(self.in_gdf) - len(self.out_gdf)]
            o_station_id = row['o_station_id']
            d_station_id = row['d_station_id']
            o_geo = self.features_df[self.features_df['station_id'] == o_station_id][self.feature_cols].values
            d_geo = self.features_df[self.features_df['station_id'] == d_station_id][self.feature_cols].values
            o_geo = o_geo[0] if len(o_geo) > 0 else np.zeros(len(self.feature_cols))
            d_geo = d_geo[0] if len(d_geo) > 0 else np.zeros(len(self.feature_cols))
            return {
                'type': 'od',
                'o_station_id': torch.tensor(o_station_id, dtype=torch.long),
                'd_station_id': torch.tensor(d_station_id, dtype=torch.long),
                'hour': torch.tensor([row['hour_sin'], row['hour_cos']], dtype=torch.float),
                'o_geo_features': torch.tensor(o_geo, dtype=torch.float),
                'd_geo_features': torch.tensor(d_geo, dtype=torch.float),
                'od_features': torch.tensor([
                    row['surface_distance'], row['translate'], row['time'], row['wait_time']
                ], dtype=torch.float),
                'trip': torch.tensor(row['trip'], dtype=torch.float),
                'index': idx - len(self.in_gdf) - len(self.out_gdf)
            }

def collate_fn(batch):
    in_batch = [x for x in batch if x['type'] == 'in']
    out_batch = [x for x in batch if x['type'] == 'out']
    od_batch = [x for x in batch if x['type'] == 'od']
    return in_batch, out_batch, od_batch

# TrafficModel without DGL
class TrafficModel(nn.Module):
    def __init__(self, num_stations, geo_feature_dim, station_dim=64, time_dim=2, hidden_dim=128):
        super().__init__()
        self.num_stations = num_stations
        self.station_dim = station_dim
        self.hidden_dim = hidden_dim
        self.time_dim = time_dim
        
        self.station_embed = nn.Embedding(num_stations, station_dim)
        self.geo_embed = nn.Linear(geo_feature_dim, hidden_dim)
        self.time_embed = nn.Linear(time_dim, time_dim)
        
        self.mlp_grid = nn.Sequential(
            nn.Linear(station_dim + hidden_dim + time_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2)
        )
        self.fc_grid = nn.Linear(hidden_dim, 1)
        
        self.dnn = nn.Sequential(
            nn.Linear(station_dim*2 + hidden_dim*2 + 4 + time_dim, 256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, hidden_dim)
        )
        self.attention = nn.MultiheadAttention(embed_dim=hidden_dim, num_heads=8)
        self.fc_od = nn.Linear(hidden_dim, 1)
        
    def forward(self, in_batch, out_batch, od_batch):
        count_pred_in, count_pred_out, trip_pred = [], [], []
        
        if in_batch:
            station_ids = torch.tensor([x['station_id'] for x in in_batch], dtype=torch.long, device=device)
            hours = torch.stack([x['hour'] for x in in_batch]).to(device)
            geo_features = torch.stack([x['geo_features'] for x in in_batch]).to(device)
            
            station_emb = self.station_embed(station_ids)
            geo_emb = self.geo_embed(geo_features)
            time_emb = self.time_embed(hours)
            combined = torch.cat([station_emb, geo_emb, time_emb], dim=-1)
            
            mlp_out = self.mlp_grid(combined)
            count_pred_in = torch.relu(self.fc_grid(mlp_out)).squeeze(-1)
            count_pred_in = torch.nan_to_num(count_pred_in, nan=0.0)
        
        if out_batch:
            station_ids = torch.tensor([x['station_id'] for x in out_batch], dtype=torch.long, device=device)
            hours = torch.stack([x['hour'] for x in out_batch]).to(device)
            geo_features = torch.stack([x['geo_features'] for x in out_batch]).to(device)
            
            station_emb = self.station_embed(station_ids)
            geo_emb = self.geo_embed(geo_features)
            time_emb = self.time_embed(hours)
            combined = torch.cat([station_emb, geo_emb, time_emb], dim=-1)
            
            mlp_out = self.mlp_grid(combined)
            count_pred_out = torch.relu(self.fc_grid(mlp_out)).squeeze(-1)
            count_pred_out = torch.nan_to_num(count_pred_out, nan=0.0)
        
        if od_batch:
            o_station_ids = torch.tensor([x['o_station_id'] for x in od_batch], dtype=torch.long, device=device)
            d_station_ids = torch.tensor([x['d_station_id'] for x in od_batch], dtype=torch.long, device=device)
            hours = torch.stack([x['hour'] for x in od_batch]).to(device)
            o_geo = torch.stack([x['o_geo_features'] for x in od_batch]).to(device)
            d_geo = torch.stack([x['d_geo_features'] for x in od_batch]).to(device)
            od_features = torch.stack([x['od_features'] for x in od_batch]).to(device)
            
            o_emb = self.station_embed(o_station_ids)
            d_emb = self.station_embed(d_station_ids)
            o_geo_emb = self.geo_embed(o_geo)
            d_geo_emb = self.geo_embed(d_geo)
            time_emb = self.time_embed(hours)
            
            combined = torch.cat([o_emb, d_emb, o_geo_emb, d_geo_emb, od_features, time_emb], dim=-1)
            dnn_out = self.dnn(combined)
            attn_out, _ = self.attention(dnn_out.unsqueeze(0), dnn_out.unsqueeze(0), dnn_out.unsqueeze(0))
            trip_pred = torch.relu(self.fc_od(attn_out.squeeze(0))).squeeze(-1)
            trip_pred = torch.nan_to_num(trip_pred, nan=0.0)
            trip_pred = torch.round(trip_pred)
        
        return count_pred_in, count_pred_out, trip_pred

# Training function
def train_model(model, train_loader, val_loader, epochs=3):
    optimizer = torch.optim.AdamW(model.parameters(), lr=1e-3, weight_decay=1e-5)
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=3, factor=0.5)
    best_loss = float('inf')
    
    for epoch in range(epochs):
        model.train()
        total_loss = 0
        count = 0
        for in_batch, out_batch, od_batch in tqdm(train_loader, desc=f"Epoch {epoch+1}"):
            optimizer.zero_grad()
            with autocast():
                count_pred_in, count_pred_out, trip_pred = model(in_batch, out_batch, od_batch)
                loss = 0
                if in_batch:
                    count_true_in = torch.stack([x['count'] for x in in_batch]).to(device)
                    loss += nn.MSELoss()(count_pred_in, count_true_in)
                if out_batch:
                    count_true_out = torch.stack([x['count'] for x in out_batch]).to(device)
                    loss += nn.MSELoss()(count_pred_out, count_true_out)
                if od_batch:
                    trip_true = torch.stack([x['trip'] for x in od_batch]).to(device)
                    loss += nn.MSELoss()(trip_pred, trip_true)
            if isinstance(loss, torch.Tensor):
                loss.backward()
                optimizer.step()
                total_loss += loss.item()
                count += 1
        avg_loss = total_loss / count if count > 0 else 0
        
        # Validation
        model.eval()
        val_loss = 0
        val_count = 0
        with torch.no_grad():
            for in_batch, out_batch, od_batch in val_loader:
                with autocast():
                    count_pred_in, count_pred_out, trip_pred = model(in_batch, out_batch, od_batch)
                    loss = 0
                    if in_batch:
                        count_true_in = torch.stack([x['count'] for x in in_batch]).to(device)
                        loss += nn.MSELoss()(count_pred_in, count_true_in)
                    if out_batch:
                        count_true_out = torch.stack([x['count'] for x in out_batch]).to(device)
                        loss += nn.MSELoss()(count_pred_out, count_true_out)
                    if od_batch:
                        trip_true = torch.stack([x['trip'] for x in od_batch]).to(device)
                        loss += nn.MSELoss()(trip_pred, trip_true)
                val_loss += loss.item()
                val_count += 1
        val_avg_loss = val_loss / val_count if val_count > 0 else 0
        
        print(f"Epoch {epoch+1}, Train Loss: {avg_loss:.4f}, Val Loss: {val_avg_loss:.4f}")
        scheduler.step(val_avg_loss)
        if val_avg_loss < best_loss:
            best_loss = val_avg_loss
            torch.save(model.state_dict(), 'new_model_no_dgl.pth')

# SHAP model wrapper
def model_predict(inputs, model, batch_type, feature_cols):
    model.eval()
    with torch.no_grad():
        if batch_type == 'in':
            in_batch = [{'station_id': inputs[i, 0].long(),
                         'hour': inputs[i, 1:3].float(),
                         'geo_features': inputs[i, 3:].float()} for i in range(inputs.shape[0])]
            out_batch, od_batch = [], []
        elif batch_type == 'out':
            out_batch = [{'station_id': inputs[i, 0].long(),
                          'hour': inputs[i, 1:3].float(),
                          'geo_features': inputs[i, 3:].float()} for i in range(inputs.shape[0])]
            in_batch, od_batch = [], []
        else:  # od
            od_batch = [{'o_station_id': inputs[i, 0].long(),
                         'd_station_id': inputs[i, 1].long(),
                         'hour': inputs[i, 2:4].float(),
                         'o_geo_features': inputs[i, 4:4+len(feature_cols)].float(),
                         'd_geo_features': inputs[i, 4+len(feature_cols):4+2*len(feature_cols)].float(),
                         'od_features': inputs[i, 4+2*len(feature_cols):].float()} for i in range(inputs.shape[0])]
            in_batch, out_batch = [], []
        
        count_pred_in, count_pred_out, trip_pred = model(in_batch, out_batch, od_batch)
        if batch_type == 'in':
            return count_pred_in.cpu().numpy()
        elif batch_type == 'out':
            return count_pred_out.cpu().numpy()
        else:
            return trip_pred.cpu().numpy()

# SHAP analysis
def perform_shap_analysis(model, test_loader, feature_cols):
    test_dataset = test_loader.dataset
    in_indices = np.random.choice(len(test_dataset.in_gdf), size=100, replace=False)
    out_indices = np.random.choice(len(test_dataset.out_gdf), size=100, replace=False)
    od_indices = np.random.choice(len(test_dataset.od_df), size=100, replace=False)
    
    background_in = [test_dataset[i] for i in np.random.choice(in_indices, size=50, replace=False)]
    background_out = [test_dataset[len(test_dataset.in_gdf) + i] for i in np.random.choice(out_indices, size=50, replace=False)]
    background_od = [test_dataset[len(test_dataset.in_gdf) + len(test_dataset.out_gdf) + i] for i in np.random.choice(od_indices, size=50, replace=False)]
    
    in_features = torch.stack([torch.cat([x['station_id'].float().unsqueeze(0), x['hour'], x['geo_features']]) for x in background_in]).to(device)
    out_features = torch.stack([torch.cat([x['station_id'].float().unsqueeze(0), x['hour'], x['geo_features']]) for x in background_out]).to(device)
    od_features = torch.stack([torch.cat([
        x['o_station_id'].float().unsqueeze(0),
        x['d_station_id'].float().unsqueeze(0),
        x['hour'],
        x['o_geo_features'],
        x['d_geo_features'],
        x['od_features']
    ]) for x in background_od]).to(device)
    
    in_feature_names = ['station_id', 'hour_sin', 'hour_cos'] + feature_cols
    od_feature_names = ['o_station_id', 'd_station_id', 'hour_sin', 'hour_cos'] + \
                       [f'o_{col}' for col in feature_cols] + [f'd_{col}' for col in feature_cols] + \
                       ['surface_distance', 'translate', 'time', 'wait_time']
    
    explainer_in = shap.DeepExplainer(lambda x: model_predict(x, model, 'in', feature_cols), in_features)
    explainer_out = shap.DeepExplainer(lambda x: model_predict(x, model, 'out', feature_cols), out_features)
    explainer_od = shap.DeepExplainer(lambda x: model_predict(x, model, 'od', feature_cols), od_features)
    
    shap_in = explainer_in.shap_values(in_features[:50])
    shap_out = explainer_out.shap_values(out_features[:50])
    shap_od = explainer_od.shap_values(od_features[:50])
    
    plt.figure(figsize=(10, 6))
    shap.summary_plot(shap_in, in_features[:50].cpu().numpy(), feature_names=in_feature_names, show=False)
    plt.savefig('shap_in_summary_no_dgl.png', bbox_inches='tight')
    plt.close()
    
    plt.figure(figsize=(10, 6))
    shap.summary_plot(shap_out, out_features[:50].cpu().numpy(), feature_names=in_feature_names, show=False)
    plt.savefig('shap_out_summary_no_dgl.png', bbox_inches='tight')
    plt.close()
    
    plt.figure(figsize=(10, 6))
    shap.summary_plot(shap_od, od_features[:50].cpu().numpy(), feature_names=od_feature_names, show=False)
    plt.savefig('shap_od_summary_no_dgl.png', bbox_inches='tight')
    plt.close()
    
    importance_in = pd.DataFrame({
        'feature': in_feature_names,
        'mean_abs_shap': np.mean(np.abs(shap_in), axis=0)
    }).sort_values('mean_abs_shap', ascending=False)
    importance_out = pd.DataFrame({
        'feature': in_feature_names,
        'mean_abs_shap': np.mean(np.abs(shap_out), axis=0)
    }).sort_values('mean_abs_shap', ascending=False)
    importance_od = pd.DataFrame({
        'feature': od_feature_names,
        'mean_abs_shap': np.mean(np.abs(shap_od), axis=0)
    }).sort_values('mean_abs_shap', ascending=False)
    
    importance_in.to_csv('shap_importance_in_no_dgl.csv', index=False)
    importance_out.to_csv('shap_importance_out_no_dgl.csv', index=False)
    importance_od.to_csv('shap_importance_od_no_dgl.csv', index=False)

# Main execution
if __name__ == "__main__":
    # Load data
    in_gdf, out_gdf, od_df, features_df, station_encoder, feature_cols = load_preprocessed_data()
    
    # Split data
    train_in = in_gdf.sample(frac=0.7, random_state=42)
    val_in = in_gdf.drop(train_in.index).sample(frac=0.5, random_state=42)
    test_in = in_gdf.drop(train_in.index).drop(val_in.index)
    
    train_out = out_gdf.sample(frac=0.7, random_state=42)
    val_out = out_gdf.drop(train_out.index).sample(frac=0.5, random_state=42)
    test_out = out_gdf.drop(train_out.index).drop(val_out.index)  # Fixed: val_in -> val_out
    
    train_od = od_df.sample(frac=0.7, random_state=42)
    val_od = od_df.drop(train_od.index).sample(frac=0.5, random_state=42)
    test_od = od_df.drop(train_od.index).drop(val_od.index)
    
    # Create datasets
    train_dataset = TrafficDataset(train_in, train_out, train_od, features_df, feature_cols)
    val_dataset = TrafficDataset(val_in, val_out, val_od, features_df, feature_cols)
    test_dataset = TrafficDataset(test_in, test_out, test_od, features_df, feature_cols)
    
    # Create data loaders
    train_loader = torch.utils.data.DataLoader(
        train_dataset, batch_size=256, shuffle=True, collate_fn=collate_fn, num_workers=4, pin_memory=True
    )
    val_loader = torch.utils.data.DataLoader(
        val_dataset, batch_size=256, shuffle=False, collate_fn=collate_fn, num_workers=4, pin_memory=True
    )
    test_loader = torch.utils.data.DataLoader(
        test_dataset, batch_size=256, shuffle=False, collate_fn=collate_fn, num_workers=4, pin_memory=True
    )
    
    # Initialize and train model
    model = TrafficModel(
        num_stations=len(station_encoder.classes_),
        geo_feature_dim=len(feature_cols)
    ).to(device)
    train_model(model, train_loader, val_loader)
    
    # Load best model
    model.load_state_dict(torch.load('new_model_no_dgl.pth'))
    
    # Perform SHAP analysis
    perform_shap_analysis(model, test_loader, feature_cols)
    print("SHAP analysis completed. Check shap_*_no_dgl.png and shap_importance_*_no_dgl.csv files.")
