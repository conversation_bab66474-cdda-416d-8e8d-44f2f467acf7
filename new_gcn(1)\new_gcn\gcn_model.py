import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import GCNConv, GATConv, GraphConv, BatchNorm
from torch_geometric.nn.pool import global_mean_pool
import numpy as np
from config import Config

class TemporalAttention(nn.Module):
    """时间注意力机制"""
    def __init__(self, hidden_dim):
        super(TemporalAttention, self).__init__()
        self.hidden_dim = hidden_dim
        self.attention = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, 1),
            nn.Sigmoid()
        )
    
    def forward(self, x):
        # x: [batch_size, seq_len, hidden_dim]
        attention_weights = self.attention(x)  # [batch_size, seq_len, 1]
        attended = x * attention_weights
        return attended, attention_weights

class SpatialTemporalEmbedding(nn.Module):
    """时空嵌入层"""
    def __init__(self, time_features, spatial_features, hidden_dim, use_float16=False):
        super(SpatialTemporalEmbedding, self).__init__()
        self.use_float16 = use_float16
        
        # 时间特征嵌入
        self.time_embedding = nn.Sequential(
            nn.Linear(time_features, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # 空间特征嵌入
        self.spatial_embedding = nn.Sequential(
            nn.Linear(spatial_features, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # 融合层
        self.fusion = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
    
    def forward(self, time_features, spatial_features):
        time_emb = self.time_embedding(time_features)
        spatial_emb = self.spatial_embedding(spatial_features)
        
        # 拼接时空特征
        combined = torch.cat([time_emb, spatial_emb], dim=-1)
        output = self.fusion(combined)
        
        if self.use_float16:
            output = output.half()
        
        return output

class GCNLayer(nn.Module):
    """增强的GCN层"""
    def __init__(self, in_dim, out_dim, use_attention=True, dropout=0.2, use_float16=False):
        super(GCNLayer, self).__init__()
        self.use_attention = use_attention
        self.use_float16 = use_float16
        
        if use_attention:
            self.conv = GATConv(in_dim, out_dim, heads=4, concat=False, dropout=dropout)
        else:
            self.conv = GCNConv(in_dim, out_dim)
        
        self.batch_norm = BatchNorm(out_dim)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x, edge_index, edge_attr=None):
        if self.use_attention:
            x = self.conv(x, edge_index)
        else:
            x = self.conv(x, edge_index, edge_attr)
        
        x = self.batch_norm(x)
        x = F.relu(x)
        x = self.dropout(x)
        
        if self.use_float16:
            x = x.half()
        
        return x

class ComplexGCNModel(nn.Module):
    """复杂的GCN模型，适合大规模数据处理"""
    def __init__(self, config):
        super(ComplexGCNModel, self).__init__()
        self.config = config
        self.hidden_dim = config.HIDDEN_DIM
        self.num_layers = config.NUM_LAYERS
        self.dropout = config.DROPOUT
        self.use_float16 = config.USE_FLOAT16
        
        # 时空嵌入层
        self.st_embedding = SpatialTemporalEmbedding(
            time_features=6,  # 时间特征维度
            spatial_features=51,  # 空间特征维度 (52-1站名)
            hidden_dim=self.hidden_dim,
            use_float16=self.use_float16
        )
        
        # 多层GCN
        self.gcn_layers = nn.ModuleList()
        
        # 第一层
        self.gcn_layers.append(GCNLayer(
            self.hidden_dim + 1,  # 嵌入维度 + 流量值
            self.hidden_dim,
            use_attention=True,
            dropout=self.dropout,
            use_float16=self.use_float16
        ))
        
        # 中间层
        for _ in range(self.num_layers - 2):
            self.gcn_layers.append(GCNLayer(
                self.hidden_dim,
                self.hidden_dim,
                use_attention=True,
                dropout=self.dropout,
                use_float16=self.use_float16
            ))
        
        # 最后一层
        self.gcn_layers.append(GCNLayer(
            self.hidden_dim,
            self.hidden_dim // 2,
            use_attention=False,
            dropout=self.dropout,
            use_float16=self.use_float16
        ))
        
        # 时间注意力
        self.temporal_attention = TemporalAttention(self.hidden_dim // 2)
        
        # 输出层
        self.output_layers = nn.ModuleDict({
            'in_flow': nn.Sequential(
                nn.Linear(self.hidden_dim // 2, self.hidden_dim // 4),
                nn.ReLU(),
                nn.Dropout(self.dropout),
                nn.Linear(self.hidden_dim // 4, 1)
            ),
            'out_flow': nn.Sequential(
                nn.Linear(self.hidden_dim // 2, self.hidden_dim // 4),
                nn.ReLU(),
                nn.Dropout(self.dropout),
                nn.Linear(self.hidden_dim // 4, 1)
            ),
            'od_flow': nn.Sequential(
                nn.Linear(self.hidden_dim, self.hidden_dim // 2),
                nn.ReLU(),
                nn.Dropout(self.dropout),
                nn.Linear(self.hidden_dim // 2, 1)
            )
        })
        
        # 残差连接
        self.residual_connections = nn.ModuleList([
            nn.Linear(self.hidden_dim, self.hidden_dim) 
            for _ in range(self.num_layers - 1)
        ])
        
    def forward(self, x, edge_index, edge_attr, time_features, spatial_features, task='in_flow'):
        batch_size, seq_len, num_nodes = x.shape
        
        # 时空嵌入
        st_emb = self.st_embedding(time_features, spatial_features)  # [batch_size, seq_len, num_nodes, hidden_dim]
        
        # 准备GCN输入
        outputs = []
        
        for t in range(seq_len):
            # 当前时间步的特征
            flow_t = x[:, t, :].unsqueeze(-1)  # [batch_size, num_nodes, 1]
            st_emb_t = st_emb[:, t, :, :]  # [batch_size, num_nodes, hidden_dim]
            
            # 合并流量和时空特征
            node_features = torch.cat([flow_t, st_emb_t], dim=-1)  # [batch_size, num_nodes, hidden_dim+1]
            
            # 展平batch维度用于GCN处理
            node_features_flat = node_features.view(-1, node_features.shape[-1])  # [batch_size*num_nodes, hidden_dim+1]
            
            # 调整edge_index用于批处理
            edge_index_batch = edge_index.clone()
            for b in range(1, batch_size):
                edge_index_batch = torch.cat([
                    edge_index_batch,
                    edge_index + b * num_nodes
                ], dim=1)
            
            # 通过GCN层
            h = node_features_flat
            for i, gcn_layer in enumerate(self.gcn_layers):
                h_new = gcn_layer(h, edge_index_batch, edge_attr)
                
                # 残差连接（除了最后一层）
                if i < len(self.gcn_layers) - 1:
                    if h.shape[-1] == h_new.shape[-1]:
                        h_new = h_new + h
                    else:
                        h_new = h_new + self.residual_connections[i](h)
                h = h_new
            
            # 重新整理成批次形式
            h = h.view(batch_size, num_nodes, -1)
            outputs.append(h)
        
        # 堆叠时间维度
        outputs = torch.stack(outputs, dim=1)  # [batch_size, seq_len, num_nodes, hidden_dim//2]
        
        # 时间注意力
        outputs_reshaped = outputs.view(-1, seq_len, outputs.shape[-1])  # [batch_size*num_nodes, seq_len, hidden_dim//2]
        attended_outputs, attention_weights = self.temporal_attention(outputs_reshaped)
        attended_outputs = attended_outputs.view(batch_size, seq_len, num_nodes, -1)
        
        # 时间聚合
        if task == 'od_flow':
            # 对于OD任务，需要两个节点的特征
            final_output = torch.mean(attended_outputs, dim=1)  # [batch_size, num_nodes, hidden_dim//2]
        else:
            # 对于进出站任务
            final_output = torch.mean(attended_outputs, dim=1)  # [batch_size, num_nodes, hidden_dim//2]
        
        # 输出预测
        if task in self.output_layers:
            if task == 'od_flow':
                # 对于OD流量，需要两个节点的特征拼接
                # 这里简化处理，实际应用中需要根据OD对进行处理
                output = self.output_layers[task](final_output)
            else:
                output = self.output_layers[task](final_output)
        else:
            raise ValueError(f"Unknown task: {task}")
        
        if self.use_float16:
            output = output.half()
        
        return output.squeeze(-1), attention_weights

class MultiTaskGCN(nn.Module):
    """多任务GCN模型"""
    def __init__(self, config):
        super(MultiTaskGCN, self).__init__()
        self.config = config
        
        # 共享的GCN编码器
        self.shared_encoder = ComplexGCNModel(config)
        
        # 任务特定的解码器
        self.task_decoders = nn.ModuleDict({
            'in_flow': nn.Sequential(
                nn.Linear(config.HIDDEN_DIM // 2, config.HIDDEN_DIM // 4),
                nn.ReLU(),
                nn.Dropout(config.DROPOUT),
                nn.Linear(config.HIDDEN_DIM // 4, 1)
            ),
            'out_flow': nn.Sequential(
                nn.Linear(config.HIDDEN_DIM // 2, config.HIDDEN_DIM // 4),
                nn.ReLU(),
                nn.Dropout(config.DROPOUT),
                nn.Linear(config.HIDDEN_DIM // 4, 1)
            ),
            'od_flow': nn.Sequential(
                nn.Linear(config.HIDDEN_DIM, config.HIDDEN_DIM // 2),
                nn.ReLU(),
                nn.Dropout(config.DROPOUT),
                nn.Linear(config.HIDDEN_DIM // 2, 1)
            )
        })
        
    def forward(self, x, edge_index, edge_attr, time_features, spatial_features, tasks=['in_flow']):
        # 获取共享特征表示
        shared_features, attention_weights = self.shared_encoder(
            x, edge_index, edge_attr, time_features, spatial_features, task='in_flow'
        )
        
        outputs = {}
        for task in tasks:
            if task in self.task_decoders:
                if task == 'od_flow':
                    # 对于OD任务，使用特殊处理
                    task_output = self.task_decoders[task](shared_features)
                else:
                    task_output = self.task_decoders[task](shared_features)
                outputs[task] = task_output.squeeze(-1)
        
        return outputs, attention_weights

def create_model(config, model_type='complex'):
    """创建模型的工厂函数"""
    if model_type == 'complex':
        return ComplexGCNModel(config)
    elif model_type == 'multitask':
        return MultiTaskGCN(config)
    else:
        raise ValueError(f"Unknown model type: {model_type}")

class ModelUtils:
    """模型工具类"""
    
    @staticmethod
    def count_parameters(model):
        """计算模型参数数量"""
        return sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    @staticmethod
    def print_model_info(model):
        """打印模型信息"""
        total_params = ModelUtils.count_parameters(model)
        print(f"模型总参数数量: {total_params:,}")
        
        # 计算模型大小（MB）
        param_size = sum(p.numel() * p.element_size() for p in model.parameters())
        buffer_size = sum(b.numel() * b.element_size() for b in model.buffers())
        model_size_mb = (param_size + buffer_size) / (1024 * 1024)
        print(f"模型大小: {model_size_mb:.2f} MB")
    
    @staticmethod
    def save_model(model, optimizer, epoch, loss, filepath):
        """保存模型"""
        torch.save({
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'loss': loss,
        }, filepath)
        print(f"模型已保存到: {filepath}")
    
    @staticmethod
    def load_model(model, optimizer, filepath, device):
        """加载模型"""
        checkpoint = torch.load(filepath, map_location=device)
        model.load_state_dict(checkpoint['model_state_dict'])
        optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        epoch = checkpoint['epoch']
        loss = checkpoint['loss']
        print(f"模型已从 {filepath} 加载，epoch: {epoch}, loss: {loss:.4f}")
        return epoch, loss 