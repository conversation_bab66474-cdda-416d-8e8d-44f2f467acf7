import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import GCNConv, GATConv, SAGEConv
from torch_geometric.data import Data
import numpy as np
import os

# 配置GPU设备
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

class TemporalAttention(nn.Module):
    """时间注意力模块"""
    def __init__(self, in_channels, out_channels):
        super(TemporalAttention, self).__init__()
        self.in_channels = in_channels
        self.out_channels = out_channels
        
        # 注意力权重
        self.W_1 = nn.Parameter(torch.Tensor(in_channels, out_channels))
        self.W_2 = nn.Parameter(torch.Tensor(in_channels, out_channels))
        self.W_3 = nn.Parameter(torch.Tensor(out_channels, 1))
        self.b_s = nn.Parameter(torch.Tensor(1, out_channels))
        self.b_t = nn.Parameter(torch.Tensor(1, 1))
        
        self.leaky_relu = nn.LeakyReLU(0.2)
        self.reset_parameters()
        
    def reset_parameters(self):
        """初始化参数"""
        nn.init.xavier_uniform_(self.W_1)
        nn.init.xavier_uniform_(self.W_2)
        nn.init.xavier_uniform_(self.W_3)
        nn.init.zeros_(self.b_s)
        nn.init.zeros_(self.b_t)
        
    def forward(self, x, batch_size, num_nodes, num_time_steps):
        """
        前向传播
        x: 输入特征 [batch_size * num_nodes * num_time_steps, in_channels]
        """
        # 确保所有张量在同一设备上
        x = x.to(device)
        self.W_1 = self.W_1.to(device)
        self.W_2 = self.W_2.to(device)
        self.W_3 = self.W_3.to(device)
        self.b_s = self.b_s.to(device)
        self.b_t = self.b_t.to(device)
        
        # 变形输入张量以进行注意力计算
        x = x.view(batch_size, num_nodes, num_time_steps, self.in_channels)
        
        # 计算注意力得分
        lhs = torch.matmul(x, self.W_1).view(batch_size, num_nodes, num_time_steps, self.out_channels)  # [b, n, t, c]
        rhs = torch.matmul(x, self.W_2).view(batch_size, num_nodes, num_time_steps, self.out_channels)  # [b, n, t, c]
        
        attention_scores = self.leaky_relu(lhs + rhs + self.b_s)  # [b, n, t, c]
        attention_scores = torch.matmul(attention_scores, self.W_3) + self.b_t  # [b, n, t, 1]
        
        # 应用softmax获取注意力权重
        attention_weights = F.softmax(attention_scores, dim=2)  # [b, n, t, 1]
        
        # 应用注意力权重
        output = torch.sum(attention_weights * x, dim=2)  # [b, n, c]
        
        return output, attention_weights
    
class SpatialAttention(nn.Module):
    """空间注意力模块"""
    def __init__(self, in_channels, out_channels):
        super(SpatialAttention, self).__init__()
        self.in_channels = in_channels
        self.out_channels = out_channels
        
        # 注意力权重
        self.W_1 = nn.Parameter(torch.Tensor(in_channels, out_channels))
        self.W_2 = nn.Parameter(torch.Tensor(in_channels, out_channels))
        self.W_3 = nn.Parameter(torch.Tensor(out_channels, 1))
        self.b_s = nn.Parameter(torch.Tensor(1, out_channels))
        self.b_t = nn.Parameter(torch.Tensor(1, 1))
        
        self.leaky_relu = nn.LeakyReLU(0.2)
        self.reset_parameters()
        
    def reset_parameters(self):
        """初始化参数"""
        nn.init.xavier_uniform_(self.W_1)
        nn.init.xavier_uniform_(self.W_2)
        nn.init.xavier_uniform_(self.W_3)
        nn.init.zeros_(self.b_s)
        nn.init.zeros_(self.b_t)
        
    def forward(self, x, edge_index):
        """
        前向传播
        x: 节点特征 [num_nodes, in_channels]
        edge_index: 边索引 [2, num_edges]
        """
        # 确保所有张量在同一设备上
        x = x.to(device)
        edge_index = edge_index.to(device)
        self.W_1 = self.W_1.to(device)
        self.W_2 = self.W_2.to(device)
        self.W_3 = self.W_3.to(device)
        self.b_s = self.b_s.to(device)
        self.b_t = self.b_t.to(device)
        
        # 获取源节点和目标节点索引
        source, target = edge_index
        
        # 计算注意力分数
        h_1 = torch.matmul(x, self.W_1)  # [num_nodes, out_channels]
        h_2 = torch.matmul(x, self.W_2)  # [num_nodes, out_channels]
        
        # 为每条边计算注意力得分
        source_h = torch.index_select(h_1, 0, source)  # [num_edges, out_channels]
        target_h = torch.index_select(h_2, 0, target)  # [num_edges, out_channels]
        
        attention_scores = self.leaky_relu(source_h + target_h + self.b_s)  # [num_edges, out_channels]
        attention_scores = torch.matmul(attention_scores, self.W_3) + self.b_t  # [num_edges, 1]
        
        # 对每个节点的所有相邻边应用softmax
        # 首先初始化一个全零张量
        attention_weights = torch.zeros_like(attention_scores)
        
        # 对每个唯一的源节点分别应用softmax
        for node in torch.unique(source):
            mask = (source == node)
            scores = attention_scores[mask]
            weights = F.softmax(scores, dim=0)
            attention_weights[mask] = weights
        
        return attention_weights
    
class STGCNLayer(nn.Module):
    """时空图卷积层"""
    def __init__(self, in_channels, out_channels, use_attention=True, use_edge_attr=False, edge_dim=None):
        super(STGCNLayer, self).__init__()
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.use_attention = use_attention
        self.use_edge_attr = use_edge_attr
        
        # 图卷积
        if use_edge_attr and edge_dim is not None:
            # 使用边特征的GAT卷积
            self.conv = GATConv(in_channels, out_channels, edge_dim=edge_dim).to(device)
        else:
            # 普通图卷积
            self.conv = GCNConv(in_channels, out_channels).to(device)
        
        # 时间注意力机制
        if use_attention:
            self.temporal_attention = TemporalAttention(in_channels, out_channels).to(device)
            self.spatial_attention = SpatialAttention(in_channels, out_channels).to(device)
        
        # 批归一化和跳跃连接
        self.batch_norm = nn.BatchNorm1d(out_channels).to(device)
        
        # 跳跃连接的线性映射
        self.skip_connection = nn.Linear(in_channels, out_channels).to(device)
        
    def forward(self, x, edge_index, edge_attr=None, batch_size=1, num_nodes=None, num_time_steps=None):
        """
        前向传播
        x: 节点特征
        edge_index: 边索引
        edge_attr: 边特征
        batch_size, num_nodes, num_time_steps: 用于时间注意力
        """
        # 确保所有输入都在同一设备上
        x = x.to(device)
        edge_index = edge_index.to(device)
        if edge_attr is not None:
            edge_attr = edge_attr.to(device)
            
        # 保存原始输入用于跳跃连接
        identity = x
        
        # 应用空间注意力（如果启用）
        if self.use_attention:
            # 获取空间注意力权重
            if num_nodes is not None and num_time_steps is not None:
                # 应用时间注意力
                x_temp, _ = self.temporal_attention(x, batch_size, num_nodes, num_time_steps)
                x_temp = x_temp.reshape(-1, self.in_channels)
            else:
                x_temp = x
            
            # 应用空间注意力
            attention_weights = self.spatial_attention(x_temp, edge_index)
            
            # 调整空间注意力的形状以匹配图卷积的需求
            edge_weight = attention_weights.view(-1)
            
            # 执行图卷积
            if self.use_edge_attr and edge_attr is not None:
                x = self.conv(x, edge_index, edge_attr=edge_attr)
            else:
                x = self.conv(x, edge_index, edge_weight=edge_weight)
        else:
            # 不使用注意力机制的普通图卷积
            if self.use_edge_attr and edge_attr is not None:
                x = self.conv(x, edge_index, edge_attr=edge_attr)
            else:
                x = self.conv(x, edge_index)
        
        # 批归一化
        x = self.batch_norm(x)
        
        # 跳跃连接
        x = x + self.skip_connection(identity)
        
        # 激活函数
        x = F.elu(x)
        
        return x

class InFlowPredictionModel(nn.Module):
    """进站流量预测模型"""
    def __init__(self, node_features, hidden_dim=64, out_dim=1, num_layers=3, dropout=0.1, use_attention=True, use_fp16=True):
        super(InFlowPredictionModel, self).__init__()
        self.node_features = node_features
        self.hidden_dim = hidden_dim
        self.out_dim = out_dim
        self.num_layers = num_layers
        self.dropout = dropout
        self.use_attention = use_attention
        self.dtype = torch.float16 if use_fp16 else torch.float32
        
        # 图卷积层
        self.graph_layers = nn.ModuleList()
        self.graph_layers.append(STGCNLayer(node_features, hidden_dim, use_attention).to(device))
        
        for i in range(1, num_layers):
            self.graph_layers.append(STGCNLayer(hidden_dim, hidden_dim, use_attention).to(device))
        
        # 线性预测层
        self.fc1 = nn.Linear(hidden_dim, hidden_dim // 2).to(device)
        self.fc2 = nn.Linear(hidden_dim // 2, out_dim).to(device)
        
        # Dropout层
        self.dropout_layer = nn.Dropout(dropout).to(device)
        
    def forward(self, data, batch_size=1, num_nodes=None, num_time_steps=None):
        """
        前向传播
        data: 图数据对象，包含 x, edge_index
        """
        # 确保数据在正确的设备上
        x, edge_index = data.x.to(device), data.edge_index.to(device)
        
        # 应用图卷积层
        for i, layer in enumerate(self.graph_layers):
            x = layer(x, edge_index, batch_size=batch_size, num_nodes=num_nodes, num_time_steps=num_time_steps)
            
            if i < len(self.graph_layers) - 1:  # 除了最后一层，其他层应用dropout
                x = self.dropout_layer(x)
        
        # 全连接层进行预测
        x = F.elu(self.fc1(x))
        x = self.dropout_layer(x)
        x = self.fc2(x)
        
        return x

class ODFlowPredictionModel(nn.Module):
    """OD流量预测模型"""
    def __init__(self, node_features, edge_features, hidden_dim=64, out_dim=1, num_layers=3, dropout=0.1, use_fp16=True):
        super(ODFlowPredictionModel, self).__init__()
        self.node_features = node_features
        self.edge_features = edge_features
        self.hidden_dim = hidden_dim
        self.out_dim = out_dim
        self.num_layers = num_layers
        self.dropout = dropout
        self.dtype = torch.float16 if use_fp16 else torch.float32
        
        # 图卷积层
        self.graph_layers = nn.ModuleList()
        self.graph_layers.append(STGCNLayer(node_features, hidden_dim, use_attention=True, use_edge_attr=True, edge_dim=edge_features).to(device))
        
        for i in range(1, num_layers):
            self.graph_layers.append(STGCNLayer(hidden_dim, hidden_dim, use_attention=True, use_edge_attr=True, edge_dim=edge_features).to(device))
        
        # 边特征处理
        self.edge_embedding = nn.Sequential(
            nn.Linear(edge_features, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim)
        ).to(device)
        
        # 节点对预测
        self.edge_predictor = nn.Sequential(
            nn.Linear(hidden_dim * 2 + hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, out_dim)
        ).to(device)
        
    def forward(self, data):
        """
        前向传播
        data: 图数据对象，包含 x, edge_index, edge_attr
        """
        # 确保数据在正确的设备上
        x = data.x.to(device)
        edge_index = data.edge_index.to(device)
        edge_attr = data.edge_attr.to(device)
        
        # 应用图卷积层
        for i, layer in enumerate(self.graph_layers):
            x = layer(x, edge_index, edge_attr=edge_attr)
        
        # 处理边特征
        edge_features = self.edge_embedding(edge_attr)
        
        # 获取源节点和目标节点的特征
        source_nodes, target_nodes = edge_index
        source_features = x[source_nodes]
        target_features = x[target_nodes]
        
        # 组合特征进行预测
        combined_features = torch.cat([source_features, target_features, edge_features], dim=1)
        predictions = self.edge_predictor(combined_features)
        
        return predictions

class OutFlowPredictionModel(nn.Module):
    """出站流量预测模型"""
    def __init__(self, node_features, hidden_dim=64, out_dim=1, num_layers=3, dropout=0.1, use_attention=True, use_fp16=True):
        super(OutFlowPredictionModel, self).__init__()
        self.node_features = node_features
        self.hidden_dim = hidden_dim
        self.out_dim = out_dim
        self.num_layers = num_layers
        self.dropout = dropout
        self.use_attention = use_attention
        self.dtype = torch.float16 if use_fp16 else torch.float32
        
        # 图卷积层
        self.graph_layers = nn.ModuleList()
        self.graph_layers.append(STGCNLayer(node_features, hidden_dim, use_attention).to(device))
        
        for i in range(1, num_layers):
            self.graph_layers.append(STGCNLayer(hidden_dim, hidden_dim, use_attention).to(device))
        
        # 线性预测层
        self.fc1 = nn.Linear(hidden_dim, hidden_dim // 2).to(device)
        self.fc2 = nn.Linear(hidden_dim // 2, out_dim).to(device)
        
        # Dropout层
        self.dropout_layer = nn.Dropout(dropout).to(device)
        
    def forward(self, data, batch_size=1, num_nodes=None, num_time_steps=None):
        """
        前向传播
        data: 图数据对象，包含 x, edge_index
        """
        # 确保数据在正确的设备上
        x, edge_index = data.x.to(device), data.edge_index.to(device)
        
        # 应用图卷积层
        for i, layer in enumerate(self.graph_layers):
            x = layer(x, edge_index, batch_size=batch_size, num_nodes=num_nodes, num_time_steps=num_time_steps)
            
            if i < len(self.graph_layers) - 1:  # 除了最后一层，其他层应用dropout
                x = self.dropout_layer(x)
        
        # 全连接层进行预测
        x = F.elu(self.fc1(x))
        x = self.dropout_layer(x)
        x = self.fc2(x)
        
        return x

class MetroFlowModel:
    """整合进站、出站和OD预测的综合模型"""
    def __init__(self, in_node_features, out_node_features, od_node_features, od_edge_features, 
                 hidden_dim=64, dropout=0.1, use_attention=True, use_fp16=True):
        self.use_fp16 = use_fp16
        self.dtype = torch.float16 if use_fp16 else torch.float32
        
        # 创建子模型
        self.in_model = InFlowPredictionModel(
            in_node_features, hidden_dim, out_dim=1, dropout=dropout, 
            use_attention=use_attention, use_fp16=use_fp16
        ).to(device)
        
        self.out_model = OutFlowPredictionModel(
            out_node_features, hidden_dim, out_dim=1, dropout=dropout, 
            use_attention=use_attention, use_fp16=use_fp16
        ).to(device)
        
        self.od_model = ODFlowPredictionModel(
            od_node_features, od_edge_features, hidden_dim, out_dim=1, dropout=dropout,
            use_fp16=use_fp16
        ).to(device)
        
        # 优化器
        self.in_optimizer = torch.optim.Adam(self.in_model.parameters(), lr=0.001)
        self.out_optimizer = torch.optim.Adam(self.out_model.parameters(), lr=0.001)
        self.od_optimizer = torch.optim.Adam(self.od_model.parameters(), lr=0.001)
        
        # 损失函数
        self.criterion = nn.MSELoss()
        
    def train_in_model(self, data, targets, batch_size=1, num_nodes=None, num_time_steps=None):
        """训练进站模型"""
        # 确保数据在正确的设备上
        targets = targets.to(device)
        
        self.in_model.train()
        self.in_optimizer.zero_grad()
        
        # 前向传播
        predictions = self.in_model(data, batch_size, num_nodes, num_time_steps)
        
        # 计算损失
        loss = self.criterion(predictions, targets)
        
        # 反向传播
        loss.backward()
        self.in_optimizer.step()
        
        # 计算MAE
        mae = torch.mean(torch.abs(predictions - targets))
        
        return loss.item(), mae.item()
    
    def train_out_model(self, data, targets, batch_size=1, num_nodes=None, num_time_steps=None):
        """训练出站模型"""
        # 确保数据在正确的设备上
        targets = targets.to(device)
        
        self.out_model.train()
        self.out_optimizer.zero_grad()
        
        # 前向传播
        predictions = self.out_model(data, batch_size, num_nodes, num_time_steps)
        
        # 计算损失
        loss = self.criterion(predictions, targets)
        
        # 反向传播
        loss.backward()
        self.out_optimizer.step()
        
        # 计算MAE
        mae = torch.mean(torch.abs(predictions - targets))
        
        return loss.item(), mae.item()
    
    def train_od_model(self, data, targets):
        """训练OD模型"""
        # 确保数据在正确的设备上
        targets = targets.to(device)
        
        self.od_model.train()
        self.od_optimizer.zero_grad()
        
        # 前向传播
        predictions = self.od_model(data)
        
        # 计算损失
        loss = self.criterion(predictions, targets)
        
        # 反向传播
        loss.backward()
        self.od_optimizer.step()
        
        # 计算MAE
        mae = torch.mean(torch.abs(predictions - targets))
        
        return loss.item(), mae.item()
    
    def predict_in_flow(self, data, batch_size=1, num_nodes=None, num_time_steps=None):
        """预测进站流量"""
        self.in_model.eval()
        with torch.no_grad():
            predictions = self.in_model(data, batch_size, num_nodes, num_time_steps)
        return predictions
    
    def predict_out_flow(self, data, batch_size=1, num_nodes=None, num_time_steps=None):
        """预测出站流量"""
        self.out_model.eval()
        with torch.no_grad():
            predictions = self.out_model(data, batch_size, num_nodes, num_time_steps)
        return predictions
    
    def predict_od_flow(self, data):
        """预测OD流量"""
        self.od_model.eval()
        with torch.no_grad():
            predictions = self.od_model(data)
        return predictions
    
    def save_models(self, path_prefix):
        """保存模型"""
        torch.save(self.in_model.state_dict(), f"{path_prefix}_in_model.pth")
        torch.save(self.out_model.state_dict(), f"{path_prefix}_out_model.pth")
        torch.save(self.od_model.state_dict(), f"{path_prefix}_od_model.pth")
    
    def load_models(self, path_prefix):
        """加载模型"""
        # 检查文件是否存在
        in_model_path = f"{path_prefix}_in_model.pth"
        out_model_path = f"{path_prefix}_out_model.pth"
        od_model_path = f"{path_prefix}_od_model.pth"
        
        # 使用os.path.normpath确保路径分隔符一致
        in_model_path = os.path.normpath(in_model_path)
        out_model_path = os.path.normpath(out_model_path)
        od_model_path = os.path.normpath(od_model_path)
        
        if os.path.exists(in_model_path):
            self.in_model.load_state_dict(torch.load(in_model_path, map_location=device))
        else:
            print(f"警告: 进站模型文件不存在: {in_model_path}")
            
        if os.path.exists(out_model_path):
            self.out_model.load_state_dict(torch.load(out_model_path, map_location=device))
        else:
            print(f"警告: 出站模型文件不存在: {out_model_path}")
            
        if os.path.exists(od_model_path):
            self.od_model.load_state_dict(torch.load(od_model_path, map_location=device))
        else:
            print(f"警告: OD模型文件不存在: {od_model_path}") 