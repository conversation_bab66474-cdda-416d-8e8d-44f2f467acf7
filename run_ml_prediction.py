"""
完整的机器学习地铁流量预测系统
基于真正的深度学习模型，支持时序预测和时空模式学习
"""
import os
import sys
import time
import numpy as np
import pandas as pd
import geopandas as gpd
import torch
import torch.nn as nn
import torch.optim as optim
from torch_geometric.nn import GCNConv
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')

class TemporalGCNModel(nn.Module):
    """时空图卷积神经网络模型"""
    
    def __init__(self, input_dim, hidden_dim=128, num_layers=3, dropout=0.3, 
                 sequence_length=12, prediction_horizon=6):
        super(TemporalGCNModel, self).__init__()
        
        self.hidden_dim = hidden_dim
        self.sequence_length = sequence_length
        self.prediction_horizon = prediction_horizon
        
        # 时间编码器
        self.time_embedding = nn.Embedding(24, 16)  # 24小时
        self.period_embedding = nn.Embedding(4, 8)  # 4个时间段
        
        # 空间GCN层
        self.gcn_layers = nn.ModuleList()
        self.gcn_layers.append(GCNConv(input_dim + 24, hidden_dim))  # +24 for time features
        
        for _ in range(num_layers - 1):
            self.gcn_layers.append(GCNConv(hidden_dim, hidden_dim))
        
        # 时序建模
        self.lstm = nn.LSTM(hidden_dim, hidden_dim, num_layers=2, 
                           dropout=dropout, batch_first=True)
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(hidden_dim, num_heads=8, dropout=dropout)
        
        # 预测头
        self.flow_predictor = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, prediction_horizon * 2)  # 进站+出站
        )
        
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x, edge_index, edge_weight, hours):
        """
        Args:
            x: [batch_size, sequence_length, num_nodes, feature_dim]
            edge_index: [2, num_edges]
            edge_weight: [batch_size, sequence_length, num_edges]
            hours: [batch_size, sequence_length]
        """
        batch_size, seq_len, num_nodes, feature_dim = x.shape
        
        # 时空特征融合
        temporal_embeddings = []
        
        for t in range(seq_len):
            # 时间特征
            hour_t = hours[:, t]  # [batch_size]
            time_embed = self.time_embedding(hour_t)  # [batch_size, 16]
            
            # 时间段特征
            period = torch.zeros_like(hour_t)
            period[(hour_t >= 6) & (hour_t < 10)] = 1  # 早高峰
            period[(hour_t >= 17) & (hour_t < 20)] = 2  # 晚高峰
            period[(hour_t >= 10) & (hour_t < 17)] = 3  # 平峰
            
            period_embed = self.period_embedding(period)  # [batch_size, 8]
            
            # 合并时间特征
            time_features = torch.cat([time_embed, period_embed], dim=-1)  # [batch_size, 24]
            
            # 扩展到所有节点
            time_features = time_features.unsqueeze(1).expand(-1, num_nodes, -1)  # [batch_size, num_nodes, 24]
            
            # 合并空间和时间特征
            x_t = x[:, t]  # [batch_size, num_nodes, feature_dim]
            combined_features = torch.cat([x_t, time_features], dim=-1)  # [batch_size, num_nodes, feature_dim+24]
            
            # 处理每个batch
            batch_embeddings = []
            for b in range(batch_size):
                node_features = combined_features[b]  # [num_nodes, feature_dim+24]
                edge_w = edge_weight[b, t] if edge_weight is not None else None
                
                # GCN层
                h = node_features
                for gcn_layer in self.gcn_layers:
                    h = gcn_layer(h, edge_index, edge_w)
                    h = torch.relu(h)
                    h = self.dropout(h)
                
                batch_embeddings.append(h)
            
            # 堆叠batch
            spatial_embeddings = torch.stack(batch_embeddings, dim=0)  # [batch_size, num_nodes, hidden_dim]
            temporal_embeddings.append(spatial_embeddings)
        
        # 时序建模
        temporal_embeddings = torch.stack(temporal_embeddings, dim=1)  # [batch_size, seq_len, num_nodes, hidden_dim]
        
        # 重塑为LSTM输入格式
        lstm_input = temporal_embeddings.view(batch_size * num_nodes, seq_len, self.hidden_dim)
        
        # LSTM处理
        lstm_out, _ = self.lstm(lstm_input)  # [batch_size*num_nodes, seq_len, hidden_dim]
        
        # 使用最后一个时间步
        final_embeddings = lstm_out[:, -1, :]  # [batch_size*num_nodes, hidden_dim]
        
        # 重塑回原始形状
        final_embeddings = final_embeddings.view(batch_size, num_nodes, self.hidden_dim)
        
        # 预测
        predictions = self.flow_predictor(final_embeddings)  # [batch_size, num_nodes, prediction_horizon*2]
        
        # 分离进出站预测
        in_flow = predictions[:, :, :self.prediction_horizon]
        out_flow = predictions[:, :, self.prediction_horizon:]
        
        return {
            'in_flow': in_flow,
            'out_flow': out_flow,
            'embeddings': final_embeddings
        }

class MetroFlowMLPredictor:
    """机器学习地铁流量预测器"""
    
    def __init__(self, sequence_length=12, prediction_horizon=6, hidden_dim=128):
        self.sequence_length = sequence_length
        self.prediction_horizon = prediction_horizon
        self.hidden_dim = hidden_dim
        
        # 设备
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
        # 数据预处理器
        self.flow_scaler = StandardScaler()
        self.feature_scaler = StandardScaler()
        
        # 模型
        self.model = None
        
        # 数据
        self.stations = []
        self.station_to_idx = {}
        
    def load_and_prepare_data(self):
        """加载和准备数据"""
        print("="*60)
        print("加载和准备数据")
        print("="*60)
        
        # 加载数据
        print("加载进出站数据...")
        try:
            in_data = gpd.read_file('in_500_with_coords.shp')
            out_data = gpd.read_file('out_500_with_coords.shp')
            
            # 提取坐标
            in_data['longitude'] = in_data.geometry.centroid.x
            in_data['latitude'] = in_data.geometry.centroid.y
            out_data['longitude'] = out_data.geometry.centroid.x
            out_data['latitude'] = out_data.geometry.centroid.y
            
            print(f"进站数据: {in_data.shape}")
            print(f"出站数据: {out_data.shape}")
            
        except Exception as e:
            print(f"加载shapefile失败: {e}")
            return False
        
        # 清理站点名称
        in_data['station_clean'] = in_data['station'].apply(lambda x: str(x).split('_')[0])
        out_data['station_clean'] = out_data['station'].apply(lambda x: str(x).split('_')[0])
        
        # 获取站点列表
        self.stations = sorted(list(set(in_data['station_clean'].unique()) | 
                                   set(out_data['station_clean'].unique())))
        self.station_to_idx = {station: idx for idx, station in enumerate(self.stations)}
        
        print(f"发现 {len(self.stations)} 个唯一站点")
        
        # 加载连接数据
        print("加载连接数据...")
        try:
            connect_data = pd.read_csv('station_connect_2023.csv')
            connect_data['station_1_clean'] = connect_data['station_1'].apply(lambda x: str(x).split('_')[0])
            connect_data['station_2_clean'] = connect_data['station_2'].apply(lambda x: str(x).split('_')[0])
            print(f"连接数据: {connect_data.shape}")
        except Exception as e:
            print(f"加载连接数据失败: {e}")
            connect_data = None
        
        # 加载栅格特征
        print("加载栅格特征...")
        try:
            grid_data = pd.read_csv('leti_data.csv')
            feature_cols = [col for col in grid_data.columns if col not in ['站名', 'id', 'geometry']]
            grid_features = grid_data[feature_cols].values
            
            # 标准化特征
            grid_features = self.feature_scaler.fit_transform(grid_features)
            print(f"栅格特征: {grid_features.shape}")
            
        except Exception as e:
            print(f"加载栅格特征失败: {e}")
            grid_features = np.random.normal(0, 0.1, (len(self.stations), 51))
        
        # 构建时序数据
        print("构建时序数据...")
        sequences = self._build_sequences(in_data, out_data, connect_data, grid_features)
        
        if len(sequences) == 0:
            print("未能构建有效的时序数据")
            return False
        
        print(f"构建了 {len(sequences)} 个时序序列")
        
        # 数据分割
        train_sequences, test_sequences = train_test_split(
            sequences, test_size=0.2, random_state=42
        )
        
        self.train_sequences = train_sequences
        self.test_sequences = test_sequences
        
        print(f"训练序列: {len(train_sequences)}")
        print(f"测试序列: {len(test_sequences)}")
        
        return True
    
    def _build_sequences(self, in_data, out_data, connect_data, grid_features):
        """构建时序序列"""
        sequences = []
        
        # 构建图结构
        edge_index = self._build_graph(connect_data)
        
        # 构建每小时的节点特征
        node_features_by_hour = {}
        
        for hour in range(24):
            # 初始化特征矩阵
            features = np.zeros((len(self.stations), grid_features.shape[1] + 2))  # +2 for flows
            
            # 填充栅格特征
            avg_grid_features = np.mean(grid_features, axis=0)
            features[:, 2:] = avg_grid_features
            
            # 填充流量数据
            hour_in = in_data[in_data['hour'] == hour]
            hour_out = out_data[out_data['hour'] == hour]
            
            for _, row in hour_in.iterrows():
                station = row['station_clean']
                if station in self.station_to_idx:
                    idx = self.station_to_idx[station]
                    features[idx, 0] = row['count']
            
            for _, row in hour_out.iterrows():
                station = row['station_clean']
                if station in self.station_to_idx:
                    idx = self.station_to_idx[station]
                    features[idx, 1] = row['count']
            
            node_features_by_hour[hour] = features
        
        # 生成序列
        hours = list(range(24))
        for start_idx in range(len(hours) - self.sequence_length - self.prediction_horizon + 1):
            hist_hours = hours[start_idx:start_idx + self.sequence_length]
            target_hours = hours[start_idx + self.sequence_length:start_idx + self.sequence_length + self.prediction_horizon]
            
            # 历史特征
            hist_features = np.stack([node_features_by_hour[h] for h in hist_hours])
            
            # 目标流量
            target_flows = np.stack([node_features_by_hour[h][:, :2] for h in target_hours])
            
            sequences.append({
                'features': hist_features,  # [seq_len, num_nodes, feature_dim]
                'hours': np.array(hist_hours),
                'target_flows': target_flows,  # [pred_horizon, num_nodes, 2]
                'edge_index': edge_index
            })
        
        return sequences
    
    def _build_graph(self, connect_data):
        """构建图结构"""
        edges = []
        
        if connect_data is not None:
            for _, row in connect_data.iterrows():
                station1 = row['station_1_clean']
                station2 = row['station_2_clean']
                
                if station1 in self.station_to_idx and station2 in self.station_to_idx:
                    idx1 = self.station_to_idx[station1]
                    idx2 = self.station_to_idx[station2]
                    edges.extend([[idx1, idx2], [idx2, idx1]])
        
        if not edges:
            # 创建完全图作为备选
            for i in range(len(self.stations)):
                for j in range(i+1, len(self.stations)):
                    edges.extend([[i, j], [j, i]])
        
        return torch.tensor(edges).T.long()
    
    def train_model(self, epochs=50, batch_size=8, learning_rate=0.001):
        """训练模型"""
        print("="*60)
        print("训练深度学习模型")
        print("="*60)
        
        # 初始化模型
        feature_dim = self.train_sequences[0]['features'].shape[-1]
        self.model = TemporalGCNModel(
            input_dim=feature_dim,
            hidden_dim=self.hidden_dim,
            sequence_length=self.sequence_length,
            prediction_horizon=self.prediction_horizon
        ).to(self.device)
        
        print(f"模型参数量: {sum(p.numel() for p in self.model.parameters()):,}")
        
        # 优化器和损失函数
        optimizer = optim.Adam(self.model.parameters(), lr=learning_rate, weight_decay=1e-5)
        criterion = nn.MSELoss()
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
        
        # 训练循环
        self.model.train()
        train_losses = []
        
        for epoch in range(epochs):
            epoch_loss = 0
            num_batches = 0
            
            # 简化批处理
            for i in range(0, len(self.train_sequences), batch_size):
                batch_sequences = self.train_sequences[i:i+batch_size]
                
                if len(batch_sequences) < 2:  # 跳过太小的批次
                    continue
                
                # 准备批次数据
                batch_features = torch.stack([torch.tensor(seq['features'], dtype=torch.float32) 
                                            for seq in batch_sequences]).to(self.device)
                batch_hours = torch.stack([torch.tensor(seq['hours'], dtype=torch.long) 
                                         for seq in batch_sequences]).to(self.device)
                batch_targets = torch.stack([torch.tensor(seq['target_flows'], dtype=torch.float32) 
                                           for seq in batch_sequences]).to(self.device)
                edge_index = batch_sequences[0]['edge_index'].to(self.device)
                
                # 前向传播
                predictions = self.model(batch_features, edge_index, None, batch_hours)
                
                # 计算损失
                target_in = batch_targets[:, :, :, 0]  # [batch, pred_horizon, num_nodes]
                target_out = batch_targets[:, :, :, 1]
                
                loss = (criterion(predictions['in_flow'], target_in) + 
                       criterion(predictions['out_flow'], target_out)) / 2
                
                # 反向传播
                optimizer.zero_grad()
                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                optimizer.step()
                
                epoch_loss += loss.item()
                num_batches += 1
            
            avg_loss = epoch_loss / max(num_batches, 1)
            train_losses.append(avg_loss)
            scheduler.step(avg_loss)
            
            if epoch % 10 == 0:
                print(f"Epoch {epoch:3d}: Loss = {avg_loss:.6f}")
        
        print("训练完成！")
        return train_losses
    
    def evaluate_model(self):
        """评估模型"""
        print("="*60)
        print("评估模型性能")
        print("="*60)
        
        self.model.eval()
        
        all_predictions_in = []
        all_predictions_out = []
        all_targets_in = []
        all_targets_out = []
        
        with torch.no_grad():
            for i in range(0, len(self.test_sequences), 4):  # 小批次评估
                batch_sequences = self.test_sequences[i:i+4]
                
                if len(batch_sequences) < 2:
                    continue
                
                # 准备数据
                batch_features = torch.stack([torch.tensor(seq['features'], dtype=torch.float32) 
                                            for seq in batch_sequences]).to(self.device)
                batch_hours = torch.stack([torch.tensor(seq['hours'], dtype=torch.long) 
                                         for seq in batch_sequences]).to(self.device)
                batch_targets = torch.stack([torch.tensor(seq['target_flows'], dtype=torch.float32) 
                                           for seq in batch_sequences]).to(self.device)
                edge_index = batch_sequences[0]['edge_index'].to(self.device)
                
                # 预测
                predictions = self.model(batch_features, edge_index, None, batch_hours)
                
                # 收集结果
                all_predictions_in.append(predictions['in_flow'].cpu().numpy())
                all_predictions_out.append(predictions['out_flow'].cpu().numpy())
                all_targets_in.append(batch_targets[:, :, :, 0].cpu().numpy())
                all_targets_out.append(batch_targets[:, :, :, 1].cpu().numpy())
        
        # 合并结果
        pred_in = np.concatenate(all_predictions_in).flatten()
        pred_out = np.concatenate(all_predictions_out).flatten()
        target_in = np.concatenate(all_targets_in).flatten()
        target_out = np.concatenate(all_targets_out).flatten()
        
        # 计算指标
        metrics = {
            'in_flow': {
                'mae': mean_absolute_error(target_in, pred_in),
                'rmse': np.sqrt(mean_squared_error(target_in, pred_in)),
                'r2': r2_score(target_in, pred_in)
            },
            'out_flow': {
                'mae': mean_absolute_error(target_out, pred_out),
                'rmse': np.sqrt(mean_squared_error(target_out, pred_out)),
                'r2': r2_score(target_out, pred_out)
            }
        }
        
        # 整体指标
        all_pred = np.concatenate([pred_in, pred_out])
        all_target = np.concatenate([target_in, target_out])
        
        metrics['overall'] = {
            'mae': mean_absolute_error(all_target, all_pred),
            'rmse': np.sqrt(mean_squared_error(all_target, all_pred)),
            'r2': r2_score(all_target, all_pred)
        }
        
        return metrics, (pred_in, pred_out, target_in, target_out)
    
    def save_results(self, metrics, predictions):
        """保存结果"""
        print("保存预测结果...")
        
        pred_in, pred_out, target_in, target_out = predictions
        
        # 保存比较结果
        comparison_data = []
        
        for i in range(len(pred_in)):
            comparison_data.append({
                'Type': 'In',
                'True_Value': float(target_in[i]),
                'Predicted_Value': float(pred_in[i])
            })
        
        for i in range(len(pred_out)):
            comparison_data.append({
                'Type': 'Out',
                'True_Value': float(target_out[i]),
                'Predicted_Value': float(pred_out[i])
            })
        
        comparison_df = pd.DataFrame(comparison_data)
        comparison_df.to_csv('ml_prediction_comparison.csv', index=False)
        
        # 保存指标
        import json
        with open('ml_metrics.json', 'w') as f:
            json.dump(metrics, f, indent=2)
        
        print("✓ 结果已保存到 ml_prediction_comparison.csv 和 ml_metrics.json")

def main():
    """主函数"""
    print("="*80)
    print("深度学习地铁流量预测系统")
    print("基于时空图卷积神经网络 (Temporal GCN)")
    print("="*80)
    
    # 检查PyTorch
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    
    start_time = time.time()
    
    try:
        # 初始化预测器
        predictor = MetroFlowMLPredictor(
            sequence_length=6,  # 使用6小时历史
            prediction_horizon=3,  # 预测未来3小时
            hidden_dim=64  # 较小的隐藏维度
        )
        
        # 加载数据
        if not predictor.load_and_prepare_data():
            print("数据加载失败")
            return False
        
        # 训练模型
        train_losses = predictor.train_model(
            epochs=30,
            batch_size=4,
            learning_rate=0.001
        )
        
        # 评估模型
        metrics, predictions = predictor.evaluate_model()
        
        # 保存结果
        predictor.save_results(metrics, predictions)
        
        # 打印结果
        print("\n" + "="*60)
        print("深度学习预测结果")
        print("="*60)
        
        print(f"模型架构:")
        print(f"  - 序列长度: {predictor.sequence_length}")
        print(f"  - 预测时长: {predictor.prediction_horizon}")
        print(f"  - 隐藏维度: {predictor.hidden_dim}")
        print(f"  - 站点数量: {len(predictor.stations)}")
        
        print(f"\n预测性能:")
        for flow_type in ['in_flow', 'out_flow', 'overall']:
            m = metrics[flow_type]
            print(f"  {flow_type.upper()}:")
            print(f"    MAE:  {m['mae']:.4f}")
            print(f"    RMSE: {m['rmse']:.4f}")
            print(f"    R²:   {m['r2']:.4f}")
        
        end_time = time.time()
        print(f"\n总运行时间: {end_time - start_time:.2f} 秒")
        print("="*60)
        
        return True
        
    except Exception as e:
        print(f"错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
