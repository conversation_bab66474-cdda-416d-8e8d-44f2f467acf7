#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
GCN地铁流量预测系统主程序
使用纯GCN+时间特征算法进行多任务预测
"""

import os
import sys
import warnings
import argparse
import traceback
from datetime import datetime

import torch
import numpy as np
import pandas as pd

# 抑制警告
warnings.filterwarnings('ignore')

# 导入项目模块
from config import Config
from data_loader import DataLoader
from graph_builder import GraphBuilder
from gcn_model import create_model, ModelUtils
from trainer import GCNTrainer
from predictor import GCNPredictor

class GCNFlowPredictionSystem:
    """GCN流量预测系统主类"""
    
    def __init__(self, config_path=None):
        """初始化系统"""
        self.config = Config()
        self.data_loader = DataLoader()
        self.graph_builder = GraphBuilder()
        self.trainer = GCNTrainer(self.config)
        self.predictor = GCNPredictor(self.config)
        
        # 数据存储
        self.data_dict = None
        self.graph_data = None
        
        print("="*60)
        print("GCN地铁流量预测系统")
        print("="*60)
        print(f"设备: {self.config.DEVICE}")
        print(f"使用Float16: {self.config.USE_FLOAT16}")
        print(f"训练轮数: {self.config.EPOCHS}")
        print(f"早停耐心: {self.config.PATIENCE}")
        print("="*60)
    
    def load_and_preprocess_data(self):
        """加载和预处理所有数据"""
        print("\n步骤1: 加载和预处理数据")
        print("-" * 40)
        
        try:
            # 加载所有数据
            self.data_dict = self.data_loader.load_all_data()
            
            # 构建图
            print("\n正在构建图网络...")
            edge_index, edge_attr, stations = self.graph_builder.build_graph(
                self.data_dict['station_coords'], 
                self.data_dict['od_data'],
                graph_type='hybrid'
            )
            
            # 分析图属性
            graph_properties = self.graph_builder.analyze_graph_properties(edge_index, stations)
            
            # 存储图数据
            self.graph_data = {
                'edge_index': edge_index,
                'edge_attr': edge_attr,
                'stations': stations,
                'properties': graph_properties
            }
            
            print(f"✓ 数据加载完成")
            print(f"✓ 图构建完成 - {graph_properties['num_nodes']}个节点，{graph_properties['num_edges']}条边")
            
            return True
            
        except Exception as e:
            print(f"✗ 数据加载失败: {str(e)}")
            traceback.print_exc()
            return False
    
    def prepare_station_features(self):
        """准备站点特征矩阵"""
        print("\n正在准备站点特征...")
        
        station_features_df = self.data_dict['station_features']
        common_stations = self.data_dict['common_stations']
        
        # 创建特征矩阵
        feature_cols = [col for col in station_features_df.columns if col != '站名']
        features_matrix = np.zeros((len(common_stations), len(feature_cols)))
        
        for i, station in enumerate(common_stations):
            station_row = station_features_df[station_features_df['站名'] == station]
            if not station_row.empty:
                features_matrix[i] = station_row[feature_cols].values[0]
        
        if self.config.USE_FLOAT16:
            features_matrix = features_matrix.astype(np.float16)
        
        print(f"✓ 站点特征矩阵形状: {features_matrix.shape}")
        return features_matrix
    
    def train_models(self):
        """训练所有任务的模型"""
        print("\n步骤2: 训练模型")
        print("-" * 40)
        
        try:
            # 准备特征
            spatial_features = self.prepare_station_features()
            
            # 为每个任务训练模型
            trained_models = {}
            
            for task in ['in_flow', 'out_flow']:
                print(f"\n训练 {task} 模型...")
                
                # 创建模型
                model = create_model(self.config, 'complex')
                ModelUtils.print_model_info(model)
                
                # 准备训练数据
                if task == 'in_flow':
                    flow_data = self.data_dict['in_flow']
                    time_features = self.data_dict['in_time_features']
                else:
                    flow_data = self.data_dict['out_flow']
                    time_features = self.data_dict['out_time_features']
                
                # 准备张量数据
                train_data = self.trainer.prepare_data_tensors(
                    flow_data, time_features, spatial_features,
                    self.graph_data['edge_index'], self.graph_data['edge_attr'],
                    self.data_dict['common_stations']
                )
                
                # 简单的训练/验证分割
                train_size = int(len(flow_data) * 0.8)
                val_size = len(flow_data) - train_size
                
                train_tensors = tuple(tensor[:train_size] for tensor in train_data)
                val_tensors = tuple(tensor[train_size:] for tensor in train_data)
                
                # 训练模型
                trained_model = self.trainer.train_model(model, train_tensors, val_tensors, task)
                trained_models[task] = trained_model
                
                # 保存模型
                model_path = f"model_{task}.pth"
                torch.save(trained_model.state_dict(), model_path)
                print(f"✓ {task} 模型已保存到 {model_path}")
            
            print("\n✓ 所有模型训练完成")
            return trained_models
            
        except Exception as e:
            print(f"✗ 模型训练失败: {str(e)}")
            traceback.print_exc()
            return None
    
    def perform_predictions(self, models):
        """执行各种预测任务"""
        print("\n步骤3: 执行预测")
        print("-" * 40)
        
        try:
            all_predictions = {}
            
            # 1. 加载模型到预测器
            for task, model in models.items():
                self.predictor.models[task] = model
            
            # 2. 栅格到站点流量预测
            print("\n正在预测栅格到站点流量...")
            
            # 模拟栅格坐标（实际使用时需要从shp文件提取）
            grid_coords = {}
            for i in range(100):  # 模拟100个栅格
                grid_coords[f'grid_{i}'] = (
                    116.0 + np.random.uniform(-0.5, 0.5),  # 北京经度范围
                    39.5 + np.random.uniform(-0.5, 0.5)    # 北京纬度范围
                )
            
            grid_to_station_pred = self.predictor.predict_grid_to_station_flow(
                grid_coords, self.data_dict['station_coords'],
                threshold=5000  # 5km阈值
            )
            all_predictions['grid_to_station'] = grid_to_station_pred
            
            # 3. 站点到栅格流量预测
            print("\n正在预测站点到栅格流量...")
            station_to_grid_pred = self.predictor.predict_station_to_grid_flow(
                self.data_dict['station_coords'], grid_coords,
                threshold=5000
            )
            all_predictions['station_to_grid'] = station_to_grid_pred
            
            # 4. 新线路影响预测（示例）
            print("\n正在预测新线路影响...")
            new_stations = ['新站点A', '新站点B', '新站点C']
            new_connections = [
                ('新站点A', '新站点B', 1.0),
                ('新站点B', '新站点C', 1.0),
                # 这里还需要添加与现有网络的连接
            ]
            
            try:
                new_line_impact = self.predictor.predict_new_line_impact(
                    self.data_dict, new_stations, new_connections
                )
                all_predictions['new_line_impact'] = new_line_impact
            except Exception as e:
                print(f"新线路影响预测失败: {str(e)}")
            
            print("\n✓ 所有预测任务完成")
            return all_predictions
            
        except Exception as e:
            print(f"✗ 预测失败: {str(e)}")
            traceback.print_exc()
            return None
    
    def save_results(self, predictions):
        """保存所有结果"""
        print("\n步骤4: 保存结果")
        print("-" * 40)
        
        try:
            # 创建输出目录
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_dir = f"results_{timestamp}"
            os.makedirs(output_dir, exist_ok=True)
            
            # 保存各类预测结果
            if 'grid_to_station' in predictions:
                self.predictor.save_predictions(
                    predictions['grid_to_station'],
                    os.path.join(output_dir, "grid_to_station_predictions.csv"),
                    'grid_to_station'
                )
            
            if 'station_to_grid' in predictions:
                self.predictor.save_predictions(
                    predictions['station_to_grid'],
                    os.path.join(output_dir, "station_to_grid_predictions.csv"),
                    'station_to_grid'
                )
            
            if 'new_line_impact' in predictions:
                self.predictor.save_predictions(
                    predictions['new_line_impact'],
                    os.path.join(output_dir, "new_line_impact.json"),
                    'new_line_impact'
                )
            
            # 生成综合报告
            report = self.predictor.generate_prediction_report(predictions, output_dir)
            
            # 保存训练历史
            if hasattr(self.trainer, 'history'):
                self.trainer.plot_training_history(
                    os.path.join(output_dir, "training_history.png")
                )
            
            print(f"✓ 所有结果已保存到 {output_dir}")
            return output_dir
            
        except Exception as e:
            print(f"✗ 结果保存失败: {str(e)}")
            traceback.print_exc()
            return None
    
    def run_system(self):
        """运行完整系统"""
        print(f"开始运行GCN流量预测系统 - {datetime.now()}")
        
        success = True
        
        # 步骤1: 加载数据
        if not self.load_and_preprocess_data():
            return False
        
        # 步骤2: 训练模型
        models = self.train_models()
        if models is None:
            return False
        
        # 步骤3: 执行预测
        predictions = self.perform_predictions(models)
        if predictions is None:
            return False
        
        # 步骤4: 保存结果
        output_dir = self.save_results(predictions)
        if output_dir is None:
            return False
        
        print("\n" + "="*60)
        print("系统运行完成!")
        print(f"结果保存在: {output_dir}")
        print("="*60)
        
        return True
    
    def run_evaluation_only(self):
        """仅运行评估模式（假设模型已训练）"""
        print("运行评估模式...")
        
        # 加载数据
        if not self.load_and_preprocess_data():
            return False
        
        # 加载预训练模型
        models = {}
        for task in ['in_flow', 'out_flow']:
            model_path = f"best_model_{task}.pth"
            if os.path.exists(model_path):
                models[task] = self.predictor.load_trained_model(model_path, task)
            else:
                print(f"警告: 未找到 {task} 模型文件")
        
        if not models:
            print("未找到任何预训练模型，请先运行训练")
            return False
        
        # 执行预测
        predictions = self.perform_predictions(models)
        if predictions is None:
            return False
        
        # 保存结果
        output_dir = self.save_results(predictions)
        return output_dir is not None

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='GCN地铁流量预测系统')
    parser.add_argument('--mode', choices=['train', 'eval'], default='train',
                       help='运行模式: train=训练+预测, eval=仅预测')
    parser.add_argument('--config', type=str, help='配置文件路径')
    parser.add_argument('--epochs', type=int, help='训练轮数')
    parser.add_argument('--lr', type=float, help='学习率')
    parser.add_argument('--batch_size', type=int, help='批次大小')
    
    args = parser.parse_args()
    
    try:
        # 创建系统实例
        system = GCNFlowPredictionSystem(args.config)
        
        # 更新配置
        if args.epochs:
            system.config.EPOCHS = args.epochs
        if args.lr:
            system.config.LEARNING_RATE = args.lr
        if args.batch_size:
            system.config.BATCH_SIZE = args.batch_size
        
        # 运行系统
        if args.mode == 'train':
            success = system.run_system()
        else:
            success = system.run_evaluation_only()
        
        if success:
            print("\n程序执行成功!")
            sys.exit(0)
        else:
            print("\n程序执行失败!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n用户中断程序")
        sys.exit(1)
    except Exception as e:
        print(f"\n程序异常: {str(e)}")
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main() 