"""
深度学习训练器 - 真正的机器学习训练流程
"""
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.preprocessing import StandardScaler, MinMaxScaler
import time
import os
import json
from collections import defaultdict
from typing import Dict, List, Tuple, Optional, Any
import warnings
warnings.filterwarnings('ignore')

from config import MetroFlowConfig
from models import StationFlowPredictor, ODFlowPredictor, TemporalGCN
from data_adapter import UniversalDataAdapter

class MetroFlowDataset(Dataset):
    """地铁流量时序数据集"""
    
    def __init__(self, sequences: List[Dict], config: MetroFlowConfig):
        self.sequences = sequences
        self.config = config
        self.sequence_length = config.get('data_processing.sequence_length', 12)
        self.prediction_horizon = config.get('data_processing.prediction_horizon', 6)
        
    def __len__(self):
        return len(self.sequences)
    
    def __getitem__(self, idx):
        sequence = self.sequences[idx]
        
        return {
            'node_features': torch.tensor(sequence['node_features'], dtype=torch.float32),
            'edge_index': torch.tensor(sequence['edge_index'], dtype=torch.long),
            'edge_weights': torch.tensor(sequence['edge_weights'], dtype=torch.float32),
            'hours': torch.tensor(sequence['hours'], dtype=torch.long),
            'target_flows': torch.tensor(sequence['target_flows'], dtype=torch.float32),
            'target_hours': torch.tensor(sequence['target_hours'], dtype=torch.long),
            'station_ids': sequence['station_ids']
        }

class DeepMetroFlowTrainer:
    """深度学习地铁流量预测训练器"""
    
    def __init__(self, config: MetroFlowConfig):
        self.config = config
        self.device = self._setup_device()
        
        # 数据适配器
        self.data_adapter = UniversalDataAdapter(config)
        
        # 数据预处理器
        self.flow_scaler = StandardScaler()
        self.feature_scaler = StandardScaler()
        
        # 模型
        self.station_model = None
        self.od_model = None
        
        # 训练历史
        self.train_history = defaultdict(list)
        self.best_val_loss = float('inf')
        
        # 数据缓存
        self._processed_data = None
        
    def _setup_device(self):
        """设置计算设备"""
        if self.config.get('device.use_cuda', True) and torch.cuda.is_available():
            device_id = self.config.get('device.cuda_device', 0)
            device = torch.device(f'cuda:{device_id}')
            print(f"使用GPU: {torch.cuda.get_device_name(device_id)}")
            print(f"GPU内存: {torch.cuda.get_device_properties(device_id).total_memory / 1e9:.1f} GB")
        else:
            device = torch.device('cpu')
            print("使用CPU")
        
        return device
    
    def prepare_data(self):
        """准备训练数据"""
        print("="*50)
        print("准备训练数据")
        print("="*50)
        
        # 自动检测数据文件
        detected_files = self.data_adapter.auto_detect_files()
        
        if not detected_files:
            raise ValueError("未找到任何数据文件")
        
        # 加载数据
        print("\n加载数据...")
        in_flow_data = self.data_adapter.load_flow_data('in')
        out_flow_data = self.data_adapter.load_flow_data('out')
        od_data = self.data_adapter.load_od_data()
        connection_data = self.data_adapter.load_connection_data()
        subway_flow_data = self.data_adapter.load_subway_flow_data()
        grid_features = self.data_adapter.load_grid_features()
        
        # 验证数据一致性
        validation_results = self.data_adapter.validate_data_consistency()
        
        # 构建时序数据
        print("\n构建时序数据...")
        sequences = self._build_temporal_sequences(
            in_flow_data, out_flow_data, od_data, 
            connection_data, subway_flow_data, grid_features
        )
        
        # 数据分割
        print("\n分割训练/验证/测试数据...")
        train_sequences, val_sequences, test_sequences = self._split_sequences(sequences)
        
        print(f"训练序列: {len(train_sequences)}")
        print(f"验证序列: {len(val_sequences)}")
        print(f"测试序列: {len(test_sequences)}")
        
        # 创建数据集
        self.train_dataset = MetroFlowDataset(train_sequences, self.config)
        self.val_dataset = MetroFlowDataset(val_sequences, self.config)
        self.test_dataset = MetroFlowDataset(test_sequences, self.config)
        
        # 创建数据加载器
        batch_size = self.config.get('model.batch_size', 32)
        num_workers = self.config.get('device.num_workers', 4)
        
        self.train_loader = DataLoader(
            self.train_dataset, 
            batch_size=batch_size, 
            shuffle=True,
            num_workers=num_workers,
            pin_memory=self.config.get('device.pin_memory', True)
        )
        
        self.val_loader = DataLoader(
            self.val_dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=num_workers,
            pin_memory=self.config.get('device.pin_memory', True)
        )
        
        self.test_loader = DataLoader(
            self.test_dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=num_workers,
            pin_memory=self.config.get('device.pin_memory', True)
        )
        
        print("数据准备完成！")
        
    def _build_temporal_sequences(self, in_flow_data, out_flow_data, od_data,
                                 connection_data, subway_flow_data, grid_features):
        """构建时序数据序列"""
        sequence_length = self.config.get('data_processing.sequence_length', 12)
        prediction_horizon = self.config.get('data_processing.prediction_horizon', 6)
        
        # 获取所有站点
        all_stations = self.data_adapter.get_all_stations()
        station_to_idx = {station: idx for idx, station in enumerate(all_stations)}
        
        print(f"构建 {len(all_stations)} 个站点的时序数据...")
        
        # 构建图结构
        edge_index, edge_weights_by_hour = self._build_graph_structure(
            connection_data, subway_flow_data, station_to_idx
        )
        
        # 构建节点特征
        node_features_by_hour = self._build_node_features(
            in_flow_data, out_flow_data, grid_features, all_stations, station_to_idx
        )
        
        # 生成时序序列
        sequences = []
        hours = sorted(node_features_by_hour.keys())
        
        for start_idx in range(len(hours) - sequence_length - prediction_horizon + 1):
            # 历史序列
            hist_hours = hours[start_idx:start_idx + sequence_length]
            # 预测目标
            target_hours = hours[start_idx + sequence_length:start_idx + sequence_length + prediction_horizon]
            
            # 构建序列数据
            sequence = {
                'node_features': [node_features_by_hour[h] for h in hist_hours],
                'edge_index': edge_index,
                'edge_weights': [edge_weights_by_hour.get(h, np.ones(edge_index.shape[1])) for h in hist_hours],
                'hours': hist_hours,
                'target_flows': [node_features_by_hour[h][:, :2] for h in target_hours],  # 进出站流量
                'target_hours': target_hours,
                'station_ids': list(range(len(all_stations)))
            }
            
            sequences.append(sequence)
        
        print(f"生成 {len(sequences)} 个时序序列")
        return sequences
    
    def _build_graph_structure(self, connection_data, subway_flow_data, station_to_idx):
        """构建图结构"""
        edges = []
        edge_weights_by_hour = defaultdict(list)
        
        if connection_data is not None:
            for _, row in connection_data.iterrows():
                station1 = row.get('station_1_clean', row.get('station_1', ''))
                station2 = row.get('station_2_clean', row.get('station_2', ''))
                
                if station1 in station_to_idx and station2 in station_to_idx:
                    idx1, idx2 = station_to_idx[station1], station_to_idx[station2]
                    edges.extend([[idx1, idx2], [idx2, idx1]])  # 无向图
        
        edge_index = np.array(edges).T if edges else np.array([[], []])
        
        # 构建每小时的边权重
        if subway_flow_data is not None:
            for hour in range(24):
                hour_data = subway_flow_data[subway_flow_data['hour'] == hour]
                weights = np.ones(len(edges))
                
                for i, (src, dst) in enumerate(edges):
                    # 查找对应的流量数据
                    src_station = [k for k, v in station_to_idx.items() if v == src][0]
                    dst_station = [k for k, v in station_to_idx.items() if v == dst][0]
                    
                    flow_row = hour_data[
                        (hour_data['origin_clean'] == src_station) & 
                        (hour_data['destination_clean'] == dst_station)
                    ]
                    
                    if not flow_row.empty:
                        weights[i] = max(flow_row['trip_count'].iloc[0], 1.0)
                
                edge_weights_by_hour[hour] = weights
        
        return edge_index, edge_weights_by_hour
    
    def _build_node_features(self, in_flow_data, out_flow_data, grid_features, 
                           all_stations, station_to_idx):
        """构建节点特征"""
        node_features_by_hour = {}
        
        # 基础特征维度
        base_feature_dim = grid_features['num_features'] if grid_features else 51
        
        for hour in range(24):
            # 初始化特征矩阵 [num_stations, feature_dim + 2]  # +2 for in/out flow
            features = np.zeros((len(all_stations), base_feature_dim + 2))
            
            # 填充地理特征
            if grid_features:
                # 简化：为每个站点分配平均特征
                avg_features = np.mean(grid_features['features'], axis=0)
                features[:, 2:] = avg_features
            
            # 填充流量特征
            if in_flow_data is not None:
                hour_in_data = in_flow_data[in_flow_data['hour'] == hour]
                for _, row in hour_in_data.iterrows():
                    station = row.get('station_clean', row.get('station_name', ''))
                    if station in station_to_idx:
                        idx = station_to_idx[station]
                        features[idx, 0] = row.get('flow_count', 0)
            
            if out_flow_data is not None:
                hour_out_data = out_flow_data[out_flow_data['hour'] == hour]
                for _, row in hour_out_data.iterrows():
                    station = row.get('station_clean', row.get('station_name', ''))
                    if station in station_to_idx:
                        idx = station_to_idx[station]
                        features[idx, 1] = row.get('flow_count', 0)
            
            node_features_by_hour[hour] = features
        
        return node_features_by_hour
    
    def _split_sequences(self, sequences):
        """分割序列数据"""
        # 时间分割：前80%用于训练，后20%用于测试
        split_idx = int(len(sequences) * 0.8)
        
        train_val_sequences = sequences[:split_idx]
        test_sequences = sequences[split_idx:]
        
        # 训练验证分割
        train_sequences, val_sequences = train_test_split(
            train_val_sequences, 
            test_size=0.2, 
            random_state=42
        )
        
        return train_sequences, val_sequences, test_sequences
    
    def initialize_models(self):
        """初始化模型"""
        print("初始化深度学习模型...")
        
        # 站点流量预测模型
        self.station_model = StationFlowPredictor(self.config).to(self.device)
        
        # OD流量预测模型
        self.od_model = ODFlowPredictor(self.config).to(self.device)
        
        # 优化器
        self.station_optimizer = optim.Adam(
            self.station_model.parameters(),
            lr=self.config.get('model.learning_rate', 0.001),
            weight_decay=self.config.get('model.weight_decay', 1e-5)
        )
        
        self.od_optimizer = optim.Adam(
            self.od_model.parameters(),
            lr=self.config.get('model.learning_rate', 0.001),
            weight_decay=self.config.get('model.weight_decay', 1e-5)
        )
        
        # 学习率调度器
        self.station_scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.station_optimizer, mode='min', patience=10, factor=0.5
        )
        
        self.od_scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.od_optimizer, mode='min', patience=10, factor=0.5
        )
        
        # 损失函数
        self.criterion = nn.MSELoss()
        self.mae_criterion = nn.L1Loss()
        
        print(f"站点模型参数量: {sum(p.numel() for p in self.station_model.parameters()):,}")
        print(f"OD模型参数量: {sum(p.numel() for p in self.od_model.parameters()):,}")
    
    def train_models(self):
        """训练模型"""
        print("="*50)
        print("开始深度学习训练")
        print("="*50)
        
        epochs = self.config.get('model.epochs', 200)
        early_stopping_patience = self.config.get('model.early_stopping_patience', 20)
        
        best_val_loss = float('inf')
        patience_counter = 0
        
        for epoch in range(epochs):
            # 训练
            train_loss, train_metrics = self._train_epoch()
            
            # 验证
            val_loss, val_metrics = self._validate_epoch()
            
            # 学习率调度
            self.station_scheduler.step(val_loss)
            self.od_scheduler.step(val_loss)
            
            # 记录历史
            self.train_history['train_loss'].append(train_loss)
            self.train_history['val_loss'].append(val_loss)
            self.train_history['train_mae'].append(train_metrics['mae'])
            self.train_history['val_mae'].append(val_metrics['mae'])
            
            # 早停检查
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                self._save_best_models()
            else:
                patience_counter += 1
            
            # 打印进度
            if epoch % self.config.get('logging.print_every', 5) == 0:
                print(f"Epoch {epoch:3d}: "
                      f"Train Loss={train_loss:.4f}, Val Loss={val_loss:.4f}, "
                      f"Train MAE={train_metrics['mae']:.4f}, Val MAE={val_metrics['mae']:.4f}")
            
            # 早停
            if patience_counter >= early_stopping_patience:
                print(f"早停于第 {epoch} 轮")
                break
        
        # 加载最佳模型
        self._load_best_models()
        
        print("训练完成！")
        return self.train_history

    def _train_epoch(self):
        """训练一个epoch"""
        self.station_model.train()
        self.od_model.train()

        total_loss = 0
        total_mae = 0
        num_batches = 0

        for batch in self.train_loader:
            # 移动数据到设备
            batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v
                    for k, v in batch.items()}

            # 站点流量预测
            station_pred = self.station_model(
                batch['node_features'],
                batch['edge_index'][0],  # 使用第一个样本的图结构
                batch['edge_weights'],
                batch['hours']
            )

            # 计算损失
            target_flows = batch['target_flows']  # [batch_size, prediction_horizon, num_nodes, 2]

            # 重塑目标
            batch_size, pred_horizon, num_nodes, _ = target_flows.shape
            target_in = target_flows[:, :, :, 0].reshape(-1, pred_horizon)
            target_out = target_flows[:, :, :, 1].reshape(-1, pred_horizon)

            # 计算站点流量损失
            station_loss = (
                self.criterion(station_pred['in_flow'], target_in) +
                self.criterion(station_pred['out_flow'], target_out)
            )

            # 反向传播
            self.station_optimizer.zero_grad()
            station_loss.backward()

            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.station_model.parameters(), max_norm=1.0)

            self.station_optimizer.step()

            # 计算指标
            with torch.no_grad():
                mae = (
                    self.mae_criterion(station_pred['in_flow'], target_in) +
                    self.mae_criterion(station_pred['out_flow'], target_out)
                ) / 2

            total_loss += station_loss.item()
            total_mae += mae.item()
            num_batches += 1

        avg_loss = total_loss / num_batches
        avg_mae = total_mae / num_batches

        return avg_loss, {'mae': avg_mae}

    def _validate_epoch(self):
        """验证一个epoch"""
        self.station_model.eval()
        self.od_model.eval()

        total_loss = 0
        total_mae = 0
        num_batches = 0

        with torch.no_grad():
            for batch in self.val_loader:
                # 移动数据到设备
                batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v
                        for k, v in batch.items()}

                # 站点流量预测
                station_pred = self.station_model(
                    batch['node_features'],
                    batch['edge_index'][0],
                    batch['edge_weights'],
                    batch['hours']
                )

                # 计算损失
                target_flows = batch['target_flows']
                batch_size, pred_horizon, num_nodes, _ = target_flows.shape
                target_in = target_flows[:, :, :, 0].reshape(-1, pred_horizon)
                target_out = target_flows[:, :, :, 1].reshape(-1, pred_horizon)

                station_loss = (
                    self.criterion(station_pred['in_flow'], target_in) +
                    self.criterion(station_pred['out_flow'], target_out)
                )

                mae = (
                    self.mae_criterion(station_pred['in_flow'], target_in) +
                    self.mae_criterion(station_pred['out_flow'], target_out)
                ) / 2

                total_loss += station_loss.item()
                total_mae += mae.item()
                num_batches += 1

        avg_loss = total_loss / num_batches
        avg_mae = total_mae / num_batches

        return avg_loss, {'mae': avg_mae}

    def _save_best_models(self):
        """保存最佳模型"""
        output_files = self.config.get_output_files()

        torch.save({
            'station_model': self.station_model.state_dict(),
            'od_model': self.od_model.state_dict(),
            'config': self.config.config
        }, output_files['model'])

    def _load_best_models(self):
        """加载最佳模型"""
        output_files = self.config.get_output_files()

        if os.path.exists(output_files['model']):
            checkpoint = torch.load(output_files['model'], map_location=self.device)
            self.station_model.load_state_dict(checkpoint['station_model'])
            self.od_model.load_state_dict(checkpoint['od_model'])

    def evaluate_models(self):
        """评估模型"""
        print("="*50)
        print("模型评估")
        print("="*50)

        self.station_model.eval()
        self.od_model.eval()

        all_predictions = []
        all_targets = []

        with torch.no_grad():
            for batch in self.test_loader:
                batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v
                        for k, v in batch.items()}

                # 预测
                station_pred = self.station_model(
                    batch['node_features'],
                    batch['edge_index'][0],
                    batch['edge_weights'],
                    batch['hours']
                )

                # 收集结果
                target_flows = batch['target_flows']
                batch_size, pred_horizon, num_nodes, _ = target_flows.shape

                all_predictions.append({
                    'in_flow': station_pred['in_flow'].cpu().numpy(),
                    'out_flow': station_pred['out_flow'].cpu().numpy()
                })

                all_targets.append({
                    'in_flow': target_flows[:, :, :, 0].reshape(-1, pred_horizon).cpu().numpy(),
                    'out_flow': target_flows[:, :, :, 1].reshape(-1, pred_horizon).cpu().numpy()
                })

        # 计算评估指标
        metrics = self._compute_evaluation_metrics(all_predictions, all_targets)

        # 保存评估结果
        self._save_evaluation_results(metrics, all_predictions, all_targets)

        return metrics

    def _compute_evaluation_metrics(self, predictions, targets):
        """计算评估指标"""
        # 合并所有预测和目标
        pred_in = np.concatenate([p['in_flow'] for p in predictions])
        pred_out = np.concatenate([p['out_flow'] for p in predictions])
        target_in = np.concatenate([t['in_flow'] for t in targets])
        target_out = np.concatenate([t['out_flow'] for t in targets])

        metrics = {}

        # 进站流量指标
        metrics['in_flow'] = {
            'mae': mean_absolute_error(target_in.flatten(), pred_in.flatten()),
            'rmse': np.sqrt(mean_squared_error(target_in.flatten(), pred_in.flatten())),
            'r2': r2_score(target_in.flatten(), pred_in.flatten()),
            'mape': np.mean(np.abs((target_in.flatten() - pred_in.flatten()) /
                                 (target_in.flatten() + 1e-8))) * 100
        }

        # 出站流量指标
        metrics['out_flow'] = {
            'mae': mean_absolute_error(target_out.flatten(), pred_out.flatten()),
            'rmse': np.sqrt(mean_squared_error(target_out.flatten(), pred_out.flatten())),
            'r2': r2_score(target_out.flatten(), pred_out.flatten()),
            'mape': np.mean(np.abs((target_out.flatten() - pred_out.flatten()) /
                                 (target_out.flatten() + 1e-8))) * 100
        }

        # 整体指标
        all_pred = np.concatenate([pred_in.flatten(), pred_out.flatten()])
        all_target = np.concatenate([target_in.flatten(), target_out.flatten()])

        metrics['overall'] = {
            'mae': mean_absolute_error(all_target, all_pred),
            'rmse': np.sqrt(mean_squared_error(all_target, all_pred)),
            'r2': r2_score(all_target, all_pred),
            'mape': np.mean(np.abs((all_target - all_pred) / (all_target + 1e-8))) * 100
        }

        return metrics

    def _save_evaluation_results(self, metrics, predictions, targets):
        """保存评估结果"""
        output_files = self.config.get_output_files()

        # 保存指标
        with open(output_files['metrics'], 'w', encoding='utf-8') as f:
            json.dump(metrics, f, indent=2, ensure_ascii=False)

        # 保存预测结果
        comparison_data = []

        pred_in = np.concatenate([p['in_flow'] for p in predictions])
        pred_out = np.concatenate([p['out_flow'] for p in predictions])
        target_in = np.concatenate([t['in_flow'] for t in targets])
        target_out = np.concatenate([t['out_flow'] for t in targets])

        # 进站比较
        for i in range(len(pred_in.flatten())):
            comparison_data.append({
                'Type': 'In',
                'True_Value': float(target_in.flatten()[i]),
                'Predicted_Value': float(pred_in.flatten()[i])
            })

        # 出站比较
        for i in range(len(pred_out.flatten())):
            comparison_data.append({
                'Type': 'Out',
                'True_Value': float(target_out.flatten()[i]),
                'Predicted_Value': float(pred_out.flatten()[i])
            })

        comparison_df = pd.DataFrame(comparison_data)
        comparison_df.to_csv(output_files['comparison'], index=False)

        print("评估结果已保存")

    def generate_predictions(self):
        """生成最终预测结果"""
        print("="*50)
        print("生成预测结果")
        print("="*50)

        # 这里可以添加生成shapefile等格式的预测结果的代码
        # 类似于之前的predictor.py中的功能

        print("预测结果生成完成")

    def print_model_summary(self):
        """打印模型摘要"""
        print("="*50)
        print("模型架构摘要")
        print("="*50)

        print("站点流量预测模型:")
        print(f"  - 时空GCN层数: {self.config.get('model.gcn_layers', 3)}")
        print(f"  - 隐藏维度: {self.config.get('model.gcn_hidden_dim', 128)}")
        print(f"  - 时间嵌入维度: {self.config.get('model.time_embed_dim', 32)}")
        print(f"  - 序列长度: {self.config.get('data_processing.sequence_length', 12)}")
        print(f"  - 预测时长: {self.config.get('data_processing.prediction_horizon', 6)}")
        print(f"  - 参数量: {sum(p.numel() for p in self.station_model.parameters()):,}")

        print("\nOD流量预测模型:")
        print(f"  - 参数量: {sum(p.numel() for p in self.od_model.parameters()):,}")

        print(f"\n训练配置:")
        print(f"  - 学习率: {self.config.get('model.learning_rate', 0.001)}")
        print(f"  - 批次大小: {self.config.get('model.batch_size', 32)}")
        print(f"  - 最大轮数: {self.config.get('model.epochs', 200)}")
        print(f"  - 早停耐心: {self.config.get('model.early_stopping_patience', 20)}")
        print(f"  - 设备: {self.device}")

        print("="*50)
