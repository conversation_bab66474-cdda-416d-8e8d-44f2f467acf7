# GCN地铁流量预测系统

基于图卷积网络(GCN)和时间特征的地铁流量预测系统，支持进出站流量预测、OD流量预测、新线路影响分析和栅格-站点流量预测。

## 功能特点

### 核心功能
- **多任务预测**: 支持进站、出站、OD流量的同时预测
- **复杂GCN模型**: 使用注意力机制和残差连接的深度图神经网络
- **时空特征融合**: 结合时间周期性特征和空间地理特征
- **新线路影响分析**: 预测新地铁线路对现有网络的影响
- **栅格-站点流量预测**: 预测栅格与站点间的人流分布

### 技术特点
- **内存优化**: 支持Float16精度降低内存使用
- **分层验证**: 时间分割 + 空间分割 + 留一法验证
- **图网络构建**: 基于地理距离、K近邻和OD流量的混合图
- **早停机制**: 防止过拟合的自动早停
- **流量平衡**: 自动平衡进出站流量数据

## 项目结构

```
├── main.py              # 主程序入口
├── config.py            # 配置文件
├── data_loader.py       # 数据加载模块
├── graph_builder.py     # 图构建模块
├── gcn_model.py        # GCN模型定义
├── trainer.py          # 训练模块
├── predictor.py        # 预测模块
├── requirements.txt    # 依赖包列表
└── README.md          # 项目说明
```

## 安装依赖

```bash
pip install -r requirements.txt
```

### 主要依赖
- PyTorch >= 1.12.0
- PyTorch Geometric >= 2.3.0
- GeoPandas >= 0.12.0
- NumPy >= 1.21.0
- Pandas >= 1.3.0
- Scikit-learn >= 1.0.0

## 数据格式

### 输入数据文件

1. **进站数据** (`in_500_with_coords.shp`)
   - `station`: 站点名称
   - `hour`: 时间(0-23)
   - `count`: 进站流量
   - `longitude`: 地铁站经度
   - `latitude`: 地铁站纬度
   - `geometry`: 栅格几何信息

2. **出站数据** (`out_500_withcoords.shp`)
   - 格式同进站数据

3. **OD数据** (`updated北京市_subway_od_2024_modified3.csv`)
   - `o_rawname`: 起点站名称
   - `d_rawname`: 终点站名称
   - `hour`: 时间(0-23)
   - `trip`: OD流量
   - `surface_distance`: 站点间距离
   - `translate`: 换乘次数
   - `time`: 旅行时间
   - `wait_time`: 等待时间

4. **站点特征数据** (`station_features_result.csv`)
   - `站名`: 站点名称
   - 其他51列为地理和社会经济特征

### 数据规模
- 进站数据: 535,016条记录
- 出站数据: 415,697条记录  
- OD数据: 5,017,920条记录
- 站点特征: 408个站点，52个特征
- 共同站点: 355个

## 使用方法

### 基本使用

```bash
# 完整训练和预测
python main.py --mode train

# 仅预测（需要预训练模型）
python main.py --mode eval

# 自定义参数
python main.py --mode train --epochs 50 --lr 0.001 --batch_size 64
```

### 配置参数

在 `config.py` 中可以调整以下参数：

```python
# 模型配置
HIDDEN_DIM = 128        # 隐藏层维度
NUM_LAYERS = 3          # GCN层数
DROPOUT = 0.2           # Dropout率
LEARNING_RATE = 0.001   # 学习率
EPOCHS = 20             # 训练轮数
PATIENCE = 5            # 早停耐心值

# 数据配置
USE_FLOAT16 = True      # 使用Float16
SPATIAL_THRESHOLD = 0.01 # 栅格-站点关联阈值
DISTANCE_THRESHOLD = 2000 # 图构建距离阈值
```

### 数据路径配置

修改 `config.py` 中的数据路径：

```python
BASE_DIR = r"你的数据目录路径"
IN_STATION_PATH = os.path.join(BASE_DIR, "in_500_with_coords.shp")
OUT_STATION_PATH = os.path.join(BASE_DIR, "out_500_withcoords.shp")
OD_PATH = os.path.join(BASE_DIR, "updated北京市_subway_od_2024_modified3.csv")
STATION_FEATURES_PATH = os.path.join(BASE_DIR, "station_features_result.csv")
```

## 核心算法

### 1. 图构建
- **距离图**: 基于地理距离构建邻接关系
- **K近邻图**: 每个站点连接K个最近邻居
- **OD流量图**: 基于实际客流OD数据构建连接
- **混合图**: 融合上述三种图的边权重

### 2. GCN模型架构
```
输入: [流量数据, 时间特征, 空间特征]
    ↓
时空嵌入层
    ↓
多层GCN (带注意力机制)
    ↓ (残差连接)
时间注意力层
    ↓
任务特定输出层
```

### 3. 特征工程
- **时间特征**: 周期性编码、高峰时段标识
- **空间特征**: 站点周边POI、土地利用、人口密度等51维特征
- **图特征**: 节点度、聚类系数、中心性等

### 4. 多任务学习
- 共享GCN编码器提取通用特征
- 任务特定解码器处理不同预测目标
- 联合优化多个损失函数

## 预测功能

### 1. 基础流量预测
- 进站流量预测
- 出站流量预测  
- OD流量预测

### 2. 新线路影响分析
```python
# 定义新线路
new_stations = ['新站点A', '新站点B', '新站点C']
new_connections = [
    ('新站点A', '新站点B', 1.0),
    ('新站点B', '新站点C', 1.0)
]

# 预测影响
impact = predictor.predict_new_line_impact(
    current_data, new_stations, new_connections
)
```

### 3. 栅格-站点流量预测
```python
# 栅格到站点
grid_to_station = predictor.predict_grid_to_station_flow(
    grid_coords, station_coords, threshold=5000
)

# 站点到栅格
station_to_grid = predictor.predict_station_to_grid_flow(
    station_coords, grid_coords, threshold=5000
)
```

## 验证策略

### 分层验证
1. **时间分割**: 按时间顺序分割训练/验证/测试集
2. **空间分割**: 按站点空间位置分割数据集
3. **留一法**: 每次留出一个站点作为测试

### 评估指标
- **MAE**: 平均绝对误差
- **MSE**: 均方误差
- **RMSE**: 均方根误差
- **MAPE**: 平均绝对百分比误差

## 输出结果

运行完成后会生成带时间戳的结果目录：

```
results_20241201_143022/
├── grid_to_station_predictions.csv    # 栅格到站点预测
├── station_to_grid_predictions.csv    # 站点到栅格预测
├── new_line_impact.json               # 新线路影响分析
├── prediction_report.json             # 综合预测报告
└── training_history.png               # 训练历史图
```

## 性能优化

### 内存优化
- 使用Float16精度减少50%内存使用
- 批处理减少内存峰值
- 梯度裁剪防止梯度爆炸

### 训练优化
- 自适应学习率调整
- 早停防止过拟合
- 模型检查点保存

### 显存要求
- 最低: 8GB专用显存
- 推荐: 16GB及以上
- 支持CPU训练（速度较慢）

## 扩展功能

### 1. 自定义图构建
```python
# 添加新的图构建方法
graph_builder = GraphBuilder()
edge_index, edge_attr = graph_builder.create_custom_graph(
    station_coords, custom_connections
)
```

### 2. 新预测任务
```python
# 扩展模型支持新任务
model.output_layers['new_task'] = nn.Sequential(...)
```

### 3. 特征扩展
```python
# 添加新的特征维度
config.SPATIAL_FEATURES += new_feature_dim
```

## 常见问题

### Q1: 内存不足错误
**A**: 减少批次大小或启用Float16：
```python
config.BATCH_SIZE = 16
config.USE_FLOAT16 = True
```

### Q2: 数据路径错误
**A**: 检查config.py中的路径设置，确保文件存在

### Q3: CUDA错误
**A**: 检查PyTorch和CUDA版本兼容性，或设置使用CPU：
```python
config.DEVICE = "cpu"
```

### Q4: 模型收敛慢
**A**: 调整学习率和网络结构：
```python
config.LEARNING_RATE = 0.01
config.HIDDEN_DIM = 256
```

## 技术支持

- 支持Python 3.8+
- 兼容Windows/Linux/macOS
- GPU加速训练（可选）
- 支持分布式训练扩展

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基础GCN流量预测
- 实现多任务学习框架
- 添加新线路影响分析
- 支持栅格-站点流量预测

## 许可证

本项目仅供学术研究使用。 