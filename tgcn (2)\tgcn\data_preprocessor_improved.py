"""
改进的数据预处理模块 - 支持有向加权图
"""
import numpy as np
import pandas as pd
import torch
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from scipy.sparse import lil_matrix
from config import Config

class ImprovedDataPreprocessor:
    def __init__(self, config=None):
        self.config = config or Config()
        self.feature_scaler = StandardScaler()
        self.flow_scaler = MinMaxScaler()
    
    def preprocess_data(self, in_data, out_data, od_data, station_features):
        """
        预处理所有数据 - 构建有向加权图
        """
        print("预处理数据（有向加权图版本）...")
        
        # 标准化站点特征
        station_features = self._standardize_features(station_features)
        
        # 获取共同站点
        common_stations = self._get_common_stations(in_data, out_data, station_features)
        
        # 过滤数据
        in_data, out_data, station_features = self._filter_data(
            in_data, out_data, station_features, common_stations
        )
        
        # 创建站点索引映射
        station_to_idx = {station: idx for idx, station in enumerate(common_stations)}
        
        # 构建有向加权图和时序OD矩阵
        edge_index, edge_weights, od_flows_tensor = self._build_directed_weighted_graph(
            od_data, common_stations, station_to_idx
        )
        
        # 准备多任务目标数据
        X, in_flows, out_flows = self._prepare_multitask_features(
            in_data, out_data, station_features, common_stations, station_to_idx
        )
        
        # 划分训练集和测试集
        train_indices, test_indices = self._split_train_test(len(common_stations))
        
        # 获取站点名称列表
        train_stations = [common_stations[i] for i in train_indices]
        test_stations = [common_stations[i] for i in test_indices]
        
        return (X, in_flows, out_flows, od_flows_tensor, edge_index, edge_weights,
                train_indices, test_indices, common_stations, train_stations, test_stations,
                station_to_idx, in_data, out_data, od_data)
    
    def _build_directed_weighted_graph(self, od_data, common_stations, station_to_idx):
        """构建有向加权图"""
        print("正在构建有向加权图...")
        n_stations = len(common_stations)
        time_steps = self.config.TIME_STEPS
        
        # 过滤OD数据
        od_subset = od_data[
            (od_data['o_rawname'].isin(common_stations)) &
            (od_data['d_rawname'].isin(common_stations))
        ].copy()
        
        # 聚合每小时的OD流量
        print("聚合时序OD流量...")
        od_flows_by_hour = {}
        
        for hour in range(time_steps):
            hour_data = od_subset[od_subset['hour'] == hour] if 'hour' in od_subset.columns else od_subset
            
            # 聚合同一OD对的流量
            hour_flows = hour_data.groupby(['o_rawname', 'd_rawname'])['trip'].sum().reset_index()
            od_flows_by_hour[hour] = hour_flows
        
        # 构建时序OD流量张量 [time_steps, n_stations, n_stations]
        od_flows_tensor = np.zeros((time_steps, n_stations, n_stations))
        
        for hour in range(time_steps):
            if hour in od_flows_by_hour:
                for _, row in od_flows_by_hour[hour].iterrows():
                    o_idx = station_to_idx[row['o_rawname']]
                    d_idx = station_to_idx[row['d_rawname']]
                    od_flows_tensor[hour, o_idx, d_idx] = row['trip']
        
        # 标准化OD流量
        od_flows_flat = od_flows_tensor.reshape(-1, 1)
        od_flows_normalized = self.flow_scaler.fit_transform(od_flows_flat)
        od_flows_tensor = od_flows_normalized.reshape(time_steps, n_stations, n_stations)
        
        # 构建图的边和边权重
        edge_list = []
        edge_weight_list = []
        
        # 计算平均OD流量作为边权重
        avg_od_flows = np.mean(od_flows_tensor, axis=0)
        
        for i in range(n_stations):
            for j in range(n_stations):
                if avg_od_flows[i, j] > 0:  # 只保留有流量的边
                    edge_list.append([i, j])
                    edge_weight_list.append(avg_od_flows[i, j])
        
        # 添加自环（重要：保持节点自身信息）
        for i in range(n_stations):
            edge_list.append([i, i])
            edge_weight_list.append(1.0)  # 自环权重设为1
        
        # 转换为PyTorch张量
        edge_index = torch.LongTensor(edge_list).T  # [2, num_edges]
        edge_weights = torch.FloatTensor(edge_weight_list)
        od_flows_tensor = torch.FloatTensor(od_flows_tensor)
        
        print(f"有向图边数量: {len(edge_list)}")
        print(f"平均边权重: {np.mean(edge_weight_list):.4f}")
        
        return edge_index, edge_weights, od_flows_tensor
    
    def _prepare_multitask_features(self, in_data, out_data, station_features, 
                                   common_stations, station_to_idx):
        """准备多任务特征和目标"""
        print("准备多任务特征...")
        
        time_steps = self.config.TIME_STEPS
        station_count = len(common_stations)
        feature_columns = station_features.columns.drop('站名')
        feature_dim = len(feature_columns)
        
        # 特征矩阵 [time_steps, n_stations, features]
        X = np.zeros((time_steps, station_count, feature_dim + 2))  # +2 for in/out flows
        
        # 目标矩阵
        in_flows = np.zeros((time_steps, station_count))
        out_flows = np.zeros((time_steps, station_count))
        
        # 构建站点特征字典
        station_feature_dict = {}
        for _, row in station_features.iterrows():
            station = row['站名']
            if station in station_to_idx:
                station_feature_dict[station] = row[feature_columns].values
        
        # 填充进站流量
        for _, row in in_data.iterrows():
            station = row['station']
            hour = int(row['hour'])
            count = float(row['count'])
            
            if station in station_to_idx:
                idx = station_to_idx[station]
                X[hour, idx, 0] = count
                in_flows[hour, idx] = count
        
        # 填充出站流量
        for _, row in out_data.iterrows():
            station = row['station']
            hour = int(row['hour'])
            count = float(row['count'])
            
            if station in station_to_idx:
                idx = station_to_idx[station]
                X[hour, idx, 1] = count
                out_flows[hour, idx] = count
        
        # 填充站点特征
        for station, idx in station_to_idx.items():
            if station in station_feature_dict:
                features = station_feature_dict[station]
                for hour in range(time_steps):
                    X[hour, idx, 2:] = features
        
        # 标准化流量数据
        in_flows_flat = in_flows.reshape(-1, 1)
        out_flows_flat = out_flows.reshape(-1, 1)
        
        in_flows_norm = self.flow_scaler.fit_transform(in_flows_flat).reshape(time_steps, station_count)
        out_flows_norm = self.flow_scaler.fit_transform(out_flows_flat).reshape(time_steps, station_count)
        
        print(f"特征矩阵形状: {X.shape}")
        print(f"进站流量矩阵形状: {in_flows_norm.shape}")
        print(f"出站流量矩阵形状: {out_flows_norm.shape}")
        
        return torch.FloatTensor(X), torch.FloatTensor(in_flows_norm), torch.FloatTensor(out_flows_norm)
    
    def _get_common_stations(self, in_data, out_data, station_features):
        """获取共同站点"""
        in_stations = set(in_data['station'].unique())
        out_stations = set(out_data['station'].unique())
        feature_stations = set(station_features['站名'].unique())
        
        common_stations = list(in_stations & out_stations & feature_stations)
        print(f"共同站点数量: {len(common_stations)}")
        return common_stations
    
    def _filter_data(self, in_data, out_data, station_features, common_stations):
        """过滤数据"""
        in_data = in_data[in_data['station'].isin(common_stations)]
        out_data = out_data[out_data['station'].isin(common_stations)]
        station_features = station_features[station_features['站名'].isin(common_stations)]
        return in_data, out_data, station_features
    
    def _standardize_features(self, station_features):
        """标准化站点特征"""
        feature_columns = station_features.columns.drop('站名')
        station_features[feature_columns] = self.feature_scaler.fit_transform(
            station_features[feature_columns]
        )
        return station_features
    
    def _split_train_test(self, station_count):
        """划分训练集和测试集"""
        n_train_stations = int(station_count * self.config.TRAIN_RATIO)
        train_indices = np.random.choice(station_count, n_train_stations, replace=False)
        test_indices = np.array([i for i in range(station_count) if i not in train_indices])
        return train_indices, test_indices 