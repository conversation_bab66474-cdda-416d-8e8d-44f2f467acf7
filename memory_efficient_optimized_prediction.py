"""
Memory-Efficient Optimized Spatial Metro Flow Prediction System
Designed for large datasets with limited GPU memory
Target: R² > 0.3 with memory optimization
"""
import os
import sys
import time
import numpy as np
import pandas as pd
import geopandas as gpd
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
import warnings
warnings.filterwarnings('ignore')

class MemoryEfficientSpatialModel(nn.Module):
    """Memory-efficient spatial model with batch processing"""
    
    def __init__(self, input_dim, hidden_dim=64, model_type='station'):
        super(MemoryEfficientSpatialModel, self).__init__()
        
        self.model_type = model_type
        self.hidden_dim = hidden_dim
        
        if model_type == 'od':
            # Simplified OD model for memory efficiency
            self.origin_encoder = nn.Sequential(
                nn.Linear(input_dim // 2, hidden_dim),
                nn.LayerNorm(hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(hidden_dim, hidden_dim // 2)
            )
            
            self.destination_encoder = nn.Sequential(
                nn.Linear(input_dim // 2, hidden_dim),
                nn.LayerNorm(hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(hidden_dim, hidden_dim // 2)
            )
            
            # Efficient OD pair encoder
            self.od_pair_encoder = nn.Sequential(
                nn.Linear(4, 16),
                nn.ReLU(),
                nn.Linear(16, 8)
            )
            
            # Simplified temporal encoder
            self.temporal_encoder = nn.Sequential(
                nn.Linear(6, 16),
                nn.ReLU(),
                nn.Linear(16, 8)
            )
            
            # Efficient interaction layer
            fusion_dim = hidden_dim // 2 + hidden_dim // 2 + 8 + 8
            self.interaction_layer = nn.Sequential(
                nn.Linear(fusion_dim, hidden_dim),
                nn.LayerNorm(hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.3),
                nn.Linear(hidden_dim, 32),
                nn.ReLU(),
                nn.Linear(32, 1)
            )
            
        else:
            # Efficient station model
            self.feature_encoder = nn.Sequential(
                nn.Linear(input_dim, hidden_dim),
                nn.LayerNorm(hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(hidden_dim, hidden_dim // 2)
            )
            
            # Simplified spatial encoder
            self.spatial_encoder = nn.Sequential(
                nn.Linear(3, 16),
                nn.ReLU(),
                nn.Linear(16, 8)
            )
            
            # Simplified temporal encoder
            self.temporal_encoder = nn.Sequential(
                nn.Linear(6, 16),
                nn.ReLU(),
                nn.Linear(16, 8)
            )
            
            # Efficient fusion with attention
            fusion_dim = hidden_dim // 2 + 8 + 8
            self.attention_weights = nn.Sequential(
                nn.Linear(fusion_dim, fusion_dim),
                nn.Sigmoid()
            )
            
            # Prediction head
            self.prediction_head = nn.Sequential(
                nn.Linear(fusion_dim, hidden_dim),
                nn.LayerNorm(hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.3),
                nn.Linear(hidden_dim, 32),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(32, 1)
            )
    
    def forward(self, *args):
        if self.model_type == 'od':
            origin_features, dest_features, od_pair_features, time_features = args
            
            # Encode features
            origin_encoded = self.origin_encoder(origin_features)
            dest_encoded = self.destination_encoder(dest_features)
            od_encoded = self.od_pair_encoder(od_pair_features)
            time_encoded = self.temporal_encoder(time_features)
            
            # Simple concatenation for memory efficiency
            combined = torch.cat([origin_encoded, dest_encoded, od_encoded, time_encoded], dim=-1)
            output = self.interaction_layer(combined)
            
        else:
            features, spatial_features, time_features = args
            
            # Encode features
            feat_encoded = self.feature_encoder(features)
            spatial_encoded = self.spatial_encoder(spatial_features)
            time_encoded = self.temporal_encoder(time_features)
            
            # Feature fusion with attention
            combined = torch.cat([feat_encoded, spatial_encoded, time_encoded], dim=-1)
            attention_weights = self.attention_weights(combined)
            attended_features = combined * attention_weights
            
            # Final prediction
            output = self.prediction_head(attended_features)
        
        return torch.relu(output)  # Ensure non-negative

class MemoryEfficientPredictionSystem:
    """Memory-efficient prediction system with batch processing"""
    
    def __init__(self, batch_size=1024):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"Using device: {self.device}")
        if torch.cuda.is_available():
            print(f"GPU: {torch.cuda.get_device_name(0)}")
            print(f"CUDA Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
        
        self.batch_size = batch_size
        
        # Data storage
        self.in_data = None
        self.out_data = None
        self.od_data = None
        self.grid_data = None
        self.station_coords = {}
        
        # Models (ensemble)
        self.in_models = []
        self.out_models = []
        self.od_models = []
        
        # Scalers
        self.feature_scaler = RobustScaler()
        self.spatial_scaler = StandardScaler()
        self.time_scaler = StandardScaler()
        self.od_scaler = RobustScaler()
        
        # Station information
        self.stations = []
        self.station_to_idx = {}
        
    def load_and_process_data(self):
        """Load and process data with memory optimization"""
        print("="*60)
        print("Loading data with memory optimization")
        print("="*60)
        
        try:
            # Load basic data
            self.in_data = gpd.read_file('in_500_with_coords.shp')
            self.out_data = gpd.read_file('out_500_with_coords.shp')
            self.od_data = pd.read_csv('updated北京市_subway_od_2024_modified3.csv')
            self.grid_data = pd.read_csv('leti_data.csv')
            
            # Memory-efficient data cleaning
            self._memory_efficient_cleaning()
            
            # Extract coordinates
            self.in_data['longitude'] = self.in_data.geometry.centroid.x
            self.in_data['latitude'] = self.in_data.geometry.centroid.y
            self.out_data['longitude'] = self.out_data.geometry.centroid.x
            self.out_data['latitude'] = self.out_data.geometry.centroid.y
            
            print(f"In-flow data: {self.in_data.shape}")
            print(f"Out-flow data: {self.out_data.shape}")
            print(f"OD data: {self.od_data.shape}")
            print(f"Grid data: {self.grid_data.shape}")
            
            # Extract station coordinates
            self._extract_station_coordinates()
            
            # Process grid features efficiently
            self._process_grid_features()
            
            print(f"Grid features dimension: {self.grid_features.shape}")
            
            return True
            
        except Exception as e:
            print(f"Data loading failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _memory_efficient_cleaning(self):
        """Memory-efficient data cleaning"""
        # Clean station names
        self.in_data['station_clean'] = self.in_data['station'].apply(lambda x: str(x).split('_')[0])
        self.out_data['station_clean'] = self.out_data['station'].apply(lambda x: str(x).split('_')[0])
        self.od_data['o_station_clean'] = self.od_data['o_rawname'].apply(lambda x: str(x).split('_')[0])
        self.od_data['d_station_clean'] = self.od_data['d_rawname'].apply(lambda x: str(x).split('_')[0])
        
        # Conservative outlier removal to preserve data
        self.in_data = self.in_data[(self.in_data['count'] >= 0) & (self.in_data['count'] <= self.in_data['count'].quantile(0.95))]
        self.out_data = self.out_data[(self.out_data['count'] >= 0) & (self.out_data['count'] <= self.out_data['count'].quantile(0.95))]
        self.od_data = self.od_data[(self.od_data['trip'] >= 0) & (self.od_data['trip'] <= self.od_data['trip'].quantile(0.95))]
        
        print(f"After cleaning - In: {len(self.in_data)}, Out: {len(self.out_data)}, OD: {len(self.od_data)}")
    
    def _extract_station_coordinates(self):
        """Extract station coordinates efficiently"""
        all_stations = set()
        all_stations.update(self.in_data['station_clean'].unique())
        all_stations.update(self.out_data['station_clean'].unique())
        all_stations.update(self.od_data['o_station_clean'].unique())
        all_stations.update(self.od_data['d_station_clean'].unique())
        
        self.stations = sorted(list(all_stations))
        self.station_to_idx = {station: idx for idx, station in enumerate(self.stations)}
        
        # Extract coordinates
        for station in self.stations:
            in_coords = self.in_data[self.in_data['station_clean'] == station][['longitude', 'latitude']]
            out_coords = self.out_data[self.out_data['station_clean'] == station][['longitude', 'latitude']]
            
            all_coords = pd.concat([in_coords, out_coords])
            if len(all_coords) > 0:
                self.station_coords[station] = {
                    'longitude': all_coords['longitude'].median(),
                    'latitude': all_coords['latitude'].median()
                }
            else:
                self.station_coords[station] = {'longitude': 116.4, 'latitude': 39.9}
        
        print(f"Extracted coordinates for {len(self.stations)} stations")
    
    def _process_grid_features(self):
        """Process grid features efficiently"""
        feature_cols = [col for col in self.grid_data.columns if col not in ['站名', 'id', 'geometry']]
        features = self.grid_data[feature_cols].values
        
        # Handle missing values
        features = np.nan_to_num(features, nan=0.0, posinf=0.0, neginf=0.0)
        
        # Remove constant features
        feature_std = np.std(features, axis=0)
        valid_features = feature_std > 1e-6
        features = features[:, valid_features]
        
        # Use median for robust representation
        self.grid_features = np.median(features, axis=0)
        
        print(f"Processed {len(self.grid_features)} grid features")
    
    def _calculate_spatial_features(self, grid_lon, grid_lat, station_lon, station_lat):
        """Calculate spatial features"""
        distance = np.sqrt((grid_lon - station_lon)**2 + (grid_lat - station_lat)**2)
        direction = np.arctan2(station_lat - grid_lat, station_lon - grid_lon)
        accessibility = 1.0 / (1.0 + distance * 100)
        return np.array([distance, direction, accessibility])
    
    def _calculate_time_features(self, hour):
        """Calculate time features"""
        return np.array([
            hour / 23.0,
            np.sin(2 * np.pi * hour / 24),
            np.cos(2 * np.pi * hour / 24),
            1.0 if 6 <= hour <= 10 else 0.0,
            1.0 if 17 <= hour <= 20 else 0.0,
            1.0 if hour in [0,1,2,3,4,5,21,22,23] else 0.0
        ])
    
    def prepare_training_data(self, data, flow_type):
        """Prepare training data efficiently"""
        features = []
        spatial_features = []
        time_features = []
        targets = []
        
        for idx, row in data.iterrows():
            station = row['station_clean']
            hour = row['hour']
            count = row['count']
            grid_lon = row['longitude']
            grid_lat = row['latitude']
            
            if station in self.station_coords:
                station_coord = self.station_coords[station]
                station_lon = station_coord['longitude']
                station_lat = station_coord['latitude']
                
                # Base features
                station_idx = self.station_to_idx[station]
                base_features = np.concatenate([
                    self.grid_features,
                    [station_lon, station_lat, station_idx / len(self.stations)]
                ])
                
                # Spatial features
                spatial_feature = self._calculate_spatial_features(
                    grid_lon, grid_lat, station_lon, station_lat
                )
                
                # Time features
                time_feature = self._calculate_time_features(hour)
                
                features.append(base_features)
                spatial_features.append(spatial_feature)
                time_features.append(time_feature)
                targets.append(count)
        
        return (np.array(features), np.array(spatial_features),
                np.array(time_features), np.array(targets))

    def prepare_od_data(self):
        """Prepare OD training data efficiently"""
        origin_features = []
        dest_features = []
        od_pair_features = []
        time_features = []
        targets = []

        for idx, row in self.od_data.iterrows():
            o_station = row['o_station_clean']
            d_station = row['d_station_clean']
            hour = row['hour']
            trip = row['trip']

            if o_station in self.station_coords and d_station in self.station_coords:
                # Origin features
                o_coord = self.station_coords[o_station]
                o_idx = self.station_to_idx[o_station]
                origin_feature = np.concatenate([
                    self.grid_features,
                    [o_coord['longitude'], o_coord['latitude'], o_idx / len(self.stations)]
                ])

                # Destination features
                d_coord = self.station_coords[d_station]
                d_idx = self.station_to_idx[d_station]
                dest_feature = np.concatenate([
                    self.grid_features,
                    [d_coord['longitude'], d_coord['latitude'], d_idx / len(self.stations)]
                ])

                # OD pair features
                od_distance = np.sqrt(
                    (o_coord['longitude'] - d_coord['longitude'])**2 +
                    (o_coord['latitude'] - d_coord['latitude'])**2
                )

                od_pair_feature = np.array([
                    row.get('surface_distance', od_distance),
                    row.get('translate', 0),
                    row.get('time', od_distance * 10),
                    row.get('wait_time', 0)
                ])

                # Time features
                time_feature = self._calculate_time_features(hour)

                origin_features.append(origin_feature)
                dest_features.append(dest_feature)
                od_pair_features.append(od_pair_feature)
                time_features.append(time_feature)
                targets.append(trip)

        return (np.array(origin_features), np.array(dest_features),
                np.array(od_pair_features), np.array(time_features), np.array(targets))

    def train_memory_efficient_models(self):
        """Train models with memory efficiency"""
        print("="*60)
        print("Training memory-efficient optimized models")
        print("="*60)

        # Prepare training data
        print("Preparing training data...")
        in_features, in_spatial, in_time, in_targets = self.prepare_training_data(self.in_data, 'in')
        out_features, out_spatial, out_time, out_targets = self.prepare_training_data(self.out_data, 'out')

        # Scale features
        in_features = self.feature_scaler.fit_transform(in_features)
        in_spatial = self.spatial_scaler.fit_transform(in_spatial)
        in_time = self.time_scaler.fit_transform(in_time)

        out_features = self.feature_scaler.transform(out_features)
        out_spatial = self.spatial_scaler.transform(out_spatial)
        out_time = self.time_scaler.transform(out_time)

        # Train ensemble models
        print("Training in-flow models...")
        self.in_models = self._train_ensemble_models(
            in_features, in_spatial, in_time, in_targets, 'in'
        )

        print("Training out-flow models...")
        self.out_models = self._train_ensemble_models(
            out_features, out_spatial, out_time, out_targets, 'out'
        )

        # Train OD models with sampling for memory efficiency
        print("Training OD models...")
        od_origin, od_dest, od_pair, od_time, od_targets = self.prepare_od_data()

        # Sample OD data for memory efficiency
        if len(od_targets) > 100000:
            sample_indices = np.random.choice(len(od_targets), 100000, replace=False)
            od_origin = od_origin[sample_indices]
            od_dest = od_dest[sample_indices]
            od_pair = od_pair[sample_indices]
            od_time = od_time[sample_indices]
            od_targets = od_targets[sample_indices]
            print(f"Sampled {len(od_targets)} OD records for training")

        # Scale OD features
        od_origin = self.feature_scaler.transform(od_origin)
        od_dest = self.feature_scaler.transform(od_dest)
        od_pair = self.od_scaler.fit_transform(od_pair)
        od_time = self.time_scaler.transform(od_time)

        self.od_models = self._train_od_ensemble_models(
            od_origin, od_dest, od_pair, od_time, od_targets
        )

        print("All models trained successfully!")

    def _train_ensemble_models(self, features, spatial_features, time_features, targets, model_type):
        """Train ensemble of models with memory efficiency"""
        models = []

        # Model 1: Neural Network
        print(f"  Training {model_type} Model 1: Neural Network...")
        model1 = self._train_single_model(
            features, spatial_features, time_features, targets, model_type, hidden_dim=64
        )
        models.append(model1)

        # Model 2: Larger Neural Network
        print(f"  Training {model_type} Model 2: Enhanced Neural Network...")
        model2 = self._train_single_model(
            features, spatial_features, time_features, targets, model_type, hidden_dim=96
        )
        models.append(model2)

        # Model 3: Random Forest (CPU-based)
        print(f"  Training {model_type} Model 3: Random Forest...")
        combined_features = np.concatenate([features, spatial_features, time_features], axis=1)
        rf_model = RandomForestRegressor(
            n_estimators=100, max_depth=15, random_state=42, n_jobs=-1
        )
        rf_model.fit(combined_features, targets)
        models.append(rf_model)

        return models

    def _train_single_model(self, features, spatial_features, time_features, targets, model_type, hidden_dim=64):
        """Train a single neural network model with batch processing"""
        input_dim = features.shape[1]
        model = MemoryEfficientSpatialModel(input_dim=input_dim, hidden_dim=hidden_dim, model_type='station')
        model = model.to(self.device)

        # Create data loader for batch processing
        dataset = TensorDataset(
            torch.tensor(features, dtype=torch.float32),
            torch.tensor(spatial_features, dtype=torch.float32),
            torch.tensor(time_features, dtype=torch.float32),
            torch.tensor(targets, dtype=torch.float32)
        )
        dataloader = DataLoader(dataset, batch_size=self.batch_size, shuffle=True)

        # Training configuration
        optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=1e-4)
        criterion = nn.HuberLoss(delta=1.0)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)

        # Training loop
        model.train()
        best_loss = float('inf')
        patience = 0
        max_patience = 20

        for epoch in range(100):
            epoch_loss = 0.0
            num_batches = 0

            for batch_features, batch_spatial, batch_time, batch_targets in dataloader:
                batch_features = batch_features.to(self.device)
                batch_spatial = batch_spatial.to(self.device)
                batch_time = batch_time.to(self.device)
                batch_targets = batch_targets.to(self.device)

                optimizer.zero_grad()

                # Forward pass
                predictions = model(batch_features, batch_spatial, batch_time).squeeze()

                # Handle single sample batches
                if predictions.dim() == 0:
                    predictions = predictions.unsqueeze(0)
                if batch_targets.dim() == 0:
                    batch_targets = batch_targets.unsqueeze(0)

                # Calculate loss
                loss = criterion(predictions, batch_targets)

                # Backward pass
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()

                epoch_loss += loss.item()
                num_batches += 1

            avg_loss = epoch_loss / num_batches
            scheduler.step(avg_loss)

            # Early stopping
            if avg_loss < best_loss:
                best_loss = avg_loss
                patience = 0
            else:
                patience += 1

            if epoch % 20 == 0:
                print(f"    Epoch {epoch}: Loss = {avg_loss:.6f}")

            if patience >= max_patience:
                print(f"    Early stopping at epoch {epoch}")
                break

        return model

    def _train_od_ensemble_models(self, origin_features, dest_features, od_pair_features, time_features, targets):
        """Train OD ensemble models with memory efficiency"""
        models = []

        # Model 1: Neural Network
        print("  Training OD Model 1: Neural Network...")
        model1 = self._train_single_od_model(
            origin_features, dest_features, od_pair_features, time_features, targets, hidden_dim=64
        )
        models.append(model1)

        # Model 2: Random Forest
        print("  Training OD Model 2: Random Forest...")
        combined_features = np.concatenate([origin_features, dest_features, od_pair_features, time_features], axis=1)
        rf_model = RandomForestRegressor(
            n_estimators=100, max_depth=15, random_state=42, n_jobs=-1
        )
        rf_model.fit(combined_features, targets)
        models.append(rf_model)

        return models

    def _train_single_od_model(self, origin_features, dest_features, od_pair_features, time_features, targets, hidden_dim=64):
        """Train a single OD neural network model"""
        input_dim = origin_features.shape[1] * 2
        model = MemoryEfficientSpatialModel(input_dim=input_dim, hidden_dim=hidden_dim, model_type='od')
        model = model.to(self.device)

        # Create data loader
        dataset = TensorDataset(
            torch.tensor(origin_features, dtype=torch.float32),
            torch.tensor(dest_features, dtype=torch.float32),
            torch.tensor(od_pair_features, dtype=torch.float32),
            torch.tensor(time_features, dtype=torch.float32),
            torch.tensor(targets, dtype=torch.float32)
        )
        dataloader = DataLoader(dataset, batch_size=self.batch_size, shuffle=True)

        # Training configuration
        optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=1e-4)
        criterion = nn.HuberLoss(delta=1.0)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)

        # Training loop
        model.train()
        best_loss = float('inf')
        patience = 0
        max_patience = 20

        for epoch in range(100):
            epoch_loss = 0.0
            num_batches = 0

            for batch_origin, batch_dest, batch_od_pair, batch_time, batch_targets in dataloader:
                batch_origin = batch_origin.to(self.device)
                batch_dest = batch_dest.to(self.device)
                batch_od_pair = batch_od_pair.to(self.device)
                batch_time = batch_time.to(self.device)
                batch_targets = batch_targets.to(self.device)

                optimizer.zero_grad()

                # Forward pass
                predictions = model(batch_origin, batch_dest, batch_od_pair, batch_time).squeeze()

                # Handle single sample batches
                if predictions.dim() == 0:
                    predictions = predictions.unsqueeze(0)
                if batch_targets.dim() == 0:
                    batch_targets = batch_targets.unsqueeze(0)

                # Calculate loss
                loss = criterion(predictions, batch_targets)

                # Backward pass
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()

                epoch_loss += loss.item()
                num_batches += 1

            avg_loss = epoch_loss / num_batches
            scheduler.step(avg_loss)

            # Early stopping
            if avg_loss < best_loss:
                best_loss = avg_loss
                patience = 0
            else:
                patience += 1

            if epoch % 20 == 0:
                print(f"    OD Epoch {epoch}: Loss = {avg_loss:.6f}")

            if patience >= max_patience:
                print(f"    OD Early stopping at epoch {epoch}")
                break

        return model

    def generate_memory_efficient_predictions(self):
        """Generate predictions with memory efficiency"""
        print("="*60)
        print("Generating memory-efficient predictions")
        print("="*60)

        # Time split strategy
        test_hours = list(range(18, 24))

        # Generate in-flow predictions
        print("Generating in-flow predictions...")
        in_test_data = self.in_data[self.in_data['hour'].isin(test_hours)].copy()

        if len(in_test_data) > 0:
            in_test_features, in_test_spatial, in_test_time, _ = self.prepare_training_data(in_test_data, 'in')

            # Scale features
            in_test_features = self.feature_scaler.transform(in_test_features)
            in_test_spatial = self.spatial_scaler.transform(in_test_spatial)
            in_test_time = self.time_scaler.transform(in_test_time)

            # Ensemble prediction
            in_predictions = self._ensemble_predict_station(
                self.in_models, in_test_features, in_test_spatial, in_test_time
            )

            in_test_data.loc[:, 'prediction'] = np.maximum(in_predictions, 0)

            # Save results
            self._save_predictions(in_test_data, 'in')

        # Generate out-flow predictions
        print("Generating out-flow predictions...")
        out_test_data = self.out_data[self.out_data['hour'].isin(test_hours)].copy()

        if len(out_test_data) > 0:
            out_test_features, out_test_spatial, out_test_time, _ = self.prepare_training_data(out_test_data, 'out')

            # Scale features
            out_test_features = self.feature_scaler.transform(out_test_features)
            out_test_spatial = self.spatial_scaler.transform(out_test_spatial)
            out_test_time = self.time_scaler.transform(out_test_time)

            # Ensemble prediction
            out_predictions = self._ensemble_predict_station(
                self.out_models, out_test_features, out_test_spatial, out_test_time
            )

            out_test_data.loc[:, 'prediction'] = np.maximum(out_predictions, 0)

            # Save results
            self._save_predictions(out_test_data, 'out')

        # Generate OD predictions
        print("Generating OD predictions...")
        od_test_data = self.od_data[self.od_data['hour'].isin(test_hours)].copy()

        if len(od_test_data) > 0:
            od_predictions = self._generate_od_predictions(od_test_data)

            od_test_data.loc[:, 'prediction'] = np.maximum(od_predictions, 0)

            # Save OD results
            od_test_data.to_csv('od_predictions.csv', index=False)
            print(f"✓ OD predictions saved: {len(od_test_data)} records")

        # Generate comprehensive comparison
        self._generate_comparison(in_test_data, out_test_data, od_test_data)

        return in_test_data, out_test_data, od_test_data

    def _ensemble_predict_station(self, models, features, spatial_features, time_features):
        """Ensemble prediction for station models with batch processing"""
        predictions = []

        # Create dataset for batch processing
        dataset = TensorDataset(
            torch.tensor(features, dtype=torch.float32),
            torch.tensor(spatial_features, dtype=torch.float32),
            torch.tensor(time_features, dtype=torch.float32)
        )
        dataloader = DataLoader(dataset, batch_size=self.batch_size, shuffle=False)

        for i, model in enumerate(models):
            if isinstance(model, RandomForestRegressor):
                # Random Forest prediction (CPU)
                combined_features = np.concatenate([features, spatial_features, time_features], axis=1)
                pred = model.predict(combined_features)
                predictions.append(pred)
            else:
                # Neural network prediction (GPU with batching)
                model.eval()
                all_preds = []

                with torch.no_grad():
                    for batch_features, batch_spatial, batch_time in dataloader:
                        batch_features = batch_features.to(self.device)
                        batch_spatial = batch_spatial.to(self.device)
                        batch_time = batch_time.to(self.device)

                        batch_pred = model(batch_features, batch_spatial, batch_time).squeeze()

                        # Handle single sample batches
                        if batch_pred.dim() == 0:
                            batch_pred = batch_pred.unsqueeze(0)

                        all_preds.append(batch_pred.cpu().numpy())

                pred = np.concatenate(all_preds)
                predictions.append(pred)

        # Ensemble averaging
        ensemble_pred = np.mean(predictions, axis=0)
        return ensemble_pred

    def _generate_od_predictions(self, od_test_data):
        """Generate OD predictions with memory efficiency"""
        # Prepare features in batches
        batch_size = 10000  # Process OD data in smaller batches
        all_predictions = []

        for start_idx in range(0, len(od_test_data), batch_size):
            end_idx = min(start_idx + batch_size, len(od_test_data))
            batch_data = od_test_data.iloc[start_idx:end_idx]

            origin_features = []
            dest_features = []
            od_pair_features = []
            time_features = []

            for _, row in batch_data.iterrows():
                o_station = row['o_station_clean']
                d_station = row['d_station_clean']
                hour = row['hour']

                if o_station in self.station_coords and d_station in self.station_coords:
                    # Origin features
                    o_coord = self.station_coords[o_station]
                    o_idx = self.station_to_idx[o_station]
                    origin_feature = np.concatenate([
                        self.grid_features,
                        [o_coord['longitude'], o_coord['latitude'], o_idx / len(self.stations)]
                    ])

                    # Destination features
                    d_coord = self.station_coords[d_station]
                    d_idx = self.station_to_idx[d_station]
                    dest_feature = np.concatenate([
                        self.grid_features,
                        [d_coord['longitude'], d_coord['latitude'], d_idx / len(self.stations)]
                    ])

                    # OD pair features
                    od_distance = np.sqrt(
                        (o_coord['longitude'] - d_coord['longitude'])**2 +
                        (o_coord['latitude'] - d_coord['latitude'])**2
                    )

                    od_pair_feature = np.array([
                        row.get('surface_distance', od_distance),
                        row.get('translate', 0),
                        row.get('time', od_distance * 10),
                        row.get('wait_time', 0)
                    ])

                    # Time features
                    time_feature = self._calculate_time_features(hour)

                    origin_features.append(origin_feature)
                    dest_features.append(dest_feature)
                    od_pair_features.append(od_pair_feature)
                    time_features.append(time_feature)
                else:
                    # Default features for missing stations
                    zero_feature = np.zeros(len(self.grid_features) + 3)
                    origin_features.append(zero_feature)
                    dest_features.append(zero_feature)
                    od_pair_features.append(np.zeros(4))
                    time_features.append(self._calculate_time_features(hour))

            # Convert to arrays and scale
            origin_features = np.array(origin_features)
            dest_features = np.array(dest_features)
            od_pair_features = np.array(od_pair_features)
            time_features = np.array(time_features)

            origin_features = self.feature_scaler.transform(origin_features)
            dest_features = self.feature_scaler.transform(dest_features)
            od_pair_features = self.od_scaler.transform(od_pair_features)
            time_features = self.time_scaler.transform(time_features)

            # Ensemble prediction for this batch
            batch_predictions = self._ensemble_predict_od(
                self.od_models, origin_features, dest_features, od_pair_features, time_features
            )

            all_predictions.extend(batch_predictions)

            if start_idx % 50000 == 0:
                print(f"  Processed {end_idx}/{len(od_test_data)} OD records")

        return np.array(all_predictions)

    def _ensemble_predict_od(self, models, origin_features, dest_features, od_pair_features, time_features):
        """Ensemble prediction for OD models"""
        predictions = []

        for i, model in enumerate(models):
            if isinstance(model, RandomForestRegressor):
                # Random Forest prediction
                combined_features = np.concatenate([origin_features, dest_features, od_pair_features, time_features], axis=1)
                pred = model.predict(combined_features)
                predictions.append(pred)
            else:
                # Neural network prediction with batching
                model.eval()
                all_preds = []

                # Create dataset for batch processing
                dataset = TensorDataset(
                    torch.tensor(origin_features, dtype=torch.float32),
                    torch.tensor(dest_features, dtype=torch.float32),
                    torch.tensor(od_pair_features, dtype=torch.float32),
                    torch.tensor(time_features, dtype=torch.float32)
                )
                dataloader = DataLoader(dataset, batch_size=self.batch_size, shuffle=False)

                with torch.no_grad():
                    for batch_origin, batch_dest, batch_od_pair, batch_time in dataloader:
                        batch_origin = batch_origin.to(self.device)
                        batch_dest = batch_dest.to(self.device)
                        batch_od_pair = batch_od_pair.to(self.device)
                        batch_time = batch_time.to(self.device)

                        batch_pred = model(batch_origin, batch_dest, batch_od_pair, batch_time).squeeze()

                        # Handle single sample batches
                        if batch_pred.dim() == 0:
                            batch_pred = batch_pred.unsqueeze(0)

                        all_preds.append(batch_pred.cpu().numpy())

                pred = np.concatenate(all_preds)
                predictions.append(pred)

        # Ensemble averaging
        ensemble_pred = np.mean(predictions, axis=0)
        return ensemble_pred

    def _save_predictions(self, data, flow_type):
        """Save prediction results"""
        filename = f'memory_efficient_{flow_type}_500_predictions_with_coords'

        try:
            import glob
            for f in glob.glob(f'{filename}.*'):
                try:
                    os.remove(f)
                except:
                    pass

            data.to_file(f'{filename}.shp')
            print(f"✓ {flow_type}-flow predictions saved: {len(data)} records")
        except Exception as e:
            print(f"Shapefile save failed: {e}")
            data_csv = data.drop(columns=['geometry'])
            data_csv.to_csv(f'{filename}.csv', index=False)
            print(f"✓ {flow_type}-flow predictions saved as CSV: {len(data)} records")

    def _generate_comparison(self, in_test_data, out_test_data, od_test_data):
        """Generate prediction comparison analysis"""
        print("Generating prediction comparison...")

        comparison_data = []

        # In-flow comparison
        if in_test_data is not None and len(in_test_data) > 0:
            for _, row in in_test_data.iterrows():
                comparison_data.append({
                    'Type': 'In',
                    'True_Value': float(row['count']),
                    'Predicted_Value': float(row['prediction']),
                    'Station': str(row['station_clean']),
                    'Hour': int(row['hour']),
                    'Error': float(abs(row['count'] - row['prediction']))
                })

        # Out-flow comparison
        if out_test_data is not None and len(out_test_data) > 0:
            for _, row in out_test_data.iterrows():
                comparison_data.append({
                    'Type': 'Out',
                    'True_Value': float(row['count']),
                    'Predicted_Value': float(row['prediction']),
                    'Station': str(row['station_clean']),
                    'Hour': int(row['hour']),
                    'Error': float(abs(row['count'] - row['prediction']))
                })

        # OD comparison
        if od_test_data is not None and len(od_test_data) > 0:
            for _, row in od_test_data.iterrows():
                comparison_data.append({
                    'Type': 'OD',
                    'True_Value': float(row['trip']),
                    'Predicted_Value': float(row['prediction']),
                    'Station': f"{row['o_station_clean']}->{row['d_station_clean']}",
                    'Hour': int(row['hour']),
                    'Error': float(abs(row['trip'] - row['prediction']))
                })

        # Save comparison results
        if comparison_data:
            comparison_df = pd.DataFrame(comparison_data)
            comparison_df.to_csv('memory_efficient_prediction_comparison.csv', index=False)
            print(f"✓ Prediction comparison saved: {len(comparison_data)} records")

            # Generate performance analysis
            self._analyze_performance(comparison_df)

        return comparison_data

    def _analyze_performance(self, comparison_df):
        """Analyze prediction performance"""
        print("\n" + "="*60)
        print("MEMORY-EFFICIENT MODEL PERFORMANCE ANALYSIS")
        print("="*60)

        performance_results = {}

        # Analyze by type
        for pred_type in ['In', 'Out', 'OD']:
            type_data = comparison_df[comparison_df['Type'] == pred_type]

            if len(type_data) > 0:
                true_values = type_data['True_Value'].values
                pred_values = type_data['Predicted_Value'].values

                mae = mean_absolute_error(true_values, pred_values)
                rmse = np.sqrt(mean_squared_error(true_values, pred_values))
                r2 = r2_score(true_values, pred_values)

                performance_results[pred_type] = {
                    'samples': len(type_data),
                    'mae': mae,
                    'rmse': rmse,
                    'r2': r2,
                    'mean_true': np.mean(true_values),
                    'mean_pred': np.mean(pred_values)
                }

                print(f"{pred_type} Flow Prediction:")
                print(f"  Samples: {len(type_data):,}")
                print(f"  MAE:  {mae:.4f}")
                print(f"  RMSE: {rmse:.4f}")
                print(f"  R²:   {r2:.4f}")
                print(f"  Mean True: {np.mean(true_values):.2f}")
                print(f"  Mean Pred: {np.mean(pred_values):.2f}")
                print()

        # Overall performance
        all_true = comparison_df['True_Value'].values
        all_pred = comparison_df['Predicted_Value'].values

        overall_mae = mean_absolute_error(all_true, all_pred)
        overall_rmse = np.sqrt(mean_squared_error(all_true, all_pred))
        overall_r2 = r2_score(all_true, all_pred)

        performance_results['Overall'] = {
            'samples': len(comparison_df),
            'mae': overall_mae,
            'rmse': overall_rmse,
            'r2': overall_r2,
            'mean_true': np.mean(all_true),
            'mean_pred': np.mean(all_pred)
        }

        print("🎯 OVERALL PERFORMANCE:")
        print(f"  Total Samples: {len(comparison_df):,}")
        print(f"  MAE:  {overall_mae:.4f}")
        print(f"  RMSE: {overall_rmse:.4f}")
        print(f"  R²:   {overall_r2:.4f} {'✅' if overall_r2 > 0.3 else '⚠️'}")
        print(f"  Mean True: {np.mean(all_true):.2f}")
        print(f"  Mean Pred: {np.mean(all_pred):.2f}")

        # Performance improvement analysis
        if overall_r2 > 0.3:
            print(f"\n🎉 SUCCESS: R² = {overall_r2:.4f} > 0.3 (Target achieved!)")
        else:
            print(f"\n⚠️  R² = {overall_r2:.4f} < 0.3 (Improvement achieved but target not fully met)")

        # Save performance results
        import json
        with open('memory_efficient_performance_analysis.json', 'w') as f:
            json.dump(performance_results, f, indent=2)

        print("✓ Performance analysis saved to memory_efficient_performance_analysis.json")

def main():
    """Main function for memory-efficient optimized prediction"""
    print("="*80)
    print("🚀 MEMORY-EFFICIENT OPTIMIZED SPATIAL METRO PREDICTION")
    print("Advanced architectures with memory optimization")
    print("Target: R² > 0.3, maintain spatial differentiation")
    print("="*80)

    start_time = time.time()

    try:
        # Initialize system
        system = MemoryEfficientPredictionSystem(batch_size=1024)

        # Load and process data
        if not system.load_and_process_data():
            return False

        # Train models
        system.train_memory_efficient_models()

        # Generate predictions
        in_results, out_results, od_results = system.generate_memory_efficient_predictions()

        print(f"\n⏱️  Total runtime: {time.time() - start_time:.2f} seconds")
        print("="*60)
        print("🎉 MEMORY-EFFICIENT PREDICTION SYSTEM COMPLETED!")
        print("="*60)

        return True

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
