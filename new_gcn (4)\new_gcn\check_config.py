#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简单的配置检查脚本
检查基本的Python环境和项目结构
"""

import sys
import os
import importlib.util

def check_python_version():
    """检查Python版本"""
    print("🐍 Python版本检查")
    print("-" * 30)
    print(f"Python版本: {sys.version}")
    
    major, minor = sys.version_info[:2]
    if major >= 3 and minor >= 8:
        print("✓ Python版本支持")
        return True
    else:
        print("✗ Python版本过低，需要3.8+")
        return False

def check_file_structure():
    """检查项目文件结构"""
    print("\n📁 项目文件检查")
    print("-" * 30)
    
    required_files = [
        'config.py',
        'data_loader.py', 
        'graph_builder.py',
        'gcn_model.py',
        'trainer.py',
        'predictor.py',
        'main.py',
        'requirements.txt'
    ]
    
    all_exist = True
    for file in required_files:
        if os.path.exists(file):
            print(f"✓ {file}")
        else:
            print(f"✗ {file} 缺失")
            all_exist = False
    
    return all_exist

def check_basic_imports():
    """检查基本导入"""
    print("\n📦 基本库检查")
    print("-" * 30)
    
    basic_libs = ['os', 'sys', 'numpy', 'pandas']
    optional_libs = ['torch', 'torch_geometric', 'geopandas', 'sklearn']
    
    for lib in basic_libs:
        try:
            spec = importlib.util.find_spec(lib)
            if spec is not None:
                print(f"✓ {lib}")
            else:
                print(f"✗ {lib} 未安装")
                return False
        except Exception as e:
            print(f"✗ {lib} 检查失败: {e}")
            return False
    
    print("\n可选库检查:")
    for lib in optional_libs:
        try:
            spec = importlib.util.find_spec(lib)
            if spec is not None:
                print(f"✓ {lib}")
            else:
                print(f"⚠ {lib} 未安装（需要安装）")
        except Exception as e:
            print(f"⚠ {lib} 检查失败: {e}")
    
    return True

def check_config_file():
    """检查配置文件"""
    print("\n⚙️ 配置文件检查")
    print("-" * 30)
    
    try:
        # 读取配置文件内容
        with open('config.py', 'r', encoding='utf-8') as f:
            config_content = f.read()
        
        # 检查关键配置
        if 'BASE_DIR' in config_content:
            print("✓ BASE_DIR 配置存在")
        else:
            print("✗ BASE_DIR 配置缺失")
        
        if 'DEVICE' in config_content:
            print("✓ DEVICE 配置存在")
        else:
            print("✗ DEVICE 配置缺失")
        
        if 'BATCH_SIZE' in config_content:
            print("✓ BATCH_SIZE 配置存在")
        else:
            print("✗ BATCH_SIZE 配置缺失")
        
        print("✓ 配置文件基本结构正确")
        return True
        
    except Exception as e:
        print(f"✗ 配置文件检查失败: {e}")
        return False

def check_data_paths():
    """检查数据文件路径"""
    print("\n📊 数据路径检查")
    print("-" * 30)
    
    try:
        # 模拟读取配置
        base_dir = r"C:\Users\<USER>\Desktop\接驳"
        
        data_files = [
            'in_500_with_coords.shp',
            'out_500_with_coords.shp', 
            'updated北京市_subway_od_2024_modified3.csv',
            'station_features_result.csv'
        ]
        
        print(f"基础目录: {base_dir}")
        if os.path.exists(base_dir):
            print("✓ 基础目录存在")
            
            for file in data_files:
                file_path = os.path.join(base_dir, file)
                if os.path.exists(file_path):
                    print(f"✓ {file}")
                else:
                    print(f"⚠ {file} 不存在")
        else:
            print("⚠ 基础目录不存在，请检查数据路径")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据路径检查失败: {e}")
        return False

def generate_install_guide():
    """生成安装指南"""
    print("\n📖 安装指南")
    print("=" * 50)
    
    print("1. 安装基础依赖:")
    print("   pip install numpy pandas matplotlib scikit-learn tqdm")
    print("")
    print("2. 安装地理数据处理库:")
    print("   pip install geopandas shapely fiona pyproj rasterio")
    print("")
    print("3. 安装PyTorch (CPU版本):")
    print("   pip install torch torchvision torchaudio")
    print("")
    print("4. 安装PyTorch (GPU版本，如果有CUDA):")
    print("   pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118")
    print("")
    print("5. 安装PyTorch Geometric:")
    print("   pip install torch-geometric")
    print("   pip install torch-scatter torch-sparse torch-cluster torch-spline-conv -f https://data.pyg.org/whl/torch-2.0.0+cpu.html")
    print("")
    print("6. 验证安装:")
    print("   python quick_test.py")

def main():
    """主函数"""
    print("🔧 GCN项目配置检查")
    print("=" * 50)
    
    checks = [
        check_python_version(),
        check_file_structure(),
        check_basic_imports(),
        check_config_file(),
        check_data_paths()
    ]
    
    if all(checks):
        print("\n✅ 基本配置检查通过！")
        print("🎯 可以尝试安装深度学习依赖并运行项目")
    else:
        print("\n⚠️ 发现配置问题，请检查上述错误")
    
    generate_install_guide()
    
    return all(checks)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 