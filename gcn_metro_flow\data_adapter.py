"""
通用数据适配器 - 支持不同城市和格式的地铁数据
"""
import os
import glob
import pandas as pd
import geopandas as gpd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
import warnings
warnings.filterwarnings('ignore')

from config import MetroFlowConfig

class UniversalDataAdapter:
    """通用地铁数据适配器"""
    
    def __init__(self, config: MetroFlowConfig):
        self.config = config
        self.field_mapping = config.get('data.field_mapping', {})
        self.base_dir = config.get('data.base_dir')
        
        # 数据缓存
        self._data_cache = {}
        
        # 自动检测的文件路径
        self._detected_files = {}
        
    def auto_detect_files(self) -> Dict[str, str]:
        """自动检测数据文件"""
        print("自动检测数据文件...")
        
        file_patterns = self.config.get('data.file_patterns', {})
        specific_files = self.config.get('data.specific_files', {})
        
        detected = {}
        
        for file_type, pattern in file_patterns.items():
            # 首先尝试使用特定文件名
            if file_type in specific_files:
                specific_path = os.path.join(self.base_dir, specific_files[file_type])
                if os.path.exists(specific_path):
                    detected[file_type] = specific_path
                    print(f"✓ {file_type}: {specific_files[file_type]}")
                    continue
            
            # 使用模式匹配
            pattern_path = os.path.join(self.base_dir, pattern)
            matching_files = glob.glob(pattern_path)
            
            if matching_files:
                # 选择最新的文件
                latest_file = max(matching_files, key=os.path.getmtime)
                detected[file_type] = latest_file
                print(f"✓ {file_type}: {os.path.basename(latest_file)} (自动检测)")
            else:
                print(f"✗ {file_type}: 未找到匹配 {pattern} 的文件")
        
        self._detected_files = detected
        return detected
    
    def load_flow_data(self, flow_type: str) -> Optional[pd.DataFrame]:
        """加载进出站流量数据"""
        file_key = f"{flow_type}_flow"
        
        if file_key not in self._detected_files:
            print(f"警告: 未找到 {flow_type} 流量数据文件")
            return None
        
        filepath = self._detected_files[file_key]
        
        try:
            # 根据文件扩展名选择加载方法
            if filepath.endswith('.shp'):
                data = gpd.read_file(filepath)
                # 转换为DataFrame，保留坐标信息
                df = pd.DataFrame(data.drop(columns='geometry'))
                if 'geometry' in data.columns:
                    # 提取坐标
                    df['longitude'] = data.geometry.x
                    df['latitude'] = data.geometry.y
            else:
                data = pd.read_csv(filepath)
                df = data.copy()
            
            print(f"✓ 加载 {flow_type} 流量数据: {df.shape}")
            
            # 标准化字段名
            df = self._standardize_fields(df, ['station_name', 'hour', 'flow_count', 'longitude', 'latitude'])
            
            # 数据清洗
            df = self._clean_flow_data(df)
            
            return df
            
        except Exception as e:
            print(f"✗ 加载 {flow_type} 流量数据失败: {e}")
            return None
    
    def load_od_data(self) -> Optional[pd.DataFrame]:
        """加载OD流量数据"""
        if 'od_flow' not in self._detected_files:
            print("警告: 未找到OD流量数据文件")
            return None
        
        filepath = self._detected_files['od_flow']
        
        try:
            df = pd.read_csv(filepath)
            print(f"✓ 加载OD数据: {df.shape}")
            
            # 标准化字段名
            df = self._standardize_fields(df, ['origin', 'destination', 'hour', 'trip_count'])
            
            # 数据清洗
            df = self._clean_od_data(df)
            
            return df
            
        except Exception as e:
            print(f"✗ 加载OD数据失败: {e}")
            return None
    
    def load_connection_data(self) -> Optional[pd.DataFrame]:
        """加载站点连接数据"""
        if 'station_connect' not in self._detected_files:
            print("警告: 未找到站点连接数据文件")
            return None
        
        filepath = self._detected_files['station_connect']
        
        try:
            df = pd.read_csv(filepath)
            print(f"✓ 加载连接数据: {df.shape}")
            
            # 标准化字段名
            df = self._standardize_fields(df, ['station_1', 'station_2'])
            
            # 数据清洗
            df = self._clean_connection_data(df)
            
            return df
            
        except Exception as e:
            print(f"✗ 加载连接数据失败: {e}")
            return None
    
    def load_subway_flow_data(self) -> Optional[pd.DataFrame]:
        """加载相邻站间流量数据"""
        if 'subway_flow' not in self._detected_files:
            print("警告: 未找到相邻站流量数据文件")
            return None
        
        filepath = self._detected_files['subway_flow']
        
        try:
            df = pd.read_csv(filepath)
            print(f"✓ 加载相邻站流量数据: {df.shape}")
            
            # 标准化字段名
            df = self._standardize_fields(df, ['origin', 'destination', 'hour', 'trip_count'])
            
            return df
            
        except Exception as e:
            print(f"✗ 加载相邻站流量数据失败: {e}")
            return None
    
    def load_grid_features(self) -> Optional[Dict[str, Any]]:
        """加载栅格特征数据"""
        if 'grid_features' not in self._detected_files:
            print("警告: 未找到栅格特征数据文件")
            return None
        
        filepath = self._detected_files['grid_features']
        
        try:
            df = pd.read_csv(filepath)
            print(f"✓ 加载栅格特征数据: {df.shape}")
            
            # 识别特征列
            id_cols = ['id', 'grid_id', '站名', 'station_name', 'geometry']
            feature_cols = [col for col in df.columns if col not in id_cols]
            
            # 提取特征
            features = df[feature_cols].values
            
            # 标准化特征
            if self.config.get('data_processing.feature_normalize', True):
                from sklearn.preprocessing import StandardScaler
                scaler = StandardScaler()
                features = scaler.fit_transform(features)
            
            result = {
                'features': features,
                'feature_names': feature_cols,
                'num_features': len(feature_cols)
            }
            
            # 添加ID信息
            if 'id' in df.columns:
                result['grid_ids'] = df['id'].values
            elif 'grid_id' in df.columns:
                result['grid_ids'] = df['grid_id'].values
            
            # 添加站点信息
            if '站名' in df.columns:
                result['station_names'] = df['站名'].values
            elif 'station_name' in df.columns:
                result['station_names'] = df['station_name'].values
            
            return result
            
        except Exception as e:
            print(f"✗ 加载栅格特征数据失败: {e}")
            return None
    
    def _standardize_fields(self, df: pd.DataFrame, required_fields: List[str]) -> pd.DataFrame:
        """标准化字段名"""
        result_df = df.copy()
        
        for field in required_fields:
            if field in self.field_mapping:
                possible_names = self.field_mapping[field]
                
                # 查找匹配的列名
                found_col = None
                for col in df.columns:
                    if col in possible_names:
                        found_col = col
                        break
                
                if found_col and found_col != field:
                    result_df = result_df.rename(columns={found_col: field})
                elif not found_col:
                    print(f"警告: 未找到字段 {field} 的匹配列")
        
        return result_df
    
    def _clean_flow_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗流量数据"""
        # 清理站点名称
        if 'station_name' in df.columns:
            df['station_clean'] = df['station_name'].apply(self._clean_station_name)
        
        # 过滤异常值
        if 'flow_count' in df.columns and self.config.get('data_processing.remove_outliers', True):
            df = self._remove_outliers(df, 'flow_count')
        
        # 过滤最小流量阈值
        min_threshold = self.config.get('data_processing.min_flow_threshold', 1)
        if 'flow_count' in df.columns:
            df = df[df['flow_count'] >= min_threshold]
        
        return df
    
    def _clean_od_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗OD数据"""
        # 清理站点名称
        if 'origin' in df.columns:
            df['origin_clean'] = df['origin'].apply(self._clean_station_name)
        if 'destination' in df.columns:
            df['destination_clean'] = df['destination'].apply(self._clean_station_name)
        
        # 过滤异常值
        if 'trip_count' in df.columns and self.config.get('data_processing.remove_outliers', True):
            df = self._remove_outliers(df, 'trip_count')
        
        return df
    
    def _clean_connection_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗连接数据"""
        # 清理站点名称
        if 'station_1' in df.columns:
            df['station_1_clean'] = df['station_1'].apply(self._clean_station_name)
        if 'station_2' in df.columns:
            df['station_2_clean'] = df['station_2'].apply(self._clean_station_name)
        
        # 去重
        if 'station_1_clean' in df.columns and 'station_2_clean' in df.columns:
            df = df.drop_duplicates(subset=['station_1_clean', 'station_2_clean'])
        
        return df
    
    def _clean_station_name(self, station_name: str) -> str:
        """清理站点名称"""
        if pd.isna(station_name):
            return station_name
        
        name = str(station_name).strip()
        
        # 移除下划线后的内容
        if '_' in name:
            name = name.split('_')[0]
        
        # 移除括号内容
        if '(' in name:
            name = name.split('(')[0]
        if '（' in name:
            name = name.split('（')[0]
        
        return name.strip()
    
    def _remove_outliers(self, df: pd.DataFrame, column: str) -> pd.DataFrame:
        """移除异常值"""
        if column not in df.columns:
            return df
        
        threshold = self.config.get('data_processing.outlier_threshold', 3.0)
        
        Q1 = df[column].quantile(0.25)
        Q3 = df[column].quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - threshold * IQR
        upper_bound = Q3 + threshold * IQR
        
        before_count = len(df)
        df_clean = df[(df[column] >= lower_bound) & (df[column] <= upper_bound)]
        after_count = len(df_clean)
        
        if before_count > after_count:
            print(f"移除 {before_count - after_count} 个异常值 ({column})")
        
        return df_clean
    
    def get_all_stations(self) -> List[str]:
        """获取所有唯一站点"""
        stations = set()
        
        # 从进出站数据获取
        for flow_type in ['in', 'out']:
            flow_data = self.load_flow_data(flow_type)
            if flow_data is not None and 'station_clean' in flow_data.columns:
                stations.update(flow_data['station_clean'].unique())
        
        # 从OD数据获取
        od_data = self.load_od_data()
        if od_data is not None:
            if 'origin_clean' in od_data.columns:
                stations.update(od_data['origin_clean'].unique())
            if 'destination_clean' in od_data.columns:
                stations.update(od_data['destination_clean'].unique())
        
        # 从连接数据获取
        connect_data = self.load_connection_data()
        if connect_data is not None:
            if 'station_1_clean' in connect_data.columns:
                stations.update(connect_data['station_1_clean'].unique())
            if 'station_2_clean' in connect_data.columns:
                stations.update(connect_data['station_2_clean'].unique())
        
        return sorted(list(stations))
    
    def validate_data_consistency(self) -> Dict[str, Any]:
        """验证数据一致性"""
        print("验证数据一致性...")
        
        validation_results = {
            'stations': {},
            'hours': {},
            'data_quality': {},
            'recommendations': []
        }
        
        # 获取所有站点
        all_stations = self.get_all_stations()
        validation_results['stations']['total_count'] = len(all_stations)
        
        # 检查各数据源的站点覆盖
        for data_type in ['in_flow', 'out_flow', 'od_flow', 'station_connect']:
            if data_type in self._detected_files:
                # 这里可以添加更详细的验证逻辑
                pass
        
        print(f"数据验证完成，共发现 {len(all_stations)} 个站点")
        return validation_results
