import networkx as nx
import pandas as pd
from collections import defaultdict

# 1. 读取数据
def read_data(od_file, connection_file):
    # 读取OD数据
    od_data = pd.read_csv(od_file)
    # 读取路线连接数据
    connections = pd.read_csv(connection_file)
    return od_data, connections

# 2. 构建图
def build_graph(connections):
    G = nx.Graph()
    
    # 添加所有边，权重为2分钟（站间运行时间）
    for _, row in connections.iterrows():
        station1, station2 = row['station_1'], row['station_2']
        G.add_edge(station1, station2, weight=2)
        
    # 处理换乘站
    transfer_stations = defaultdict(set)
    for node in G.nodes():
        station_name = node.split('_')[0]
        line_number = node.split('_')[1]
        transfer_stations[station_name].add(line_number)
        
    # 添加换乘边，权重设为3分钟（换乘时间）
    for station, lines in transfer_stations.items():
        if len(lines) > 1:
            lines = list(lines)
            for i in range(len(lines)):
                for j in range(i+1, len(lines)):
                    station1 = f"{station}_{lines[i]}"
                    station2 = f"{station}_{lines[j]}"
                    G.add_edge(station1, station2, weight=3)
    print(2)
    
    return G

# 3. 计算最短路径并分配流量
def calculate_flow(G, od_data):
    flow_on_edges = defaultdict(float)
    
    for _, row in od_data.iterrows():
        o_station = row['o_rawname']
        d_station = row['d_rawname']
        flow = row['trip']
        
        # 找到起终点对应的所有可能的站点（考虑不同线路）
        o_nodes = [n for n in G.nodes() if n.startswith(f"{o_station}_")]
        d_nodes = [n for n in G.nodes() if n.startswith(f"{d_station}_")]
        
        if not o_nodes or not d_nodes:
            continue
            
        # 找到最短路径
        shortest_path = None
        min_length = float('inf')
        
        for o_node in o_nodes:
            for d_node in d_nodes:
                try:
                    path = nx.shortest_path(G, o_node, d_node, weight='weight')
                    length = sum(G[path[i]][path[i+1]]['weight'] for i in range(len(path)-1))
                    if length < min_length:
                        min_length = length
                        shortest_path = path
                except nx.NetworkXNoPath:
                    continue
        
        if shortest_path:
            # 将流量分配到路径上的每条边
            for i in range(len(shortest_path)-1):
                edge = tuple(sorted([shortest_path[i], shortest_path[i+1]]))
                flow_on_edges[edge] += flow
    print(3)
    return flow_on_edges

# 4. 生成结果
def generate_results(flow_on_edges):
    results = []
    for (station1, station2), flow in flow_on_edges.items():
        results.append({
            'o_rawname': station1.split('_')[0],
            'd_rawname': station2.split('_')[0],
            'trip': flow,
            'time': 2 if station1.split('_')[1] == station2.split('_')[1] else 3
        })
    print(4)
    return pd.DataFrame(results)

# 主函数
def main():
    # 文件路径
    od_file = r"C:\Users\<USER>\Desktop\接驳\updated北京市_subway_od_2024_modified3.csv"
    connection_file = r"C:\Users\<USER>\Desktop\接驳\station_connect_2023.csv"
    output_file = r"C:\Users\<USER>\Desktop\接驳\station_flow_results.csv"
    
    # 读取数据
    od_data, connections = read_data(od_file, connection_file)
    
    # 构建图
    G = build_graph(connections)
    
    # 计算流量
    flow_on_edges = calculate_flow(G, od_data)
    
    # 生成结果
    results = generate_results(flow_on_edges)
    
    # 保存结果
    results.to_csv(output_file, index=False)

if __name__ == "__main__":
    main()

