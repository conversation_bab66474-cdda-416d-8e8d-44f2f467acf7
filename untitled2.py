import pandas as pd
import os

# File paths
od_csv = "C:\\Users\\<USER>\\Desktop\\接驳\\updated_北京市_subway_od_2024_modified3.csv"
missing_values_csv = "C:\\Users\\<USER>\\Desktop\\接驳\\od_missing_values.csv"



# Ensure the output directory exists
output_dir = os.path.dirname(missing_values_csv)
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

try:
    od_df = pd.read_csv(od_csv)
    print(f"Loaded {od_csv} successfully.")
except Exception as e:
    print(f"Error loading {od_csv}: {str(e)}")


# Step 1: Extract rows with missing values in the specified columns
missing_cols = [
    'o_subway_id_x', 'd_subway_id_x', 'surface_distance', 'o_id', 'd_id',
    'o_subway_id_y', 'd_subway_id_y', 'translate', 'time'
]
missing_rows = od_df[od_df[missing_cols].isna().any(axis=1)]

# Save missing rows to a new CSV for inspection
if not missing_rows.empty:
    missing_rows.to_csv(missing_values_csv, index=False, encoding='utf-8')
    print(f"Saved {len(missing_rows)} rows with missing values to {missing_values_csv}.")
else:
    print("No rows with missing values found.")

# Step 2: Fill missing values in specified columns
od_df['surface_distance'] = od_df['surface_distance'].fillna(2000)
od_df['translate'] = od_df['translate'].fillna(300)
od_df['time'] = od_df['time'].fillna(1000)

# Verify filling
print("\nMissing values after filling:")
print(od_df[['surface_distance', 'translate', 'time']].isna().sum())

# Step 3: Save the modified DataFrame back to the original file
try:
    od_df.to_csv(od_csv, index=False, encoding='utf-8')
    print(f"Saved modified DataFrame back to {od_csv}.")
except Exception as e:
    print(f"Error saving to {od_csv}: {str(e)}")