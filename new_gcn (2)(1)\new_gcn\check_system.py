#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
GCN地铁流量预测系统环境检查脚本
验证系统依赖和配置是否正确
"""

import sys
import os
import warnings
warnings.filterwarnings('ignore')

def check_python_version():
    """检查Python版本"""
    print("=" * 50)
    print("检查Python版本...")
    
    version = sys.version_info
    print(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major >= 3 and version.minor >= 8:
        print("✓ Python版本满足要求 (>= 3.8)")
        return True
    else:
        print("✗ Python版本过低，需要 >= 3.8")
        return False

def check_dependencies():
    """检查依赖包"""
    print("\n" + "=" * 50)
    print("检查依赖包...")
    
    required_packages = [
        ('torch', '1.12.0'),
        ('torch_geometric', '2.3.0'),
        ('numpy', '1.21.0'),
        ('pandas', '1.3.0'),
        ('geopandas', '0.12.0'),
        ('shapely', '1.8.0'),
        ('scikit-learn', '1.0.0'),
        ('matplotlib', '3.5.0'),
        ('seaborn', '0.11.0'),
        ('tqdm', '4.64.0'),
        ('networkx', '2.8.0')
    ]
    
    missing_packages = []
    
    for package, min_version in required_packages:
        try:
            if package == 'torch_geometric':
                import torch_geometric
                version = torch_geometric.__version__
            else:
                module = __import__(package)
                version = getattr(module, '__version__', 'unknown')
            
            print(f"✓ {package}: {version}")
        except ImportError:
            print(f"✗ {package}: 未安装")
            missing_packages.append(package)
        except Exception as e:
            print(f"? {package}: 检查时出错 - {e}")
    
    if missing_packages:
        print(f"\n缺少以下包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    else:
        print("\n✓ 所有依赖包已正确安装")
        return True

def check_torch_installation():
    """检查PyTorch安装"""
    print("\n" + "=" * 50)
    print("检查PyTorch安装...")
    
    try:
        import torch
        import torch_geometric
        
        print(f"PyTorch版本: {torch.__version__}")
        print(f"PyTorch Geometric版本: {torch_geometric.__version__}")
        
        # 检查CUDA支持
        if torch.cuda.is_available():
            print(f"✓ CUDA可用 - 版本: {torch.version.cuda}")
            print(f"✓ GPU数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                gpu_name = torch.cuda.get_device_name(i)
                gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
                print(f"  GPU {i}: {gpu_name} ({gpu_memory:.1f}GB)")
        else:
            print("! CUDA不可用，将使用CPU训练")
        
        # 测试基本操作
        x = torch.randn(10, 10)
        if torch.cuda.is_available():
            x = x.cuda()
            print("✓ GPU张量操作正常")
        
        print("✓ PyTorch安装正常")
        return True
        
    except Exception as e:
        print(f"✗ PyTorch安装有问题: {e}")
        return False

def check_project_files():
    """检查项目文件完整性"""
    print("\n" + "=" * 50)
    print("检查项目文件...")
    
    required_files = [
        'config.py',
        'data_loader.py',
        'graph_builder.py',
        'gcn_model.py',
        'trainer.py',
        'predictor.py',
        'main.py',
        'requirements.txt'
    ]
    
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✓ {file}")
        else:
            print(f"✗ {file}")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n缺少文件: {', '.join(missing_files)}")
        return False
    else:
        print("\n✓ 所有项目文件完整")
        return True

def check_data_paths():
    """检查数据路径配置"""
    print("\n" + "=" * 50)
    print("检查数据路径配置...")
    
    try:
        from config import Config
        config = Config()
        
        data_files = [
            ('进站数据', config.IN_STATION_PATH),
            ('出站数据', config.OUT_STATION_PATH),
            ('OD数据', config.OD_PATH),
            ('站点特征', config.STATION_FEATURES_PATH)
        ]
        
        all_exist = True
        
        for name, path in data_files:
            if os.path.exists(path):
                size = os.path.getsize(path) / 1024 / 1024  # MB
                print(f"✓ {name}: {path} ({size:.1f}MB)")
            else:
                print(f"✗ {name}: {path} (文件不存在)")
                all_exist = False
        
        if all_exist:
            print("\n✓ 所有数据文件路径正确")
        else:
            print("\n! 部分数据文件未找到，请检查config.py中的路径配置")
            print("或者运行示例模式测试基本功能")
        
        return all_exist
        
    except Exception as e:
        print(f"✗ 配置检查失败: {e}")
        return False

def check_memory_requirements():
    """检查内存要求"""
    print("\n" + "=" * 50)
    print("检查内存要求...")
    
    try:
        import psutil
        
        # 系统内存
        memory = psutil.virtual_memory()
        total_memory_gb = memory.total / 1024**3
        available_memory_gb = memory.available / 1024**3
        
        print(f"系统总内存: {total_memory_gb:.1f}GB")
        print(f"可用内存: {available_memory_gb:.1f}GB")
        
        if available_memory_gb >= 8:
            print("✓ 内存充足")
        elif available_memory_gb >= 4:
            print("! 内存偏少，建议启用Float16模式")
        else:
            print("! 内存不足，可能会出现内存错误")
        
        # GPU内存
        try:
            import torch
            if torch.cuda.is_available():
                for i in range(torch.cuda.device_count()):
                    gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
                    print(f"GPU {i} 内存: {gpu_memory:.1f}GB")
                    
                    if gpu_memory >= 8:
                        print(f"✓ GPU {i} 内存充足")
                    else:
                        print(f"! GPU {i} 内存偏少，建议使用Float16模式")
        except:
            pass
        
        return True
        
    except ImportError:
        print("! psutil未安装，无法检查内存")
        return True
    except Exception as e:
        print(f"内存检查出错: {e}")
        return True

def check_model_creation():
    """检查模型创建"""
    print("\n" + "=" * 50)
    print("检查模型创建...")
    
    try:
        from config import Config
        from gcn_model import create_model, ModelUtils
        
        config = Config()
        config.HIDDEN_DIM = 64  # 减小模型以快速测试
        
        print("创建GCN模型...")
        model = create_model(config, 'complex')
        
        # 打印模型信息
        param_count = ModelUtils.count_parameters(model)
        print(f"✓ 模型创建成功，参数数量: {param_count:,}")
        
        # 测试前向传播
        import torch
        import numpy as np
        
        batch_size, seq_len, num_nodes = 2, 5, 10
        x = torch.randn(batch_size, seq_len, num_nodes)
        edge_index = torch.randint(0, num_nodes, (2, 20))
        edge_attr = torch.randn(20)
        time_features = torch.randn(batch_size, seq_len, num_nodes, 6)
        spatial_features = torch.randn(batch_size, seq_len, num_nodes, 51)
        
        with torch.no_grad():
            output, attention = model(x, edge_index, edge_attr, 
                                    time_features, spatial_features)
        
        print(f"✓ 前向传播测试成功，输出形状: {output.shape}")
        return True
        
    except Exception as e:
        print(f"✗ 模型创建失败: {e}")
        return False

def run_basic_functionality_test():
    """运行基本功能测试"""
    print("\n" + "=" * 50)
    print("运行基本功能测试...")
    
    try:
        from graph_builder import GraphBuilder
        import numpy as np
        
        # 测试图构建
        print("测试图构建功能...")
        graph_builder = GraphBuilder()
        
        # 模拟站点坐标
        station_coords = {
            'station_A': (116.3974, 39.9093),
            'station_B': (116.4074, 39.9093),
            'station_C': (116.3974, 39.9193)
        }
        
        edge_index, edge_attr, stations = graph_builder.create_distance_graph(
            station_coords, distance_threshold=2000
        )
        
        print(f"✓ 图构建成功: {len(stations)}个节点, {edge_index.shape[1]}条边")
        
        # 测试数据加载器基本功能
        print("测试数据处理功能...")
        from data_loader import DataLoader
        
        data_loader = DataLoader()
        
        # 测试时间特征创建
        hours = np.array([8, 12, 18, 22])
        time_features = data_loader.create_time_features(hours)
        print(f"✓ 时间特征创建成功: {time_features.shape}")
        
        print("✓ 基本功能测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 基本功能测试失败: {e}")
        return False

def main():
    """主检查函数"""
    print("GCN地铁流量预测系统 - 环境检查")
    print("开始系统检查...")
    
    checks = [
        ("Python版本", check_python_version),
        ("依赖包", check_dependencies),
        ("PyTorch安装", check_torch_installation),
        ("项目文件", check_project_files),
        ("数据路径", check_data_paths),
        ("内存要求", check_memory_requirements),
        ("模型创建", check_model_creation),
        ("基本功能", run_basic_functionality_test)
    ]
    
    results = {}
    
    for name, check_func in checks:
        try:
            results[name] = check_func()
        except Exception as e:
            print(f"✗ {name} 检查时出错: {e}")
            results[name] = False
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("检查结果汇总:")
    print("=" * 50)
    
    passed = 0
    total = len(checks)
    
    for name, result in results.items():
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{name:<15}: {status}")
        if result:
            passed += 1
    
    print("=" * 50)
    print(f"总计: {passed}/{total} 项检查通过")
    
    if passed == total:
        print("\n🎉 所有检查通过！系统可以正常使用。")
        print("\n建议操作：")
        print("1. 运行 python example.py 查看使用示例")
        print("2. 运行 python main.py --mode train 开始训练")
    elif passed >= total * 0.7:
        print("\n⚠️  大部分检查通过，系统基本可用。")
        print("建议修复失败的检查项后再使用。")
    else:
        print("\n❌ 多项检查失败，建议先解决环境问题。")
        print("\n常见解决方案：")
        print("1. pip install -r requirements.txt")
        print("2. 检查config.py中的数据路径")
        print("3. 确保有足够的内存和存储空间")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 