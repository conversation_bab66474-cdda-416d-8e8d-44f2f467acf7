"""
GPU优化的T-GCN模型
使用混合精度、高效图卷积、内存优化等技术
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.checkpoint import checkpoint
import math

class GPUOptimizedTGCN(nn.Module):
    def __init__(self, input_dim, hidden_dim, num_nodes, output_tasks=['in_flow', 'out_flow'], 
                 use_checkpoint=True, num_heads=4, dropout=0.1):
        super(GPUOptimizedTGCN, self).__init__()
        
        self.hidden_dim = hidden_dim
        self.num_nodes = num_nodes
        self.output_tasks = output_tasks
        self.use_checkpoint = use_checkpoint
        
        # 高效的图卷积层
        self.gcn_layers = nn.ModuleList([
            EfficientGCNLayer(input_dim, hidden_dim, dropout=dropout),
            EfficientGCNLayer(hidden_dim, hidden_dim, dropout=dropout)
        ])
        
        # 轻量级时序建模
        self.temporal_encoder = MemoryEfficientTemporalEncoder(
            hidden_dim, hidden_dim, dropout=dropout
        )
        
        # 内存优化的注意力
        self.attention = MemoryEfficientAttention(
            hidden_dim, num_heads=num_heads, dropout=dropout
        )
        
        # 多任务输出头（权重共享减少参数）
        self.shared_encoder = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout)
        )
        
        self.output_heads = nn.ModuleDict()
        for task in output_tasks:
            self.output_heads[task] = nn.Linear(hidden_dim // 2, 1)
        
        # LayerNorm替代BatchNorm (更内存友好)
        self.layer_norm = nn.LayerNorm(hidden_dim)
        
        # 初始化权重
        self.apply(self._init_weights)
    
    def _init_weights(self, module):
        """高效的权重初始化"""
        if isinstance(module, nn.Linear):
            torch.nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
    
    def forward(self, x, edge_index, edge_weights=None):
        """
        GPU优化的前向传播
        
        Args:
            x: [batch_size, time_steps, num_nodes, input_dim]
            edge_index: [2, num_edges]
            edge_weights: [num_edges] or None
        """
        batch_size, time_steps, num_nodes, input_dim = x.shape
        
        # 处理边权重
        if edge_weights is None:
            edge_weights = torch.ones(edge_index.size(1), device=x.device)
        
        # 使用checkpointing减少内存使用
        if self.use_checkpoint and self.training:
            node_features = checkpoint(
                self._forward_spatial_temporal, x, edge_index, edge_weights, use_reentrant=False
            )
        else:
            node_features = self._forward_spatial_temporal(x, edge_index, edge_weights)
        
        # 注意力机制
        if self.use_checkpoint and self.training:
            attended_features = checkpoint(self.attention, node_features, use_reentrant=False)
        else:
            attended_features = self.attention(node_features)
        
        # 残差连接 + LayerNorm
        features = self.layer_norm(attended_features + node_features)
        
        # 共享编码器
        encoded_features = self.shared_encoder(features)
        
        # 多任务预测
        outputs = {}
        for task in self.output_tasks:
            outputs[task] = self.output_heads[task](encoded_features).squeeze(-1)
        
        return outputs
    
    def _forward_spatial_temporal(self, x, edge_index, edge_weights):
        """空间-时序特征提取"""
        batch_size, time_steps, num_nodes, input_dim = x.shape
        
        # 批量处理所有时间步的图卷积
        x_flat = x.view(-1, num_nodes, input_dim)  # [batch*time, nodes, features]
        
        # 高效图卷积
        h = x_flat
        for gcn_layer in self.gcn_layers:
            h = gcn_layer(h, edge_index, edge_weights)
        
        # 重塑回时序格式
        h = h.view(batch_size, time_steps, num_nodes, self.hidden_dim)
        
        # 时序编码
        node_features = self.temporal_encoder(h)
        
        return node_features

class EfficientGCNLayer(nn.Module):
    """高效的图卷积层"""
    
    def __init__(self, input_dim, output_dim, dropout=0.1, bias=True):
        super(EfficientGCNLayer, self).__init__()
        
        self.input_dim = input_dim
        self.output_dim = output_dim
        
        # 使用单个线性层减少参数
        self.lin = nn.Linear(input_dim, output_dim, bias=bias)
        self.dropout = nn.Dropout(dropout)
        
        self.reset_parameters()
    
    def reset_parameters(self):
        self.lin.reset_parameters()
    
    def forward(self, x, edge_index, edge_weights=None):
        """
        高效的前向传播
        
        Args:
            x: [batch*num_nodes, input_dim] 或 [num_nodes, input_dim]
            edge_index: [2, num_edges]
            edge_weights: [num_edges]
        """
        # 处理批量维度
        if x.dim() == 3:
            batch_size, num_nodes, input_dim = x.shape
            x = x.view(-1, input_dim)
            is_batched = True
            batch_offset = torch.arange(batch_size, device=x.device) * num_nodes
            batch_offset = batch_offset.view(-1, 1, 1).expand(-1, 2, edge_index.size(1))
            edge_index_batch = edge_index.unsqueeze(0).expand(batch_size, -1, -1) + batch_offset
            edge_index = edge_index_batch.view(2, -1)
            if edge_weights is not None:
                edge_weights = edge_weights.repeat(batch_size)
        else:
            batch_size = 1
            num_nodes = x.size(0)
            is_batched = False
        
        # 线性变换
        x_transformed = self.lin(x)
        
        # 图卷积操作
        out = self._graph_conv(x_transformed, edge_index, edge_weights)
        
        # 恢复批量维度
        if is_batched:
            out = out.view(batch_size, num_nodes, self.output_dim)
        
        return F.relu(self.dropout(out), inplace=True)
    
    def _graph_conv(self, x, edge_index, edge_weights):
        """高效的图卷积实现"""
        num_nodes = x.size(0)
        
        # 度归一化
        row, col = edge_index
        deg = torch.zeros(num_nodes, dtype=x.dtype, device=x.device)
        deg.scatter_add_(0, col, torch.ones_like(col, dtype=x.dtype))
        deg_inv_sqrt = deg.pow(-0.5)
        deg_inv_sqrt[deg_inv_sqrt == float('inf')] = 0
        
        # 归一化边权重
        if edge_weights is not None:
            norm = deg_inv_sqrt[row] * deg_inv_sqrt[col] * edge_weights
        else:
            norm = deg_inv_sqrt[row] * deg_inv_sqrt[col]
        
        # 消息传递
        out = torch.zeros_like(x)
        out.index_add_(0, col, norm.view(-1, 1) * x[row])
        
        # 添加自环
        out = out + x
        
        return out

class MemoryEfficientTemporalEncoder(nn.Module):
    """内存高效的时序编码器"""
    
    def __init__(self, input_dim, hidden_dim, dropout=0.1):
        super(MemoryEfficientTemporalEncoder, self).__init__()
        
        self.hidden_dim = hidden_dim
        
        # 使用1D卷积替代复杂的RNN
        self.conv1 = nn.Conv1d(input_dim, hidden_dim, kernel_size=3, padding=1)
        self.conv2 = nn.Conv1d(hidden_dim, hidden_dim, kernel_size=3, padding=1)
        
        # 全局池化替代逐节点RNN
        self.global_pool = nn.AdaptiveMaxPool1d(1)
        
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x):
        """
        Args:
            x: [batch_size, time_steps, num_nodes, hidden_dim]
        Returns:
            [batch_size, num_nodes, hidden_dim]
        """
        batch_size, time_steps, num_nodes, hidden_dim = x.shape
        
        # 重塑为卷积格式: [batch*nodes, hidden_dim, time_steps]
        x = x.permute(0, 2, 3, 1).reshape(-1, hidden_dim, time_steps)
        
        # 1D卷积处理时序
        x = F.relu(self.conv1(x), inplace=True)
        x = self.dropout(x)
        x = F.relu(self.conv2(x), inplace=True)
        
        # 全局池化
        x = self.global_pool(x).squeeze(-1)
        
        # 重塑回节点格式
        x = x.view(batch_size, num_nodes, self.hidden_dim)
        
        return x

class MemoryEfficientAttention(nn.Module):
    """内存高效的注意力机制"""
    
    def __init__(self, hidden_dim, num_heads=4, dropout=0.1):
        super(MemoryEfficientAttention, self).__init__()
        
        self.hidden_dim = hidden_dim
        self.num_heads = num_heads
        self.head_dim = hidden_dim // num_heads
        
        assert hidden_dim % num_heads == 0, "hidden_dim must be divisible by num_heads"
        
        # 使用单个线性层减少参数
        self.qkv = nn.Linear(hidden_dim, hidden_dim * 3)
        self.out_proj = nn.Linear(hidden_dim, hidden_dim)
        
        self.dropout = nn.Dropout(dropout)
        self.scale = math.sqrt(self.head_dim)
        
    def forward(self, x):
        """
        Args:
            x: [batch_size, num_nodes, hidden_dim]
        """
        batch_size, num_nodes, hidden_dim = x.shape
        
        # 计算Q, K, V
        qkv = self.qkv(x).reshape(batch_size, num_nodes, 3, self.num_heads, self.head_dim)
        qkv = qkv.permute(2, 0, 3, 1, 4)  # [3, batch, heads, nodes, head_dim]
        q, k, v = qkv[0], qkv[1], qkv[2]
        
        # 使用Flash Attention风格的实现（如果节点数量大）
        if num_nodes > 100:
            out = self._flash_attention(q, k, v)
        else:
            # 标准注意力
            attn = torch.matmul(q, k.transpose(-2, -1)) / self.scale
            attn = F.softmax(attn, dim=-1)
            attn = self.dropout(attn)
            out = torch.matmul(attn, v)
        
        out = out.transpose(1, 2).reshape(batch_size, num_nodes, hidden_dim)
        
        return self.out_proj(out)
    
    def _flash_attention(self, q, k, v):
        """内存高效的注意力计算"""
        batch_size, num_heads, num_nodes, head_dim = q.shape
        
        # 分块处理以减少内存使用
        chunk_size = min(64, num_nodes)
        output = torch.zeros_like(v)
        
        for i in range(0, num_nodes, chunk_size):
            end_i = min(i + chunk_size, num_nodes)
            q_chunk = q[:, :, i:end_i]
            
            # 计算与所有K的注意力
            attn_chunk = torch.matmul(q_chunk, k.transpose(-2, -1)) / self.scale
            attn_chunk = F.softmax(attn_chunk, dim=-1)
            attn_chunk = self.dropout(attn_chunk)
            
            # 应用到V
            output[:, :, i:end_i] = torch.matmul(attn_chunk, v)
        
        return output 