import torch
import numpy as np
import pandas as pd
import geopandas as gpd
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import os
import matplotlib.pyplot as plt

class EarlyStopping:
    """早停机制"""
    def __init__(self, patience=7, verbose=False, delta=0, path='best_model.pth'):
        self.patience = patience
        self.verbose = verbose
        self.counter = 0
        self.best_score = None
        self.early_stop = False
        self.val_loss_min = np.Inf
        self.delta = delta
        self.path = path

    def __call__(self, val_loss, model):
        score = -val_loss

        if self.best_score is None:
            self.best_score = score
            self.save_checkpoint(val_loss, model)
        elif score < self.best_score + self.delta:
            self.counter += 1
            if self.verbose:
                print(f'EarlyStopping counter: {self.counter} out of {self.patience}')
            if self.counter >= self.patience:
                self.early_stop = True
        else:
            self.best_score = score
            self.save_checkpoint(val_loss, model)
            self.counter = 0

    def save_checkpoint(self, val_loss, model):
        '''Saves model when validation loss decrease.'''
        if self.verbose:
            print(f'Validation loss decreased ({self.val_loss_min:.6f} --> {val_loss:.6f}).  Saving model ...')
        torch.save(model.state_dict(), self.path)
        self.val_loss_min = val_loss

def calculate_metrics(y_true, y_pred):
    """计算评估指标"""
    y_true = np.array(y_true)
    y_pred = np.array(y_pred)
    
    # 过滤无效值
    valid_mask = ~(np.isnan(y_true) | np.isnan(y_pred) | np.isinf(y_true) | np.isinf(y_pred))
    y_true_valid = y_true[valid_mask]
    y_pred_valid = y_pred[valid_mask]
    
    if len(y_true_valid) == 0:
        return {
            'MAE': np.nan,
            'MSE': np.nan,
            'RMSE': np.nan,
            'R2': np.nan,
            'MAPE': np.nan
        }
    
    mae = mean_absolute_error(y_true_valid, y_pred_valid)
    mse = mean_squared_error(y_true_valid, y_pred_valid)
    rmse = np.sqrt(mse)
    
    try:
        r2 = r2_score(y_true_valid, y_pred_valid)
    except:
        r2 = np.nan
    
    # 计算MAPE (Mean Absolute Percentage Error)
    mape = np.mean(np.abs((y_true_valid - y_pred_valid) / (np.abs(y_true_valid) + 1e-8))) * 100
    
    return {
        'MAE': mae,
        'MSE': mse,
        'RMSE': rmse,
        'R2': r2,
        'MAPE': mape
    }

def save_predictions(predictions, true_values, data_type, output_path):
    """保存预测结果"""
    try:
        results_df = pd.DataFrame({
            'Type': [data_type] * len(predictions),
            'True_Value': true_values,
            'Predicted_Value': predictions
        })
        
        # 计算指标
        metrics = calculate_metrics(true_values, predictions)
        
        # 保存结果
        os.makedirs(output_path, exist_ok=True)
        output_file = os.path.join(output_path, f'{data_type.lower()}_predictions.csv')
        results_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        # 保存指标
        metrics_file = os.path.join(output_path, f'{data_type.lower()}_metrics.txt')
        with open(metrics_file, 'w', encoding='utf-8') as f:
            f.write(f'{data_type} Prediction Metrics:\n')
            f.write('='*40 + '\n')
            for metric, value in metrics.items():
                f.write(f'{metric}: {value:.4f}\n')
        
        print(f'{data_type} predictions saved to {output_file}')
        print(f'{data_type} metrics:')
        for metric, value in metrics.items():
            print(f'  {metric}: {value:.4f}')
        
        return metrics
    except Exception as e:
        print(f"Error saving predictions for {data_type}: {e}")
        return {}

def save_shapefile_predictions(original_shp_path, predictions, station_names, output_path, prediction_column='prediction'):
    """将预测结果保存到shapefile"""
    try:
        # 读取原始shapefile
        gdf = gpd.read_file(original_shp_path)
        
        # 创建预测结果的DataFrame
        pred_df = pd.DataFrame({
            'station': station_names,
            prediction_column: predictions
        })
        
        # 合并数据
        gdf_merged = gdf.merge(pred_df, on='station', how='left')
        
        # 保存到新的shapefile
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        gdf_merged.to_file(output_path, encoding='utf-8')
        print(f"Predictions saved to shapefile: {output_path}")
        
    except Exception as e:
        print(f"Error saving shapefile predictions: {e}")

def create_comparison_dataframe(in_preds, in_true, out_preds, out_true, od_preds=None, od_true=None):
    """创建预测值与真实值比较的数据框"""
    comparison_data = []
    
    # 进站数据
    if in_preds is not None and in_true is not None:
        for pred, true in zip(in_preds, in_true):
            comparison_data.append({
                'Type': 'In',
                'True_Value': true,
                'Predicted_Value': pred
            })
    
    # 出站数据
    if out_preds is not None and out_true is not None:
        for pred, true in zip(out_preds, out_true):
            comparison_data.append({
                'Type': 'Out',
                'True_Value': true,
                'Predicted_Value': pred
            })
    
    # OD数据
    if od_preds is not None and od_true is not None:
        for pred, true in zip(od_preds, od_true):
            comparison_data.append({
                'Type': 'OD',
                'True_Value': true,
                'Predicted_Value': pred
            })
    
    return pd.DataFrame(comparison_data)

def normalize_features(features, scaler=None, fit=True):
    """特征归一化"""
    from sklearn.preprocessing import StandardScaler
    
    if scaler is None:
        scaler = StandardScaler()
    
    if fit:
        normalized = scaler.fit_transform(features)
    else:
        normalized = scaler.transform(features)
    
    return normalized, scaler

def create_time_features(hour):
    """创建时间特征"""
    return np.array([
        np.sin(2 * np.pi * hour / 24),  # 小时的正弦编码
        np.cos(2 * np.pi * hour / 24),  # 小时的余弦编码
        hour / 23.0  # 归一化小时
    ])

def haversine_distance(lat1, lon1, lat2, lon2):
    """计算两点间的球面距离"""
    from math import radians, cos, sin, asin, sqrt
    
    # 将十进制度数转化为弧度
    lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])
    
    # haversine公式
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
    c = 2 * asin(sqrt(a))
    r = 6371  # 地球平均半径，单位为公里
    
    return c * r

def find_nearby_grids(station_lat, station_lon, grid_data, radius_km=3):
    """找到站点附近指定半径内的栅格"""
    nearby_grids = []
    
    for idx, grid in grid_data.iterrows():
        # 假设栅格数据中有经纬度信息
        if 'latitude' in grid and 'longitude' in grid:
            distance = haversine_distance(
                station_lat, station_lon,
                grid['latitude'], grid['longitude']
            )
            if distance <= radius_km:
                nearby_grids.append(idx)
    
    return nearby_grids

def print_data_summary(data_dict):
    """打印数据摘要"""
    print("\n" + "="*50)
    print("DATA SUMMARY")
    print("="*50)
    
    for key, value in data_dict.items():
        if isinstance(value, pd.DataFrame):
            print(f"{key}: {value.shape}")
        elif isinstance(value, dict):
            print(f"{key}: dict with {len(value)} keys")
        else:
            print(f"{key}: {type(value)}")
    
    print("="*50 + "\n")

def plot_predictions_vs_actual(y_true, y_pred, data_type, output_path):
    """绘制预测值vs真实值散点图"""
    try:
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 散点图
        ax1.scatter(y_true, y_pred, alpha=0.6)
        ax1.plot([min(y_true), max(y_true)], [min(y_true), max(y_true)], 'r--', lw=2)
        ax1.set_xlabel('True Values')
        ax1.set_ylabel('Predicted Values')
        ax1.set_title(f'{data_type} - Predictions vs Actual')
        ax1.grid(True, alpha=0.3)
        
        # 残差图
        residuals = np.array(y_pred) - np.array(y_true)
        ax2.scatter(y_pred, residuals, alpha=0.6)
        ax2.axhline(y=0, color='r', linestyle='--')
        ax2.set_xlabel('Predicted Values')
        ax2.set_ylabel('Residuals')
        ax2.set_title(f'{data_type} - Residual Plot')
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plot_path = os.path.join(output_path, f'{data_type.lower()}_predictions_plot.png')
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"Plot saved to {plot_path}")
        
    except Exception as e:
        print(f"Error creating plot for {data_type}: {e}")

def memory_usage():
    """获取GPU内存使用情况"""
    if torch.cuda.is_available():
        allocated = torch.cuda.memory_allocated() / 1024**3  # GB
        cached = torch.cuda.memory_reserved() / 1024**3     # GB
        print(f"GPU Memory - Allocated: {allocated:.2f}GB, Cached: {cached:.2f}GB")
        return allocated, cached
    else:
        print("CUDA not available")
        return 0, 0
