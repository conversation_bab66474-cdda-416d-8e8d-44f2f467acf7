import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import torch
import geopandas as gpd
from shapely.geometry import Point
import time
from datetime import datetime

def create_directory(directory_path):
    """创建目录（如果不存在）"""
    if not os.path.exists(directory_path):
        os.makedirs(directory_path)
        print(f"创建目录: {directory_path}")
    return directory_path

def evaluate_predictions(y_true, y_pred):
    """
    评估预测结果
    
    参数:
    y_true: 实际值
    y_pred: 预测值
    
    返回:
    指标字典，包含MAE、MSE、RMSE和R²
    """
    mae = mean_absolute_error(y_true, y_pred)
    mse = mean_squared_error(y_true, y_pred)
    rmse = np.sqrt(mse)
    r2 = r2_score(y_true, y_pred)
    
    return {
        'MAE': mae,
        'MSE': mse,
        'RMSE': rmse,
        'R2': r2
    }

def plot_training_curves(train_losses, val_losses, train_maes, val_maes, save_path=None):
    """
    绘制训练曲线
    
    参数:
    train_losses: 训练损失历史
    val_losses: 验证损失历史
    train_maes: 训练MAE历史
    val_maes: 验证MAE历史
    save_path: 保存路径
    """
    plt.figure(figsize=(15, 7))
    
    # 绘制损失曲线
    plt.subplot(1, 2, 1)
    plt.plot(train_losses, label='训练损失')
    plt.plot(val_losses, label='验证损失')
    plt.xlabel('轮次')
    plt.ylabel('损失')
    plt.title('训练和验证损失')
    plt.legend()
    plt.grid(True)
    
    # 绘制MAE曲线
    plt.subplot(1, 2, 2)
    plt.plot(train_maes, label='训练MAE')
    plt.plot(val_maes, label='验证MAE')
    plt.xlabel('轮次')
    plt.ylabel('MAE')
    plt.title('训练和验证MAE')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"训练曲线已保存至: {save_path}")
    
    plt.close()

def plot_prediction_comparison(y_true, y_pred, title='Prediction vs True Values', save_path=None):
    """
    绘制预测值与真实值的对比散点图
    
    参数:
    y_true: 实际值
    y_pred: 预测值
    title: 图表标题
    save_path: 保存路径
    """
    plt.figure(figsize=(10, 8))
    
    # 绘制散点图
    plt.scatter(y_true, y_pred, alpha=0.5)
    
    # 绘制理想的对角线
    min_val = min(np.min(y_true), np.min(y_pred))
    max_val = max(np.max(y_true), np.max(y_pred))
    plt.plot([min_val, max_val], [min_val, max_val], 'r--')
    
    plt.xlabel('真实值')
    plt.ylabel('预测值')
    plt.title(title)
    plt.grid(True)
    
    # 添加评估指标
    metrics = evaluate_predictions(y_true, y_pred)
    metrics_text = '\n'.join([f"{k}: {v:.4f}" for k, v in metrics.items()])
    plt.annotate(metrics_text, xy=(0.05, 0.95), xycoords='axes fraction', 
                 bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="black", alpha=0.8))
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"预测对比图已保存至: {save_path}")
    
    plt.close()

def save_results_to_csv(df, output_path):
    """
    将结果保存为CSV文件
    
    参数:
    df: 包含结果的DataFrame
    output_path: 保存路径
    """
    df.to_csv(output_path, index=False)
    print(f"结果已保存至: {output_path}")

def save_results_to_shapefile(gdf, output_path):
    """
    将结果保存为Shapefile文件
    
    参数:
    gdf: 包含结果的GeoDataFrame
    output_path: 保存路径
    """
    gdf.to_file(output_path)
    print(f"结果已保存至: {output_path}")

def save_training_history(history, output_path):
    """
    保存训练历史为CSV文件
    
    参数:
    history: 训练历史字典
    output_path: 保存路径
    """
    df = pd.DataFrame(history)
    df.to_csv(output_path, index=False)
    print(f"训练历史已保存至: {output_path}")

def log_message(message, log_file=None):
    """
    记录消息到日志文件和控制台
    
    参数:
    message: 要记录的消息
    log_file: 日志文件路径
    """
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    log_message = f"[{timestamp}] {message}"
    
    print(log_message)
    
    if log_file:
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(log_message + '\n')

def timer(func):
    """
    函数计时装饰器
    
    参数:
    func: 要计时的函数
    """
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        print(f"函数 {func.__name__} 执行时间: {end_time - start_time:.2f} 秒")
        return result
    return wrapper

def make_predictions_for_shapefile(model, gdf, graph_data, column_name='prediction'):
    """
    为GeoDataFrame中的每个特征进行预测并添加预测结果
    
    参数:
    model: 预测模型
    gdf: GeoDataFrame对象
    graph_data: 用于预测的图数据
    column_name: 预测结果列名
    
    返回:
    添加了预测结果的GeoDataFrame
    """
    # 进行预测
    predictions = model.numpy()
    
    # 确保预测结果和GeoDataFrame长度一致
    if len(predictions) != len(gdf):
        raise ValueError(f"预测结果长度 ({len(predictions)}) 与GeoDataFrame长度 ({len(gdf)}) 不匹配")
    
    # 添加预测结果
    gdf[column_name] = predictions
    
    return gdf

def prepare_prediction_comparison(in_data, out_data, od_data):
    """
    准备预测结果与真实值的比较数据
    
    参数:
    in_data: 进站数据，包含真实值和预测值
    out_data: 出站数据，包含真实值和预测值
    od_data: OD数据，包含真实值和预测值
    
    返回:
    比较结果DataFrame
    """
    # 提取进站数据的真实值和预测值
    in_true = in_data[in_data['dataset'] == 'test']['count'].values
    in_pred = in_data[in_data['dataset'] == 'test']['prediction'].values
    
    # 提取出站数据的真实值和预测值
    out_true = out_data[out_data['dataset'] == 'test']['count'].values
    out_pred = out_data[out_data['dataset'] == 'test']['prediction'].values
    
    # 提取OD数据的真实值和预测值
    od_true = od_data[od_data['dataset'] == 'test']['trip'].values
    od_pred = od_data[od_data['dataset'] == 'test']['prediction'].values
    
    # 创建比较DataFrame
    comparison_df = pd.DataFrame({
        'Type': ['In'] * len(in_true) + ['Out'] * len(out_true) + ['OD'] * len(od_true),
        'True_Value': np.concatenate([in_true, out_true, od_true]),
        'Predicted_Value': np.concatenate([in_pred, out_pred, od_pred])
    })
    
    return comparison_df

def visualize_flow_on_map(gdf, flow_column, title, save_path=None):
    """
    在地图上可视化流量数据
    
    参数:
    gdf: 包含地理信息和流量数据的GeoDataFrame
    flow_column: 流量数据列名
    title: 图表标题
    save_path: 保存路径
    """
    fig, ax = plt.subplots(1, 1, figsize=(15, 10))
    
    # 绘制底图
    gdf.plot(ax=ax, alpha=0.5, edgecolor='k')
    
    # 绘制流量热图
    gdf.plot(ax=ax, column=flow_column, cmap='Reds', legend=True)
    
    # 添加标题
    plt.title(title)
    
    # 保存图表
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"流量地图已保存至: {save_path}")
    
    plt.close()

def calculate_model_size(model):
    """
    计算模型大小（参数数量）
    
    参数:
    model: PyTorch模型
    
    返回:
    参数数量
    """
    return sum(p.numel() for p in model.parameters())

def to_device(data, device):
    """
    将数据移动到指定设备
    
    参数:
    data: 要移动的数据
    device: 目标设备
    """
    if isinstance(data, (list, tuple)):
        return [to_device(x, device) for x in data]
    return data.to(device, non_blocking=True)

def normalize_predictions(predictions, lower_bound=0):
    """
    对预测值进行正则化处理，确保所有预测值都大于等于lower_bound
    
    参数:
    predictions: 预测值数组
    lower_bound: 下界值，默认为0，确保预测值非负
    
    返回:
    正则化后的预测值
    """
    # 转换为numpy数组以便处理
    if isinstance(predictions, torch.Tensor):
        predictions = predictions.detach().cpu().numpy()
    
    # 对小于lower_bound的值进行截断
    normalized_predictions = np.maximum(predictions, lower_bound)
    
    return normalized_predictions

def normalize_predictions_minmax(predictions, min_val=None, max_val=None):
    """
    使用Min-Max缩放对预测值进行正则化处理
    
    参数:
    predictions: 预测值数组
    min_val: 最小值，如果为None则使用predictions的最小值
    max_val: 最大值，如果为None则使用predictions的最大值
    
    返回:
    正则化后的预测值，范围在[0, 1]之间
    """
    # 转换为numpy数组以便处理
    if isinstance(predictions, torch.Tensor):
        predictions = predictions.detach().cpu().numpy()
    
    # 确定最小值和最大值
    if min_val is None:
        min_val = np.min(predictions)
    if max_val is None:
        max_val = np.max(predictions)
    
    # 避免除以零
    if max_val == min_val:
        return np.zeros_like(predictions)
    
    # 进行Min-Max缩放
    normalized_predictions = (predictions - min_val) / (max_val - min_val)
    
    return normalized_predictions 