**Metro Flow Prediction Code Documentation**

**1. 预测任务 (Prediction Tasks)**

- **栅格→站点流量预测 (Grid→Station In/Out)**\
  统计各栅格（70K）在每小时内向/从对应地铁站（350）进站和出站的人流量。\
  **输出文件**：

  - `in_500_predictions_with_coords.shp`
  - `out_500_predictions_with_coords.shp`

- **站点↔站点 OD 流量预测 (Station↔Station OD)**\
  预测任意两个地铁站之间每小时的乘客 OD 流量，结合距离、换乘次数、旅程时间与等待时间等特征。\
  **输出文件**：

  - `od_predictions.csv`

**2. 输入数据 (Inputs)**

| 数据文件                                      | 作用                 | 在代码中的接入位置                      |
| ----------------------------------------- | ------------------ | ------------------------------ |
| `in_500_with_coords.shp`                  | 栅格→站点进站流量 & 栅格地理位置 | `load_shapefile` → `in_gdf`    |
| `out_500_with_coords.shp`                 | 栅格→站点出站流量 & 栅格地理位置 | `load_shapefile` → `out_gdf`   |
| `updated北京市_subway_od_2024_modified3.csv` | 站点间 OD 流量及特征       | `pd.read_csv` → `od_df`        |
| `leti_data.csv`                           | 栅格地理/社会经济多维度特征     | `pd.read_csv` → `grid_feats`   |
| `station_connect.csv`                     | 地铁网络拓扑边集（含换乘）      | `build_station_graph` → `A_ss` |

**3. 代码结构与预测过程 (Workflow)**

```text
main()
 ├─ 加载数据 (load_shapefile, pd.read_csv)
 ├─ 构建站点图邻接 A_ss (build_station_graph)
 ├─ 聚合站点特征 station_feats (aggregate_station_features)
 ├─ 构建栅格—站点二部图邻接 A_gs
 ├─ 初始化 Dataset/ DataLoader (MetroDataset)
 ├─ 构建模型 (MetroSTGCN)
 ├─ 训练循环 (优化 MSE Loss, 强调 OD 部分)
 │    └─ 每个 batch：
 │        ├─ 拆分时序 seq_o, seq_d
 │        ├─ 静态 features (grid_feats, station_feats)
 │        ├─ OD 特征 (translate, time, wait_time)
 │        ├─ 前向 (model)
 │        └─ 反向更新 (loss.backward, opt.step)
 └─ 推理 (inference)
      ├─ 全图静态特征重复生成
      ├─ 前向计算 in_pred, out_pred, od_pred
      └─ 保存结果至 shapefile/CSV
```

**4. 关键模块说明**

- `build_station_graph(connect_csv)`\
  解析 `station_connect.csv`，清洗站名后缀，生成 350×350 的归一化邻接矩阵 `A_ss`，并返回站点列表与映射。

- `aggregate_station_features(grid_gdf, grid_feats_df, station_idx)`\
  对每个站点周边 1.5km 内栅格特征做平均聚合，生成 `station_feats` DataFrame。

- `MetroDataset`\
  整合时序 OD(过去6小时)、静态栅格/站点特征、OD 对距离与换乘等特征，输出模型输入向量 `X` 与标签 `y`，并返回对应站点索引 `(o_sid, d_sid)`。

- `STGCNBlock`\
  实现单层时空卷积：

  1. **Spatial**：`h = ReLU(A @ h_prev @ W_s)`
  2. **Temporal**：`h = ReLU(Conv1d(h))`

- `MetroSTGCN`\
  **双流**（Grid-Station 与 Station-Station），每流 3 层 STGCNBlock 后：

  - **Decoder In/Out**：对二部图边 `(g,s)` 拼接特征 `h_g[g]‖h_s[s]`，预测栅格进/出站流量。
  - **Decoder OD**：对站点对 `(o,s)` 拼接 `h_s[o]‖h_s[s]‖od_feats`，预测 OD 流量。

**5. 229 行以后的 "删除部分" 建议补充内容**

```python
# After training: inference on full graph and save predictions
# 1. 构造 full static feature tensor: static_full
# 2. 构造 full index_pairs 字典:
#       {
#         'gs': [(grid_id, station_id), ...],
#         'od': [(o_id, d_id), ...]
#       }
# 3. 批量前向推理:
#       in_pred, out_pred, od_pred = model(None, static_full, od_feats_full, idx_full)
# 4. 将 od_pred 写回 od_df:
#       od_df['prediction'] = od_pred.cpu().numpy()
#       od_df.to_csv(OUT_OD, index=False)
# 5. 将 in_pred/out_pred 填回 in_gdf/out_gdf 并保存:
#       in_gdf['prediction'] = in_pred.flatten().cpu().numpy()
#       in_gdf.to_file(OUT_IN)
#       out_gdf['prediction'] = out_pred.flatten().cpu().numpy()
#       out_gdf.to_file(OUT_OUT)
# 6. 若需要计算预测精度，可以加载真实测试集，计算 RMSE/MAPE 并记录。

# 注意：
# - 推理时请确保 static_full、od_feats_full 与 idx_full 长度匹配。
# - 对大规模数据建议分批次推理并增量写入文件，避免显存/内存激增。
```

以上文档涵盖了预测目标、数据流、模型结构与推理流程，并详细说明了第229行以后的推理与保存逻辑，供维护者参考。

