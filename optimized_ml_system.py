"""
优化的机器学习地铁流量预测系统
基于北京市真实数据，改进模型架构提高R²性能
"""
import os
import sys
import time
import numpy as np
import pandas as pd
import geopandas as gpd
import torch
import torch.nn as nn
import torch.optim as optim
from torch_geometric.nn import GCNConv, GATConv
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')

class OptimizedMetroGCN(nn.Module):
    """优化的地铁流量预测模型"""
    
    def __init__(self, input_dim, hidden_dim=128, dropout=0.3):
        super(OptimizedMetroGCN, self).__init__()
        
        # 特征预处理
        self.input_norm = nn.LayerNorm(input_dim)
        self.feature_projection = nn.Linear(input_dim, hidden_dim)
        
        # 时间编码
        self.hour_embedding = nn.Embedding(24, 24)
        self.time_mlp = nn.Sequential(
            nn.Linear(24, 32),
            nn.ReLU(),
            nn.Linear(32, 16)
        )
        
        # 改进的图卷积层
        self.gat1 = GATConv(hidden_dim + 16, hidden_dim, heads=4, dropout=dropout)
        self.gat2 = GATConv(hidden_dim, hidden_dim, heads=4, dropout=dropout)
        self.gat3 = GATConv(hidden_dim, hidden_dim, heads=1, dropout=dropout)
        
        # 批归一化
        self.bn1 = nn.BatchNorm1d(hidden_dim)
        self.bn2 = nn.BatchNorm1d(hidden_dim)
        self.bn3 = nn.BatchNorm1d(hidden_dim)
        
        # 残差连接
        self.residual1 = nn.Linear(hidden_dim + 16, hidden_dim)
        self.residual2 = nn.Linear(hidden_dim, hidden_dim)
        
        # 预测头
        self.predictor = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 4, 2),  # 进站 + 出站
            nn.Softplus()  # 确保输出非负
        )
        
        self.dropout = nn.Dropout(dropout)
        self._init_weights()
    
    def _init_weights(self):
        """权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
    
    def forward(self, x, edge_index, edge_weight, hour):
        """前向传播"""
        # 特征预处理
        x = self.input_norm(x)
        x = self.feature_projection(x)
        
        # 时间编码
        if hour.dim() == 0:
            hour = hour.unsqueeze(0).expand(x.size(0))
        
        time_embed = self.hour_embedding(hour)
        time_features = self.time_mlp(time_embed)
        
        # 合并特征
        x_with_time = torch.cat([x, time_features], dim=-1)
        
        # GAT层1
        residual = self.residual1(x_with_time)
        h = self.gat1(x_with_time, edge_index)
        h = self.bn1(h) if h.size(0) > 1 else h
        h = h + residual
        h = torch.relu(h)
        h = self.dropout(h)
        
        # GAT层2
        residual = self.residual2(h)
        h = self.gat2(h, edge_index)
        h = self.bn2(h) if h.size(0) > 1 else h
        h = h + residual
        h = torch.relu(h)
        h = self.dropout(h)
        
        # GAT层3
        h = self.gat3(h, edge_index)
        h = self.bn3(h) if h.size(0) > 1 else h
        h = torch.relu(h)
        
        # 预测
        output = self.predictor(h)
        
        return {
            'in_flow': output[:, 0],
            'out_flow': output[:, 1],
            'embeddings': h
        }

class OptimizedMLPredictor:
    """优化的机器学习预测器"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
        # 使用鲁棒的标准化器
        self.flow_scaler = RobustScaler()
        self.feature_scaler = StandardScaler()
        
        self.model = None
        self.stations = []
        self.station_to_idx = {}
        
    def load_beijing_data(self):
        """加载北京市数据"""
        print("="*60)
        print("加载北京市地铁数据")
        print("="*60)
        
        try:
            # 加载进出站数据
            print("加载进出站数据...")
            in_data = gpd.read_file('in_500_with_coords.shp')
            out_data = gpd.read_file('out_500_with_coords.shp')
            
            # 数据清洗
            in_data = self._clean_data(in_data)
            out_data = self._clean_data(out_data)
            
            print(f"清洗后进站数据: {in_data.shape}")
            print(f"清洗后出站数据: {out_data.shape}")
            
            # 获取站点
            self.stations = sorted(list(set(in_data['station_clean'].unique()) | 
                                       set(out_data['station_clean'].unique())))
            self.station_to_idx = {station: idx for idx, station in enumerate(self.stations)}
            print(f"发现 {len(self.stations)} 个唯一站点")
            
            # 加载连接数据
            connect_data = pd.read_csv('station_connect_2023.csv')
            connect_data = self._clean_connections(connect_data)
            
            # 加载栅格特征
            grid_data = pd.read_csv('leti_data.csv')
            grid_features = self._process_grid_features(grid_data)
            
            # 构建训练数据
            self._build_training_data(in_data, out_data, connect_data, grid_features)
            
            print(f"训练样本: {len(self.train_data)}")
            print(f"测试样本: {len(self.test_data)}")
            
            return True
            
        except Exception as e:
            print(f"数据加载失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _clean_data(self, data):
        """清洗数据"""
        # 提取坐标
        if 'geometry' in data.columns:
            data['longitude'] = data.geometry.centroid.x
            data['latitude'] = data.geometry.centroid.y
        
        # 清理站点名称
        data['station_clean'] = data['station'].apply(lambda x: str(x).split('_')[0])
        
        # 移除异常值 - 更严格的过滤
        data = data[data['count'] >= 0]
        data = data[data['count'] <= data['count'].quantile(0.99)]
        
        # 移除缺失值
        data = data.dropna(subset=['station_clean', 'hour', 'count'])
        
        return data
    
    def _clean_connections(self, data):
        """清洗连接数据"""
        data['station_1_clean'] = data['station_1'].apply(lambda x: str(x).split('_')[0])
        data['station_2_clean'] = data['station_2'].apply(lambda x: str(x).split('_')[0])
        return data.drop_duplicates(subset=['station_1_clean', 'station_2_clean'])
    
    def _process_grid_features(self, data):
        """处理栅格特征"""
        feature_cols = [col for col in data.columns if col not in ['站名', 'id', 'geometry']]
        features = data[feature_cols].values
        features = np.nan_to_num(features, nan=0.0)
        return self.feature_scaler.fit_transform(features)
    
    def _build_training_data(self, in_data, out_data, connect_data, grid_features):
        """构建训练数据"""
        # 构建图
        edge_index = self._build_graph(connect_data)
        
        # 计算平均栅格特征
        avg_grid_features = np.mean(grid_features, axis=0)
        
        # 按小时构建样本
        samples = []
        all_hours = sorted(set(in_data['hour'].unique()) & set(out_data['hour'].unique()))
        
        for hour in all_hours:
            hour_in = in_data[in_data['hour'] == hour]
            hour_out = out_data[out_data['hour'] == hour]
            
            # 构建特征和目标
            features, targets = self._build_hour_sample(hour_in, hour_out, avg_grid_features, hour)
            
            if features is not None:
                samples.append({
                    'features': torch.tensor(features, dtype=torch.float32),
                    'targets': torch.tensor(targets, dtype=torch.float32),
                    'edge_index': edge_index,
                    'hour': torch.tensor(hour, dtype=torch.long)
                })
        
        # 时间分割
        split_idx = int(len(samples) * 0.8)
        self.train_data = samples[:split_idx]
        self.test_data = samples[split_idx:]
    
    def _build_hour_sample(self, hour_in, hour_out, grid_features, hour):
        """构建每小时样本"""
        num_stations = len(self.stations)
        
        # 特征矩阵：栅格特征 + 时间特征
        features = np.zeros((num_stations, len(grid_features) + 4))
        targets = np.zeros((num_stations, 2))
        
        # 填充栅格特征
        features[:, :-4] = grid_features
        
        # 时间特征
        features[:, -4] = hour / 23.0  # 标准化小时
        features[:, -3] = 1.0 if 6 <= hour <= 10 else 0.0  # 早高峰
        features[:, -2] = 1.0 if 17 <= hour <= 20 else 0.0  # 晚高峰
        features[:, -1] = np.sin(2 * np.pi * hour / 24)  # 周期性
        
        # 填充目标
        valid_stations = 0
        
        for _, row in hour_in.iterrows():
            station = row['station_clean']
            if station in self.station_to_idx:
                idx = self.station_to_idx[station]
                targets[idx, 0] = max(0, row['count'])
                valid_stations += 1
        
        for _, row in hour_out.iterrows():
            station = row['station_clean']
            if station in self.station_to_idx:
                idx = self.station_to_idx[station]
                targets[idx, 1] = max(0, row['count'])
        
        # 只返回有足够数据的样本
        if valid_stations < len(self.stations) * 0.1:
            return None, None
        
        return features, targets
    
    def _build_graph(self, connect_data):
        """构建图"""
        edges = []
        for _, row in connect_data.iterrows():
            s1, s2 = row['station_1_clean'], row['station_2_clean']
            if s1 in self.station_to_idx and s2 in self.station_to_idx:
                idx1, idx2 = self.station_to_idx[s1], self.station_to_idx[s2]
                edges.extend([[idx1, idx2], [idx2, idx1]])
        
        if not edges:
            # 创建简单连接
            for i in range(min(len(self.stations), 10)):
                for j in range(i+1, min(len(self.stations), 10)):
                    edges.extend([[i, j], [j, i]])
        
        return torch.tensor(edges).T.long()
    
    def train_model(self, epochs=100, learning_rate=0.001):
        """训练模型"""
        print("="*60)
        print("训练优化的深度学习模型")
        print("="*60)
        
        # 初始化模型
        input_dim = self.train_data[0]['features'].shape[1]
        self.model = OptimizedMetroGCN(input_dim=input_dim, hidden_dim=128).to(self.device)
        
        print(f"模型参数量: {sum(p.numel() for p in self.model.parameters()):,}")
        
        # 优化器和损失函数
        optimizer = optim.AdamW(self.model.parameters(), lr=learning_rate, weight_decay=1e-4)
        criterion = nn.MSELoss()
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=15, factor=0.5)
        
        # 训练循环
        best_loss = float('inf')
        patience = 0
        
        for epoch in range(epochs):
            self.model.train()
            epoch_loss = 0
            valid_samples = 0
            
            for sample in self.train_data:
                try:
                    features = sample['features'].to(self.device)
                    targets = sample['targets'].to(self.device)
                    edge_index = sample['edge_index'].to(self.device)
                    hour = sample['hour'].to(self.device)
                    
                    # 前向传播
                    pred = self.model(features, edge_index, None, hour)
                    
                    # 计算损失
                    loss = (criterion(pred['in_flow'], targets[:, 0]) + 
                           criterion(pred['out_flow'], targets[:, 1])) / 2
                    
                    if torch.isnan(loss):
                        continue
                    
                    # 反向传播
                    optimizer.zero_grad()
                    loss.backward()
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
                    optimizer.step()
                    
                    epoch_loss += loss.item()
                    valid_samples += 1
                    
                except Exception:
                    continue
            
            if valid_samples > 0:
                avg_loss = epoch_loss / valid_samples
                scheduler.step(avg_loss)
                
                if avg_loss < best_loss:
                    best_loss = avg_loss
                    patience = 0
                    torch.save(self.model.state_dict(), 'best_optimized_model.pth')
                else:
                    patience += 1
                
                if epoch % 20 == 0:
                    print(f"Epoch {epoch}: Loss = {avg_loss:.6f}")
                
                if patience >= 25:
                    print(f"早停于第 {epoch} 轮")
                    break
        
        # 加载最佳模型
        if os.path.exists('best_optimized_model.pth'):
            self.model.load_state_dict(torch.load('best_optimized_model.pth'))
        
        print("训练完成！")
    
    def evaluate_model(self):
        """评估模型"""
        print("="*60)
        print("评估模型")
        print("="*60)
        
        self.model.eval()
        all_pred_in, all_pred_out = [], []
        all_target_in, all_target_out = [], []
        
        with torch.no_grad():
            for sample in self.test_data:
                try:
                    features = sample['features'].to(self.device)
                    targets = sample['targets'].to(self.device)
                    edge_index = sample['edge_index'].to(self.device)
                    hour = sample['hour'].to(self.device)
                    
                    pred = self.model(features, edge_index, None, hour)
                    
                    # 只收集有真实值的预测
                    mask_in = targets[:, 0] > 0
                    mask_out = targets[:, 1] > 0
                    
                    if mask_in.sum() > 0:
                        all_pred_in.extend(pred['in_flow'][mask_in].cpu().numpy())
                        all_target_in.extend(targets[:, 0][mask_in].cpu().numpy())
                    
                    if mask_out.sum() > 0:
                        all_pred_out.extend(pred['out_flow'][mask_out].cpu().numpy())
                        all_target_out.extend(targets[:, 1][mask_out].cpu().numpy())
                        
                except Exception:
                    continue
        
        # 计算指标
        metrics = {}
        
        if len(all_pred_in) > 0:
            pred_in = np.maximum(np.array(all_pred_in), 0)
            target_in = np.array(all_target_in)
            
            metrics['in_flow'] = {
                'mae': mean_absolute_error(target_in, pred_in),
                'rmse': np.sqrt(mean_squared_error(target_in, pred_in)),
                'r2': r2_score(target_in, pred_in)
            }
        
        if len(all_pred_out) > 0:
            pred_out = np.maximum(np.array(all_pred_out), 0)
            target_out = np.array(all_target_out)
            
            metrics['out_flow'] = {
                'mae': mean_absolute_error(target_out, pred_out),
                'rmse': np.sqrt(mean_squared_error(target_out, pred_out)),
                'r2': r2_score(target_out, pred_out)
            }
        
        if len(all_pred_in) > 0 and len(all_pred_out) > 0:
            all_pred = np.concatenate([pred_in, pred_out])
            all_target = np.concatenate([target_in, target_out])
            
            metrics['overall'] = {
                'mae': mean_absolute_error(all_target, all_pred),
                'rmse': np.sqrt(mean_squared_error(all_target, all_pred)),
                'r2': r2_score(all_target, all_pred)
            }
        
        return metrics, (all_pred_in, all_pred_out, all_target_in, all_target_out)
    
    def save_results(self, metrics, predictions):
        """保存结果"""
        pred_in, pred_out, target_in, target_out = predictions
        
        comparison_data = []
        for i in range(len(pred_in)):
            comparison_data.append({
                'Type': 'In', 'True_Value': target_in[i], 'Predicted_Value': pred_in[i]
            })
        for i in range(len(pred_out)):
            comparison_data.append({
                'Type': 'Out', 'True_Value': target_out[i], 'Predicted_Value': pred_out[i]
            })
        
        pd.DataFrame(comparison_data).to_csv('optimized_ml_results.csv', index=False)
        
        import json
        with open('optimized_ml_metrics.json', 'w') as f:
            json.dump(metrics, f, indent=2)
        
        print("✓ 结果已保存")

def main():
    """主函数"""
    print("="*80)
    print("优化的机器学习地铁流量预测系统")
    print("基于北京市真实数据，提升R²性能")
    print("="*80)
    
    start_time = time.time()
    
    try:
        predictor = OptimizedMLPredictor()
        
        if not predictor.load_beijing_data():
            return False
        
        predictor.train_model(epochs=80, learning_rate=0.001)
        metrics, predictions = predictor.evaluate_model()
        predictor.save_results(metrics, predictions)
        
        # 打印结果
        print("\n" + "="*60)
        print("优化的预测结果")
        print("="*60)
        
        for flow_type in ['in_flow', 'out_flow', 'overall']:
            if flow_type in metrics:
                m = metrics[flow_type]
                print(f"{flow_type.upper()}:")
                print(f"  MAE:  {m['mae']:.4f}")
                print(f"  RMSE: {m['rmse']:.4f}")
                print(f"  R²:   {m['r2']:.4f}")
        
        # R²分析
        if 'overall' in metrics:
            r2 = metrics['overall']['r2']
            if r2 > 0:
                print(f"\n✓ 模型性能优于简单均值预测 (R² = {r2:.4f})")
            else:
                print(f"\n⚠ 模型仍需优化 (R² = {r2:.4f})")
        
        print(f"\n运行时间: {time.time() - start_time:.2f} 秒")
        return True
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
