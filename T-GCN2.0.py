# -*- coding: utf-8 -*-
"""
Created on Tue May 27 23:29:04 2025

@author: <PERSON><PERSON><PERSON><PERSON>
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import pandas as pd
import geopandas as gpd
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.metrics import mean_absolute_error, mean_squared_error
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.spatial.distance import pdist, squareform
import networkx as nx
from torch_geometric.nn import GCNConv, GATConv
from torch_geometric.data import Data
import warnings
warnings.filterwarnings('ignore')

# 设置随机种子
torch.manual_seed(42)
np.random.seed(42)

class TemporalGCN(nn.Module):
    """复杂的时空图卷积网络(T-GCN)模型"""
    
    def __init__(self, num_nodes, num_features, hidden_dim=128, num_layers=3, 
                 rnn_hidden_dim=64, dropout=0.3, attention_heads=8):
        super(TemporalGCN, self).__init__()
        
        self.num_nodes = num_nodes
        self.num_features = num_features
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        self.rnn_hidden_dim = rnn_hidden_dim
        self.dropout = dropout
        
        # 多层图卷积网络 - 使用图注意力网络增强
        self.gconv_layers = nn.ModuleList()
        self.gconv_layers.append(GATConv(num_features, hidden_dim, heads=attention_heads, 
                                        dropout=dropout, concat=False))
        
        for i in range(num_layers - 2):
            self.gconv_layers.append(GATConv(hidden_dim, hidden_dim, heads=attention_heads,
                                           dropout=dropout, concat=False))
        
        if num_layers > 1:
            self.gconv_layers.append(GATConv(hidden_dim, hidden_dim, heads=attention_heads,
                                           dropout=dropout, concat=False))
        
        # 时间建模 - 使用双向LSTM
        self.lstm = nn.LSTM(hidden_dim, rnn_hidden_dim, num_layers=2, 
                           batch_first=True, bidirectional=True, dropout=dropout)
        
        # 自注意力机制
        self.self_attention = nn.MultiheadAttention(rnn_hidden_dim * 2, 8, dropout=dropout)
        
        # 残差连接和归一化
        self.layer_norm1 = nn.LayerNorm(hidden_dim)
        self.layer_norm2 = nn.LayerNorm(rnn_hidden_dim * 2)
        
        # 输出层 - 多层感知机
        self.output_mlp = nn.Sequential(
            nn.Linear(rnn_hidden_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, 1)
        )
        
        # 特征融合网络
        self.feature_fusion = nn.Sequential(
            nn.Linear(num_features, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim)
        )
        
        self.dropout_layer = nn.Dropout(dropout)
        
    def forward(self, x, edge_index, edge_weight=None):
        """
        前向传播
        x: [batch_size, seq_len, num_nodes, num_features]
        edge_index: [2, num_edges]
        edge_weight: [num_edges]
        """
        batch_size, seq_len, num_nodes, num_features = x.shape
        
        # 重塑输入以进行图卷积
        x_reshaped = x.view(-1, num_nodes, num_features)
        
        temporal_outputs = []
        
        for t in range(seq_len):
            # 获取当前时刻的特征
            x_t = x_reshaped[t * batch_size:(t + 1) * batch_size]
            x_t = x_t.view(-1, num_features)  # [batch_size * num_nodes, num_features]
            
            # 特征融合
            # x_fused = self.feature_fusion(x_t)
            
            # # 图卷积层
            # graph_out = x_t
            # for i, gconv in enumerate(self.gconv_layers):
            #     # 扩展边索引以适应批次
            #     batch_edge_index = []
            #     batch_edge_weight = []
                
            #     for b in range(batch_size):
            #         offset = b * num_nodes
            #         batch_edge_index.append(edge_index + offset)
            #         if edge_weight is not None:
            #             batch_edge_weight.append(edge_weight)
                
            #     if len(batch_edge_index) > 0:
            #         batch_edge_index = torch.cat(batch_edge_index, dim=1)
            #         if edge_weight is not None:
            #             batch_edge_weight = torch.cat(batch_edge_weight)
            #         else:
            #             batch_edge_weight = None
                    
            #         # 应用图卷积
            #         conv_out = gconv(x_t if i == 0 else graph_out, batch_edge_index, batch_edge_weight)
                    
            #         # 残差连接和归一化
            #         if i > 0 and conv_out.shape == graph_out.shape:
            #             conv_out = conv_out + graph_out
                    
            #         graph_out = self.layer_norm1(conv_out)
            #         graph_out = F.relu(graph_out)
            #         graph_out = self.dropout_layer(graph_out)
            
            # # 重塑回节点维度
            # graph_out = graph_out.view(batch_size, num_nodes, -1)
            
            # 图卷积层
            graph_out = x_t
            for i, gconv in enumerate(self.gconv_layers):
                # 第一层使用原始特征，后续层使用前一层输出
                if i == 0:
                    conv_input = x_t
                else:
                    conv_input = graph_out
                # 扩展边索引以适应批次
                batch_edge_index = []
                batch_edge_weight = []
                
                for b in range(batch_size):
                    offset = b * num_nodes
                    batch_edge_index.append(edge_index + offset)
                    if edge_weight is not None:
                        batch_edge_weight.append(edge_weight)
                
                if len(batch_edge_index) > 0:
                    batch_edge_index = torch.cat(batch_edge_index, dim=1)
                    if edge_weight is not None:
                        batch_edge_weight = torch.cat(batch_edge_weight)
                    else:
                        batch_edge_weight = None
                
                # 应用图卷积
                conv_out = gconv(conv_input, batch_edge_index, batch_edge_weight)
                
                # 残差连接和归一化（第一层后开始）
                if i > 0 and conv_out.shape == graph_out.shape:
                    conv_out = conv_out + graph_out
                
                graph_out = self.layer_norm1(conv_out)
                graph_out = F.relu(graph_out)
                graph_out = self.dropout_layer(graph_out)
            
            # 应用特征融合到最终输出
            graph_out = self.feature_fusion(graph_out)
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            temporal_outputs.append(graph_out)
        
        # 堆叠时间维度
        temporal_features = torch.stack(temporal_outputs, dim=1)  # [batch_size, seq_len, num_nodes, hidden_dim]
        
        # 时间建模 - 对每个节点应用LSTM
        lstm_outputs = []
        for node_idx in range(num_nodes):
            node_features = temporal_features[:, :, node_idx, :]  # [batch_size, seq_len, hidden_dim]
            lstm_out, _ = self.lstm(node_features)
            
            # 自注意力机制
            lstm_out_transposed = lstm_out.transpose(0, 1)  # [seq_len, batch_size, hidden_dim]
            attn_out, _ = self.self_attention(lstm_out_transposed, lstm_out_transposed, lstm_out_transposed)
            attn_out = attn_out.transpose(0, 1)  # [batch_size, seq_len, hidden_dim]
            
            # 残差连接和归一化
            lstm_out = lstm_out + attn_out
            lstm_out = self.layer_norm2(lstm_out)
            
            lstm_outputs.append(lstm_out[:, -1, :])  # 取最后一个时间步
        
        # 合并所有节点的输出
        final_features = torch.stack(lstm_outputs, dim=1)  # [batch_size, num_nodes, hidden_dim]
        
        # 输出预测
        predictions = self.output_mlp(final_features)  # [batch_size, num_nodes, 1]
        
        return predictions.squeeze(-1)  # [batch_size, num_nodes]


class MetroFlowPredictor:
    """地铁流量预测器"""
    
    def __init__(self, base_path="C:\\Users\\<USER>\\Desktop\\接驳\\"):
        self.base_path = base_path
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
        # 数据缩放器
        self.flow_scaler = MinMaxScaler()
        self.feature_scaler = StandardScaler()
        
        # 模型参数
        self.seq_length = 8  # 使用过去8小时预测下一小时
        self.batch_size = 16
        self.learning_rate = 0.001
        self.num_epochs = 4
        
    def load_and_preprocess_data(self):
        """加载和预处理数据"""
        print("正在加载数据...")
        
        # 加载地理数据文件
        in_data = gpd.read_file(self.base_path + "in_500_with_coords.shp")
        out_data = gpd.read_file(self.base_path + "out_500_with_coords.shp")
        od_data = pd.read_csv(self.base_path + "updated_北京市_subway_od_2024_modified3.csv")
        station_features = pd.read_csv(self.base_path + "station_features_result.csv")
        
        print(f"进站数据形状: {in_data.shape}")
        print(f"出站数据形状: {out_data.shape}")
        print(f"OD数据形状: {od_data.shape}")
        print(f"站点特征数据形状: {station_features.shape}")
        
        # 从shapefile中提取栅格中心坐标
        in_data['longitude'] = in_data.geometry.centroid.x
        in_data['latitude'] = in_data.geometry.centroid.y
        out_data['longitude'] = out_data.geometry.centroid.x
        out_data['latitude'] = out_data.geometry.centroid.y
        
        # 获取所有唯一站点
        stations_in = set(in_data['station'].unique())
        stations_out = set(out_data['station'].unique())
        stations_od = set(od_data['o_rawname'].unique()) | set(od_data['d_rawname'].unique())
        stations_features = set(station_features['站名'].unique())
        
        # 取交集，确保所有数据源都有该站点
        common_stations = stations_in & stations_out & stations_od & stations_features
        print(f"共同站点数量: {len(common_stations)}")
        
        # 过滤数据只保留共同站点
        in_data = in_data[in_data['station'].isin(common_stations)]
        out_data = out_data[out_data['station'].isin(common_stations)]
        od_data = od_data[
            (od_data['o_rawname'].isin(common_stations)) & 
            (od_data['d_rawname'].isin(common_stations))
        ]
        station_features = station_features[station_features['站名'].isin(common_stations)]
        
        # 创建站点到索引的映射
        self.station_list = sorted(list(common_stations))
        self.station_to_idx = {station: idx for idx, station in enumerate(self.station_list)}
        self.num_stations = len(self.station_list)
        
        print(f"处理后的站点数量: {self.num_stations}")
        
        # 构建邻接矩阵
        self.adjacency_matrix, self.edge_index, self.edge_weight = self.build_graph(
            in_data, out_data, od_data
        )
        
        # 准备时空特征矩阵
        self.prepare_temporal_features(in_data, out_data, station_features)
        
        return in_data, out_data, od_data, station_features
    
    def build_graph(self, in_data, out_data, od_data):
        """构建图结构"""
        print("正在构建图结构...")
        
        # 基于地理距离的邻接矩阵
        coords = {}
        for _, row in in_data.drop_duplicates('station').iterrows():
            coords[row['station']] = (row['longitude'], row['latitude'])
        
        # 计算站点间的地理距离
        station_coords = np.array([coords[station] for station in self.station_list])
        distances = squareform(pdist(station_coords, metric='euclidean'))
        
        # 基于距离的邻接（只连接最近的k个邻居）
        k_neighbors = min(8, self.num_stations - 1)
        geo_adjacency = np.zeros((self.num_stations, self.num_stations))
        
        for i in range(self.num_stations):
            # 找到最近的k个邻居
            neighbor_indices = np.argsort(distances[i])[1:k_neighbors+1]
            for j in neighbor_indices:
                # 使用距离的倒数作为权重
                weight = 1.0 / (distances[i][j] + 1e-6)
                geo_adjacency[i][j] = weight
                geo_adjacency[j][i] = weight  # 无向图
        
        # 基于OD流量的邻接矩阵
        od_adjacency = np.zeros((self.num_stations, self.num_stations))
        od_weights = od_data.groupby(['o_rawname', 'd_rawname'])['trip'].sum().reset_index()
        
        for _, row in od_weights.iterrows():
            if row['o_rawname'] in self.station_to_idx and row['d_rawname'] in self.station_to_idx:
                i = self.station_to_idx[row['o_rawname']]
                j = self.station_to_idx[row['d_rawname']]
                od_adjacency[i][j] = row['trip']
        
        # 归一化OD邻接矩阵
        od_max = od_adjacency.max()
        if od_max > 0:
            od_adjacency = od_adjacency / od_max
        
        # 组合地理和OD邻接矩阵
        alpha = 0.6  # 地理权重
        beta = 0.4   # OD流量权重
        combined_adjacency = alpha * geo_adjacency + beta * od_adjacency
        
        # 添加自环
        combined_adjacency += np.eye(self.num_stations) * 0.1
        
        # 转换为PyTorch格式
        edge_index = []
        edge_weight = []
        
        for i in range(self.num_stations):
            for j in range(self.num_stations):
                if combined_adjacency[i][j] > 0:
                    edge_index.append([i, j])
                    edge_weight.append(combined_adjacency[i][j])
        
        edge_index = torch.tensor(edge_index, dtype=torch.long).t().contiguous()
        edge_weight = torch.tensor(edge_weight, dtype=torch.float)
        
        print(f"图边数量: {edge_index.shape[1]}")
        
        return combined_adjacency, edge_index, edge_weight
    
    def prepare_temporal_features(self, in_data, out_data, station_features):
        """准备时空特征"""
        print("正在准备时空特征...")
        
        # 合并进出站数据
        in_data_pivot = in_data.pivot_table(
            index=['station', 'hour'], 
            values='count', 
            aggfunc='sum'
        ).reset_index()
        in_data_pivot.columns = ['station', 'hour', 'in_flow']
        
        out_data_pivot = out_data.pivot_table(
            index=['station', 'hour'], 
            values='count', 
            aggfunc='sum'
        ).reset_index()
        out_data_pivot.columns = ['station', 'hour', 'out_flow']
        
        # 合并流量数据
        flow_data = pd.merge(
            in_data_pivot, out_data_pivot, 
            on=['station', 'hour'], 
            how='outer'
        ).fillna(0)
        
        # 添加站点特征
        station_features_clean = station_features.copy()
        station_features_clean.columns = ['station'] + [f'feature_{i}' for i in range(1, len(station_features.columns))]
        
        flow_data = pd.merge(
            flow_data, station_features_clean, 
            on='station', 
            how='left'
        ).fillna(0)
        
        # 添加时间特征
        flow_data['hour_sin'] = np.sin(2 * np.pi * flow_data['hour'] / 24)
        flow_data['hour_cos'] = np.cos(2 * np.pi * flow_data['hour'] / 24)
        flow_data['is_weekend'] = 0  # 简化处理，实际应根据日期计算
        flow_data['is_peak'] = flow_data['hour'].apply(
            lambda x: 1 if x in [7, 8, 9, 17, 18, 19] else 0
        )
        
        # 确保所有站点和小时都有数据
        all_combinations = []
        for station in self.station_list:
            for hour in range(24):
                all_combinations.append({'station': station, 'hour': hour})
        
        complete_data = pd.DataFrame(all_combinations)
        flow_data = pd.merge(
            complete_data, flow_data, 
            on=['station', 'hour'], 
            how='left'
        ).fillna(0)
        
        # 构建特征矩阵
        feature_columns = [col for col in flow_data.columns 
                          if col not in ['station', 'hour', 'in_flow', 'out_flow']]
        
        self.feature_matrix = np.zeros((24, self.num_stations, len(feature_columns) + 2))
        self.flow_matrix_in = np.zeros((24, self.num_stations))
        self.flow_matrix_out = np.zeros((24, self.num_stations))
        
        for _, row in flow_data.iterrows():
            if row['station'] in self.station_to_idx:
                station_idx = self.station_to_idx[row['station']]
                hour_idx = int(row['hour'])
                
                # 填充特征
                features = [row['in_flow'], row['out_flow']] + [row[col] for col in feature_columns]
                self.feature_matrix[hour_idx, station_idx, :] = features
                
                # 填充流量标签
                self.flow_matrix_in[hour_idx, station_idx] = row['in_flow']
                self.flow_matrix_out[hour_idx, station_idx] = row['out_flow']
        
        print(f"特征矩阵形状: {self.feature_matrix.shape}")
        print(f"流量矩阵形状: {self.flow_matrix_in.shape}")
    
    def create_sequences(self, data, seq_length):
        """创建时间序列"""
        sequences = []
        targets = []
        
        for i in range(seq_length, len(data)):
            sequences.append(data[i-seq_length:i])
            targets.append(data[i])
        
        return np.array(sequences), np.array(targets)
    
    def train_model(self, model_type='in'):
        """训练模型"""
        print(f"正在训练{model_type}站流量预测模型...")
        
        # 选择目标数据
        if model_type == 'in':
            target_data = self.flow_matrix_in
        else:
            target_data = self.flow_matrix_out
        
        # 标准化特征和目标
        feature_data_scaled = self.feature_matrix.copy()
        feature_data_scaled = feature_data_scaled.reshape(-1, feature_data_scaled.shape[-1])
        feature_data_scaled = self.feature_scaler.fit_transform(feature_data_scaled)
        feature_data_scaled = feature_data_scaled.reshape(self.feature_matrix.shape)
        
        target_data_scaled = self.flow_scaler.fit_transform(target_data.reshape(-1, 1))
        target_data_scaled = target_data_scaled.reshape(target_data.shape)
        
        # 创建序列
        X, y = self.create_sequences(feature_data_scaled, self.seq_length)
        y_target, _ = self.create_sequences(target_data_scaled, self.seq_length)
        
        print(f"序列形状: X={X.shape}, y={y_target.shape}")
        
        # 划分训练和测试集
        split_idx = int(len(X) * 0.8)
        X_train, X_test = X[:split_idx], X[split_idx:]
        y_train, y_test = y_target[:split_idx], y_target[split_idx:]
        
        # 转换为PyTorch张量
        X_train = torch.FloatTensor(X_train).to(self.device)
        X_test = torch.FloatTensor(X_test).to(self.device)
        y_train = torch.FloatTensor(y_train).to(self.device)
        y_test = torch.FloatTensor(y_test).to(self.device)
        
        # 创建模型
        model = TemporalGCN(
            num_nodes=self.num_stations,
            num_features=self.feature_matrix.shape[-1],
            hidden_dim=128,
            num_layers=3,
            rnn_hidden_dim=64,
            dropout=0.3,
            attention_heads=8
        ).to(self.device)
        
        # 边索引和权重
        edge_index = self.edge_index.to(self.device)
        edge_weight = self.edge_weight.to(self.device)
        
        # 优化器和损失函数
        optimizer = optim.AdamW(model.parameters(), lr=self.learning_rate, weight_decay=1e-5)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', patience=10, factor=0.5)
        criterion = nn.MSELoss()
        
        # 训练历史
        train_losses = []
        train_maes = []
        val_losses = []
        val_maes = []
        
        print("开始训练...")
        for epoch in range(self.num_epochs):
            model.train()
            epoch_train_loss = 0
            epoch_train_mae = 0
            num_batches = 0
            
            # 批量训练
            for i in range(0, len(X_train), self.batch_size):
                batch_X = X_train[i:i+self.batch_size]
                batch_y = y_train[i:i+self.batch_size]
                
                if len(batch_X) == 0:
                    continue
                
                optimizer.zero_grad()
                
                # 前向传播
                predictions = model(batch_X, edge_index, edge_weight)
                
                # 计算损失
                loss = criterion(predictions, batch_y)
                
                # 反向传播
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                # 计算MAE
                with torch.no_grad():
                    mae = torch.mean(torch.abs(predictions - batch_y))
                
                epoch_train_loss += loss.item()
                epoch_train_mae += mae.item()
                num_batches += 1
            
            # 验证
            model.eval()
            with torch.no_grad():
                val_predictions = model(X_test, edge_index, edge_weight)
                val_loss = criterion(val_predictions, y_test)
                val_mae = torch.mean(torch.abs(val_predictions - y_test))
            
            # 更新学习率
            scheduler.step(val_loss)
            
            # 记录历史
            avg_train_loss = epoch_train_loss / max(num_batches, 1)
            avg_train_mae = epoch_train_mae / max(num_batches, 1)
            
            train_losses.append(avg_train_loss)
            train_maes.append(avg_train_mae)
            val_losses.append(val_loss.item())
            val_maes.append(val_mae.item())
            
            if (epoch + 1) % 10 == 0:
                print(f"Epoch {epoch+1}/{self.num_epochs}")
                print(f"  训练损失: {avg_train_loss:.6f}, 训练MAE: {avg_train_mae:.6f}")
                print(f"  验证损失: {val_loss:.6f}, 验证MAE: {val_mae:.6f}")
                print(f"  学习率: {optimizer.param_groups[0]['lr']:.8f}")
        
        # 最终预测和评估
        model.eval()
        with torch.no_grad():
            train_pred = model(X_train, edge_index, edge_weight)
            test_pred = model(X_test, edge_index, edge_weight)
        
        # 反标准化
        train_pred_rescaled = self.flow_scaler.inverse_transform(
            train_pred.cpu().numpy().reshape(-1, 1)
        ).reshape(train_pred.shape)
        
        test_pred_rescaled = self.flow_scaler.inverse_transform(
            test_pred.cpu().numpy().reshape(-1, 1)
        ).reshape(test_pred.shape)
        
        y_train_rescaled = self.flow_scaler.inverse_transform(
            y_train.cpu().numpy().reshape(-1, 1)
        ).reshape(y_train.shape)
        
        y_test_rescaled = self.flow_scaler.inverse_transform(
            y_test.cpu().numpy().reshape(-1, 1)
        ).reshape(y_test.shape)
        
        # 计算最终指标
        train_mae_final = mean_absolute_error(
            y_train_rescaled.flatten(), 
            train_pred_rescaled.flatten()
        )
        test_mae_final = mean_absolute_error(
            y_test_rescaled.flatten(), 
            test_pred_rescaled.flatten()
        )
        
        print(f"\n{model_type}站流量预测结果:")
        print(f"训练集MAE: {train_mae_final:.4f}")
        print(f"测试集MAE: {test_mae_final:.4f}")
        
        # 绘制训练历史
        self.plot_training_history(train_losses, train_maes, val_losses, val_maes, model_type)
        
        return {
            'model': model,
            'train_pred': train_pred_rescaled,
            'test_pred': test_pred_rescaled,
            'train_true': y_train_rescaled,
            'test_true': y_test_rescaled,
            'train_mae': train_mae_final,
            'test_mae': test_mae_final,
            'history': {
                'train_loss': train_losses,
                'train_mae': train_maes,
                'val_loss': val_losses,
                'val_mae': val_maes
            }
        }
    
    def plot_training_history(self, train_losses, train_maes, val_losses, val_maes, model_type):
        """绘制训练历史"""
        fig, ((ax1, ax2)) = plt.subplots(1, 2, figsize=(15, 5))
        
        # 损失曲线
        ax1.plot(train_losses, label='训练损失', alpha=0.8)
        ax1.plot(val_losses, label='验证损失', alpha=0.8)
        ax1.set_title(f'{model_type}站流量预测 - 损失曲线')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # MAE曲线
        ax2.plot(train_maes, label='训练MAE', alpha=0.8)
        ax2.plot(val_maes, label='验证MAE', alpha=0.8)
        ax2.set_title(f'{model_type}站流量预测 - MAE曲线')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('MAE')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(f'{self.base_path}{model_type}_tgcn_training_history.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def save_predictions(self, in_results, out_results, original_data):
        """保存预测结果"""
        print("正在保存预测结果...")
        
        in_data, out_data, od_data, _ = original_data
        
        # 创建预测结果DataFrame
        predictions_df = []
        
        # 处理进站预测
        test_indices = range(len(in_results['train_pred']), 
                           len(in_results['train_pred']) + len(in_results['test_pred']))
        
        for i, idx in enumerate(test_indices):
            hour = (idx + self.seq_length) % 24
            for station_idx, station in enumerate(self.station_list):
                predictions_df.append({
                    'type': 'in',
                    'station': station,
                    'hour': hour,
                    'true_value': in_results['test_true'][i, station_idx],
                    'predicted_value': in_results['test_pred'][i, station_idx]
                })
        
        # 处理出站预测
        for i, idx in enumerate(test_indices):
            hour = (idx + self.seq_length) % 24
            for station_idx, station in enumerate(self.station_list):
                predictions_df.append({
                    'type': 'out',
                    'station': station,
                    'hour': hour,
                    'true_value': out_results['test_true'][i, station_idx],
                    'predicted_value': out_results['test_pred'][i, station_idx]
                })
        
        # 保存综合预测结果
        predictions_df = pd.DataFrame(predictions_df)
        predictions_df.to_csv(f'{self.base_path}tgcn_prediction_comparison.csv', index=False)
        
        # 保存带坐标的进站预测结果
        in_test_data = in_data.copy()
        in_predictions_with_coords = []
        
        for _, row in in_test_data.iterrows():
            if row['station'] in self.station_to_idx:
                station_idx = self.station_to_idx[row['station']]
                # 找到对应的预测值
                pred_rows = predictions_df[
                    (predictions_df['type'] == 'in') & 
                    (predictions_df['station'] == row['station']) & 
                    (predictions_df['hour'] == row['hour'])
                ]
                
                if not pred_rows.empty:
                    prediction = pred_rows.iloc[0]['predicted_value']
                else:
                    prediction = 0
                
                new_row = row.copy()
                new_row['prediction'] = prediction
                in_predictions_with_coords.append(new_row)
        
        if in_predictions_with_coords:
            in_pred_gdf = gpd.GeoDataFrame(in_predictions_with_coords)
            in_pred_gdf.to_file(f'{self.base_path}in_500_tgcn_predictions_with_coords.shp')
        
        # 保存带坐标的出站预测结果
        out_test_data = out_data.copy()
        out_predictions_with_coords = []
        
        for _, row in out_test_data.iterrows():
            if row['station'] in self.station_to_idx:
                station_idx = self.station_to_idx[row['station']]
                # 找到对应的预测值
                pred_rows = predictions_df[
                    (predictions_df['type'] == 'out') & 
                    (predictions_df['station'] == row['station']) & 
                    (predictions_df['hour'] == row['hour'])
                ]
                
                if not pred_rows.empty:
                    prediction = pred_rows.iloc[0]['predicted_value']
                else:
                    prediction = 0
                
                new_row = row.copy()
                new_row['prediction'] = prediction
                out_predictions_with_coords.append(new_row)
        
        if out_predictions_with_coords:
            out_pred_gdf = gpd.GeoDataFrame(out_predictions_with_coords)
            out_pred_gdf.to_file(f'{self.base_path}out_500_tgcn_predictions_with_coords.shp')
        
        print("预测结果已保存")
        
        return predictions_df
    
    def visualize_results(self, in_results, out_results):
        """可视化结果"""
        fig, axes = plt.subplots(2, 2, figsize=(20, 12))
        
        # 进站流量预测散点图
        axes[0,0].scatter(in_results['test_true'].flatten(), 
                         in_results['test_pred'].flatten(), 
                         alpha=0.6, s=20)
        axes[0,0].plot([0, in_results['test_true'].max()], 
                      [0, in_results['test_true'].max()], 
                      'r--', lw=2)
        axes[0,0].set_xlabel('真实进站流量')
        axes[0,0].set_ylabel('预测进站流量')
        axes[0,0].set_title(f'进站流量预测 (MAE: {in_results["test_mae"]:.4f})')
        axes[0,0].grid(True, alpha=0.3)
        
        # 出站流量预测散点图
        axes[0,1].scatter(out_results['test_true'].flatten(), 
                         out_results['test_pred'].flatten(), 
                         alpha=0.6, s=20)
        axes[0,1].plot([0, out_results['test_true'].max()], 
                      [0, out_results['test_true'].max()], 
                      'r--', lw=2)
        axes[0,1].set_xlabel('真实出站流量')
        axes[0,1].set_ylabel('预测出站流量')
        axes[0,1].set_title(f'出站流量预测 (MAE: {out_results["test_mae"]:.4f})')
        axes[0,1].grid(True, alpha=0.3)
        
        # 时间序列对比（选择几个代表性站点）
        sample_stations = min(3, len(self.station_list))
        sample_indices = np.random.choice(len(self.station_list), sample_stations, replace=False)
        
        for i, station_idx in enumerate(sample_indices):
            if i < 2:  # 只绘制前两个
                ax = axes[1, i]
                
                # 绘制进站流量时间序列
                true_series = in_results['test_true'][:, station_idx]
                pred_series = in_results['test_pred'][:, station_idx]
                
                time_steps = range(len(true_series))
                ax.plot(time_steps, true_series, label='真实值', linewidth=2)
                ax.plot(time_steps, pred_series, label='预测值', linewidth=2, alpha=0.8)
                ax.set_xlabel('时间步')
                ax.set_ylabel('进站流量')
                ax.set_title(f'站点 {self.station_list[station_idx]} 进站流量时间序列')
                ax.legend()
                ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(f'{self.base_path}tgcn_prediction_results.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def run_complete_analysis(self):
        """运行完整的分析流程"""
        print("="*60)
        print("T-GCN地铁流量预测分析开始")
        print("="*60)
        
        # 加载数据
        original_data = self.load_and_preprocess_data()
        
        # 训练进站流量预测模型
        print("\n" + "="*40)
        print("训练进站流量预测模型")
        print("="*40)
        in_results = self.train_model('in')
        
        # 训练出站流量预测模型
        print("\n" + "="*40)
        print("训练出站流量预测模型")
        print("="*40)
        out_results = self.train_model('out')
        
        # 保存预测结果
        predictions_df = self.save_predictions(in_results, out_results, original_data)
        
        # 可视化结果
        self.visualize_results(in_results, out_results)
        
        # 打印最终结果摘要
        print("\n" + "="*60)
        print("T-GCN预测结果摘要")
        print("="*60)
        print(f"模型类型: 时空图卷积网络 (T-GCN)")
        print(f"站点数量: {self.num_stations}")
        print(f"序列长度: {self.seq_length} 小时")
        print(f"训练轮数: {self.num_epochs}")
        print(f"\n进站流量预测:")
        print(f"  训练集MAE: {in_results['train_mae']:.4f}")
        print(f"  测试集MAE: {in_results['test_mae']:.4f}")
        print(f"\n出站流量预测:")
        print(f"  训练集MAE: {out_results['train_mae']:.4f}")
        print(f"  测试集MAE: {out_results['test_mae']:.4f}")
        
        # 每代的MAE和loss打印
        print(f"\n最后10轮训练历史 (进站):")
        for i in range(max(0, len(in_results['history']['train_loss'])-10), 
                      len(in_results['history']['train_loss'])):
            print(f"  Epoch {i+1}: Loss={in_results['history']['train_loss'][i]:.6f}, "
                  f"MAE={in_results['history']['train_mae'][i]:.6f}")
        
        print(f"\n最后10轮训练历史 (出站):")
        for i in range(max(0, len(out_results['history']['train_loss'])-10), 
                      len(out_results['history']['train_loss'])):
            print(f"  Epoch {i+1}: Loss={out_results['history']['train_loss'][i]:.6f}, "
                  f"MAE={out_results['history']['train_mae'][i]:.6f}")
        
        print(f"\n文件已保存到: {self.base_path}")
        print("- tgcn_prediction_comparison.csv: 预测结果对比")
        print("- in_500_tgcn_predictions_with_coords.shp: 进站预测结果(带坐标)")
        print("- out_500_tgcn_predictions_with_coords.shp: 出站预测结果(带坐标)")
        print("- tgcn_prediction_results.png: 预测结果可视化")
        print("- in_tgcn_training_history.png: 进站模型训练历史")
        print("- out_tgcn_training_history.png: 出站模型训练历史")
        
        return in_results, out_results, predictions_df


# 使用示例
if __name__ == "__main__":
    # 创建预测器实例
    predictor = MetroFlowPredictor()
    
    # 运行完整分析
    in_results, out_results, predictions_df = predictor.run_complete_analysis()