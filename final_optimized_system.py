"""
最终优化的机器学习地铁流量预测系统
专门针对北京市数据优化，确保R²为正值
"""
import os
import sys
import time
import numpy as np
import pandas as pd
import geopandas as gpd
import torch
import torch.nn as nn
import torch.optim as optim
from torch_geometric.nn import GCNConv
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.ensemble import RandomForestRegressor
import warnings
warnings.filterwarnings('ignore')

class EnhancedMetroGCN(nn.Module):
    """增强的地铁流量预测模型"""
    
    def __init__(self, input_dim, hidden_dim=64):
        super(EnhancedMetroGCN, self).__init__()
        
        # 特征处理
        self.feature_norm = nn.BatchNorm1d(input_dim)
        self.feature_projection = nn.Linear(input_dim, hidden_dim)
        
        # 时间编码
        self.hour_embedding = nn.Embedding(24, 16)
        
        # GCN层
        self.gcn1 = GCNConv(hidden_dim + 16, hidden_dim)
        self.gcn2 = GCNConv(hidden_dim, hidden_dim)
        
        # 预测头
        self.in_predictor = nn.Sequential(
            nn.Linear(hidden_dim, 32),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(32, 1),
            nn.ReLU()  # 确保非负
        )
        
        self.out_predictor = nn.Sequential(
            nn.Linear(hidden_dim, 32),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(32, 1),
            nn.ReLU()  # 确保非负
        )
    
    def forward(self, x, edge_index, hour):
        # 特征标准化
        if x.size(0) > 1:
            x = self.feature_norm(x)
        x = self.feature_projection(x)
        
        # 时间编码
        if hour.dim() == 0:
            hour = hour.expand(x.size(0))
        time_embed = self.hour_embedding(hour)
        
        # 合并特征
        x = torch.cat([x, time_embed], dim=-1)
        
        # GCN处理
        x = torch.relu(self.gcn1(x, edge_index))
        x = torch.relu(self.gcn2(x, edge_index))
        
        # 预测
        in_flow = self.in_predictor(x).squeeze(-1)
        out_flow = self.out_predictor(x).squeeze(-1)
        
        return {'in_flow': in_flow, 'out_flow': out_flow}

class FinalOptimizedPredictor:
    """最终优化的预测器"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
        self.scaler = StandardScaler()
        self.model = None
        self.rf_model_in = None
        self.rf_model_out = None
        
        self.stations = []
        self.station_to_idx = {}
        
    def load_and_process_data(self):
        """加载和处理数据"""
        print("="*60)
        print("加载北京市地铁数据")
        print("="*60)
        
        try:
            # 加载数据
            in_data = gpd.read_file('in_500_with_coords.shp')
            out_data = gpd.read_file('out_500_with_coords.shp')
            
            # 清理数据
            in_data['station_clean'] = in_data['station'].apply(lambda x: str(x).split('_')[0])
            out_data['station_clean'] = out_data['station'].apply(lambda x: str(x).split('_')[0])
            
            # 过滤异常值
            in_data = in_data[(in_data['count'] >= 0) & (in_data['count'] <= in_data['count'].quantile(0.98))]
            out_data = out_data[(out_data['count'] >= 0) & (out_data['count'] <= out_data['count'].quantile(0.98))]
            
            print(f"进站数据: {in_data.shape}")
            print(f"出站数据: {out_data.shape}")
            
            # 获取站点
            self.stations = sorted(list(set(in_data['station_clean'].unique()) | 
                                       set(out_data['station_clean'].unique())))
            self.station_to_idx = {station: idx for idx, station in enumerate(self.stations)}
            print(f"站点数量: {len(self.stations)}")
            
            # 构建完整的数据集
            self._build_comprehensive_dataset(in_data, out_data)
            
            return True
            
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
    
    def _build_comprehensive_dataset(self, in_data, out_data):
        """构建完整的数据集"""
        # 创建完整的站点-小时组合
        all_combinations = []
        
        for hour in range(24):
            for station in self.stations:
                # 获取该站点该小时的进出站数据
                in_flow = in_data[(in_data['station_clean'] == station) & 
                                 (in_data['hour'] == hour)]['count'].sum()
                out_flow = out_data[(out_data['station_clean'] == station) & 
                                   (out_data['hour'] == hour)]['count'].sum()
                
                # 构建特征
                features = self._build_features(station, hour)
                
                all_combinations.append({
                    'station': station,
                    'hour': hour,
                    'features': features,
                    'in_flow': in_flow,
                    'out_flow': out_flow
                })
        
        # 转换为DataFrame
        self.dataset = pd.DataFrame(all_combinations)
        
        # 过滤有效数据（至少有一些流量）
        valid_mask = (self.dataset['in_flow'] > 0) | (self.dataset['out_flow'] > 0)
        self.dataset = self.dataset[valid_mask].reset_index(drop=True)
        
        print(f"有效数据点: {len(self.dataset)}")
        
        # 准备特征和目标
        X = np.vstack(self.dataset['features'].values)
        y_in = self.dataset['in_flow'].values
        y_out = self.dataset['out_flow'].values
        
        # 标准化特征
        X = self.scaler.fit_transform(X)
        
        # 分割数据
        self.X_train, self.X_test, self.y_in_train, self.y_in_test, self.y_out_train, self.y_out_test = \
            train_test_split(X, y_in, y_out, test_size=0.2, random_state=42)
        
        print(f"训练集大小: {len(self.X_train)}")
        print(f"测试集大小: {len(self.X_test)}")
    
    def _build_features(self, station, hour):
        """构建特征向量"""
        features = []
        
        # 基础特征
        station_idx = self.station_to_idx[station]
        features.extend([
            station_idx / len(self.stations),  # 标准化站点ID
            hour / 23.0,  # 标准化小时
            np.sin(2 * np.pi * hour / 24),  # 小时的周期性
            np.cos(2 * np.pi * hour / 24),
        ])
        
        # 时间段特征
        features.extend([
            1.0 if 6 <= hour <= 9 else 0.0,    # 早高峰
            1.0 if 17 <= hour <= 20 else 0.0,  # 晚高峰
            1.0 if 10 <= hour <= 16 else 0.0,  # 平峰
            1.0 if hour in [0,1,2,3,4,5,21,22,23] else 0.0,  # 低峰
        ])
        
        # 工作日特征（简化）
        features.extend([
            1.0 if hour >= 6 and hour <= 22 else 0.0,  # 运营时间
            1.0 if hour >= 7 and hour <= 19 else 0.0,  # 主要运营时间
        ])
        
        # 站点类型特征（基于站点名称简单分类）
        station_name = station.lower()
        features.extend([
            1.0 if any(word in station_name for word in ['机场', '火车', '高铁']) else 0.0,  # 交通枢纽
            1.0 if any(word in station_name for word in ['商场', '购物', '中心']) else 0.0,   # 商业区
            1.0 if any(word in station_name for word in ['大学', '学院', '学校']) else 0.0,   # 教育区
            1.0 if any(word in station_name for word in ['医院', '医疗']) else 0.0,         # 医疗区
        ])
        
        return np.array(features)
    
    def train_ensemble_model(self):
        """训练集成模型"""
        print("="*60)
        print("训练集成模型")
        print("="*60)
        
        # 1. 训练随机森林模型
        print("训练随机森林模型...")
        self.rf_model_in = RandomForestRegressor(
            n_estimators=100, 
            max_depth=10, 
            random_state=42,
            n_jobs=-1
        )
        self.rf_model_out = RandomForestRegressor(
            n_estimators=100, 
            max_depth=10, 
            random_state=42,
            n_jobs=-1
        )
        
        self.rf_model_in.fit(self.X_train, self.y_in_train)
        self.rf_model_out.fit(self.X_train, self.y_out_train)
        
        # 2. 训练神经网络模型
        print("训练神经网络模型...")
        self._train_neural_network()
        
        print("集成模型训练完成！")
    
    def _train_neural_network(self):
        """训练神经网络"""
        # 简化的神经网络
        input_dim = self.X_train.shape[1]
        
        class SimpleNN(nn.Module):
            def __init__(self, input_dim):
                super().__init__()
                self.net = nn.Sequential(
                    nn.Linear(input_dim, 64),
                    nn.ReLU(),
                    nn.Dropout(0.3),
                    nn.Linear(64, 32),
                    nn.ReLU(),
                    nn.Dropout(0.3),
                    nn.Linear(32, 2),
                    nn.ReLU()  # 确保非负
                )
            
            def forward(self, x):
                return self.net(x)
        
        self.nn_model = SimpleNN(input_dim).to(self.device)
        optimizer = optim.Adam(self.nn_model.parameters(), lr=0.001)
        criterion = nn.MSELoss()
        
        # 转换为张量
        X_train_tensor = torch.tensor(self.X_train, dtype=torch.float32).to(self.device)
        y_train_tensor = torch.tensor(
            np.column_stack([self.y_in_train, self.y_out_train]), 
            dtype=torch.float32
        ).to(self.device)
        
        # 训练
        self.nn_model.train()
        for epoch in range(200):
            optimizer.zero_grad()
            pred = self.nn_model(X_train_tensor)
            loss = criterion(pred, y_train_tensor)
            loss.backward()
            optimizer.step()
            
            if epoch % 50 == 0:
                print(f"NN Epoch {epoch}: Loss = {loss.item():.6f}")
    
    def evaluate_ensemble_model(self):
        """评估集成模型"""
        print("="*60)
        print("评估集成模型")
        print("="*60)
        
        # 随机森林预测
        rf_pred_in = self.rf_model_in.predict(self.X_test)
        rf_pred_out = self.rf_model_out.predict(self.X_test)
        
        # 神经网络预测
        self.nn_model.eval()
        with torch.no_grad():
            X_test_tensor = torch.tensor(self.X_test, dtype=torch.float32).to(self.device)
            nn_pred = self.nn_model(X_test_tensor).cpu().numpy()
            nn_pred_in = nn_pred[:, 0]
            nn_pred_out = nn_pred[:, 1]
        
        # 集成预测（加权平均）
        ensemble_pred_in = 0.6 * rf_pred_in + 0.4 * nn_pred_in
        ensemble_pred_out = 0.6 * rf_pred_out + 0.4 * nn_pred_out
        
        # 确保非负
        ensemble_pred_in = np.maximum(ensemble_pred_in, 0)
        ensemble_pred_out = np.maximum(ensemble_pred_out, 0)
        
        # 计算指标
        metrics = {}
        
        # 进站流量指标
        metrics['in_flow'] = {
            'mae': mean_absolute_error(self.y_in_test, ensemble_pred_in),
            'rmse': np.sqrt(mean_squared_error(self.y_in_test, ensemble_pred_in)),
            'r2': r2_score(self.y_in_test, ensemble_pred_in)
        }
        
        # 出站流量指标
        metrics['out_flow'] = {
            'mae': mean_absolute_error(self.y_out_test, ensemble_pred_out),
            'rmse': np.sqrt(mean_squared_error(self.y_out_test, ensemble_pred_out)),
            'r2': r2_score(self.y_out_test, ensemble_pred_out)
        }
        
        # 整体指标
        all_pred = np.concatenate([ensemble_pred_in, ensemble_pred_out])
        all_true = np.concatenate([self.y_in_test, self.y_out_test])
        
        metrics['overall'] = {
            'mae': mean_absolute_error(all_true, all_pred),
            'rmse': np.sqrt(mean_squared_error(all_true, all_pred)),
            'r2': r2_score(all_true, all_pred)
        }
        
        # 单独评估随机森林
        rf_all_pred = np.concatenate([rf_pred_in, rf_pred_out])
        metrics['random_forest'] = {
            'mae': mean_absolute_error(all_true, rf_all_pred),
            'rmse': np.sqrt(mean_squared_error(all_true, rf_all_pred)),
            'r2': r2_score(all_true, rf_all_pred)
        }
        
        return metrics, (ensemble_pred_in, ensemble_pred_out, self.y_in_test, self.y_out_test)
    
    def save_final_results(self, metrics, predictions):
        """保存最终结果"""
        pred_in, pred_out, true_in, true_out = predictions
        
        # 保存比较结果
        comparison_data = []
        for i in range(len(pred_in)):
            comparison_data.append({
                'Type': 'In',
                'True_Value': float(true_in[i]),
                'Predicted_Value': float(pred_in[i]),
                'Error': float(abs(true_in[i] - pred_in[i]))
            })
        
        for i in range(len(pred_out)):
            comparison_data.append({
                'Type': 'Out',
                'True_Value': float(true_out[i]),
                'Predicted_Value': float(pred_out[i]),
                'Error': float(abs(true_out[i] - pred_out[i]))
            })
        
        pd.DataFrame(comparison_data).to_csv('final_optimized_results.csv', index=False)
        
        # 保存指标
        import json
        with open('final_optimized_metrics.json', 'w') as f:
            json.dump(metrics, f, indent=2)
        
        print("✓ 最终结果已保存")

def main():
    """主函数"""
    print("="*80)
    print("最终优化的机器学习地铁流量预测系统")
    print("专门针对北京市数据优化，确保R²为正值")
    print("="*80)
    
    start_time = time.time()
    
    try:
        predictor = FinalOptimizedPredictor()
        
        # 加载数据
        if not predictor.load_and_process_data():
            return False
        
        # 训练模型
        predictor.train_ensemble_model()
        
        # 评估模型
        metrics, predictions = predictor.evaluate_ensemble_model()
        
        # 保存结果
        predictor.save_final_results(metrics, predictions)
        
        # 打印结果
        print("\n" + "="*60)
        print("最终优化的预测结果")
        print("="*60)
        
        print("集成模型性能:")
        for flow_type in ['in_flow', 'out_flow', 'overall']:
            if flow_type in metrics:
                m = metrics[flow_type]
                print(f"  {flow_type.upper()}:")
                print(f"    MAE:  {m['mae']:.4f}")
                print(f"    RMSE: {m['rmse']:.4f}")
                print(f"    R²:   {m['r2']:.4f}")
        
        print("\n随机森林基线:")
        if 'random_forest' in metrics:
            m = metrics['random_forest']
            print(f"  MAE:  {m['mae']:.4f}")
            print(f"  RMSE: {m['rmse']:.4f}")
            print(f"  R²:   {m['r2']:.4f}")
        
        # 性能分析
        overall_r2 = metrics.get('overall', {}).get('r2', 0)
        if overall_r2 > 0:
            print(f"\n✅ 成功！模型性能优于简单均值预测")
            print(f"   R² = {overall_r2:.4f} > 0")
            print(f"   模型解释了 {overall_r2*100:.1f}% 的数据方差")
        else:
            print(f"\n⚠️  模型仍需优化 (R² = {overall_r2:.4f})")
        
        print(f"\n技术特点:")
        print(f"  ✓ 基于北京市真实地铁数据")
        print(f"  ✓ 集成学习（随机森林 + 神经网络）")
        print(f"  ✓ 丰富的特征工程")
        print(f"  ✓ 时间和空间特征融合")
        print(f"  ✓ 数据量: {len(predictor.dataset):,} 个样本")
        print(f"  ✓ 站点数: {len(predictor.stations)} 个")
        
        print(f"\n运行时间: {time.time() - start_time:.2f} 秒")
        print("="*60)
        
        return True
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
