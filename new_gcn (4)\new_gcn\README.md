# GCN地铁流量预测系统

基于图卷积网络(GCN)和时空特征的智能地铁流量预测系统，支持多任务流量预测、OD流量分析、新线路影响评估和栅格-站点流量预测。

## 🎯 项目概述

### 主要任务

本项目旨在构建一个综合性的地铁流量预测系统，解决以下核心问题：

1. **基础流量预测**
   - 地铁站点进站流量预测
   - 地铁站点出站流量预测
   - 基于时间序列的短期/长期预测

2. **OD流量预测** ⭐️ 核心功能
   - 预测任意两个地铁站点之间的客流量
   - 利用站点间距离、换乘次数、出行时间、等待时间等特征
   - 支持新OD对的流量预测

3. **栅格-站点流量预测**
   - 基于地理栅格到地铁站点的流量预测
   - 利用栅格地理特征（经纬度、面积、形状等）
   - 站点到栅格的反向流量预测

4. **新线路影响分析**
   - 评估新地铁线路对现有网络的影响
   - 客流重分布分析
   - 网络效率提升评估

### 技术特色

- **图神经网络**: 使用GCN建模地铁网络拓扑关系
- **时空融合**: 结合时间周期性和空间地理特征
- **多任务学习**: 共享编码器，任务特定解码器
- **注意力机制**: 时间注意力捕获重要时间模式
- **内存优化**: Float16精度，梯度检查点，适配中等配置GPU

## 🏗️ 技术架构

### 模型架构

```
输入数据
├── 流量时间序列 [batch_size, seq_len, num_nodes]
├── 时间特征 [batch_size, seq_len, num_nodes, 6]
├── 空间特征 [num_nodes, 51]
├── 图结构 [edge_index, edge_attr]
└── OD特征 [num_od_pairs, 4]
    ↓
时空嵌入层
├── 时间特征嵌入 (6 → hidden_dim//2)
├── 空间特征嵌入 (51 → hidden_dim//2)
└── 特征融合 (hidden_dim → hidden_dim)
    ↓
多层GCN编码器
├── GCN层1: (hidden_dim+1 → hidden_dim) + 注意力
├── GCN层2: (hidden_dim → hidden_dim) + 残差连接
└── GCN层3: (hidden_dim → hidden_dim//2)
    ↓
时间注意力层
└── 注意力权重计算 + 时间聚合
    ↓
任务特定解码器
├── 进站流量预测: Linear(hidden_dim//2 → 1)
├── 出站流量预测: Linear(hidden_dim//2 → 1)
└── OD流量预测: ODFlowPredictor(起点+终点+OD特征)
```

### 数据流程

```
原始数据文件
├── in_500_with_coords.shp (进站数据+栅格坐标)
├── out_500_with_coords.shp (出站数据+栅格坐标)  
├── updated北京市_subway_od_2024_modified3.csv (OD数据+特征)
└── station_features_result.csv (站点特征)
    ↓
数据预处理
├── 栅格地理特征提取 (5维)
├── 时间特征构造 (6维: sin/cos + 高峰标识)
├── 时间序列构建 (滑动窗口, sequence_length=6)
└── 图网络构建 (混合图: 地理+K近邻+OD)
    ↓
模型训练
├── 多任务训练 (进站+出站)
├── 早停机制 + 学习率调度
└── 模型保存 (best_model_*.pth)
    ↓
预测推理
├── 基础流量预测 (进站/出站)
├── OD流量预测 (节点嵌入+特征融合)
├── 栅格-站点预测 (地理位置匹配)
└── 新线路影响分析 (网络扩展模拟)
```

## 📊 数据格式说明

### 输入数据要求

#### 1. 进站/出站数据 (.shp格式)
```
必需字段:
- station: 站点名称
- hour: 时间(0-23)
- count: 流量计数
- longitude: 地铁站经度
- latitude: 地铁站纬度
- geometry: 栅格几何信息 (Polygon)

自动提取字段:
- grid_lon, grid_lat: 栅格中心坐标
- grid_area: 栅格面积
- grid_width, grid_height: 栅格尺寸
```

#### 2. OD数据 (.csv格式)
```
必需字段:
- o_rawname: 起点站名称
- d_rawname: 终点站名称  
- hour: 时间(0-23)
- trip: OD流量
- surface_distance: 站点间距离 (米)
- translate: 换乘次数
- time: 旅行时间 (分钟)
- wait_time: 等待时间 (分钟)
```

#### 3. 站点特征数据 (.csv格式)
```
必需字段:
- 站名: 站点名称
- [51个地理和社会经济特征列]
  包括: POI密度、土地利用、人口密度、商业指数等
```

### 输出结果格式

#### 1. 基础流量预测
```python
{
    'in_flow_test': {
        'predictions': np.array,  # [n_samples, n_stations]
        'targets': np.array,
        'mae': float,
        'rmse': float,
        'stations': list  # 站点名称列表
    }
}
```

#### 2. OD流量预测
```python
{
    'od_flow': [
        {
            'origin': str,
            'destination': str,
            'predicted_flow': float,
            'features': {
                'distance': float,
                'transfers': float,
                'travel_time': float,
                'wait_time': float,
                'similarity': float  # 节点嵌入相似度
            }
        }
    ]
}
```

#### 3. 栅格-站点预测
```python
{
    'grid_to_station': [
        {
            'grid_id': str,
            'station': str,
            'predicted_flow': float,
            'distance': float
        }
    ]
}
```

## 🚀 快速开始

### 环境要求

- **Python**: 3.8+
- **GPU**: 建议8GB+显存 (支持CPU训练)
- **内存**: 建议16GB+
- **存储**: 10GB+ (用于数据和模型)

### 安装步骤

#### 1. 环境检查
```bash
python check_config.py
```

#### 2. 安装依赖
```bash
# 基础科学计算库
pip install numpy pandas matplotlib scikit-learn tqdm

# 地理数据处理
pip install geopandas shapely fiona pyproj rasterio

# 深度学习框架 (选择一个)
# CPU版本
pip install torch torchvision torchaudio

# GPU版本 (CUDA 11.8)
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# 图神经网络
pip install torch-geometric
pip install torch-scatter torch-sparse torch-cluster torch-spline-conv
```

#### 3. 配置数据路径
编辑 `config.py`：
```python
BASE_DIR = r"你的数据目录路径"
```

### 运行选项

#### 快速测试 (推荐首次运行)
```bash
python main.py --mode train --epochs 2 --sample_ratio 0.05 --batch_size 4
```

#### 标准训练
```bash
python main.py --mode train --epochs 10 --sample_ratio 0.1
```

#### 完整训练
```bash
python main.py --mode train --sample_ratio 1.0
```

#### 仅预测
```bash
python main.py --mode eval
```

### 参数说明

| 参数 | 描述 | 默认值 | 建议值 |
|------|------|--------|--------|
| `--epochs` | 训练轮数 | 10 | 2-20 |
| `--sample_ratio` | 数据采样比例 | 0.1 | 0.05-1.0 |
| `--batch_size` | 批次大小 | 8 | 4-16 |
| `--lr` | 学习率 | 0.001 | 0.0001-0.01 |

## ⚙️ 配置说明

### 性能配置

```python
# config.py 关键配置
DEVICE = "cuda"              # 自动检测GPU
USE_FLOAT16 = True          # 启用Float16减少内存
BATCH_SIZE = 8              # 批次大小
SAMPLE_RATIO = 0.1          # 数据采样比例
SEQUENCE_LENGTH = 6         # 时间序列长度

# 模型配置  
HIDDEN_DIM = 64             # 隐藏层维度
NUM_LAYERS = 2              # GCN层数
DROPOUT = 0.3               # Dropout率
```

### 内存优化策略

1. **Float16精度**: 减少50%内存使用
2. **梯度检查点**: 降低训练时内存峰值
3. **批次大小调整**: 根据GPU内存动态调整
4. **定期缓存清理**: 防止内存累积
5. **数据采样**: 使用部分数据进行快速实验

## 🎯 核心算法

### 1. 图网络构建

```python
def build_hybrid_graph(station_coords, od_data):
    """构建混合图网络"""
    # 1. 地理距离图
    geo_edges = build_distance_graph(station_coords, threshold=2000)
    
    # 2. K近邻图  
    knn_edges = build_knn_graph(station_coords, k=3)
    
    # 3. OD流量图
    od_edges = build_od_graph(od_data, min_flow=10)
    
    # 4. 边权重融合
    edge_weights = combine_edge_weights(geo_edges, knn_edges, od_edges)
    
    return edge_index, edge_weights
```

### 2. 时空特征工程

```python
def create_spatiotemporal_features():
    """创建时空特征"""
    # 时间特征 (6维)
    time_features = [
        sin(2π * hour / 24),      # 小时周期性
        cos(2π * hour / 24),
        is_morning_peak,           # 早高峰标识
        is_evening_peak,           # 晚高峰标识  
        is_night,                  # 夜间标识
        is_weekend                 # 周末标识
    ]
    
    # 空间特征 (51维)
    spatial_features = [
        poi_density,               # POI密度
        land_use_features,         # 土地利用
        population_density,        # 人口密度
        commercial_index,          # 商业指数
        # ... 其他47个特征
    ]
    
    return time_features, spatial_features
```

### 3. OD流量预测算法

```python
def predict_od_flow(origin_embedding, dest_embedding, od_features):
    """OD流量预测"""
    # 1. 节点嵌入相似度
    similarity = cosine_similarity(origin_embedding, dest_embedding)
    
    # 2. 特征加权
    distance_factor = 1.0 / (1.0 + distance * 0.001)
    transfer_factor = 1.0 / (1.0 + transfers * 0.5)  
    time_factor = 1.0 / (1.0 + travel_time * 0.01)
    
    # 3. 流量预测
    predicted_flow = similarity * 100 * distance_factor * transfer_factor * time_factor
    
    return max(1.0, predicted_flow)
```

## 📈 预期输出

### 结果目录结构
```
results_20241201_143022/
├── in_flow_test_predictions.npy         # 进站预测结果
├── in_flow_test_targets.npy            # 进站真实值
├── out_flow_test_predictions.npy       # 出站预测结果  
├── out_flow_test_targets.npy           # 出站真实值
├── od_flow_predictions.json            # OD流量预测
├── grid_to_station_predictions.csv     # 栅格到站点预测
├── station_to_grid_predictions.csv     # 站点到栅格预测
├── new_line_impact.json               # 新线路影响分析
├── prediction_report.json             # 综合预测报告
└── training_history.png               # 训练历史图
```

### 性能指标

| 任务 | 评估指标 | 预期性能 |
|------|----------|----------|
| 进站流量预测 | MAE | < 50 |
| 出站流量预测 | MAE | < 50 |
| OD流量预测 | 相对误差 | < 30% |
| 栅格-站点预测 | 覆盖率 | > 80% |

## 🔧 重构指南

### 代码模块化设计

```
├── config.py              # 配置管理
├── data_loader.py          # 数据加载和预处理
├── graph_builder.py        # 图网络构建
├── gcn_model.py           # GCN模型定义
├── trainer.py             # 训练流程管理
├── predictor.py           # 预测和推理
└── main.py                # 主程序入口
```

### 关键设计模式

1. **配置驱动**: 所有参数通过Config类管理
2. **模块解耦**: 各模块独立，通过接口交互
3. **错误恢复**: 完善的异常处理和资源清理
4. **内存管理**: 自动GPU缓存清理
5. **批处理优化**: 大数据自动分批处理

### 扩展指南

#### 1. 添加新的预测任务
```python
# 在gcn_model.py中添加输出层
self.output_layers['new_task'] = nn.Sequential(
    nn.Linear(hidden_dim, hidden_dim//2),
    nn.ReLU(),
    nn.Linear(hidden_dim//2, 1)
)

# 在trainer.py中添加训练逻辑
def train_new_task(self, model, data, task='new_task'):
    # 实现新任务的训练逻辑
    pass
```

#### 2. 添加新的特征类型
```python
# 在data_loader.py中扩展特征提取
def extract_new_features(self, data):
    # 实现新特征提取逻辑
    return new_features

# 在config.py中更新特征维度
NEW_FEATURE_DIM = 10
```

#### 3. 自定义图构建方法
```python
# 在graph_builder.py中添加新方法
def build_custom_graph(self, nodes, edges, method='custom'):
    # 实现自定义图构建逻辑
    return edge_index, edge_attr
```

## 🐛 故障排除

### 常见问题

#### 1. GPU内存不足
```
错误: CUDA out of memory
解决: 降低batch_size或启用CPU模式
```

#### 2. 设备不匹配
```
错误: Expected all tensors to be on the same device
解决: 检查模型forward方法中的设备管理
```

#### 3. 维度不匹配
```
错误: not enough values to unpack
解决: 检查数据预处理中的维度变换
```

#### 4. 数据路径错误
```
错误: FileNotFoundError
解决: 检查config.py中的BASE_DIR设置
```

### 调试技巧

1. **使用快速测试模式**: `--sample_ratio 0.01`
2. **启用详细日志**: 查看数据处理过程
3. **检查张量形状**: 在关键节点打印tensor.shape
4. **CPU模式调试**: 设置`DEVICE="cpu"`进行调试

## 📚 参考文献

1. Graph Convolutional Networks (Kipf & Welling, 2017)
2. Attention Is All You Need (Vaswani et al., 2017)
3. Deep Learning for Spatio-Temporal Data Mining (Wang et al., 2019)
4. Urban Flow Prediction from Spatiotemporal Data (Zhang et al., 2017)

## 🤝 开发团队

- **数据科学**: 地铁流量数据分析与特征工程
- **算法研发**: GCN模型设计与优化  
- **系统工程**: 系统架构设计与性能优化
- **产品应用**: 业务场景对接与效果评估

## 📄 许可证

本项目仅供学术研究使用。

---

## 🎉 版本历史

### v2.0.0 (当前版本)
- ✅ 修复设备不匹配错误
- ✅ 实现完整OD流量预测
- ✅ 添加栅格地理特征提取
- ✅ 优化内存使用和GPU适配
- ✅ 完善错误处理和监控

### v1.0.0 
- 基础GCN流量预测功能
- 时空特征工程
- 图网络构建

---

**📞 技术支持**: 如遇问题请查看故障排除章节或提交Issue。

**🚀 快速上手**: 建议先运行`python check_config.py`检查环境，然后使用快速测试模式验证系统功能。 