"""
T-GCN模型定义
时序图卷积网络的PyTorch实现
"""
import torch
import torch.nn as nn

class TGCN(nn.Module):
    def __init__(self, input_dim, hidden_dim, output_dim=1, num_nodes=None):
        super(TGCN, self).__init__()
        
        self.hidden_dim = hidden_dim
        self.num_nodes = num_nodes
        
        # GCN权重
        self.weight = nn.Parameter(torch.FloatTensor(input_dim, hidden_dim))
        self.bias = nn.Parameter(torch.FloatTensor(hidden_dim))
        
        # GRU单元参数
        self.ru_gate_weight = nn.Parameter(torch.FloatTensor(hidden_dim + hidden_dim, hidden_dim * 2))
        self.ru_gate_bias = nn.Parameter(torch.FloatTensor(hidden_dim * 2))
        
        self.candidate_weight = nn.Parameter(torch.FloatTensor(hidden_dim + hidden_dim, hidden_dim))
        self.candidate_bias = nn.Parameter(torch.FloatTensor(hidden_dim))
        
        # 输出层
        self.output_layer = nn.Linear(hidden_dim, output_dim)
        
        # 初始化参数
        self.reset_parameters()
    
    def reset_parameters(self):
        """初始化模型参数"""
        nn.init.xavier_uniform_(self.weight)
        nn.init.zeros_(self.bias)
        
        nn.init.xavier_uniform_(self.ru_gate_weight)
        nn.init.zeros_(self.ru_gate_bias)
        
        nn.init.xavier_uniform_(self.candidate_weight)
        nn.init.zeros_(self.candidate_bias)
    
    def gcn_operation(self, x, adj):
        """GCN操作"""
        batch_size, num_nodes, input_dim = x.shape
        
        # 线性变换
        x = x.reshape(-1, input_dim)
        x = torch.matmul(x, self.weight) + self.bias
        
        # 重塑以应用邻接矩阵
        x = x.reshape(batch_size, num_nodes, self.hidden_dim)
        
        # 图卷积操作
        output = torch.zeros_like(x)
        
        # 对每个批次应用图卷积
        for b in range(batch_size):
            adj_indices = adj[0], adj[1]
            out_nodes, in_nodes = adj_indices
            
            # 收集邻居节点特征
            for i in range(adj.shape[1]):
                out_node = out_nodes[i].item()
                in_node = in_nodes[i].item()
                output[b, out_node] += x[b, in_node]
        
        return output
    
    def gru_operation(self, x, h):
        """GRU操作"""
        batch_size, num_nodes, _ = x.shape
        
        # 合并输入和隐藏状态
        combined = torch.cat([x, h], dim=2)
        combined = combined.reshape(-1, self.hidden_dim * 2)
        
        # 计算重置门和更新门
        ru_gates = torch.matmul(combined, self.ru_gate_weight) + self.ru_gate_bias
        ru_gates = ru_gates.reshape(batch_size, num_nodes, self.hidden_dim * 2)
        
        r, u = torch.split(torch.sigmoid(ru_gates), self.hidden_dim, dim=2)
        
        # 计算候选隐藏状态
        combined_r = torch.cat([x, r * h], dim=2)
        combined_r = combined_r.reshape(-1, self.hidden_dim * 2)
        
        c = torch.matmul(combined_r, self.candidate_weight) + self.candidate_bias
        c = c.reshape(batch_size, num_nodes, self.hidden_dim)
        c = torch.tanh(c)
        
        # 更新隐藏状态
        new_h = u * h + (1 - u) * c
        
        return new_h
    
    def forward(self, x, adj):
        """
        前向传播
        
        Args:
            x: [batch_size, time_steps, num_nodes, input_dim]
            adj: 稀疏邻接矩阵的边索引
            
        Returns:
            output: [batch_size, num_nodes]
        """
        batch_size, time_steps, num_nodes, input_dim = x.shape
        
        # 初始化隐藏状态
        h = torch.zeros(batch_size, num_nodes, self.hidden_dim, device=x.device)
        
        # 对每个时间步进行处理
        for t in range(time_steps):
            # 获取当前时间步的输入
            current_input = x[:, t]
            
            # 执行GCN操作
            gcn_out = self.gcn_operation(current_input, adj)
            
            # 执行GRU操作
            h = self.gru_operation(gcn_out, h)
        
        # 输出层
        output = self.output_layer(h)
        
        return output.squeeze(-1)  # [batch_size, num_nodes]



