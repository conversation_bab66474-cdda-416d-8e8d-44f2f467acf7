import os
import torch
import torch.nn as nn
import torch.optim as optim
import pandas as pd
import geopandas as gpd
import numpy as np
from torch.nn import TransformerEncoder, TransformerEncoderLayer
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import mean_absolute_error
from tqdm import tqdm
import shap

# 设置随机种子和设备
torch.manual_seed(42)
np.random.seed(42)
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"Using device: {device}")

# 数据加载
def load_data(in_shp, out_shp, od_csv, features_csv):
    in_gdf = gpd.read_file(in_shp)
    out_gdf = gpd.read_file(out_shp)
    od_df = pd.read_csv(od_csv)
    features_df = pd.read_csv(features_csv)
    print("Data loaded successfully.")
    return in_gdf, out_gdf, od_df, features_df

# 数据预处理
def preprocess_data(in_gdf, out_gdf, od_df, features_df):
    in_gdf = in_gdf[~in_gdf['hour'].isin([0, 1, 2, 3, 4])].copy()
    out_gdf = out_gdf[~out_gdf['hour'].isin([0, 1, 2, 3, 4])].copy()
    od_df = od_df[~od_df['hour'].isin([0, 1, 2, 3, 4])].copy()
    features_df = features_df.copy()
    
    all_stations = pd.concat([
        in_gdf['station'], out_gdf['station'],
        od_df['o_rawname'], od_df['d_rawname'],
        features_df['站名']
    ]).dropna().unique()
    
    station_encoder = LabelEncoder()
    station_encoder.fit(all_stations)
    
    in_gdf.loc[:, 'station_id'] = station_encoder.transform(in_gdf['station'])
    out_gdf.loc[:, 'station_id'] = station_encoder.transform(out_gdf['station'])
    od_df.loc[:, 'o_station_id'] = station_encoder.transform(od_df['o_rawname'])
    od_df.loc[:, 'd_station_id'] = station_encoder.transform(od_df['d_rawname'])
    
    scaler = StandardScaler()
    od_features = ['surface_distance', 'translate', 'time', 'wait_time']
    od_df.loc[:, od_features] = scaler.fit_transform(od_df[od_features])
    
    feature_cols = [col for col in features_df.columns if col != '站名']
    features_df.loc[:, feature_cols] = scaler.fit_transform(features_df[feature_cols])
    features_df.loc[:, 'station_id'] = features_df['站名'].apply(
        lambda x: station_encoder.transform([x])[0] if x in station_encoder.classes_ else -1
    )
    
    def encode_hour(hour):
        hour = hour % 24
        sin = np.sin(2 * np.pi * hour / 24)
        cos = np.cos(2 * np.pi * hour / 24)
        return sin, cos
    
    for df in [in_gdf, out_gdf, od_df]:
        df.loc[:, ['hour_sin', 'hour_cos']] = pd.DataFrame(
            df['hour'].apply(encode_hour).tolist(), index=df.index, columns=['hour_sin', 'hour_cos']
        )
    
    return in_gdf, out_gdf, od_df, features_df, station_encoder, feature_cols

# 数据集
class TrafficDataset(torch.utils.data.Dataset):
    def __init__(self, in_gdf, out_gdf, od_df, features_df, feature_cols):
        self.in_gdf = in_gdf
        self.out_gdf = out_gdf
        self.od_df = od_df
        self.features_df = features_df
        self.feature_cols = feature_cols
        
    def __len__(self):
        return len(self.in_gdf) + len(self.out_gdf) + len(self.od_df)
    
    def __getitem__(self, idx):
        if idx < len(self.in_gdf):
            row = self.in_gdf.iloc[idx]
            station_id = row['station_id']
            geo_features = self.features_df[self.features_df['station_id'] == station_id][self.feature_cols].values
            geo_features = geo_features[0] if len(geo_features) > 0 else np.zeros(len(self.feature_cols))
            return {
                'type': 'in',
                'station_id': torch.tensor(station_id, dtype=torch.long),
                'hour': torch.tensor([row['hour_sin'], row['hour_cos']], dtype=torch.float),
                'geo_features': torch.tensor(geo_features, dtype=torch.float),
                'count': torch.tensor(row['count'], dtype=torch.float)
            }
        elif idx < len(self.in_gdf) + len(self.out_gdf):
            row = self.out_gdf.iloc[idx - len(self.in_gdf)]
            station_id = row['station_id']
            geo_features = self.features_df[self.features_df['station_id'] == station_id][self.feature_cols].values
            geo_features = geo_features[0] if len(geo_features) > 0 else np.zeros(len(self.feature_cols))
            return {
                'type': 'out',
                'station_id': torch.tensor(station_id, dtype=torch.long),
                'hour': torch.tensor([row['hour_sin'], row['hour_cos']], dtype=torch.float),
                'geo_features': torch.tensor(geo_features, dtype=torch.float),
                'count': torch.tensor(row['count'], dtype=torch.float)
            }
        else:
            row = self.od_df.iloc[idx - len(self.in_gdf) - len(self.out_gdf)]
            o_station_id = row['o_station_id']
            d_station_id = row['d_station_id']
            o_geo = self.features_df[self.features_df['station_id'] == o_station_id][self.feature_cols].values
            d_geo = self.features_df[self.features_df['station_id'] == d_station_id][self.feature_cols].values
            o_geo = o_geo[0] if len(o_geo) > 0 else np.zeros(len(self.feature_cols))
            d_geo = d_geo[0] if len(d_geo) > 0 else np.zeros(len(self.feature_cols))
            return {
                'type': 'od',
                'o_station_id': torch.tensor(o_station_id, dtype=torch.long),
                'd_station_id': torch.tensor(d_station_id, dtype=torch.long),
                'hour': torch.tensor([row['hour_sin'], row['hour_cos']], dtype=torch.float),
                'o_geo_features': torch.tensor(o_geo, dtype=torch.float),
                'd_geo_features': torch.tensor(d_geo, dtype=torch.float),
                'od_features': torch.tensor([
                    row['surface_distance'], row['translate'], row['time'], row['wait_time']
                ], dtype=torch.float),
                'trip': torch.tensor(row['trip'], dtype=torch.float)
            }

def collate_fn(batch):
    in_batch = [x for x in batch if x['type'] == 'in']
    out_batch = [x for x in batch if x['type'] == 'out']
    od_batch = [x for x in batch if x['type'] == 'od']
    return in_batch, out_batch, od_batch

# 自定义损失函数
def weighted_mse_loss(pred, target):
    weights = torch.where(target > 0, 2.0, 1.0)  # 非零值权重更高
    return torch.mean(weights * (pred - target) ** 2)

# 模型定义
class TrafficModel(nn.Module):
    def __init__(self, num_stations, geo_feature_dim, station_dim=64, time_dim=2, hidden_dim=128):
        super().__init__()
        self.num_stations = num_stations
        self.station_dim = station_dim
        self.hidden_dim = hidden_dim
        self.time_dim = time_dim
        
        self.station_embed = nn.Embedding(num_stations, station_dim)
        self.geo_embed = nn.Linear(geo_feature_dim, hidden_dim)
        self.time_embed = nn.Linear(time_dim, time_dim)
        
        self.spatial_encoder = nn.Sequential(
            nn.Linear(station_dim + hidden_dim + time_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        transformer_layer = TransformerEncoderLayer(
            d_model=hidden_dim, nhead=8, batch_first=True
        )
        self.transformer = TransformerEncoder(transformer_layer, num_layers=4)
        self.fc_grid = nn.Linear(hidden_dim, 1)
        
        self.od_dnn = nn.Sequential(
            nn.Linear(station_dim*2 + hidden_dim*2 + 4 + time_dim, 512),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, hidden_dim)
        )
        self.od_attention = nn.MultiheadAttention(embed_dim=hidden_dim, num_heads=8)
        self.fc_od = nn.Linear(hidden_dim, 1)
        
    def forward(self, in_batch, out_batch, od_batch):
        count_pred_in, count_pred_out, trip_pred = [], [], []
        
        # 处理 in 和 out 数据
        if in_batch:
            station_ids = torch.tensor([x['station_id'] for x in in_batch], dtype=torch.long, device=device)
            hours = torch.stack([x['hour'] for x in in_batch]).to(device)
            geo_features = torch.stack([x['geo_features'] for x in in_batch]).to(device)
            
            station_emb = self.station_embed(station_ids)
            geo_emb = self.geo_embed(geo_features)
            time_emb = self.time_embed(hours)
            batch_features = torch.cat([station_emb, geo_emb, time_emb], dim=-1)
            spatial_out = self.spatial_encoder(batch_features)
            transformer_out = self.transformer(spatial_out.unsqueeze(1)).squeeze(1)
            count_pred_in = torch.relu(self.fc_grid(transformer_out)).squeeze(-1)
        
        if out_batch:
            station_ids = torch.tensor([x['station_id'] for x in out_batch], dtype=torch.long, device=device)
            hours = torch.stack([x['hour'] for x in out_batch]).to(device)
            geo_features = torch.stack([x['geo_features'] for x in out_batch]).to(device)
            
            station_emb = self.station_embed(station_ids)
            geo_emb = self.geo_embed(geo_features)
            time_emb = self.time_embed(hours)
            batch_features = torch.cat([station_emb, geo_emb, time_emb], dim=-1)
            spatial_out = self.spatial_encoder(batch_features)
            transformer_out = self.transformer(spatial_out.unsqueeze(1)).squeeze(1)
            count_pred_out = torch.relu(self.fc_grid(transformer_out)).squeeze(-1)
        
        if od_batch:
            o_station_ids = torch.tensor([x['o_station_id'] for x in od_batch], dtype=torch.long, device=device)
            d_station_ids = torch.tensor([x['d_station_id'] for x in od_batch], dtype=torch.long, device=device)
            hours = torch.stack([x['hour'] for x in od_batch]).to(device)
            o_geo = torch.stack([x['o_geo_features'] for x in od_batch]).to(device)
            d_geo = torch.stack([x['d_geo_features'] for x in od_batch]).to(device)
            od_features = torch.stack([x['od_features'] for x in od_batch]).to(device)
            
            o_emb = self.station_embed(o_station_ids)
            d_emb = self.station_embed(d_station_ids)
            o_geo_emb = self.geo_embed(o_geo)
            d_geo_emb = self.geo_embed(d_geo)
            time_emb = self.time_embed(hours)
            
            combined = torch.cat([o_emb, d_emb, o_geo_emb, d_geo_emb, od_features, time_emb], dim=-1)
            dnn_out = self.od_dnn(combined)
            attn_out, _ = self.od_attention(dnn_out.unsqueeze(0), dnn_out.unsqueeze(0), dnn_out.unsqueeze(0))
            trip_pred = torch.relu(self.fc_od(attn_out.squeeze(0))).squeeze(-1)
        
        return count_pred_in, count_pred_out, trip_pred

# 训练和评估
def train_model(model, train_loader, val_loader, epochs=3):
    optimizer = optim.AdamW(model.parameters(), lr=1e-3, weight_decay=1e-5)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=2, factor=0.5)
    best_mae = float('inf')
    
    for epoch in range(epochs):
        model.train()
        total_loss = 0
        for in_batch, out_batch, od_batch in tqdm(train_loader, desc=f"Epoch {epoch+1}"):
            optimizer.zero_grad()
            
            count_pred_in, count_pred_out, trip_pred = model(in_batch, out_batch, od_batch)
            
            loss = 0
            if in_batch:
                count_true_in = torch.stack([x['count'] for x in in_batch]).to(device)
                loss += weighted_mse_loss(count_pred_in, count_true_in)
            if out_batch:
                count_true_out = torch.stack([x['count'] for x in out_batch]).to(device)
                loss += weighted_mse_loss(count_pred_out, count_true_out)
            if od_batch:
                trip_true = torch.stack([x['trip'] for x in od_batch]).to(device)
                loss += weighted_mse_loss(trip_pred, trip_true)
            
            if isinstance(loss, torch.Tensor):
                loss.backward()
                optimizer.step()
                total_loss += loss.item()
        
        mae = evaluate_model(model, val_loader)
        scheduler.step(mae)
        print(f"Epoch {epoch+1}, Loss: {total_loss/len(train_loader):.4f}, Val MAE: {mae:.4f}")
        
        if mae < best_mae:
            best_mae = mae
            torch.save(model.state_dict(), 'best_model.pth')
            print("Saved best model.")
        elif epoch > 2 and mae >= best_mae:  # Early stopping
            print("Early stopping triggered.")
            break

def evaluate_model(model, loader):
    model.eval()
    maes = []
    with torch.no_grad():
        for in_batch, out_batch, od_batch in loader:
            count_pred_in, count_pred_out, trip_pred = model(in_batch, out_batch, od_batch)
            
            if in_batch:
                count_true_in = torch.stack([x['count'] for x in in_batch]).cpu().numpy()
                pred = np.maximum(count_pred_in.cpu().numpy(), 0)
                maes.append(mean_absolute_error(count_true_in, pred))
            if out_batch:
                count_true_out = torch.stack([x['count'] for x in out_batch]).cpu().numpy()
                pred = np.maximum(count_pred_out.cpu().numpy(), 0)
                maes.append(mean_absolute_error(count_true_out, pred))
            if od_batch:
                trip_true = torch.stack([x['trip'] for x in od_batch]).cpu().numpy()
                pred = np.maximum(trip_pred.cpu().numpy(), 0)
                maes.append(mean_absolute_error(trip_true, pred))
    
    return np.mean(maes)

# SHAP 分析
def shap_analysis(model, test_loader, station_encoder):
    model.eval()
    background_data = []
    test_data = []
    
    for in_batch, out_batch, od_batch in test_loader:
        if od_batch:
            o_station_ids = torch.tensor([x['o_station_id'] for x in od_batch], dtype=torch.long, device=device)
            d_station_ids = torch.tensor([x['d_station_id'] for x in od_batch], dtype=torch.long, device=device)
            hours = torch.stack([x['hour'] for x in od_batch]).to(device)
            o_geo = torch.stack([x['o_geo_features'] for x in od_batch]).to(device)
            d_geo = torch.stack([x['d_geo_features'] for x in od_batch]).to(device)
            od_features = torch.stack([x['od_features'] for x in od_batch]).to(device)
            combined = torch.cat([model.station_embed(o_station_ids), model.station_embed(d_station_ids),
                                  model.geo_embed(o_geo), model.geo_embed(d_geo), od_features, model.time_embed(hours)], dim=-1)
            if len(background_data) < 100:  # 限制背景数据量
                background_data.append(combined.cpu().detach().numpy())
            test_data.append(combined.cpu().detach().numpy())
    
    background = np.vstack(background_data[:100])
    test_samples = np.vstack(test_data)
    
    def predict_fn(inputs):
        inputs = torch.tensor(inputs, dtype=torch.float, device=device)
        dnn_out = model.od_dnn(inputs)
        attn_out, _ = model.od_attention(dnn_out.unsqueeze(0), dnn_out.unsqueeze(0), dnn_out.unsqueeze(0))
        return model.fc_od(attn_out.squeeze(0)).detach().cpu().numpy().flatten()
    
    explainer = shap.KernelExplainer(predict_fn, background)
    shap_values = explainer.shap_values(test_samples[:10])  # 限制样本数以加快计算
    
    shap.summary_plot(shap_values, test_samples[:10], feature_names=[
        f"o_emb_{i}" for i in range(model.station_dim)] + [
        f"d_emb_{i}" for i in range(model.station_dim)] + [
        f"o_geo_{i}" for i in range(model.hidden_dim)] + [
        f"d_geo_{i}" for i in range(model.hidden_dim)] + [
        'surface_distance', 'translate', 'time', 'wait_time', 'hour_sin', 'hour_cos'
    ])
    print("SHAP analysis completed and summary plot generated.")

# 执行
in_shp = "C:\\Users\\<USER>\\Desktop\\接驳\\in_500.shp"
out_shp = "C:\\Users\\<USER>\\Desktop\\接驳\\out_500.shp"
od_csv = "C:\\Users\\<USER>\\Desktop\\接驳\\updated_北京市_subway_od_2024_modified3.csv"
features_csv = "C:\\Users\\<USER>\\Desktop\\接驳\\station_features_result.csv"

in_gdf, out_gdf, od_df, features_df = load_data(in_shp, out_shp, od_csv, features_csv)
in_gdf, out_gdf, od_df, features_df, station_encoder, feature_cols = preprocess_data(
    in_gdf, out_gdf, od_df, features_df
)

train_in = in_gdf.sample(frac=0.7, random_state=42)
val_in = in_gdf.drop(train_in.index).sample(frac=0.5, random_state=42)
test_in = in_gdf.drop(train_in.index).drop(val_in.index)

train_out = out_gdf.sample(frac=0.7, random_state=42)
val_out = out_gdf.drop(train_out.index).sample(frac=0.5, random_state=42)
test_out = out_gdf.drop(train_out.index).drop(val_out.index)

train_od = od_df.sample(frac=0.7, random_state=42)
val_od = od_df.drop(train_od.index).sample(frac=0.5, random_state=42)
test_od = od_df.drop(train_od.index).drop(val_od.index)

train_dataset = TrafficDataset(train_in, train_out, train_od, features_df, feature_cols)
val_dataset = TrafficDataset(val_in, val_out, val_od, features_df, feature_cols)
test_dataset = TrafficDataset(test_in, test_out, test_od, features_df, feature_cols)

train_loader = torch.utils.data.DataLoader(
    train_dataset, batch_size=64, shuffle=True, collate_fn=collate_fn
)
val_loader = torch.utils.data.DataLoader(
    val_dataset, batch_size=64, shuffle=False, collate_fn=collate_fn
)
test_loader = torch.utils.data.DataLoader(
    test_dataset, batch_size=64, shuffle=False, collate_fn=collate_fn
)

model = TrafficModel(
    num_stations=len(station_encoder.classes_),
    geo_feature_dim=len(feature_cols)
).to(device)

train_model(model, train_loader, val_loader)

model.load_state_dict(torch.load('best_model.pth'))
test_mae = evaluate_model(model, test_loader)
print(f"Test MAE: {test_mae:.4f}")

# 输出预测结果
model.eval()
in_preds, out_preds, od_preds = [], [], []
in_true, out_true, od_true = [], [], []

with torch.no_grad():
    for in_batch, out_batch, od_batch in test_loader:
        count_pred_in, count_pred_out, trip_pred = model(in_batch, out_batch, od_batch)
        
        if in_batch:
            preds = np.maximum(count_pred_in.cpu().numpy(), 0)
            trues = torch.stack([x['count'] for x in in_batch]).cpu().numpy()
            in_preds.extend(preds)
            in_true.extend(trues)
            for i, item in enumerate(in_batch):
                in_preds[-len(in_batch) + i] = {
                    'hour': item['hour'].cpu().numpy(),
                    'station_id': item['station_id'].cpu().numpy(),
                    'count_pred': preds[i],
                    'count_true': trues[i]
                }
        
        if out_batch:
            preds = np.maximum(count_pred_out.cpu().numpy(), 0)
            trues = torch.stack([x['count'] for x in out_batch]).cpu().numpy()
            out_preds.extend(preds)
            out_true.extend(trues)
            for i, item in enumerate(out_batch):
                out_preds[-len(out_batch) + i] = {
                    'hour': item['hour'].cpu().numpy(),
                    'station_id': item['station_id'].cpu().numpy(),
                    'count_pred': preds[i],
                    'count_true': trues[i]
                }
        
        if od_batch:
            preds = np.maximum(trip_pred.cpu().numpy(), 0)
            trues = torch.stack([x['trip'] for x in od_batch]).cpu().numpy()
            od_preds.extend(preds)
            od_true.extend(trues)
            for i, item in enumerate(od_batch):
                od_preds[-len(od_batch) + i] = {
                    'hour': item['hour'].cpu().numpy(),
                    'o_station_id': item['o_station_id'].cpu().numpy(),
                    'd_station_id': item['d_station_id'].cpu().numpy(),
                    'trip_pred': preds[i],
                    'trip_true': trues[i]
                }

# 保存预测结果
in_out_df = test_in.copy()
in_out_df['count_pred'] = [x['count_pred'] for x in in_preds]
in_out_df['count_true'] = [x['count_true'] for x in in_preds]
in_out_df.to_file('in_500_pred.shp')

out_out_df = test_out.copy()
out_out_df['count_pred'] = [x['count_pred'] for x in out_preds]
out_out_df['count_true'] = [x['count_true'] for x in out_preds]
out_out_df.to_file('out_500_pred.shp')

od_out_df = test_od.copy()
od_out_df['trip_pred'] = [x['trip_pred'] for x in od_preds]
od_out_df['trip_true'] = [x['trip_true'] for x in od_preds]
od_out_df.to_csv('od_pred.csv', index=False)

print("Prediction files saved: in_500_pred.shp, out_500_pred.shp, od_pred.csv")

# SHAP 分析
shap_analysis(model, test_loader, station_encoder)