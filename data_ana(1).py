import networkx as nx
import pandas as pd
from collections import defaultdict

# 1. 读取数据
def read_data(od_file, connection_file):
    # 读取OD数据
    od_data = pd.read_csv(od_file)
    # 读取路线连接数据
    connections = pd.read_csv(connection_file)
    return od_data, connections

# 2. 构建图
def build_graph(connections):
    G = nx.Graph()
    
    # 添加所有边，权重为2分钟（站间运行时间）
    for _, row in connections.iterrows():
        station1, station2 = row['station_1'], row['station_2']
        G.add_edge(station1, station2, weight=2)
        
    # 处理换乘站
    transfer_stations = defaultdict(set)
    for node in G.nodes():
        station_name = node.split('_')[0]
        line_number = node.split('_')[1]
        transfer_stations[station_name].add(line_number)
        
    # 添加换乘边，权重设为3分钟（换乘时间）
    for station, lines in transfer_stations.items():
        if len(lines) > 1:
            lines = list(lines)
            for i in range(len(lines)):
                for j in range(i+1, len(lines)):
                    station1 = f"{station}_{lines[i]}"
                    station2 = f"{station}_{lines[j]}"
                    G.add_edge(station1, station2, weight=2)
    
    return G, transfer_stations

# 3. 预计算所有站点对之间的最短路径
def precompute_shortest_paths(G):
    shortest_paths_dict = {}
    station_names = set(node.split('_')[0] for node in G.nodes())
    
    print("开始预计算最短路径...")
    total = len(station_names) * len(station_names)
    count = 0
    
    for o_station in station_names:
        for d_station in station_names:
            if o_station == d_station:
                continue
                
            o_nodes = [n for n in G.nodes() if n.startswith(f"{o_station}_")]
            d_nodes = [n for n in G.nodes() if n.startswith(f"{d_station}_")]
            
            all_shortest_paths = []
            min_length = float('inf')
            
            for o_node in o_nodes:
                for d_node in d_nodes:
                    try:
                        length = nx.shortest_path_length(G, o_node, d_node, weight='weight')
                        if length < min_length:
                            min_length = length
                            all_shortest_paths = [nx.shortest_path(G, o_node, d_node, weight='weight')]
                        elif length == min_length:
                            all_shortest_paths.append(nx.shortest_path(G, o_node, d_node, weight='weight'))
                    except nx.NetworkXNoPath:
                        continue
            
            if all_shortest_paths:
                shortest_paths_dict[(o_station, d_station)] = all_shortest_paths
            
            count += 1
            if count % 1000 == 0:
                print(f"进度: {count}/{total} ({count/total*100:.2f}%)")
    
    return shortest_paths_dict

# 4. 计算边上的累计流量
def calculate_edge_flows(od_data, shortest_paths_dict, G):
    # 创建24小时的字典，每小时存储边的流量
    hourly_edge_flows = {hour: defaultdict(float) for hour in range(24)}
    
    # 按小时处理OD数据
    for hour, hour_data in od_data.groupby('hour'):
        print(f"处理第 {hour} 小时的数据...")
        
        # 处理每个OD对
        for _, row in hour_data.iterrows():
            o_station = row['o_rawname']
            d_station = row['d_rawname']
            flow = row['trip']
            
            # 获取该OD对的所有最短路径
            paths = shortest_paths_dict.get((o_station, d_station), [])
            if not paths:
                continue
            
            # 将流量平均分配到每条最短路径
            flow_per_path = flow / len(paths)
            
            # 对每条最短路径
            for path in paths:
                # 处理路径中的每对相邻站点
                for i in range(len(path)-1):
                    edge = (path[i], path[i+1])  # 保持边的方向
                    hourly_edge_flows[hour][edge] += flow_per_path
    
    return hourly_edge_flows

# 5. 生成结果
def generate_edge_flow_results(hourly_edge_flows):
    results = []
    
    for hour, edge_flows in hourly_edge_flows.items():
        for (station_1, station_2), flow in edge_flows.items():
            # 提取站名和线路信息
            station1_name = station_1.split('_')[0]
            station2_name = station_2.split('_')[0]
            station1_line = station_1.split('_')[1]
            station2_line = station_2.split('_')[1]
            
            results.append({
                'hour': hour,
                'o_rawname': station1_name,
                'd_rawname': station2_name,
                'o_line': station1_line,    
                'd_line': station2_line,
                'trip': flow,
                'time': 2 if station1_line == station2_line else 3
            })
    
    return pd.DataFrame(results)

# 主函数
def main():
    # 文件路径
    od_file = r"C:\Users\<USER>\Desktop\接驳\updated北京市_subway_od_2024_modified3.csv"
    connection_file = r"C:\Users\<USER>\Desktop\接驳\station_connect_2023.csv"
    output_file = r"C:\Users\<USER>\Desktop\接驳\edge_flows.csv"
    
    print("1. 读取数据...")
    od_data, connections = read_data(od_file, connection_file)
    
    print("2. 构建地铁网络图...")
    G, transfer_stations = build_graph(connections)
    
    print("3. 预计算所有站点对之间的最短路径...")
    shortest_paths_dict = precompute_shortest_paths(G)
    
    print("4. 计算边上的累计流量...")
    hourly_edge_flows = calculate_edge_flows(od_data, shortest_paths_dict, G)
    
    print("5. 生成结果...")
    results = generate_edge_flow_results(hourly_edge_flows)
    
    print("6. 保存结果...")
    results.to_csv(output_file, index=False)
    
    # 输出统计信息
    print("\n处理完成！")
    print(f"总记录数：{len(results)}")
    print("\n每小时记录数：")
    print(results.groupby('hour').size())
    print("\n每小时总人流量：")
    print(results.groupby('hour')['trip'].sum())
    print("\n流量最大的10条边：")
    edge_total_flow = results.groupby(['o_rawname', 'd_rawname'])['trip'].sum()
    print(edge_total_flow.sort_values(ascending=False).head(10))

if __name__ == "__main__":
    main()

