"""
数据加载模块
负责从文件系统加载各种数据文件
"""
import pandas as pd
import geopandas as gpd
from config import Config

class DataLoader:
    def __init__(self, config=None):
        self.config = config or Config()
    
    def load_data(self):
        """
        加载所有数据文件
        
        Returns:
            tuple: (in_data, out_data, od_data, station_features)
        """
        print("读取数据...")
        
        # 读取地理数据
        in_data = gpd.read_file(self.config.IN_FILE)
        out_data = gpd.read_file(self.config.OUT_FILE)
        
        # 提取栅格中心点坐标
        in_data['longitude'] = in_data.geometry.centroid.x
        in_data['latitude'] = in_data.geometry.centroid.y
        out_data['longitude'] = out_data.geometry.centroid.x
        out_data['latitude'] = out_data.geometry.centroid.y
        
        # 读取CSV数据
        od_data = pd.read_csv(self.config.OD_FILE)
        station_features = pd.read_csv(self.config.FEATURES_FILE)
        
        print(f"进站数据形状: {in_data.shape}")
        print(f"出站数据形状: {out_data.shape}")
        print(f"OD数据形状: {od_data.shape}")
        print(f"站点特征数据形状: {station_features.shape}")
        
        return in_data, out_data, od_data, station_features



