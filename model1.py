


#conda activate traffic_env





import torch
import torch.nn as nn
import torch.optim as optim
import pandas as pd
import geopandas as gpd
import numpy as np
import dgl
from dgl.nn import GraphConv
from torch.nn import TransformerEncoder, TransformerEncoderLayer
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import mean_absolute_error
import os
from tqdm import tqdm

# Set random seed for reproducibility
torch.manual_seed(42)
np.random.seed(42)
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(device)
# 1. Data Loading and Preprocessing
def load_data(in_shp, out_shp, od_csv, features_csv):
    # Load shp files
    in_gdf = gpd.read_file(in_shp)
    out_gdf = gpd.read_file(out_shp)
    # Load csv files
    od_df = pd.read_csv(od_csv)
    features_df = pd.read_csv(features_csv)
    
    return in_gdf, out_gdf, od_df, features_df

def preprocess_data(in_gdf, out_gdf, od_df, features_df):
    # Filter hours 0-4
    in_gdf = in_gdf[~in_gdf['hour'].isin([0, 1, 2, 3, 4])]
    out_gdf = out_gdf[~out_gdf['hour'].isin([0, 1, 2, 3, 4])]
    od_df = od_df[~od_df['hour'].isin([0, 1, 2, 3, 4])]
    
    # Encode station names
    all_stations = pd.concat([
        in_gdf['station'], out_gdf['station'],
        od_df['o_rawname'], od_df['d_rawname']
    ]).unique()
    station_encoder = LabelEncoder()
    station_encoder.fit(all_stations)
    
    in_gdf['station_id'] = station_encoder.transform(in_gdf['station'])
    out_gdf['station_id'] = station_encoder.transform(out_gdf['station'])
    od_df['o_station_id'] = station_encoder.transform(od_df['o_rawname'])
    od_df['d_station_id'] = station_encoder.transform(od_df['d_rawname'])
    
    # Standardize numerical features
    scaler = StandardScaler()
    od_features = ['surface_distance', 'translate', 'time', 'wait_time']
    od_df[od_features] = scaler.fit_transform(od_df[od_features])
    
    # Standardize station features
    feature_cols = [col for col in features_df.columns if col != '站名']
    features_df[feature_cols] = scaler.fit_transform(features_df[feature_cols])
    features_df['station_id'] = station_encoder.transform(features_df['站名'])
    
    # Time encoding (sinusoidal for hour)
    def encode_hour(hour):
        hour = hour % 24
        sin = np.sin(2 * np.pi * hour / 24)
        cos = np.cos(2 * np.pi * hour / 24)
        return sin, cos
    
    in_gdf['hour_sin'], in_gdf['hour_cos'] = zip(*in_gdf['hour'].apply(encode_hour))
    out_gdf['hour_sin'], out_gdf['hour_cos'] = zip(*out_gdf['hour'].apply(encode_hour))
    od_df['hour_sin'], od_df['hour_cos'] = zip(*od_df['hour'].apply(encode_hour))
    
    return in_gdf, out_gdf, od_df, features_df, station_encoder, feature_cols

# 2. Create Graph for GNN
def create_graph(in_gdf, out_gdf):
    # Simple graph: stations as nodes, edges based on OD data or proximity
    # For simplicity, assume fully connected graph for stations
    stations = pd.concat([in_gdf['station_id'], out_gdf['station_id']]).unique()
    num_nodes = len(stations)
    src, dst = [], []
    for i in range(num_nodes):
        for j in range(num_nodes):
            if i != j:
                src.append(i)
                dst.append(j)
    g = dgl.graph((src, dst), num_nodes=num_nodes).to(device)
    return g

# 3. Dataset and DataLoader
class TrafficDataset(torch.utils.data.Dataset):
    def __init__(self, in_gdf, out_gdf, od_df, features_df, feature_cols):
        self.in_gdf = in_gdf
        self.out_gdf = out_gdf
        self.od_df = od_df
        self.features_df = features_df
        self.feature_cols = feature_cols
        
    def __len__(self):
        return len(self.in_gdf) + len(self.out_gdf) + len(self.od_df)
    
    def __getitem__(self, idx):
        if idx < len(self.in_gdf):
            # Grid to station
            row = self.in_gdf.iloc[idx]
            station_id = row['station_id']
            geo_features = self.features_df[self.features_df['station_id'] == station_id][self.feature_cols].values
            if len(geo_features) == 0:
                geo_features = np.zeros(len(self.feature_cols))
            else:
                geo_features = geo_features[0]
            return {
                'type': 'in',
                'station_id': torch.tensor(station_id, dtype=torch.long),
                'hour': torch.tensor([row['hour_sin'], row['hour_cos']], dtype=torch.float),
                'geo_features': torch.tensor(geo_features, dtype=torch.float),
                'count': torch.tensor(row['count'], dtype=torch.float)
            }
        elif idx < len(self.in_gdf) + len(self.out_gdf):
            # Station to grid
            row = self.out_gdf.iloc[idx - len(self.in_gdf)]
            station_id = row['station_id']
            geo_features = self.features_df[self.features_df['station_id'] == station_id][self.feature_cols].values
            if len(geo_features) == 0:
                geo_features = np.zeros(len(self.feature_cols))
            else:
                geo_features = geo_features[0]
            return {
                'type': 'out',
                'station_id': torch.tensor(station_id, dtype=torch.long),
                'hour': torch.tensor([row['hour_sin'], row['hour_cos']], dtype=torch.float),
                'geo_features': torch.tensor(geo_features, dtype=torch.float),
                'count': torch.tensor(row['count'], dtype=torch.float)
            }
        else:
            # Station to station OD
            row = self.od_df.iloc[idx - len(self.in_gdf) - len(self.out_gdf)]
            o_station_id = row['o_station_id']
            d_station_id = row['d_station_id']
            o_geo = self.features_df[self.features_df['station_id'] == o_station_id][self.feature_cols].values
            d_geo = self.features_df[self.features_df['station_id'] == d_station_id][self.feature_cols].values
            o_geo = o_geo[0] if len(o_geo) > 0 else np.zeros(len(self.feature_cols))
            d_geo = d_geo[0] if len(d_geo) > 0 else np.zeros(len(self.feature_cols))
            return {
                'type': 'od',
                'o_station_id': torch.tensor(o_station_id, dtype=torch.long),
                'd_station_id': torch.tensor(d_station_id, dtype=torch.long),
                'hour': torch.tensor([row['hour_sin'], row['hour_cos']], dtype=torch.float),
                'o_geo_features': torch.tensor(o_geo, dtype=torch.float),
                'd_geo_features': torch.tensor(d_geo, dtype=torch.float),
                'od_features': torch.tensor([
                    row['surface_distance'], row['translate'], row['time'], row['wait_time']
                ], dtype=torch.float),
                'trip': torch.tensor(row['trip'], dtype=torch.float)
            }

def collate_fn(batch):
    in_batch = [x for x in batch if x['type'] == 'in']
    out_batch = [x for x in batch if x['type'] == 'out']
    od_batch = [x for x in batch if x['type'] == 'od']
    return in_batch, out_batch, od_batch

# 4. Model Definition
class TrafficModel(nn.Module):
    def __init__(self, num_stations, geo_feature_dim, station_dim=64, time_dim=2, hidden_dim=128):
        super().__init__()
        # Shared embeddings
        self.station_embed = nn.Embedding(num_stations, station_dim)
        self.geo_embed = nn.Linear(geo_feature_dim, hidden_dim)
        self.time_embed = nn.Linear(time_dim, time_dim)
        
        # Sub-architecture 1: GNN + Transformer for grid-station/station-grid
        self.gnn = GraphConv(station_dim + hidden_dim + time_dim, hidden_dim)
        transformer_layer = TransformerEncoderLayer(d_model=hidden_dim, nhead=8, batch_first=True)
        self.transformer = TransformerEncoder(transformer_layer, num_layers=4)
        self.fc_grid = nn.Linear(hidden_dim, 1)
        
        # Sub-architecture 2: DNN + Attention for station-station OD
        self.dnn = nn.Sequential(
            nn.Linear(station_dim*2 + hidden_dim*2 + 4 + time_dim, 512),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, hidden_dim)
        )
        self.attention = nn.MultiheadAttention(embed_dim=hidden_dim, num_heads=8)
        self.fc_od = nn.Linear(hidden_dim, 1)
        
    def forward(self, in_batch, out_batch, od_batch, graph):
        # Grid-station/station-grid
        count_pred_in, count_pred_out = [], []
        if in_batch:
            station_ids = torch.stack([x['station_id'] for x in in_batch]).to(device)
            hours = torch.stack([x['hour'] for x in in_batch]).to(device)
            geo_features = torch.stack([x['geo_features'] for x in in_batch]).to(device)
            
            station_emb = self.station_embed(station_ids)
            geo_emb = self.geo_embed(geo_features)
            time_emb = self.time_embed(hours)
            node_features = torch.cat([station_emb, geo_emb, time_emb], dim=-1)
            
            gnn_out = self.gnn(graph, node_features)
            transformer_out = self.transformer(gnn_out.unsqueeze(0)).squeeze(0)
            count_pred_in = torch.relu(self.fc_grid(transformer_out))
        
        if out_batch:
            station_ids = torch.stack([x['station_id'] for x in out_batch]).to(device)
            hours = torch.stack([x['hour'] for x in out_batch]).to(device)
            geo_features = torch.stack([x['geo_features'] for x in out_batch]).to(device)
            
            station_emb = self.station_embed(station_ids)
            geo_emb = self.geo_embed(geo_features)
            time_emb = self.time_embed(hours)
            node_features = torch.cat([station_emb, geo_emb, time_emb], dim=-1)
            
            gnn_out = self.gnn(graph, node_features)
            transformer_out = self.transformer(gnn_out.unsqueeze(0)).squeeze(0)
            count_pred_out = torch.relu(self.fc_grid(transformer_out))
        
        # Station-station OD
        trip_pred = []
        if od_batch:
            o_station_ids = torch.stack([x['o_station_id'] for x in od_batch]).to(device)
            d_station_ids = torch.stack([x['d_station_id'] for x in od_batch]).to(device)
            hours = torch.stack([x['hour'] for x in od_batch]).to(device)
            o_geo = torch.stack([x['o_geo_features'] for x in od_batch]).to(device)
            d_geo = torch.stack([x['d_geo_features'] for x in od_batch]).to(device)
            od_features = torch.stack([x['od_features'] for x in od_batch]).to(device)
            
            o_emb = self.station_embed(o_station_ids)
            d_emb = self.station_embed(d_station_ids)
            o_geo_emb = self.geo_embed(o_geo)
            d_geo_emb = self.geo_embed(d_geo)
            time_emb = self.time_embed(hours)
            
            combined = torch.cat([o_emb, d_emb, o_geo_emb, d_geo_emb, od_features, time_emb], dim=-1)
            dnn_out = self.dnn(combined)
            attn_out, _ = self.attention(dnn_out.unsqueeze(0), dnn_out.unsqueeze(0), dnn_out.unsqueeze(0))
            trip_pred = torch.relu(self.fc_od(attn_out.squeeze(0)))
        
        return count_pred_in, count_pred_out, trip_pred

# 5. Training and Evaluation
def train_model(model, train_loader, val_loader, graph, epochs=100):
    optimizer = optim.AdamW(model.parameters(), lr=1e-3, weight_decay=1e-5)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=5, factor=0.5)
    best_mae = float('inf')
    
    for epoch in range(epochs):
        model.train()
        total_loss = 0
        for in_batch, out_batch, od_batch in tqdm(train_loader, desc=f"Epoch {epoch+1}"):
            optimizer.zero_grad()
            
            count_pred_in, count_pred_out, trip_pred = model(in_batch, out_batch, od_batch, graph)
            
            loss = 0
            if in_batch:
                count_true_in = torch.stack([x['count'] for x in in_batch]).to(device)
                loss += nn.MSELoss()(count_pred_in, count_true_in)
            if out_batch:
                count_true_out = torch.stack([x['count'] for x in out_batch]).to(device)
                loss += nn.MSELoss()(count_pred_out, count_true_out)
            if od_batch:
                trip_true = torch.stack([x['trip'] for x in od_batch]).to(device)
                loss += nn.MSELoss()(trip_pred, trip_true)
            
            if isinstance(loss, torch.Tensor):
                loss.backward()
                optimizer.step()
                total_loss += loss.item()
        
        # Validation
        mae = evaluate_model(model, val_loader, graph)
        scheduler.step(mae)
        print(f"Epoch {epoch+1}, Loss: {total_loss/len(train_loader):.4f}, Val MAE: {mae:.4f}")
        
        if mae < best_mae:
            best_mae = mae
            torch.save(model.state_dict(), 'best_model.pth')

def evaluate_model(model, loader, graph):
    model.eval()
    maes = []
    with torch.no_grad():
        for in_batch, out_batch, od_batch in loader:
            count_pred_in, count_pred_out, trip_pred = model(in_batch, out_batch, od_batch, graph)
            
            if in_batch:
                count_true_in = torch.stack([x['count'] for x in in_batch]).cpu().numpy()
                pred = np.maximum(count_pred_in.cpu().numpy(), 0)
                maes.append(mean_absolute_error(count_true_in, pred))
            if out_batch:
                count_true_out = torch.stack([x['count'] for x in out_batch]).cpu().numpy()
                pred = np.maximum(count_pred_out.cpu().numpy(), 0)
                maes.append(mean_absolute_error(count_true_out, pred))
            if od_batch:
                trip_true = torch.stack([x['trip'] for x in od_batch]).cpu().numpy()
                pred = np.maximum(trip_pred.cpu().numpy(), 0)
                maes.append(mean_absolute_error(trip_true, pred))
    
    return np.mean(maes)

# 6. Main Execution
# def main():
# File paths (update as needed)
in_shp = "in_500.shp"
out_shp = "out_500.shp"
od_csv = "updated_北京市_subway_od_2024_modified3.csv"
features_csv = "station_features_result.csv"

# Load and preprocess data
in_gdf, out_gdf, od_df, features_df = load_data(in_shp, out_shp, od_csv, features_csv)
in_gdf, out_gdf, od_df, features_df, station_encoder, feature_cols = preprocess_data(
    in_gdf, out_gdf, od_df, features_df
)

# Split data (70% train, 15% val, 15% test)
train_in = in_gdf.sample(frac=0.7, random_state=42)
val_in = in_gdf.drop(train_in.index).sample(frac=0.5, random_state=42)
test_in = in_gdf.drop(train_in.index).drop(val_in.index)

train_out = out_gdf.sample(frac=0.7, random_state=42)
val_out = out_gdf.drop(train_out.index).sample(frac=0.5, random_state=42)
test_out = out_gdf.drop(train_out.index).drop(val_out.index)

train_od = od_df.sample(frac=0.7, random_state=42)
val_od = od_df.drop(train_od.index).sample(frac=0.5, random_state=42)
test_od = od_df.drop(train_od.index).drop(val_od.index)

# Create datasets
train_dataset = TrafficDataset(train_in, train_out, train_od, features_df, feature_cols)
val_dataset = TrafficDataset(val_in, val_out, val_od, features_df, feature_cols)
test_dataset = TrafficDataset(test_in, test_out, test_od, features_df, feature_cols)

# Create dataloaders
train_loader = torch.utils.data.DataLoader(
    train_dataset, batch_size=128, shuffle=True, collate_fn=collate_fn
)
val_loader = torch.utils.data.DataLoader(
    val_dataset, batch_size=128, shuffle=False, collate_fn=collate_fn
)
test_loader = torch.utils.data.DataLoader(
    test_dataset, batch_size=128, shuffle=False, collate_fn=collate_fn
)

# Create graph
graph = create_graph(in_gdf, out_gdf)

# Initialize model
model = TrafficModel(
    num_stations=len(station_encoder.classes_),
    geo_feature_dim=len(feature_cols)
).to(device)

# Train model
train_model(model, train_loader, val_loader, graph)

# Evaluate on test set
model.load_state_dict(torch.load('best_model.pth'))
test_mae = evaluate_model(model, test_loader, graph)
print(f"Test MAE: {test_mae:.4f}")

# Save predictions (optional)
model.eval()
predictions = []
with torch.no_grad():
    for in_batch, out_batch, od_batch in test_loader:
        count_pred_in, count_pred_out, trip_pred = model(in_batch, out_batch, od_batch, graph)
        if in_batch:
            predictions.extend(np.maximum(count_pred_in.cpu().numpy(), 0))
        if out_batch:
            predictions.extend(np.maximum(count_pred_out.cpu().numpy(), 0))
        if od_batch:
            predictions.extend(np.maximum(trip_pred.cpu().numpy(), 0))

# Save predictions to file (optional)
pd.DataFrame(predictions, columns=['prediction']).to_csv('predictions.csv', index=False)

# if __name__ == "__main__":
#     main()