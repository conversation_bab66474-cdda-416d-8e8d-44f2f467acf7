# -*- coding: utf-8 -*-
"""
Created on Wed Apr 16 07:09:38 2025

@author: <PERSON><PERSON><PERSON><PERSON>
"""

import geopandas as gpd
import pandas as pd
import uuid

# Load the shapefile and CSV
shp_file = '全市轨道吸引范围2024_站点.shp'
csv_file = 'od_station_metrics.csv'

gdf = gpd.read_file(shp_file)
cdf = pd.read_csv(csv_file)

# Define station name replacements
station_replacements = {
    'T2航站楼': '2号航站楼',
    'T3航站楼': '3号航站楼',
    '丰台': '丰台站',
    '亦庄火车': '亦庄火车站',
    '北京': '北京站',
    '北京南': '北京南站',
    '北京西': '北京西站',
    '黄村火车': '黄村火车站'
}

# Apply station name replacements to the shapefile
gdf['closest_st'] = gdf['closest_st'].replace(station_replacements)

# Identify stations in shp but not in csv
shp_stations = set(gdf['closest_st'].unique())
csv_stations = set(cdf['station'].unique())
missing_stations = shp_stations - csv_stations

# Print missing stations
print("Stations in shapefile but not in CSV:")
for station in missing_stations:
    print(station)

# Create a DataFrame for missing stations with default values
missing_data = pd.DataFrame({
    'station': list(missing_stations),
    'true_total_flow': 1000,
    'pred_total_flow': 1000,
    'MAE': 1,
    'RMSE': 5
})

# Append missing data to the CSV DataFrame
cdf = pd.concat([cdf, missing_data], ignore_index=True)

# Merge the shapefile with the CSV data
gdf_merged = gdf.merge(cdf, left_on='closest_st', right_on='station', how='left')

# Save the merged shapefile
output_shp = '全市轨道吸引范围2024_站点_merged.shp'
gdf_merged.to_file(output_shp)

print(f"Merged shapefile saved as {output_shp}")