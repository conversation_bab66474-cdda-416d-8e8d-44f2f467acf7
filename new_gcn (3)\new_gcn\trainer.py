import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from sklearn.model_selection import KFold
from sklearn.metrics import mean_absolute_error, mean_squared_error
import matplotlib.pyplot as plt
from tqdm import tqdm
import time
import copy
from config import Config
from gcn_model import create_model, ModelUtils

class EarlyStopping:
    """早停类"""
    def __init__(self, patience=7, min_delta=0, restore_best_weights=True):
        self.patience = patience
        self.min_delta = min_delta
        self.restore_best_weights = restore_best_weights
        self.best_loss = None
        self.counter = 0
        self.best_weights = None
        
    def __call__(self, val_loss, model):
        if self.best_loss is None:
            self.best_loss = val_loss
            self.save_checkpoint(model)
        elif val_loss < self.best_loss - self.min_delta:
            self.best_loss = val_loss
            self.counter = 0
            self.save_checkpoint(model)
        else:
            self.counter += 1
            
        if self.counter >= self.patience:
            if self.restore_best_weights:
                model.load_state_dict(self.best_weights)
            return True
        return False
    
    def save_checkpoint(self, model):
        self.best_weights = copy.deepcopy(model.state_dict())

class DataSplitter:
    """数据分割器，实现分层验证策略"""
    def __init__(self, config):
        self.config = config
    
    def temporal_split(self, data, time_column='hour'):
        """时间分割"""
        print("执行时间分割...")
        unique_times = sorted(data[time_column].unique())
        
        n_train = int(len(unique_times) * self.config.TRAIN_RATIO)
        n_val = int(len(unique_times) * self.config.VAL_RATIO)
        
        train_times = unique_times[:n_train]
        val_times = unique_times[n_train:n_train + n_val]
        test_times = unique_times[n_train + n_val:]
        
        train_data = data[data[time_column].isin(train_times)]
        val_data = data[data[time_column].isin(val_times)]
        test_data = data[data[time_column].isin(test_times)]
        
        print(f"时间分割结果: 训练={len(train_data)}, 验证={len(val_data)}, 测试={len(test_data)}")
        return train_data, val_data, test_data
    
    def spatial_split(self, data, station_column='station', stations=None):
        """空间分割"""
        print("执行空间分割...")
        if stations is None:
            stations = data[station_column].unique()
        
        np.random.shuffle(stations)
        n_train = int(len(stations) * self.config.TRAIN_RATIO)
        n_val = int(len(stations) * self.config.VAL_RATIO)
        
        train_stations = stations[:n_train]
        val_stations = stations[n_train:n_train + n_val]
        test_stations = stations[n_train + n_val:]
        
        train_data = data[data[station_column].isin(train_stations)]
        val_data = data[data[station_column].isin(val_stations)]
        test_data = data[data[station_column].isin(test_stations)]
        
        print(f"空间分割结果: 训练={len(train_data)}, 验证={len(val_data)}, 测试={len(test_data)}")
        return train_data, val_data, test_data, train_stations, val_stations, test_stations
    
    def leave_one_out_split(self, data, station_column='station'):
        """留一法验证"""
        print("执行留一法分割...")
        stations = data[station_column].unique()
        splits = []
        
        for test_station in stations:
            train_val_data = data[data[station_column] != test_station]
            test_data = data[data[station_column] == test_station]
            
            # 在训练验证集中进一步分割
            remaining_stations = train_val_data[station_column].unique()
            n_val = max(1, int(len(remaining_stations) * 0.2))
            val_stations = np.random.choice(remaining_stations, n_val, replace=False)
            
            val_data = train_val_data[train_val_data[station_column].isin(val_stations)]
            train_data = train_val_data[~train_val_data[station_column].isin(val_stations)]
            
            splits.append({
                'train': train_data,
                'val': val_data,
                'test': test_data,
                'test_station': test_station
            })
        
        print(f"留一法分割创建了 {len(splits)} 个验证折")
        return splits

class GCNTrainer:
    """GCN训练器"""
    def __init__(self, config):
        self.config = config
        self.device = torch.device(config.DEVICE)
        self.splitter = DataSplitter(config)
        self.history = {
            'train_loss': [],
            'val_loss': [],
            'train_mae': [],
            'val_mae': []
        }
        
    def prepare_data_tensors(self, flow_data, time_features, spatial_features, 
                           edge_index, edge_attr, stations):
        """准备数据张量（增强版本，包含数据验证）"""
        print("🔍 正在验证和准备数据张量...")
        
        # ✅ 数据验证和清理
        def validate_and_clean_data(data, name):
            """验证并清理数据"""
            print(f"  验证 {name}: 形状={data.shape}, 类型={data.dtype}")
            
            # 检查无效值
            if hasattr(data, 'isnan'):
                nan_count = np.isnan(data).sum()
                inf_count = np.isinf(data).sum()
            else:
                nan_count = 0
                inf_count = 0
                
            print(f"    NaN数量: {nan_count}, Inf数量: {inf_count}")
            
            if nan_count > 0 or inf_count > 0:
                print(f"    ⚠️ 清理 {name} 中的无效值...")
                data = np.nan_to_num(data, nan=0.0, posinf=1e6, neginf=-1e6)
            
            return data
        
        # 验证和清理各个数据
        flow_data_clean = validate_and_clean_data(flow_data, "flow_data")
        time_features_clean = validate_and_clean_data(time_features, "time_features")  
        spatial_features_clean = validate_and_clean_data(spatial_features, "spatial_features")
        
        print(f"  📊 数据维度检查:")
        print(f"    flow_data: {flow_data_clean.shape}")
        print(f"    time_features: {time_features_clean.shape}")
        print(f"    spatial_features: {spatial_features_clean.shape}")
        print(f"    edge_index: {edge_index.shape}")
        print(f"    edge_attr: {edge_attr.shape}")
        
        # ✅ 维度兼容性检查
        try:
            # 转换为张量（先在CPU上）
            flow_tensor = torch.tensor(flow_data_clean, dtype=torch.float32)
            time_tensor = torch.tensor(time_features_clean, dtype=torch.float32)
            spatial_tensor = torch.tensor(spatial_features_clean, dtype=torch.float32)
            
            print(f"  ✅ 张量创建成功")
            print(f"    flow_tensor: {flow_tensor.shape}")
            print(f"    time_tensor: {time_tensor.shape}")
            print(f"    spatial_tensor: {spatial_tensor.shape}")
            
            # 检查张量的有效性
            if torch.any(torch.isnan(flow_tensor)):
                print("  ⚠️ flow_tensor 包含NaN，替换为0")
                flow_tensor = torch.nan_to_num(flow_tensor, nan=0.0)
                
            if torch.any(torch.isnan(time_tensor)):
                print("  ⚠️ time_tensor 包含NaN，替换为0")
                time_tensor = torch.nan_to_num(time_tensor, nan=0.0)
                
            if torch.any(torch.isnan(spatial_tensor)):
                print("  ⚠️ spatial_tensor 包含NaN，替换为0")
                spatial_tensor = torch.nan_to_num(spatial_tensor, nan=0.0)
            
            # Float16转换（如果启用）
            if self.config.USE_FLOAT16:
                print("  🔄 转换为Float16...")
                flow_tensor = flow_tensor.half()
                time_tensor = time_tensor.half()
                spatial_tensor = spatial_tensor.half()
                edge_attr = edge_attr.half()
            
            # ✅ 移动到设备（逐个检查）
            print(f"  📱 移动到设备: {self.device}")
            
            try:
                flow_tensor = flow_tensor.to(self.device)
                print("    ✅ flow_tensor 移动成功")
            except Exception as e:
                print(f"    ❌ flow_tensor 移动失败: {e}")
                raise
                
            try:
                time_tensor = time_tensor.to(self.device)
                print("    ✅ time_tensor 移动成功")
            except Exception as e:
                print(f"    ❌ time_tensor 移动失败: {e}")
                raise
                
            try:
                spatial_tensor = spatial_tensor.to(self.device)
                print("    ✅ spatial_tensor 移动成功")
            except Exception as e:
                print(f"    ❌ spatial_tensor 移动失败: {e}")
                raise
                
            try:
                edge_index = edge_index.to(self.device)
                print("    ✅ edge_index 移动成功")
            except Exception as e:
                print(f"    ❌ edge_index 移动失败: {e}")
                raise
                
            try:
                edge_attr = edge_attr.to(self.device)
                print("    ✅ edge_attr 移动成功")
            except Exception as e:
                print(f"    ❌ edge_attr 移动失败: {e}")
                raise
            
            print("  🎉 所有张量准备完成！")
            
            return (flow_tensor, time_tensor, spatial_tensor, edge_index, edge_attr)
            
        except Exception as e:
            print(f"  💥 张量准备过程中出错: {e}")
            print("  🔧 建议:")
            print("    1. 切换到CPU模式调试")
            print("    2. 检查数据维度是否匹配")
            print("    3. 确认GPU内存是否充足")
            raise
    
    def create_batches(self, *tensors, batch_size=None):
        """创建批次数据"""
        if batch_size is None:
            batch_size = self.config.BATCH_SIZE
        
        n_samples = tensors[0].shape[0]
        indices = torch.randperm(n_samples)
        
        batches = []
        for i in range(0, n_samples, batch_size):
            end_idx = min(i + batch_size, n_samples)
            batch_indices = indices[i:end_idx]
            batch = tuple(tensor[batch_indices] for tensor in tensors)
            batches.append(batch)
        
        return batches
    
    def calculate_metrics(self, predictions, targets):
        """计算评估指标"""
        # 转换为numpy数组
        if torch.is_tensor(predictions):
            predictions = predictions.detach().cpu().numpy()
        if torch.is_tensor(targets):
            targets = targets.detach().cpu().numpy()
        
        # 展平数组
        predictions = predictions.flatten()
        targets = targets.flatten()
        
        # 计算指标
        mae = mean_absolute_error(targets, predictions)
        mse = mean_squared_error(targets, predictions)
        rmse = np.sqrt(mse)
        
        # 计算MAPE（避免除零）
        mask = targets != 0
        if mask.sum() > 0:
            mape = np.mean(np.abs((targets[mask] - predictions[mask]) / targets[mask])) * 100
        else:
            mape = 0
        
        return {
            'mae': mae,
            'mse': mse,
            'rmse': rmse,
            'mape': mape
        }
    
    def train_epoch(self, model, optimizer, criterion, train_batches, task='in_flow'):
        """训练一个epoch"""
        model.train()
        total_loss = 0
        all_predictions = []
        all_targets = []
        
        pbar = tqdm(train_batches, desc="Training")
        for batch in pbar:
            flow_batch, time_batch, spatial_batch, edge_index, edge_attr = batch[:5]
            
            optimizer.zero_grad()
            
            # 前向传播
            predictions, _ = model(flow_batch[:, :-1], edge_index, edge_attr, 
                                 time_batch, spatial_batch, task=task)
            
            # 计算损失
            targets = flow_batch[:, -1]  # 最后一个时间步作为目标
            loss = criterion(predictions, targets)
            
            # 反向传播
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            total_loss += loss.item()
            all_predictions.append(predictions.detach())
            all_targets.append(targets.detach())
            
            pbar.set_postfix({'loss': loss.item()})
        
        # 计算整体指标
        all_predictions = torch.cat(all_predictions, dim=0)
        all_targets = torch.cat(all_targets, dim=0)
        metrics = self.calculate_metrics(all_predictions, all_targets)
        
        return total_loss / len(train_batches), metrics
    
    def validate_epoch(self, model, criterion, val_batches, task='in_flow'):
        """验证一个epoch"""
        model.eval()
        total_loss = 0
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for batch in val_batches:
                flow_batch, time_batch, spatial_batch, edge_index, edge_attr = batch[:5]
                
                predictions, _ = model(flow_batch[:, :-1], edge_index, edge_attr,
                                     time_batch, spatial_batch, task=task)
                
                targets = flow_batch[:, -1]
                loss = criterion(predictions, targets)
                
                total_loss += loss.item()
                all_predictions.append(predictions)
                all_targets.append(targets)
        
        all_predictions = torch.cat(all_predictions, dim=0)
        all_targets = torch.cat(all_targets, dim=0)
        metrics = self.calculate_metrics(all_predictions, all_targets)
        
        return total_loss / len(val_batches), metrics
    
    def train_model(self, model, train_data, val_data, task='in_flow'):
        """训练模型"""
        print(f"开始训练 {task} 任务...")
        
        # 准备优化器和损失函数
        optimizer = optim.Adam(model.parameters(), lr=self.config.LEARNING_RATE, 
                              weight_decay=1e-5)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', 
                                                        factor=0.5, patience=3)
        criterion = nn.MSELoss()
        
        # 早停
        early_stopping = EarlyStopping(patience=self.config.PATIENCE)
        
        # 准备数据
        train_batches = self.create_batches(*train_data)
        val_batches = self.create_batches(*val_data)
        
        best_val_loss = float('inf')
        
        for epoch in range(self.config.EPOCHS):
            start_time = time.time()
            
            # 训练
            train_loss, train_metrics = self.train_epoch(model, optimizer, criterion, 
                                                       train_batches, task)
            
            # 验证
            val_loss, val_metrics = self.validate_epoch(model, criterion, val_batches, task)
            
            # 更新学习率
            scheduler.step(val_loss)
            
            # 记录历史
            self.history['train_loss'].append(train_loss)
            self.history['val_loss'].append(val_loss)
            self.history['train_mae'].append(train_metrics['mae'])
            self.history['val_mae'].append(val_metrics['mae'])
            
            epoch_time = time.time() - start_time
            
            print(f"Epoch {epoch+1}/{self.config.EPOCHS}")
            print(f"  训练损失: {train_loss:.4f}, MAE: {train_metrics['mae']:.4f}")
            print(f"  验证损失: {val_loss:.4f}, MAE: {val_metrics['mae']:.4f}")
            print(f"  用时: {epoch_time:.2f}s")
            print("-" * 50)
            
            # 早停检查
            if early_stopping(val_loss, model):
                print(f"Early stopping at epoch {epoch+1}")
                break
            
            # 保存最佳模型
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                torch.save(model.state_dict(), f'best_model_{task}.pth')
        
        return model
    
    def hierarchical_validation(self, data_dict, model_type='complex'):
        """分层验证策略"""
        print("开始分层验证...")
        
        results = {
            'temporal': {},
            'spatial': {},
            'leave_one_out': {}
        }
        
        # 1. 时间分割验证
        print("\n=== 时间分割验证 ===")
        for task in ['in_flow', 'out_flow']:
            print(f"\n训练任务: {task}")
            
            # 创建模型
            model = create_model(self.config, model_type).to(self.device)
            ModelUtils.print_model_info(model)
            
            # 这里需要根据实际数据结构来准备数据
            # 简化示例，实际使用时需要调整
            train_data = self.prepare_sample_data(data_dict, 'train', task)
            val_data = self.prepare_sample_data(data_dict, 'val', task)
            test_data = self.prepare_sample_data(data_dict, 'test', task)
            
            # 训练模型
            trained_model = self.train_model(model, train_data, val_data, task)
            
            # 测试模型
            test_loss, test_metrics = self.validate_epoch(trained_model, nn.MSELoss(), 
                                                        [test_data], task)
            
            results['temporal'][task] = test_metrics
            print(f"{task} 时间验证结果: MAE={test_metrics['mae']:.4f}")
        
        # 2. 空间分割验证
        print("\n=== 空间分割验证 ===")
        # 类似的实现...
        
        # 3. 留一法验证
        print("\n=== 留一法验证 ===")
        # 类似的实现...
        
        return results
    
    def prepare_sample_data(self, data_dict, split, task):
        """准备示例数据（需要根据实际数据结构调整）"""
        # 这里是一个简化的示例实现
        # 实际使用时需要根据data_dict的结构来正确提取和处理数据
        
        if task == 'in_flow':
            flow_data = data_dict['in_flow']
            time_features = data_dict['in_time_features']
        else:
            flow_data = data_dict['out_flow']
            time_features = data_dict['out_time_features']
        
        # 模拟空间特征（实际需要从station_features提取）
        num_stations = len(data_dict['common_stations'])
        spatial_features = np.random.rand(num_stations, 51).astype(np.float32)
        
        # 模拟边信息（实际需要从图构建器获取）
        edge_index = torch.randint(0, num_stations, (2, 1000))
        edge_attr = torch.rand(1000)
        
        return self.prepare_data_tensors(
            flow_data, time_features, spatial_features, 
            edge_index, edge_attr, data_dict['common_stations']
        )
    
    def plot_training_history(self, save_path=None):
        """绘制训练历史"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 损失曲线
        axes[0, 0].plot(self.history['train_loss'], label='Training Loss')
        axes[0, 0].plot(self.history['val_loss'], label='Validation Loss')
        axes[0, 0].set_title('Loss Curves')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Loss')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
        
        # MAE曲线
        axes[0, 1].plot(self.history['train_mae'], label='Training MAE')
        axes[0, 1].plot(self.history['val_mae'], label='Validation MAE')
        axes[0, 1].set_title('MAE Curves')
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('MAE')
        axes[0, 1].legend()
        axes[0, 1].grid(True)
        
        # 学习率曲线（如果有的话）
        axes[1, 0].set_title('Learning Rate')
        axes[1, 0].set_xlabel('Epoch')
        axes[1, 0].set_ylabel('Learning Rate')
        axes[1, 0].grid(True)
        
        # 其他指标
        axes[1, 1].set_title('Other Metrics')
        axes[1, 1].grid(True)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def save_results(self, results, filepath):
        """保存结果"""
        import json
        
        # 转换numpy类型为Python原生类型
        def convert_numpy(obj):
            if isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, np.float32):
                return float(obj)
            elif isinstance(obj, dict):
                return {k: convert_numpy(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy(v) for v in obj]
            else:
                return obj
        
        results_clean = convert_numpy(results)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(results_clean, f, indent=2, ensure_ascii=False)
        
        print(f"结果已保存到: {filepath}") 