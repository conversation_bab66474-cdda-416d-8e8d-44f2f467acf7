import os
import torch

class Config:
    # 数据路径配置
    BASE_DIR = r"C:\Users\<USER>\Desktop\接驳"
    IN_STATION_PATH = os.path.join(BASE_DIR, "in_500_with_coords.shp")
    OUT_STATION_PATH = os.path.join(BASE_DIR, "out_500_with_coords.shp")
    OD_PATH = os.path.join(BASE_DIR, "updated北京市_subway_od_2024_modified3.csv")
    STATION_FEATURES_PATH = os.path.join(BASE_DIR, "station_features_result.csv")
    
    # 输出路径配置
    OUT_IN_PREDICTIONS = os.path.join(BASE_DIR, "in_500_predictions_with_coords.shp")
    OUT_OUT_PREDICTIONS = os.path.join(BASE_DIR, "out_500_predictions_with_coords.shp")
    OUT_OD_PREDICTIONS = os.path.join(BASE_DIR, "od_predictions.csv")
    OUT_COMPARISON = os.path.join(BASE_DIR, "prediction_comparison.csv")
    
    # 模型配置
    HIDDEN_DIM = 128
    NUM_LAYERS = 3
    DROPOUT = 0.2
    LEARNING_RATE = 0.001
    EPOCHS = 20
    PATIENCE = 5
    BATCH_SIZE = 32
    
    # 数据处理配置
    USE_FLOAT16 = True  # 使用float16减少内存使用
    TIME_FEATURES = 24  # 24小时时间特征
    SPATIAL_THRESHOLD = 0.01  # 栅格-站点关联阈值
    
    # 图构建配置
    DISTANCE_THRESHOLD = 2000  # 米，用于构建图的距离阈值
    K_NEAREST = 5  # K近邻图
    
    # 验证配置
    TRAIN_RATIO = 0.7
    VAL_RATIO = 0.15
    TEST_RATIO = 0.15
    
    # 设备配置
    DEVICE = "cuda" if torch.cuda.is_available() else "cpu" 