"""
工具函数模块
包含各种辅助功能
"""
import numpy as np
import torch

class ModelUtils:
    @staticmethod
    def set_random_seed(seed=42):
        """设置随机种子以确保结果可复现"""
        np.random.seed(seed)
        torch.manual_seed(seed)
        if torch.cuda.is_available():
            torch.cuda.manual_seed(seed)
    
    @staticmethod
    def count_parameters(model):
        """计算模型参数数量"""
        return sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    @staticmethod
    def get_device():
        """获取可用设备"""
        return torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    @staticmethod
    def print_model_info(model):
        """打印模型信息"""
        device = ModelUtils.get_device()
        param_count = ModelUtils.count_parameters(model)
        print(f"模型参数数量: {param_count:,}")
        print(f"使用设备: {device}")
        return device



