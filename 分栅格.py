import pandas as pd
import numpy as np
from shapely.geometry import Polygon
import geopandas as gpd
import math
#in里面的s_lon没用，out的s_lon没用
# 读取数据
df = pd.read_csv('jiebo_in_modified3.csv')

# # 转换时间戳为小时
# df['hour'] = pd.to_datetime(df['date'], unit='ms').dt.hour

# 北京的经纬度范围（大致）
beijing_bounds = {
    'min_lon': 115.7,
    'max_lon': 117.4,
    'min_lat': 39.4,
    'max_lat': 41.6
}

# 计算每度经纬度对应的米数（在北京纬度附近）
lat_mid = (beijing_bounds['min_lat'] + beijing_bounds['max_lat']) / 2
meters_per_lat = 111000  # 每纬度约111km
meters_per_lon = 111000 * math.cos(math.radians(lat_mid))  # 考虑纬度影响

for grid_size_meters in [200,250,500,1000]:

    grid_size_lat = grid_size_meters / meters_per_lat
    grid_size_lon = grid_size_meters / meters_per_lon
    
    # 计算栅格数量
    n_lat = int((beijing_bounds['max_lat'] - beijing_bounds['min_lat']) / grid_size_lat) + 1
    n_lon = int((beijing_bounds['max_lon'] - beijing_bounds['min_lon']) / grid_size_lon) + 1
    
    # 打印实际栅格边长
    actual_lat_meters = grid_size_lat * meters_per_lat
    actual_lon_meters = grid_size_lon * meters_per_lon
    print(f"栅格边长 - 纬度方向: {actual_lat_meters:.2f}米, 经度方向: {actual_lon_meters:.2f}米")
    
    # 创建栅格ID
    df['grid_lat'] = ((df['pre_lat'] - beijing_bounds['min_lat']) / grid_size_lat).astype(int)
    df['grid_lon'] = ((df['pre_lon'] - beijing_bounds['min_lon']) / grid_size_lon).astype(int)
    df['grid_id'] = df['grid_lat'] * n_lon + df['grid_lon']
    
    # 按小时、栅格和站点统计人数
    stats = df.groupby(['hour', 'grid_id', 'station']).size().reset_index(name='count')
    
    # 创建栅格的几何形状
    def create_grid_polygon(grid_id):
        grid_lat = grid_id // n_lon
        grid_lon = grid_id % n_lon
        
        min_lat = beijing_bounds['min_lat'] + grid_lat * grid_size_lat
        min_lon = beijing_bounds['min_lon'] + grid_lon * grid_size_lon
        
        polygon = Polygon([
            (min_lon, min_lat),
            (min_lon + grid_size_lon, min_lat),
            (min_lon + grid_size_lon, min_lat + grid_size_lat),
            (min_lon, min_lat + grid_size_lat),
            (min_lon, min_lat)
        ])
        return polygon
    
    # 添加几何信息
    grid_geometries = {grid_id: create_grid_polygon(grid_id) for grid_id in stats['grid_id'].unique()}
    stats['geometry'] = stats['grid_id'].map(grid_geometries)
    
    # 转换为GeoDataFrame
    gdf = gpd.GeoDataFrame(stats, geometry='geometry', crs="EPSG:4326")
    
    # 处理中文编码
    gdf['station'] = gdf['station'].str.encode('utf-8').str.decode('utf-8')
    
    # 保存为CSV
    gdf.drop('geometry', axis=1).to_csv(f'in_{grid_size_meters}.csv', index=False, encoding='utf-8-sig')
    
    # 保存为shp文件
    gdf.to_file(f'in_{grid_size_meters}.shp', encoding='utf-8')
    
    print("处理完成，已生成 grid_station_counts.csv 和 grid_station_counts.shp")