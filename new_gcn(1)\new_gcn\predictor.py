import torch
import numpy as np
import pandas as pd
import geopandas as gpd
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import seaborn as sns
from config import Config
from graph_builder import GraphBuilder
from gcn_model import create_model, ModelUtils

class GCNPredictor:
    """GCN预测器"""
    def __init__(self, config):
        self.config = config
        self.device = torch.device(config.DEVICE)
        self.graph_builder = GraphBuilder()
        self.models = {}
        self.scalers = {}
        
    def load_trained_model(self, model_path, task='in_flow', model_type='complex'):
        """加载训练好的模型"""
        print(f"加载 {task} 模型...")
        model = create_model(self.config, model_type).to(self.device)
        model.load_state_dict(torch.load(model_path, map_location=self.device))
        model.eval()
        self.models[task] = model
        print(f"{task} 模型加载完成")
        return model
    
    def predict_flow(self, model, flow_data, time_features, spatial_features, 
                    edge_index, edge_attr, task='in_flow'):
        """执行流量预测"""
        model.eval()
        with torch.no_grad():
            # 准备输入数据
            flow_tensor = torch.tensor(flow_data, dtype=torch.float32).to(self.device)
            time_tensor = torch.tensor(time_features, dtype=torch.float32).to(self.device)
            spatial_tensor = torch.tensor(spatial_features, dtype=torch.float32).to(self.device)
            
            if self.config.USE_FLOAT16:
                flow_tensor = flow_tensor.half()
                time_tensor = time_tensor.half()
                spatial_tensor = spatial_tensor.half()
            
            # 预测
            predictions, attention_weights = model(
                flow_tensor, edge_index, edge_attr, 
                time_tensor, spatial_tensor, task=task
            )
            
            return predictions.cpu().numpy(), attention_weights.cpu().numpy()
    
    def predict_new_line_impact(self, current_data, new_stations, new_connections, 
                               impact_radius=2000):
        """预测新线路对人流量的影响"""
        print("正在预测新线路影响...")
        
        # 1. 构建包含新线路的图
        current_edge_index, current_edge_attr, current_stations = self.graph_builder.build_graph(
            current_data['station_coords'], current_data['od_data']
        )
        
        new_edge_index, new_edge_attr, all_stations = self.graph_builder.add_new_line_to_graph(
            current_edge_index, current_edge_attr, current_stations, 
            new_stations, new_connections
        )
        
        # 2. 计算影响区域
        impact_stations = self.calculate_impact_area(
            current_data['station_coords'], new_stations, impact_radius
        )
        
        # 3. 预测各任务的流量变化
        predictions = {}
        for task in ['in_flow', 'out_flow']:
            if task in self.models:
                # 准备预测数据
                pred_data = self.prepare_prediction_data(
                    current_data, all_stations, task
                )
                
                # 执行预测
                current_pred, _ = self.predict_flow(
                    self.models[task], *pred_data, edge_index=current_edge_index, 
                    edge_attr=current_edge_attr, task=task
                )
                
                new_pred, _ = self.predict_flow(
                    self.models[task], *pred_data, edge_index=new_edge_index, 
                    edge_attr=new_edge_attr, task=task
                )
                
                # 计算变化
                flow_change = new_pred - current_pred[:len(new_pred)]
                
                predictions[task] = {
                    'current': current_pred,
                    'new': new_pred,
                    'change': flow_change,
                    'impact_stations': impact_stations
                }
        
        # 4. 分析和可视化结果
        self.analyze_line_impact(predictions, new_stations, all_stations)
        
        return predictions
    
    def calculate_impact_area(self, station_coords, new_stations, radius):
        """计算新线路的影响区域"""
        impact_stations = set()
        
        for new_station in new_stations:
            # 假设新站点坐标已知，这里需要提供
            new_coord = (0, 0)  # 需要实际坐标
            
            for station, coord in station_coords.items():
                distance = self.calculate_distance(new_coord, coord)
                if distance <= radius:
                    impact_stations.add(station)
        
        return list(impact_stations)
    
    def calculate_distance(self, coord1, coord2):
        """计算两点间距离（米）"""
        from math import radians, cos, sin, asin, sqrt
        
        lon1, lat1 = coord1
        lon2, lat2 = coord2
        
        # 转换为弧度
        lon1, lat1, lon2, lat2 = map(radians, [lon1, lat1, lon2, lat2])
        
        # haversine公式
        dlon = lon2 - lon1
        dlat = lat2 - lat1
        a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
        c = 2 * asin(sqrt(a))
        r = 6371000  # 地球半径（米）
        
        return c * r
    
    def predict_grid_to_station_flow(self, grid_coords, station_coords, 
                                   threshold=None, time_hour=8):
        """预测栅格到站点的人流量"""
        if threshold is None:
            threshold = self.config.SPATIAL_THRESHOLD
        
        print(f"正在预测栅格到站点流量 (阈值: {threshold})...")
        
        # 1. 创建栅格-站点连接
        connections = self.create_grid_station_connections(
            grid_coords, station_coords, threshold
        )
        
        # 2. 构建栅格-站点图
        grid_station_edge_index, grid_station_edge_attr, all_nodes, node_mapping = \
            self.graph_builder.create_grid_station_graph(
                grid_coords, station_coords, threshold
            )
        
        # 3. 预测流量
        predictions = {}
        
        # 预测从栅格到站点的流量
        for grid_id, connected_stations in connections.items():
            if connected_stations:  # 如果有连接的站点
                grid_predictions = {}
                
                for station, distance in connected_stations:
                    # 基于距离和时间计算预测流量
                    # 这里使用简化的重力模型
                    base_flow = self.estimate_base_flow(station, time_hour)
                    distance_factor = 1.0 / (1.0 + distance * 0.001)  # 距离衰减
                    
                    predicted_flow = base_flow * distance_factor
                    grid_predictions[station] = predicted_flow
                
                predictions[grid_id] = grid_predictions
        
        # 4. 应用阈值过滤
        filtered_predictions = self.apply_flow_threshold(predictions, threshold)
        
        print(f"预测了 {len(filtered_predictions)} 个栅格的流量")
        return filtered_predictions
    
    def predict_station_to_grid_flow(self, station_coords, grid_coords, 
                                   threshold=None, time_hour=18):
        """预测站点到栅格的人流量"""
        if threshold is None:
            threshold = self.config.SPATIAL_THRESHOLD
        
        print(f"正在预测站点到栅格流量 (阈值: {threshold})...")
        
        # 1. 创建站点-栅格连接
        connections = {}
        for station, (station_lon, station_lat) in station_coords.items():
            connections[station] = []
            for grid_id, (grid_lon, grid_lat) in grid_coords.items():
                distance = self.calculate_distance(
                    (station_lon, station_lat), (grid_lon, grid_lat)
                )
                if distance < threshold:
                    connections[station].append((grid_id, distance))
        
        # 2. 预测流量
        predictions = {}
        for station, connected_grids in connections.items():
            if connected_grids:  # 如果有连接的栅格
                station_predictions = {}
                
                # 估计站点的出站流量
                base_outflow = self.estimate_base_outflow(station, time_hour)
                
                # 根据距离分配到各个栅格
                total_weight = sum(1.0 / (1.0 + dist * 0.001) for _, dist in connected_grids)
                
                for grid_id, distance in connected_grids:
                    weight = (1.0 / (1.0 + distance * 0.001)) / total_weight
                    predicted_flow = base_outflow * weight
                    station_predictions[grid_id] = predicted_flow
                
                predictions[station] = station_predictions
        
        # 3. 应用阈值过滤
        filtered_predictions = self.apply_flow_threshold(predictions, threshold)
        
        print(f"预测了 {len(filtered_predictions)} 个站点的流量分布")
        return filtered_predictions
    
    def create_grid_station_connections(self, grid_coords, station_coords, threshold):
        """创建栅格-站点连接"""
        connections = {}
        
        for grid_id, (grid_lon, grid_lat) in grid_coords.items():
            connections[grid_id] = []
            for station, (station_lon, station_lat) in station_coords.items():
                distance = self.calculate_distance(
                    (grid_lon, grid_lat), (station_lon, station_lat)
                )
                if distance < threshold:
                    connections[grid_id].append((station, distance))
        
        # 移除没有连接的栅格
        connections = {k: v for k, v in connections.items() if v}
        
        return connections
    
    def estimate_base_flow(self, station, hour):
        """估计站点基础进站流量"""
        # 这里使用简化的估计方法
        # 实际应用中应该基于历史数据和站点特征
        
        # 早高峰时段
        if 7 <= hour <= 9:
            base_flow = 100.0
        # 晚高峰时段
        elif 17 <= hour <= 19:
            base_flow = 80.0
        # 平峰时段
        else:
            base_flow = 30.0
        
        # 添加一些随机变化
        variation = np.random.normal(1.0, 0.2)
        return max(0, base_flow * variation)
    
    def estimate_base_outflow(self, station, hour):
        """估计站点基础出站流量"""
        # 类似进站流量的估计
        if 8 <= hour <= 10:
            base_flow = 80.0
        elif 18 <= hour <= 20:
            base_flow = 120.0
        else:
            base_flow = 40.0
        
        variation = np.random.normal(1.0, 0.2)
        return max(0, base_flow * variation)
    
    def apply_flow_threshold(self, predictions, threshold):
        """应用流量阈值过滤"""
        filtered = {}
        
        for source, targets in predictions.items():
            filtered_targets = {
                target: flow for target, flow in targets.items() 
                if flow >= threshold
            }
            if filtered_targets:
                filtered[source] = filtered_targets
        
        return filtered
    
    def prepare_prediction_data(self, data_dict, stations, task):
        """准备预测数据"""
        if task == 'in_flow':
            flow_data = data_dict['in_flow']
            time_features = data_dict['in_time_features']
        else:
            flow_data = data_dict['out_flow']
            time_features = data_dict['out_time_features']
        
        # 模拟空间特征
        num_stations = len(stations)
        spatial_features = np.random.rand(1, 24, num_stations, 51).astype(np.float32)
        
        return flow_data, time_features, spatial_features
    
    def analyze_line_impact(self, predictions, new_stations, all_stations):
        """分析新线路影响"""
        print("\n=== 新线路影响分析 ===")
        
        for task, pred_data in predictions.items():
            print(f"\n{task.upper()} 流量变化:")
            
            change = pred_data['change']
            impact_stations = pred_data['impact_stations']
            
            # 统计正负影响
            positive_impact = np.sum(change > 0)
            negative_impact = np.sum(change < 0)
            total_change = np.sum(np.abs(change))
            
            print(f"  受正面影响的站点: {positive_impact}")
            print(f"  受负面影响的站点: {negative_impact}")
            print(f"  总流量变化量: {total_change:.2f}")
            
            # 找出影响最大的站点
            max_increase_idx = np.argmax(change)
            max_decrease_idx = np.argmin(change)
            
            print(f"  流量增加最多的站点: {all_stations[max_increase_idx]} (+{change[max_increase_idx]:.2f})")
            print(f"  流量减少最多的站点: {all_stations[max_decrease_idx]} ({change[max_decrease_idx]:.2f})")
    
    def visualize_predictions(self, predictions, coords, title="Flow Predictions"):
        """可视化预测结果"""
        plt.figure(figsize=(12, 8))
        
        # 提取坐标和流量值
        lons, lats, flows = [], [], []
        
        for source, targets in predictions.items():
            if source in coords:
                source_coord = coords[source]
                total_flow = sum(targets.values())
                
                lons.append(source_coord[0])
                lats.append(source_coord[1])
                flows.append(total_flow)
        
        # 创建散点图
        plt.scatter(lons, lats, c=flows, s=100, alpha=0.7, cmap='viridis')
        plt.colorbar(label='Flow')
        plt.xlabel('Longitude')
        plt.ylabel('Latitude')
        plt.title(title)
        plt.grid(True, alpha=0.3)
        plt.show()
    
    def save_predictions(self, predictions, output_path, prediction_type='grid_to_station'):
        """保存预测结果"""
        if prediction_type == 'grid_to_station':
            # 保存栅格到站点的预测
            results = []
            for grid_id, station_flows in predictions.items():
                for station, flow in station_flows.items():
                    results.append({
                        'grid_id': grid_id,
                        'station': station,
                        'predicted_flow': flow
                    })
            
            df = pd.DataFrame(results)
            df.to_csv(output_path, index=False, encoding='utf-8')
            
        elif prediction_type == 'station_to_grid':
            # 保存站点到栅格的预测
            results = []
            for station, grid_flows in predictions.items():
                for grid_id, flow in grid_flows.items():
                    results.append({
                        'station': station,
                        'grid_id': grid_id,
                        'predicted_flow': flow
                    })
            
            df = pd.DataFrame(results)
            df.to_csv(output_path, index=False, encoding='utf-8')
            
        elif prediction_type == 'new_line_impact':
            # 保存新线路影响预测
            results = {}
            for task, pred_data in predictions.items():
                results[task] = {
                    'change': pred_data['change'].tolist(),
                    'impact_stations': pred_data['impact_stations']
                }
            
            import json
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"预测结果已保存到: {output_path}")
    
    def generate_prediction_report(self, all_predictions, output_dir):
        """生成预测报告"""
        print("正在生成预测报告...")
        
        report = {
            'summary': {},
            'detailed_results': all_predictions,
            'statistics': {}
        }
        
        # 计算汇总统计
        for pred_type, predictions in all_predictions.items():
            if predictions:
                flows = []
                for source, targets in predictions.items():
                    flows.extend(targets.values())
                
                report['summary'][pred_type] = {
                    'total_connections': len(predictions),
                    'total_flow': sum(flows),
                    'average_flow': np.mean(flows) if flows else 0,
                    'max_flow': max(flows) if flows else 0,
                    'min_flow': min(flows) if flows else 0
                }
        
        # 保存报告
        import json
        report_path = f"{output_dir}/prediction_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"预测报告已保存到: {report_path}")
        return report 