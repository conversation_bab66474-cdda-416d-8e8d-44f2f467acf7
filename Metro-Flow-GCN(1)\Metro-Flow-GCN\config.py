#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件
"""

import os

# 数据路径配置
DATA_CONFIG = {
    'data_path': 'C:/Users/<USER>/Desktop/接驳/',
    'in_station_file': 'in_500_with_coords.shp',
    'out_station_file': 'out_500_with_coords.shp',
    'od_file': 'updated北京市_subway_od_2024_modified3.csv',
    'station_features_file': 'station_features_result.csv',
    'grid_file': 'grid_data.shp'  # 可选
}

# 模型配置
MODEL_CONFIG = {
    'spatial_hidden_dim': 128,
    'temporal_embed_dim': 64,
    'num_gcn_layers': 3,
    'num_prediction_tasks': 3,  # 进站、出站、OD
    'dropout': 0.2,
    'use_float16': True
}

# 训练配置
TRAINING_CONFIG = {
    'epochs': 20,
    'patience': 5,
    'batch_size': 1,
    'learning_rate': 0.001,
    'weight_decay': 1e-4,
    'balance_weight': 0.1  # 流量平衡损失权重
}

# 数据处理配置
DATA_PROCESSING_CONFIG = {
    'distance_threshold': 1000,  # 栅格-站点连接阈值(米)
    'test_size': 0.2,
    'val_size': 0.1,
    'random_state': 42,
    'use_float16': True
}

# 输出配置
OUTPUT_CONFIG = {
    'output_dir': './outputs',
    'save_predictions': True,
    'save_plots': True,
    'save_model': True
}

# 设备配置
DEVICE_CONFIG = {
    'use_cuda': True,
    'cuda_device': 0
}

# 验证策略配置
VALIDATION_CONFIG = {
    'strategy': 'hierarchical',  # 分层验证
    'time_split_ratio': [0.7, 0.1, 0.2],  # 训练、验证、测试时间比例
    'spatial_split_ratio': 0.2,  # 空间测试比例
    'leave_one_out': False  # 是否使用留一法
}

# 栅格分析配置
GRID_CONFIG = {
    'connection_threshold': 0.3,  # 连接强度阈值
    'distance_weight': 0.6,  # 距离权重
    'flow_weight': 0.4,  # 流量权重
    'visualization': True
}

# 新线路影响分析配置
NEW_LINE_CONFIG = {
    'impact_radius': 2000,  # 影响半径(米)
    'analysis_methods': ['connectivity', 'flow_redistribution', 'accessibility']
}

def get_config():
    """获取完整配置"""
    config = {
        'data': DATA_CONFIG,
        'model': MODEL_CONFIG,
        'training': TRAINING_CONFIG,
        'data_processing': DATA_PROCESSING_CONFIG,
        'output': OUTPUT_CONFIG,
        'device': DEVICE_CONFIG,
        'validation': VALIDATION_CONFIG,
        'grid': GRID_CONFIG,
        'new_line': NEW_LINE_CONFIG
    }
    return config

def update_data_path(new_path):
    """更新数据路径"""
    DATA_CONFIG['data_path'] = new_path
    
def get_file_paths():
    """获取所有文件的完整路径"""
    base_path = DATA_CONFIG['data_path']
    return {
        'in_station': os.path.join(base_path, DATA_CONFIG['in_station_file']),
        'out_station': os.path.join(base_path, DATA_CONFIG['out_station_file']),
        'od': os.path.join(base_path, DATA_CONFIG['od_file']),
        'station_features': os.path.join(base_path, DATA_CONFIG['station_features_file']),
        'grid': os.path.join(base_path, DATA_CONFIG['grid_file'])
    } 