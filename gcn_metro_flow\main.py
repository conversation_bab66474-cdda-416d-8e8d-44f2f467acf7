"""
主程序 - 纯GCN+时间特征地铁流量预测系统
"""
import os
import sys
import time
import torch
import warnings
warnings.filterwarnings('ignore')

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_loader import MetroDataLoader
from graph_builder import MetroGraphBuilder
from trainer import MetroFlowTrainer
from predictor import MetroFlowPredictor
from grid_allocator import GridFlowAllocator
from config import MODEL_CONFIG, DEVICE_CONFIG

def main():
    """主函数"""
    print("="*60)
    print("纯GCN+时间特征地铁流量预测系统")
    print("="*60)
    
    # 检查CUDA可用性
    if torch.cuda.is_available():
        print(f"CUDA available: {torch.cuda.get_device_name(0)}")
        print(f"CUDA memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    else:
        print("CUDA not available, using CPU")
    
    start_time = time.time()
    
    try:
        # 1. 数据加载和预处理
        print("\n" + "="*50)
        print("1. 数据加载和预处理")
        print("="*50)
        
        data_loader = MetroDataLoader()
        data_loader.load_all_data()
        
        # 2. 图构建
        print("\n" + "="*50)
        print("2. 构建地铁网络图")
        print("="*50)
        
        graph_builder = MetroGraphBuilder(data_loader)
        graphs = graph_builder.build_graphs()
        
        # 3. 模型训练
        print("\n" + "="*50)
        print("3. 模型训练")
        print("="*50)
        
        trainer = MetroFlowTrainer(data_loader, graph_builder)
        training_results = trainer.train_with_hierarchical_validation()
        
        # 4. 预测
        print("\n" + "="*50)
        print("4. 流量预测")
        print("="*50)
        
        predictor = MetroFlowPredictor(data_loader, graph_builder, 'best_model_fold.pth')
        hourly_results = predictor.predict_all_hours()
        
        # 5. 评估
        print("\n" + "="*50)
        print("5. 模型评估")
        print("="*50)
        
        overall_metrics = predictor.compute_metrics()
        hourly_metrics = predictor.compute_hourly_metrics()
        
        # 6. 栅格流量分配
        print("\n" + "="*50)
        print("6. 栅格流量分配")
        print("="*50)
        
        grid_allocator = GridFlowAllocator(data_loader, predictor)
        grid_allocator.train_allocation_model()
        grid_allocator.allocate_station_flows_to_grids()
        
        # 7. 保存结果
        print("\n" + "="*50)
        print("7. 保存预测结果")
        print("="*50)
        
        predictor.save_predictions()
        grid_allocator.save_grid_predictions()
        
        # 8. 生成报告
        print("\n" + "="*50)
        print("8. 生成总结报告")
        print("="*50)
        
        predictor.generate_summary_report(overall_metrics, hourly_metrics)
        
        # 保存训练结果
        save_training_results(training_results, overall_metrics, hourly_metrics)
        
        end_time = time.time()
        print(f"\n总运行时间: {end_time - start_time:.2f} 秒")
        print("预测完成！所有结果已保存。")
        
    except Exception as e:
        print(f"\n错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def save_training_results(training_results, overall_metrics, hourly_metrics):
    """保存训练结果"""
    import json
    import pandas as pd
    
    # 保存训练结果
    results_dict = {
        'training_results': training_results,
        'overall_metrics': overall_metrics,
        'hourly_metrics': hourly_metrics,
        'model_config': MODEL_CONFIG,
        'device_config': DEVICE_CONFIG
    }
    
    # 转换numpy数组为列表以便JSON序列化
    def convert_numpy(obj):
        if hasattr(obj, 'tolist'):
            return obj.tolist()
        elif isinstance(obj, dict):
            return {k: convert_numpy(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_numpy(item) for item in obj]
        else:
            return obj
    
    results_dict = convert_numpy(results_dict)
    
    # 保存为JSON
    with open('training_results.json', 'w', encoding='utf-8') as f:
        json.dump(results_dict, f, indent=2, ensure_ascii=False)
    
    # 保存每小时指标为CSV
    hourly_df_data = []
    for hour, metrics in hourly_metrics.items():
        row = {'hour': hour}
        row.update(metrics)
        hourly_df_data.append(row)
    
    if hourly_df_data:
        hourly_df = pd.DataFrame(hourly_df_data)
        hourly_df.to_csv('hourly_metrics.csv', index=False)
    
    print("训练结果已保存到 training_results.json 和 hourly_metrics.csv")

def run_quick_test():
    """快速测试模式（用于调试）"""
    print("运行快速测试模式...")
    
    # 修改配置以减少计算量
    MODEL_CONFIG['epochs'] = 10
    MODEL_CONFIG['gcn_layers'] = 2
    MODEL_CONFIG['gcn_hidden_dim'] = 64
    
    return main()

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='地铁流量预测系统')
    parser.add_argument('--test', action='store_true', help='运行快速测试模式')
    parser.add_argument('--gpu', type=int, default=0, help='GPU设备ID')
    parser.add_argument('--batch_size', type=int, default=None, help='批次大小')
    
    args = parser.parse_args()
    
    # 设置GPU
    if args.gpu is not None:
        DEVICE_CONFIG['cuda_device'] = args.gpu
    
    # 设置批次大小
    if args.batch_size is not None:
        MODEL_CONFIG['batch_size'] = args.batch_size
    
    # 运行程序
    if args.test:
        success = run_quick_test()
    else:
        success = main()
    
    sys.exit(0 if success else 1)
